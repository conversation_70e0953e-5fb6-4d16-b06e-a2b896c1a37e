<?php


namespace App\Tests\Api;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Env;
use Codeception\Example;
use Codeception\Attribute\Examples;
use CoreBundle\Utils\Util;

class UserCest
{
    #[Examples([
        'user_id' => *********,
        'email' => '<EMAIL>',
        'fullName' => '<PERSON><PERSON><PERSON>',
        'status' => 'active',
        'supportUniTeller'=> true,
        'hasBankRecipient' => true,
        'hasCashRecipient' => true
    ], [
        'user_id' => *********,
        'email' => '<EMAIL>',
        'fullName' => 'BOTM Test member',
        'status' => 'active',
        'supportUniTeller'=> true,
        'hasBankRecipient' => true,
        'hasCashRecipient' => true
    ]
    )]
    public function tryGetProfile(ApiTester $I, Example $example)
    {
        $response = $I->sendGET('/mex/m/user/profile');
        $responseJson = json_decode($response, true);

        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "id" => $example['user_id'],
                "email" => $example['email'],
                "fullName" => $example['fullName'],
                "status" => $example['status'],
                "supportUniTeller" => $example['supportUniTeller'],
                "hasBankRecipient" => $example['hasBankRecipient'],
                "hasCashRecipient" => $example['hasCashRecipient'],
            ]
        ]);
        $I->assertNotEmpty($responseJson['data']['payoutMethods']['bank.MX.MXN']);
        $I->assertNotEmpty($responseJson['data']['payoutMethods']['cash.MX.MXN']);
    }

    #[Examples(
        ['user_id' => *********, 'processor' => 'Rapid' ], //Mesina Ramirez - non BOTM
        ['user_id' => *********, 'processor' => 'BOTM' ]  //BOTM Test member
    )]
    public function tryGetHome(ApiTester $I, Example $example)
    {
        $response = $I->sendGET("/mex/m/user/home");
        $responseJson = json_decode($response, true);

        $I->seeResponseContainsJson([ "success"=> true ]);
        $I->assertNotEmpty($responseJson["data"]["card"]);
        $I->assertIsArray($responseJson["data"]["transactions"]);

        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "card" => [
                    "processor" => $example['processor']
                ]
            ]
        ]);
    }

    #[Examples(
        // [ 'user_id' => *********, 'cardNumber' => '********' ], // Rapid
        [ 'user_id' => *********, 'cardNumber' => '22191857' ], // BOTM
    )]
    public function tryChangeAndViewPin(ApiTester $I, Example $example)
    {
        $I->sendGET("/mex/m/user/pin-send?passcode={$I->getPasscode($example['user_id'])}");
        $I->seeResponseContainsJson([ "success" => true ]);

        $pinVerifyResponse = $I->sendGet("/mex/m/user/pin-verify?code={$I->getPinCode()}&action=pin_token");
        $I->seeResponseContainsJson([ "success" => true ]);
        $pinVerifyResponseJson = json_decode($pinVerifyResponse, true);

        $newPin = mt_rand(1000,9999);
        $I->sendPost("/mex/m/user/change-pin?token={$pinVerifyResponseJson['data']}&pin={$newPin}&cardNumber={$example['cardNumber']}");
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([ "success" => true ]);

        $I->sendGet("/mex/m/user/view-pin?token={$pinVerifyResponseJson['data']}");
        $I->seeResponseContainsJson([ "success" => true, "data" => "****" ]);
    }

    #[Examples(
        ['user_id' => ********* ], //Mesina Ramirez - non BOTM
        ['user_id' => ********* ]  //BOTM Test member
    )]
    public function tryWebViewUrls(ApiTester $I)
    {
        $response = $I->sendGET("/mex/m/user/webview/urls");
        $responseJson = json_decode($response, true);

        $I->seeResponseContainsJson([ "success" => true ]);
        $I->assertStringStartsWith("https://", $responseJson['data'][0]['url']);
    }

    #[Examples(
        ['user_id' => ********* ], //Mesina Ramirez - non BOTM
        ['user_id' => ********* ]  //BOTM Test member
    )]
    public function tryVerifyPasscode(ApiTester $I, Example $example)
    {
        $passcode = $I->getPasscode($example['user_id']);
        $I->sendGET("/mex/m/user/verify-passcode?input={$passcode}");
        $I->seeResponseContainsJson([ "success"=> true ]);
    }

    #[Env("dev")]
    #[Examples(
        ['user_id' => ********* ] //Mesina Ramirez - non BOTM
    )]
    public function tryGetStateDisclaimer(ApiTester $I)
    {
        $I->sendGET("/mex/m/user/getStateDisclaimer");
        $resp = $I->allowedUniTellerServerError();

        if (true !== $resp) {
            $I->seeResponseContainsJson([ "success" => true ]);
            $I->assertNotEmpty($resp["data"]["content"]);
        }
    }

    #[Env("dev")]
    #[Examples([
        'user_id' => *********, //Mesina Ramirez - non BOTM
        'answer' => "New York",
        'answerHint' => "New York",
        'title' =>  "In what city did you meet your spouse/significant other?"
    ]
    )]
    public function tryGetQuestionAnswer(ApiTester $I, Example $example)
    {
        $I->sendGET("/mex/m/user/getQuestionAnswer");
        $resp = $I->allowedUniTellerServerError();

        if ($resp !== true) {
            $I->seeResponseContainsJson([
                "success" => true,
                "data" => [
                    "answer" => $example["answer"],
                    "answerHint" => $example["answerHint"],
                    "title" => $example["title"]
                ]
            ]);
        }
    }
}
