<?php


namespace App\Tests\Api;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Env;
use Codeception\Attribute\Examples;
use Codeception\Example;

class BankTransferCest
{
    #[Examples(
        ['user_id' => *********, 'minAmount' => 10, 'result' => true, ], //<PERSON><PERSON><PERSON> - non BOTM
        ['user_id' => *********, 'minAMount' => 2500, 'result' => true, ]  //BOTM Test member
    )]
    public function tryInitTransfer(ApiTester $I, Example $example)
    {
        $I->sendGet("/mex/m/transfer/init");

        $expectedResult = (bool) $example['result'];
        $I->seeResponseContainsJson($expectedResult ? [
            'success' => true,
            'data' => [
                'payoutOptions' => [
                    'minAmount' => $example['minAmount'] ?? 2500,
                    'maxAmount' => 500000,
                ]
            ],
        ] : [
            'success' => false,
        ]);
    }

    #[Examples(
        //Mesina - non BOTM
        ['user_id' => *********, 'amount' => 200000, 'fee' => 100, 'recipient' => *********, 'result' => false, 'resultData' => []],
        ['user_id' => *********, 'amount' => 600000, 'fee' => 100, 'recipient' => *********, 'result' => false],
        ['user_id' => *********, 'amount' => 10, 'fee' => 100, 'recipient' => *********, 'result' => true],
        //BOTM test
        ['user_id' => *********, 'amount' => 100, 'fee' => 100, 'recipient' => *********, 'result' => false],
        ['user_id' => *********, 'amount' => 2500, 'fee' => 100, 'recipient' => *********, 'result' => true]
    )]
    // #[Env("staging")]
    public function tryCreateBankTransfer(ApiTester $I, Example $example)
    {
        $transferResponse = $I->sendPost("/mex/m/transfer/create?passcode={$I->getPasscode($example['user_id'])}", [
            "transferMethod" => 'Bank Transfer',
            'amount'=> $example['amount'],
            'recipient'=> $example['recipient'],
        ]);

        $expectedResult = (bool) $example['result'];
        if ($expectedResult) {
            if (true === $I->allowedBankTransferError()) {
                $I->seeResponseContainsJson([
                    'success' => false,
                ]);
                return;
            }
        }

        $I->seeResponseContainsJson($expectedResult ? [
            'success' => true,
            'data' => [
                'status' => 'Completed',
                'payoutType' => 'bank',
                'partner' => 'rapyd',
                'recipient' => [
                    'id' => $example['recipient']
                ],
                //amounts
                'sendAmount' => $example['amount'],
//                'transferFee' => $example['fee'],
//                'totalCost' => $example['amount'] + $example['fee'],
            ],
        ] : [
            'success' => false,
        ]);

        //do not test transactions when transfer failed
        if ($expectedResult == false)
            return;

        $transferResponseJson = json_decode($transferResponse, true);
        $transferId = (int)($transferResponseJson['data']['transfer']['id']);

        //verify that transaction is in transaction list
        $I->sendGet("/mex/m/transaction/list");
        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "transactions" => [
                    [
                        "id" => $transferId,
                        'recipient' => [
                            'id' => $example['recipient']
                        ],
                        'sendAmount' => $example['amount'],
//                        "totalCost" => $example['amount'] + $example['fee'],
                    ]
                ]
            ]
        ]);

        //verify transaction details
        $I->sendGet("/mex/m/transaction/{$transferId}/detail");
        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "id" => $transferId,
                "status" => 'Completed',
                'recipient' => [
                    'id' => $example['recipient']
                ],
                "sendAmount" => $example['amount'],
//                'transferFee' => $example['fee'],
//                "totalCost" => $example['amount'] + $example['fee'],
            ]
        ]);

        // cancel it
        $I->sendPost("/mex/m/transfer/staging/cancel/{$transferId}");
        $I->seeResponseContainsJson([
            "success"=> true,
        ]);
    }
}
