<?php


namespace App\Tests\Api;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Env;
use \Codeception\Attribute\Examples;
use \Codeception\Example;
use CoreBundle\Utils\Util;

class CashTransferCest
{
//    #[Examples(
//        ['user_id' => 500017146, 'result' => false, ], //<PERSON><PERSON><PERSON> - non BOTM
//        ['user_id' => 500110202, 'result' => true ]  //BOTM Test member
//    )]
//    public function tryInitTransfer(ApiTester $I, Example $example)
//    {
//        $I->sendGet("/mex/m/transfer/init");
//
//        $expectedResult = (bool) $example['result'];
//        $I->seeResponseContainsJson([
//            'success' => $expectedResult,
//            'data' => $expectedResult ? [
//                'payoutOptions' => [
//                    'minAmount' => 2500,
//                    'maxAmount' => 300000,
//                ]
//            ] : null,
//        ]);
//    }

    #[Env("dev")]
    #[Examples(
        // Rapid
        ['user_id' => 500017146, 'amount' => 100, 'fee' => 300, 'recipient' => 500109825, 'result' => false],
        ['user_id' => 500017146, 'amount' => 200000, 'fee' => 300, 'recipient' => 500109825, 'result' => false],
        ['user_id' => 500017146, 'amount' => 600000, 'fee' => 300, 'recipient' => 500109825, 'result' => false],
        ['user_id' => 500017146, 'amount' => 3000, 'fee' => 300, 'recipient' => 500109825, 'result' => true],
        // BOTM
        ['user_id' => 500110202, 'amount' => 100, 'fee' => 300, 'recipient' => 500110268, 'result' => false],
        ['user_id' => 500110202, 'amount' => 3000, 'fee' => 300, 'recipient' => 500110268, 'result' => true],
    )]
    public function tryCreateCashTransfer(ApiTester $I, Example $example)
    {
        $transferResponse = $I->sendPost("/mex/m/transfer/create?passcode={$I->getPasscode($example['user_id'])}", [
            "transferMethod" => 'Cash Pickup',
            'amount'=> $example['amount'],
            'recipient'=> $example['recipient'],
        ]);

        $expectedResult = (bool) $example['result'];
        if ($expectedResult) {
            if (true !== $I->allowedUniTellerServerError()) {
                $I->seeResponseContainsJson([
                    'success' => true,
                    'data' => [
                        'status' => 'Created',
                        'payoutType' => 'cash',
                        'partner' => 'uniteller',
                        'recipient' => [
                            'id' => $example['recipient']
                        ],
                        //amounts
                        'sendAmount' => $example['amount'],
//                        'transferFee' => $example['fee'],
//                        'totalCost' => $example['amount'] + $example['fee'],
                    ]
                ]);
            }
        } else {
            $I->seeResponseContainsJson([
                'success' => false,
            ]);
        }

        //do not test transactions when transfer failed
        if ($expectedResult === false)
            return;

        $transferResponseJson = json_decode($transferResponse, true);
        $transferId = (int)($transferResponseJson['data']['transfer']['id']);

        //verify that transaction is in transaction list
        $I->sendGet("/mex/m/transaction/list");
        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "transactions" => [
                    [
                        "id" => $transferId,
                        'recipient' => [
                            'id' => $example['recipient']
                        ],
                        'sendAmount' => $example['amount'],
//                        "totalCost" => $example['amount'] + $example['fee'],
                    ]
                ]
            ]
        ]);

        //verify transaction details
        $I->sendGet("/mex/m/transaction/{$transferId}/detail");
        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "id" => $transferId,
                "status" => 'Created',
                'recipient' => [
                    'id' => $example['recipient']
                ],
                "sendAmount" => $example['amount'],
//                'transferFee' => $example['fee'],
//                "totalCost" => $example['amount'] + $example['fee'],
            ]
        ]);

        //verify receipt is HTML
        $receiptResponse = $I->sendGet("/mex/m/transfer/receipt/{$transferId}");
        $receiptResponseJson = json_decode($receiptResponse, true);
        $I->seeResponseCodeIsSuccessful();
        $I->assertStringStartsWith('<!DOCTYPE html>', $receiptResponseJson['data']);

        //verify sendMessage
        $I->sendGet("/mex/m/transfer/sendMessage/{$transferId}");
        $I->seeResponseCodeIsSuccessful();
        $I->seeResponseContainsJson([ "success"=> true ]);

        //verify getReceipt has url address
        $getReceiptResponse = $I->sendGet("/mex/m/transfer/getReceipt/{$transferId}");
        $getReceiptResponseJson = json_decode($getReceiptResponse, true);
        $I->seeResponseCodeIsSuccessful();
        $I->assertStringStartsWith('https://', $getReceiptResponseJson['data']);

        //cancel the transfer
        $I->sendPost("/mex/m/transfer/cancel/{$transferId}");
        $I->seeResponseCodeIsSuccessful();
        $I->seeResponseContainsJson([ "success" => true]);

        //verify transaction was canceled
        $I->sendGet("/mex/m/transaction/{$transferId}/detail");
        $I->seeResponseContainsJson([
            "success"=> true,
            "data"=> [
                "id" => $transferId,
                "status" => 'Canceled'
            ]
        ]);
    }
}
