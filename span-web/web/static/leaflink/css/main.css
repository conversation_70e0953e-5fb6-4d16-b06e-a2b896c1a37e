@font-face {
  font-family: LL_Sofia_Pro;
  src: local(LL_Sofia_Pro);
  src: url("../font/LL_Sofia_Pro/LL_Sofia_Pro-Regular.otf") format('otf');
  src: url("../font/LL_Sofia_Pro/LL_Sofia_Pro-Regular.woff2") format('woff2');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: LL_Sofia_Pro;
  src: local(LL_Sofia_Pro);
  src: url("../font/LL_Sofia_Pro/LL_Sofia_Pro-Medium.otf") format('otf');
  src: url("../font/LL_Sofia_Pro/LL_Sofia_Pro-Medium.woff2") format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: LL_Sofia_Pro;
  src: local(LL_Sofia_Pro);
  src: url("../font/LL_Sofia_Pro/LL_Sofia_Pro-Bold.otf") format('otf');
  src: url("../font/LL_Sofia_Pro/LL_Sofia_Pro-Bold.woff2") format('woff2');
  font-weight: bold;
  font-style: normal;
}

body {
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  display: flex;
  flex-direction: column;
  font-family: LL_Sofia_Pro, serif;
}

#wrapper {
  /*background: #EEF2F4;*/
  background: #ffffff;
  display: flex;
  width: 100%;
  height: 100%;
  position: fixed;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: #676A6C;
  text-align: center;
}

#wrapper > .logo {
  margin-top: auto;
  text-align: right;
  padding-top: 12px;
}

#wrapper > .logo > img {
  max-width: 230px;
}

.yodlee-logo > img {
  max-width: 150px;
  margin-top: 12px;
}

.logo-txt {
  margin-top: 12px;
  font-family: LL_Sofia_Pro, serif;
  font-style: normal;
  font-weight: 500;
  font-size: 22px;
  /*line-height: 20px;*/
  max-width: 45%;
  min-width: 300px;
  text-align: center;
  display: inline-block;
  color: #676A6C;
}

.reassurance {
  margin-top: 10px;
  margin-right: auto;
  margin-left: auto;
  font-family: LL_Sofia_Pro, serif;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  max-width: 45%;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  color: #676A6C;
  align-content: center;
  text-align: center;
}

.reassurance-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  text-align: left;
}

#wrapper > .logo > .financial {
  text-transform: uppercase;
  margin: -28px 20px;
}

#wrapper > .box {
  width: 60%;
  height: 50rem;
  max-width: 827px;
  max-height: calc(100% - 200px);
  background: white;
  border-radius: 4px;
  margin-top: 30px;
  overflow: auto;
  color: #333;
  padding: 20px 30px;
  font-size: 16px;
  /*box-shadow: 0px 2px 4px rgba(0, 15, 35, 0.06);*/
  /*box-shadow: 0 0 20px 0 rgba(27,20,67,0.1);*/
}

#wrapper > .iframe-box {
  width: 100%;
  max-width: 827px;
  max-height: calc(100% - 200px);
  background: white;
  /*border-radius: 4px;*/
  /*margin-top: 30px;*/
  overflow: auto;
  color: #333;
  /*padding: 20px 30px;*/
  font-size: 16px;
  /*box-shadow: 0px 2px 4px rgba(0, 15, 35, 0.06);*/
  /*box-shadow: 0 0 20px 0 rgba(27,20,67,0.1);*/
}

.disclaimer {
  font-size: 14px;
  color: #676A6C;
  max-width: 45%;
  min-width: 300px;
  text-align: center;
  display: inline-block;
}

.continue {
  display: inline-block;
  color: white;
  background-color: #0c55f2;
  transition: .5s;
  width: 45%;
  min-width: 300px;
  height: 40px;
  border: none;
  font-size: 18px;
  font-weight: 500;
}

.continue:hover {
  background-color: #0b8cf2;
  transition: .5s;
}

#wrapper > footer {
  font-size: 10px;
  color: #676A6C;
  text-align: center;
  flex: 0 0 50px;
  margin-top: auto;
  width: 100%;
}

#wrapper > footer > .footer-div {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

#wrapper > footer > .footer-div > p {
  padding-left: 10px;
  padding-right: 10px;
}

#wrapper > footer .footer-div > p > .dot {
  position: absolute;
  bottom: 10px;
  left: 50%;
  text-align: center;
}

#wrapper > footer > .footer-div > p > img {
  max-width: 90px;
  display: inline;
  padding-right: 5px;
  padding-bottom: 5px;
}

.lock-icon {
  filter: invert(44%) sepia(3%) saturate(439%) hue-rotate(161deg) brightness(90%) contrast(90%);
  margin-right: 6px;
}

.shield-icon {
  filter: invert(44%) sepia(3%) saturate(439%) hue-rotate(161deg) brightness(90%) contrast(90%);
  margin-right: 6px;
}

.alt-bank-title {
  font-size: 28px;
  color: #676A6C;
}

.alt-bank {
  max-width: 60%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 300px;
}

.alt-form {
  text-align: left;
  color: #676A6C;
}

.bank-submit {
  display: inline-block;
  color: white;
  background-color: #0c55f2;
  transition: .5s;
  width: 50%;
  min-width: 300px;
  height: 40px;
  border: none;
  font-size: 18px;
  font-weight: 500;
}

.bank-submit:hover {
  background-color: #0b8cf2;
  transition: .5s;
}

.bank-detail-secondary {
  color: #676A6C;
}

.bank-detail-primary {
  font-weight: bold;
  color: #676A6C;
}

.special-text {
  color: #676A6C;
}

.thank-you-div {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -75%);
}

.thank-you {
  text-align: center;
}

.wait {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -75%);
}

.wait-container {
  padding-top: 1rem;
  display: flex;
  justify-content: center;
}

.ll_link {
  padding-top: 10px;
}

.ll-link-p {
  margin-bottom: 0;
  padding-bottom: 0;
  color: #676A6C;
}

.ll-link-text {
  color: #676A6C;
  text-decoration: none;
  transition: 0.5s;
  margin-top: 0;
  padding-top: 0;
}

.ll-link-text:hover {
  text-decoration: none;
  transition: 0.5s;
}

.ispinner {
  position: relative;
  width: 30px;
  height: 30px; }
.ispinner .ispinner-blade {
  position: absolute;
  top: 37%;
  left: 44.5%;
  width: 10%;
  height: 25%;
  background-color: #8e8e93;
  border-radius: 50%/20%;
  animation: iSpinnerBlade 1s linear infinite;
  will-change: opacity; }
.ispinner .ispinner-blade:nth-child(1) {
  transform: rotate(30deg) translate(0, -150%);
  animation-delay: -1.6666666667s; }
.ispinner .ispinner-blade:nth-child(2) {
  transform: rotate(60deg) translate(0, -150%);
  animation-delay: -1.5833333333s; }
.ispinner .ispinner-blade:nth-child(3) {
  transform: rotate(90deg) translate(0, -150%);
  animation-delay: -1.5s; }
.ispinner .ispinner-blade:nth-child(4) {
  transform: rotate(120deg) translate(0, -150%);
  animation-delay: -1.4166666667s; }
.ispinner .ispinner-blade:nth-child(5) {
  transform: rotate(150deg) translate(0, -150%);
  animation-delay: -1.3333333333s; }
.ispinner .ispinner-blade:nth-child(6) {
  transform: rotate(180deg) translate(0, -150%);
  animation-delay: -1.25s; }
.ispinner .ispinner-blade:nth-child(7) {
  transform: rotate(210deg) translate(0, -150%);
  animation-delay: -1.1666666667s; }
.ispinner .ispinner-blade:nth-child(8) {
  transform: rotate(240deg) translate(0, -150%);
  animation-delay: -1.0833333333s; }
.ispinner .ispinner-blade:nth-child(9) {
  transform: rotate(270deg) translate(0, -150%);
  animation-delay: -1s; }
.ispinner .ispinner-blade:nth-child(10) {
  transform: rotate(300deg) translate(0, -150%);
  animation-delay: -0.9166666667s; }
.ispinner .ispinner-blade:nth-child(11) {
  transform: rotate(330deg) translate(0, -150%);
  animation-delay: -0.8333333333s; }
.ispinner .ispinner-blade:nth-child(12) {
  transform: rotate(360deg) translate(0, -150%);
  animation-delay: -0.75s; }
.ispinner.ispinner-large {
  width: 35px;
  height: 35px; }
.ispinner.ispinner-large .ispinner-blade {
  width: 8.5714285714%;
  height: 25.7142857143%;
  border-radius: 50%/16.67%; }

@keyframes iSpinnerBlade {
  0% {
    opacity: 0.85; }
  50% {
    opacity: 0.25; }
  100% {
    opacity: 0.25; } }

@media only screen
and (max-device-width: 900px) {
  #wrapper > .box {
    width: 90%;
    height: 40rem;
    max-width: 90vw;
    max-height: calc(100% - 200px);
    background: white;
    border-radius: 14px;
    margin-top: 30px;
    overflow: auto;
    color: #333;
    font-size: 16px;
    /*box-shadow: 0 0 20px 0 rgba(27,20,67,0.1);*/
  }
}

@media only screen and (max-width: 900px){
  #wrapper > .box {
    width: 90%;
    height: 50rem;
    max-width: 90vw;
    max-height: calc(100% - 200px);
    background: white;
    border-radius: 14px;
    margin-top: 30px;
    overflow: auto;
    color: #333;
    font-size: 16px;
    /*box-shadow: 0 0 20px 0 rgba(27,20,67,0.1);*/
  }

  @media only screen
  and (max-device-width: 900px) {
    #wrapper > .iframe-box {
      width: 100%;
      /*height: 40rem;*/
      max-width: 90vw;
      max-height: calc(100% - 200px);
      background: white;
      /*border-radius: 14px;*/
      margin-top: 30px;
      overflow: auto;
      color: #333;
      font-size: 16px;
      /*box-shadow: 0 0 20px 0 rgba(27,20,67,0.1);*/
    }
  }

  @media only screen and (max-width: 900px) {
    #wrapper > .iframe-box {
      width: 100%;
      /*height: 50rem;*/
      max-width: 90vw;
      max-height: calc(100% - 200px);
      background: white;
      /*border-radius: 14px;*/
      margin-top: 30px;
      overflow: auto;
      color: #333;
      font-size: 16px;
      /*box-shadow: 0 0 20px 0 rgba(27,20,67,0.1);*/
    }
  }

  .reassurance {
    max-width: 300px;
    min-width: 300px;
  }

  .disclaimer {
    max-width: 300px;
    min-width: 300px;
  }

  .continue {
    max-width: 300px;
    min-width: 300px;
  }

  .logo-txt {
    max-width: 300px;
    min-width: 300px;
  }
}
