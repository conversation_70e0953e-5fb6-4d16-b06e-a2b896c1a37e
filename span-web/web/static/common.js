// Global helper
var ts = ts || {};

ts.isDev = function () {
    try {
        return location.host.endsWith('.local');
    } catch (e) {
        console.error(e);
        return false;
    }
};

window.addEventListener('segment_load', function () {
  if (location.pathname === '' || location.pathname === '/') {
    ts.analytics.track("Visit consumer portal index");
  }
}, false);

ts.setLangFlag = function (lang) {
  var flags = {
    'en': 'us',
    'zh': 'cn',
    'es': 'es',
    'pt-BR': 'br',
    'ja': 'jp',
    'ar': 'sa',
  };
  var flag = flags[lang] || 'us';
  $('header .lang > a img').attr('src', '/node_modules/flag-icon-css/flags/4x3/' + flag + '.svg');
};

ts.modal = function (text, modaless) {
    if (modaless === undefined) {
        modaless = false
    }
    swal({
        title: '',
        text: text || 'Submitting...',
        showConfirmButton: false,
        allowEscapeKey: modaless,
        allowOutsideClick: modaless
    });
};

ts.msg = function (text, timer) {
    swal({
        title: '',
        text: text || 'Finished!',
        timer: timer || 1500,
        showConfirmButton: false
    });
};

ts.confirm = function (text, callback, options) {
    swal($.extend({
        title: '',
        text: text || 'Are you sure you want to continue?',
        type: 'warning',
        showCancelButton: true,
        allowEscapeKey: false,
        allowOutsideClick: false,
        showLoaderOnConfirm: true,
        closeOnConfirm: false,
        html: true,
    }, options || {}), function (confirmed) {
        if (confirmed) {
            callback();
        }
    });
};

ts.success = function (text, callback, autoHide) {
    var config = {
      title: '',
      text: text || 'Operation finished successfully!',
      type: 'success'
    };
    if (autoHide) {
        config.timer = 1500;
        config.showConfirmButton = false;
    }
    swal(config, function () {
        if (callback) {
            callback();
        }
    });
    if (autoHide) {
        setTimeout(function () {
          ts.close();
        }, 1500);
    }
};

ts.error = function (text, callback) {
    swal({
        title: '',
        text: text || 'Operation failed!',
        type: 'error'
    }, function () {
        if (callback) {
            callback();
        }
    });
};

ts.close = function () {
    swal.close();
};

ts.notify = function (resp, onlyFailed) {
    if (resp.success && !onlyFailed) {
        ts.success();
    } else if (!resp.success) {
        ts.error(resp.message);
    }
};

ts.formatCardNumber = function (number) {
    number = number || '';
    var ns = number.split(''),
        ps = [],
        p = '';
    for(var k = 0; k < ns.length; k++) {
        p += ns[k];
        if (k % 4 === 3) {
            ps.push(p);
            p = '';
        }
    }
    if (p) {
        ps.push(p);
    }
    return ps.join(' ');
};

ts.refreshFlagExpired = function (key, expireTime) {
    var ts = localStorage.getItem(key),
        now = +new Date(),
        expired = false;
    if (ts) {
        ts = parseInt(ts) || 0;
        if (now - ts > expireTime) {
            localStorage.removeItem(key);
            expired = true;
        }
    } else {
        expired = true;
    }

    return expired;
};

ts.updateRefreshFlag = function (key) {
    localStorage.setItem(key, +new Date());
};

ts.convertSerialize = function (params) {
  var ret = {};
  _.forEach(params, function (v, k) {
    ret[v.name] = v.value;
  });
  return ret;
};

ts.checkPassword = function (pwd) {
  var regex1 = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{8,}$/;
  var regex2 = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@#$%^&*()\-_=+\[{\]}\\|;:'",<.>/?])[\s\S]{8,}$/;
  var regex3 = /^(?=.*[a-z])(?=.*\d)(?=.*[`~!@#$%^&*()\-_=+\[{\]}\\|;:'",<.>/?])[\s\S]{8,}$/;
  var regex4 = /^(?=.*[A-Z])(?=.*\d)(?=.*[`~!@#$%^&*()\-_=+\[{\]}\\|;:'",<.>/?])[\s\S]{8,}$/;
  return regex1.test(pwd) || regex2.test(pwd) ||regex3.test(pwd) ||regex4.test(pwd);
};

ts.invalidPwdMsg = [
  'Minimum 8 characters in length',
  'Contain at least three of the following four character types: uppercase letter, lowercase letter, number and special character'
];

function baseUrl() {
    var url = 'https://a2a.uatfisprepaid.com/a2a/';
    if (!ts.isDev()) {
        url = 'https://a2a.fisprepaid.com/'
    }
    return url;
}

function request (url, method, data, silent, responseType) {
    method = method || 'get'
    data = data || {}
    silent = silent || false
    responseType = responseType || 'xml'
    if (!_.startsWith(url, '/')) {
      url = '/' + url
    }
    var contentType = 'application/x-www-form-urlencoded';
    $.ajax({
      url: baseUrl() + url,
      method: method,
      contentType: contentType,
      data: data,
      dataType: responseType,
      success: function (data) {
        return deal(data);
      },
      error: function (error) {
        ts.error(error ? error.message : 'Unknown error');
      }
    })
}

function deal (data) {
  var parser = require('xml2json');
  parser.toJson(data);
  return data;
}

$(function () {
    $(window).bind("pageshow", function(event) {
        if (event.originalEvent.persisted) {
            swal.close();
        }
    });

    $('body').on('click', '.refresh-balance', function () {
        var t = $(this),
            fa = t.find('.fa');
        fa.addClass('fa-spin fa-refresh').removeClass('fa-warning');
        $.get('/user/card-balance/' + (t.data('uc') || ''), function (resp) {
            if (!resp.success) {
                fa.removeClass('fa-spin fa-refresh').addClass('fa-warning');
                t.addClass('hint--top hint-rounded').attr('aria-label', resp.message);
                return;
            }
            t.prev().text(resp.data.text);
            fa.removeClass('fa-spin');
        });
    }).on('click', '.refresh-cvc', function () {
        var t = $(this);
        if (t.find('.fa').length) {
            t.html('<i class="fa fa-spin fa-refresh"></i>');
            t.attr('aria-label', 'Click here to toggle the CVC');
            $.get('/user/card-cvc/' + t.data('uc'), function (resp) {
                if (!resp.success) {
                    if (resp.data && resp.data.url && ts.side === 'front') {
                        swal({
                            title: '',
                            type: 'error',
                            text: resp.message,
                            confirmButtonText: resp.data.buttonText || 'OK',
                            allowEscapeKey: false
                        }, function () {
                            location.href = resp.data.url;
                        });
                    } else {
                        t.find('.fa').removeClass('fa-spin fa-refresh').addClass('fa-warning');
                        t.attr('aria-label', resp.message);
                    }
                    return;
                }
                t.text(resp.data.text);
            });
        } else {
            t.html('<i class="fa fa-refresh"></i>');
        }
    });

    if (window.moment && ts.refreshFlagExpired('timezone_ts', 3600000)) {
        $.post('/user/timezone', {tz: moment.tz.guess()}, function () {
            ts.updateRefreshFlag('timezone_ts');
        });
    }
});
