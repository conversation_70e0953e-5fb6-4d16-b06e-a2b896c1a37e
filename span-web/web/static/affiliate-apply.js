var itv = setInterval(function () {
  var $ = window.jQuery;
  if ($ && $.fn && $.fn.jquery) {
    clearInterval(itv);

    $('head').append('<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>');

    $(function () {
      var $form = $('#gform_5');
      var time = null;
      $form.find('[name="input_31.1"]').on('click', function () {
        if (this.checked) {
          time = +new Date();
        }
      });

      var $submit = $('#gform_submit_button_5')[0];
      $submit.onkeypress = function () {
        return false;
      };

      $submit.onclick = function () {
        if (!$form.find('[name="input_31.1"]')[0].checked) {
          swal('Error', 'Please accept our affiliate terms and conditions to continue!', 'error');
          return false;
        }

        var fields = {
          '1': 'firstName',
          '2': 'lastName',
          '22': 'email',
          '5': 'companyName',
          // '29': 'skype',
          '10': 'phone',
          '23': 'website',
          '24.1': 'streetAddress',
          '24.2': 'addressLine2',
          '24.3': 'city',
          '24.4': 'state',
          '24.5': 'zip',
          '24.6': 'country',
          '18': 'audience',
          '17': 'monthlyVisitors',
          // '25': 'facebook',
          // '26': 'linkedIn',
          // '27': 'twitter',
          // '28': 'instagram'
        };
        var error = false;
        $.each(fields, function (k, v) {
          var value = $.trim($form.find('[name="input_' + k + '"]').val());
          if (!value) {
            error = true;
            swal('Error', 'Please fill all the fields in the Contact Information and Business Information sections, and also other fields marked with "*".', 'error');
            return false;
          }
        });
        if (error) {
          return false;
        }

        swal({
          text: 'Submitting...',
          button: false
        });
        var data = $form.serializeArray();
        data.push({
          name: 'accepted_at',
          value: time
        });
        $.post('https://www.virtualcards.us/affiliate-apply/submit', data, function (resp) {
          if (resp && resp.success) {
            $form[0].submit();
          } else {
            swal('Error', 'Failed to submit! ' + (resp && resp.message ? resp.message : ''), 'error');
          }
        });
        return false;
      };
    });
  }
}, 100);
