PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
#Mins        Hours        Days        Months        Day of the week
1            1            *           *             *        /usr/local/sbin/sitebackup.sh
1            1            *           *             *        /usr/local/sbin/cloudflare-whitelist.sh
*            *            *           *             *        chmod -R 777 /var/cache/span/
5            3            *           *             SUN      > /var/log/span/dev.log && > /var/log/span/test.log && > /var/log/span/prod.log
### General [{env_full}]
*            *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/email-batch
*/10         *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/update-email-details
21           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/system/check-redis
9            3            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/system/check-disk
12           3            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/update-currency-rate
20           1            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/update-google-fonts
30           1            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/delete-outdated-reports
30           0            *           *             *        php {project_directory}/bin/console span:db:remove-old-token --env=prod
30           6            *           *             *        php {project_directory}/bin/console span:db:backup --env=prod
18           0            *           *             *        php {project_directory}/bin/console span:dev:log:backup --env=prod
22           2            *           *             *        php {project_directory}/bin/console span:dev:remove-outdated-backups --env=prod
10           12           *           *             SUN      php {project_directory}/bin/console span:update-dev-cookie --env=prod
3            5,6,7,8      *           3,11          SUN      php {project_directory}/bin/console span:dev:install-cron-jobs --install
11           *            *           *             *        php {project_directory}/bin/console span:dev:inspect-aws-events --hours=1 --env=prod
45,15        *            *           *             *        php {project_directory}/bin/console span:dev:move-files-to-s3 --env=prod
*/10         *            *           *             *        php {project_directory}/bin/console span:app:update-exchange-rate
*            *            *           *             *        php {project_directory}/bin/console span:bg:instant --env=prod
*            *            *           *             *        php {project_directory}/bin/console span:monitor:system --env=prod
10           0            *           *             *        php {project_directory}/bin/console span:db:remove-old-email --env=prod
15           0            *           *             *        php {project_directory}/bin/console span:db:remove-old-pin --env=prod
###### 12    *            *           *             *        php {project_directory}/bin/console span:dev:monitor-error-log --env=prod
5            0            *           *             *        php {project_directory}/bin/console span:store-idology-usage-report --env=prod
20           0            *           *             *        php {project_directory}/bin/console span:store-twilio-report --env=prod



### US Unlocked [{env_full}]
## wget
*/3          *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/unlock-users
17           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/load-card-batch
25           */6          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/gc-daily-report
40           3            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/fee/monthly
35           12           *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/remind-pending-loads
35           13           *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/remind-low-balance
48           */8          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/check-spending-limit
*/5          *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/retry-updating-spending-limit
31           */12         *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/remind-home-users
43           12           *           *             FRI      wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/report/fee
# 20         */4          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/refund-batch
# 00         12           *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/email/pending
# 40           4            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/usu/affiliate/load-commission


## Command
25           1            *           *             *        php {project_directory}/bin/console --env=prod span:usu:find-users-diff-running-balance
2            0            *           *             *        php {project_directory}/bin/console --env=prod span:usu:report:daily --send
12           7            *           *             *        php {project_directory}/bin/console --env=prod span:usu:remind-neg-users --remind
20           */4          *           *             *        php {project_directory}/bin/console --env=prod span:usu:report:fee
33           20           1           *             *        php {project_directory}/bin/console --env=prod span:usu:report:revenue
30           13           *           *             *        php {project_directory}/bin/console --env=prod span:usu:remind-id-expired-users --remind
3            0            28          *             *        php {project_directory}/bin/console --env=prod span:usu:report:positive-user --send
0            13           1           *             *        php {project_directory}/bin/console --env=prod span:usu:export:load-for-users
18           3            *           *             SUN      php {project_directory}/bin/console --env=prod span:dev:usu:submit-mautic-events --start=fortnight
20           4            *           *             *        php {project_directory}/bin/console --env=prod span:dev:update-paypal-subscription
51           *            *           *             *        php {project_directory}/bin/console --env=prod span:usu:coinflow:sync-payments
22           0,14         *           *             *        php {project_directory}/bin/console --env=prod span:usu:rain:update-spend-limit --sync --update
5            2,14         *           *             *        php {project_directory}/bin/console --env=prod span:usu:rain:update-transactions --tag=semi-daily --hours=13
51           2            *           *             *        php {project_directory}/bin/console --env=prod span:usu:rain:close-delayed-cards
8            0            *           *             *        php {project_directory}/bin/console --env=prod span:usu:user:mautic --method=syncUnsubscribedUsers
8            2            *           *             *        php {project_directory}/bin/console --env=prod span:usu:user:mautic
30           2            *           *             *        php {project_directory}/bin/console --env=prod span:usu:report:new-revenue --type=month --from=2025-07-01
30           3            *           *             *        php {project_directory}/bin/console --env=prod span:usu:report:new-revenue --type=day --from=2025-07-01


### TransferMex [{env_full}]
## Wget
30           5,17         *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/rapid/check-failed-refunds
18           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/dashboard/update-cache
*/30         *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/rapyd/monitor-instant-transfer/alert
12           2            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/check-canceled-transfers
24           */12         *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/retry-settlement
22           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/botm/nacha/generate/retry
*/5          *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/botm/nacha/error-email/send
10           */6          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/check-batch-payment
5            19           *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/mex/remind-revenue-settlement

## Command
5            */12         *           *             *        php {project_directory}/bin/console span:mex:botm:ensure-config
#15          */8          *           *             *        php {project_directory}/bin/console span:mex:store-program-chart-data --env=prod
50           3            *           *             *        php {project_directory}/bin/console span:mex:employer:update-month-activity-report --type=day --env=prod
18           0-5,7-23     *           *             *        php {project_directory}/bin/console span:mex:update-transactions --total --days=1 --env=prod
18           6            *           *             *        php {project_directory}/bin/console span:mex:update-transactions --total --days=15 --env=prod
47           *            *           *             *        php {project_directory}/bin/console span:mex:update-agent-balances
33           3            *           *             *        php {project_directory}/bin/console span:mex:rapid:find-unmatched-txn --fix --env=prod
11           */4          *           *             *        php {project_directory}/bin/console span:mex:update-payout-status --days=31 --env=prod
12           3            *           *             *        php {project_directory}/bin/console span:mex:update-error-payout-status --days=30 --env=prod
12           17           3           *             *        php {project_directory}/bin/console span:mex:update-error-payout-status --days=400 --env=prod
*/20         *            *           *             *        php {project_directory}/bin/console span:mex:employer-payout-execute --env=prod
*/10         *            *           *             *        php {project_directory}/bin/console span:mex:check-dup-refunds --alert --env=prod
25           */2          *           *             *        php {project_directory}/bin/console span:mex:process-payout-queue --env=prod
7            *            *           *             *        php {project_directory}/bin/console span:mex:check-legacy-cards --move
# */30       *            *           *             *        php {project_directory}/bin/console span:mex:check-settlement --env=prod
34           0            *           *             *        php {project_directory}/bin/console span:mex:update-rapid-agent-transactions --days=3 --notify --env=prod
34           1-6          *           *             *        php {project_directory}/bin/console span:mex:update-rapid-agent-transactions --days=1 --notify --env=prod
34           7-23         *           *             *        php {project_directory}/bin/console span:mex:update-rapid-agent-transactions --days=0 --notify --env=prod
50           0            *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=3 --notify --env=prod
50           1-6          *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=1 --notify --env=prod
50           7-23         *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=0 --notify --env=prod
40           0            *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=3 --type=business_to_program --notify --env=prod
40           1-6          *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=1 --type=business_to_program --notify --env=prod
40           7-23         *           *             *        php {project_directory}/bin/console span:mex:update-botm-agent-transactions --days=0 --type=business_to_program --notify --env=prod
50           2            *           *             *        php {project_directory}/bin/console span:dev:mex:deduct-dup-refunds --env=prod
# 15         4            *           *             *        php {project_directory}/bin/console span:mex:update-rapyd-transactions --days=30 --detailDays=30 --env=prod
05           5            *           *             *        php {project_directory}/bin/console span:mex:inspect-rapyd-transactions --alert --env=prod
# 45         */2          *           *             *        php {project_directory}/bin/console span:mex:update-rapyd-transactions --days=2 --detailDays=2 --env=prod
0            19           *           *             FRI      php {project_directory}/bin/console span:dev:mex:send-remind-msg --env=prod
40           13           *           *             *        php {project_directory}/bin/console span:mex:check-all-cards-status --env=prod
45           19           *           *             *        php {project_directory}/bin/console span:mex:remind-insecure-pin --notify --env=prod
# 4          */8          *           *             *        php {project_directory}/bin/console span:mex:send-account-limit-error-email --env=prod
6            */6          *           *             *        php {project_directory}/bin/console span:mex:member:find-duplicated-accounts
22,52        *            *           *             *        php {project_directory}/bin/console span:mex:process-operation-queue --env=prod
4            *            *           *             *        php {project_directory}/bin/console span:mex:process-partner-error-pool --live --env=prod
13           */3          *           *             *        php {project_directory}/bin/console span:mex:process-unhandled-rapyd-errors --env=prod
37           11           */7         *             *        php {project_directory}/bin/console span:mex:update-card-balance-status --startDays=0 --endDays=3 --env=prod
35           4            *           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transactions --days=30 --env=prod
45           */2          *           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transactions --days=2 --env=prod
0            */6          *           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transfers-status --env=prod
52           6            *           *             *        php {project_directory}/bin/console span:mex:remind-under-review-payout --notify --env=prod
8            */12         *           *             *        php {project_directory}/bin/console span:mex:update-member-record --env=prod
5            0            *           *             *        php {project_directory}/bin/console span:mex:update-program-list --env=prod
*/15         *            *           *             *        php {project_directory}/bin/console span:mex:sent-message-batch --env=prod
13           *            *           *             *        php {project_directory}/bin/console span:mex:check-rapyd-balance --env=prod
14           *            *           *             *        php {project_directory}/bin/console span:mex:transfer:refund-failed-deduction-batch
53           *            *           *             *        php {project_directory}/bin/console span:mex:remind-rapyd-created --notify --env=prod
21           *            *           *             *        php {project_directory}/bin/console span:mex:send-rapyd-inquiry --notify --env=prod
37           3            *           *             *        php {project_directory}/bin/console span:mex:update-card-balance-status --env=prod
# 27         1            *           *             *        php {project_directory}/bin/console span:mex:update-payout-methods --env=prod
18           4,16         *           *             *        php {project_directory}/bin/console span:mex:member:sync-botm-user
0            3            *           *             *        php {project_directory}/bin/console span:mex:transfer-min-card-to-base --env=prod
30           0,12,18      *           *             *        php {project_directory}/bin/console span:mex:update-botm-transactions --days=2 --env=prod
30           6            *           *             *        php {project_directory}/bin/console span:mex:update-botm-transactions --days=30 --env=prod
3            *            *           *             *        php {project_directory}/bin/console span:mex:create-settlement
*/5          *            *           *             *        php {project_directory}/bin/console span:mex:remind-upload-payout --env=prod
10           12           1           *             *        php {project_directory}/bin/console span:mex:update-uniteller-transfer-report --env=prod
15           4,5,12,20    *           *             *        php {project_directory}/bin/console span:mex:update-monthly-snapshot-report --type=day --force
25           4,5,17       *           *             *        php {project_directory}/bin/console span:mex:update-monthly-snapshot-report --type=week --force
35           4,5,18       *           *             *        php {project_directory}/bin/console span:mex:update-monthly-snapshot-report --type=month --force
10           */2          *           *             *        php {project_directory}/bin/console span:mex:update-intermex-transfers-status --env=prod
10           12           *           *             *        php {project_directory}/bin/console span:mex:member:notify-employee-with-exceed-balance --notify --env=prod
10           */6          *           *             *        php {project_directory}/bin/console span:mex:update-program-chart-data  --days=180 --env=prod
10           3            *           *             *        php {project_directory}/bin/console span:mex:charge-month-fee --env=prod --charge --alert


### Wilen [{env_full}]
## Wget
30           4            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/wilen/soap/get-wilen-data
*/30         *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/wilen/soap/execute-campaign

### LeafLink [{env_full}]
### These jobs end up running in EST and are subject to DST
##  Wget
### 00       12           *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/onboard/email
40           22^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/daily-counter-reset
40           22^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/weekly-counter-reset
40           22^          1           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/monthly-counter-reset
0            *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/return
40           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/post-fed
45           20^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/set-settled
5            21^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/report/daily-check

### LeafLink Batch - Week Days
30           21^          *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             2        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             3        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             4        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch
30           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/updated-batch

### LeafLink B2B
### 45       21           *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
20           21^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-daily-counter-reset
20           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-weekly-counter-reset
20           21^          1           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-monthly-counter-reset
15           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-return
55           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-post-fed
0            21^          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-set-settled

### LeafLink Batch - Week Days
45           21^          *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             2        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             3        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             4        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch
45           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ach/b2b-batch

### LeafLink EWB
50           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/imap/ewb-prefed
10           *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-return
44           */8          *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/email/test

### LeafLink EWB Batch - Week Days
40           21^          *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             2        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             3        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             4        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch
40           21^          *           *             5        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/leaflink/ewb-batch

### LeafLink upload CSV file
## Command
10           0            *           *             *        php {project_directory}/bin/console span:leaflink:upload-bank-info --env=prod

### Spendr [{env_full}]
## Wget
#10           6            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/fee/monthly/consumer/inactivity-fee
# 10         6            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/fee/monthly/spendr/on-file-fee
# */15       *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/user/handle-prefund-balance
#10           2            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/find-users-diff-running-balance
# */10       *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/load-card-batch
#15           2            *           *             1        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/send-negative-balance-info
#*/5          *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/update-transaction-status
#0            17           *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/find-delete-applies
#0            6            */1         *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/revoke-promo-code-rewards
#1            *            *           *             *        wget --no-check-certificate -q --spider --tries=1 {host}/t/cron/spendr/scan-consumers-ofac

## Command
#0            20^          *           *             *        php {project_directory}/bin/console span:spendr:merchant-auto-withdrawal
