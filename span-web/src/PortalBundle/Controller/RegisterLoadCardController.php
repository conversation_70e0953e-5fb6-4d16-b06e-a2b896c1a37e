<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/4/5
 * Time: 下午4:49
 */

namespace PortalBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Constant\GlobalCollectSupported;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\MaintenanceEvent;
use CoreBundle\Entity\Promotion;
use CoreBundle\Entity\Transaction;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserDiscount;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\APIServices\AlternativePaymentService;
use CoreBundle\Services\APIServices\GlobalCollectService;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\FirstView\FirstViewAPI;
use CoreBundle\Services\Velocity\VelocityLoadsService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Session;
use CoreBundle\Utils\Util;
use Doctrine\Common\Util\ClassUtils;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\CitconService;
use UsUnlockedBundle\Services\Coinflow\CoinflowService;
use UsUnlockedBundle\Services\LoadService;
use UsUnlockedBundle\Services\SlackService;
use CoreBundle\Services\APIServices\RapydPaymentService;
use CoreBundle\Entity\RapydCountryPaymentType;
use Worldline\Connect\Sdk\V1\Domain\CreatePaymentResponse;
use Worldline\Connect\Sdk\V1\ReferenceException;

class RegisterLoadCardController extends RegisterController
{
    public function __construct()
    {
        parent::__construct();
        $this->isMaintenance();
    }

    private function isMaintenance()
    {
        $cp = Util::cardProgram();
        if (!$cp) {
            return;
        }
        $time = time();
        $query = Util::em()->getRepository(\CoreBundle\Entity\MaintenanceEvent::class)
            ->createQueryBuilder('me')
            ->join('me.cardProgram', 'cp');
        $expr = $query->expr();

        $rs = $query->where($expr->andX(
            $expr->orX(
                $expr->eq('me.type', ':all'),
                $expr->eq('me.type', ':type')
            ),
            $expr->eq('cp', ':cp'),
            $expr->lte('me.startTs', ':now'),
            $expr->gte('me.endTs', ':now')
        ))
            ->setParameter('now', $time)
            ->setParameter('all', MaintenanceEvent::VALUE_TYPE_ALL)
            ->setParameter('type', MaintenanceEvent::VALUE_TYPE_LOAD)
            ->setParameter('cp', $cp)
            ->orderBy('me.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();

        if (count($rs)) {
            /** @var MaintenanceEvent $r */
            $r = array_first($rs);
            if ($r) {
                throw new PortalException($r->getMessage());
            }
        }
    }

    protected function render($view, array $parameters = array(), Response $response = null): Response
    {
        if (!isset($parameters['loading'])) {
            $parameters['loading'] = Session::get(User::LOADING_CARD);
        }
        $uc = null;
        /** @var UserCard $uc */
        if (!isset($parameters['reload'])) {
            if ($parameters['loading']) {
                $uc = $parameters['userCard'] ?? $parameters['uc'] ?? null;
                if ($uc) {
                    $parameters['reload'] = $uc->everLoaded();
                }
            } else {
                $parameters['reload'] = false;
            }
        }
        if (!empty($parameters['userCard'])) {
            $uc = $parameters['userCard'];
            if ($uc) {
                $parameters['upgradeTargetCard'] = $uc->getCard()->getUpgradeTargetCard();
            }
        }

        if ($uc && !$uc->isApiActive()) {
            return new ErrorResponse('Visa card has been inactivated in the system. Please try to create or load Mastercard.');
        }

        return parent::render($view, $parameters, $response);
    }

    /**
     * @Route("/consumer-register/card-load", methods={"GET"})
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws PortalException
     */
    public function LoadCardPage(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user) {
            return $this->redirect('/choose-card');
        }

        $em = Util::em();
        $load = null;
        if ($request->get('load')) {
            $load = $em->getRepository(\CoreBundle\Entity\UserCardLoad::class)->find($request->get('load'));
            /** @var UserCard $uc */
            $uc = $load->getUserCard();
            $card = $uc->getCard();
            Session::set(User::LOADING_CARD, $card->getId());
        } else {
            $uc = $user->getLoadingCard();
        }
        if ($request->query->has('startup')) {
            if ($user->getRegisterStep() === RegisterStep::CONSUMER_BILLING_ADDRESS_SET) {
                if ($uc) {
                    $card = $uc->getCard();
                    if ($uc->isUsUnlockedLegacy() && $card->isReloadable() && !$user->isIdVerified()) {
                        return $this->redirect('/consumer-register/id-verification');
                    }
                    if ($uc->everInitiated()) {
                        Util::persist($user);
                        return $this->redirect('/');
                    }
                }
            }
            Session::remove(User::LOADING_CARD);
            Session::remove('force_reload');
        } else if ($request->get('loading')) {
            Session::set(User::LOADING_CARD, $request->get('loading'));
        }

        if (!$uc) {
            return $this->redirect('/choose-card');
        }

        if (!$uc->isActive()) {
            return new ErrorResponse('The card is inactive!');
        }

        if ($uc->isLoadDisabled()) {
            return new ErrorResponse('Failed to load this card because its load operation is disabled.');
        }

        $legacy = $uc->isUsUnlockedLegacy();
        if ($legacy) {
            // TCSPAN-748: Prevent duplicated active cards
            $ucId = $uc->getId();
            $otherCards = $user->getActiveCards()->filter(function (UserCard $_uc) use ($ucId) {
                return $_uc->getId() !== $ucId;
            });
            $em = Util::em();
            if (!$otherCards->isEmpty() && !$request->get('loading')) {
                if (!$uc->getInitializedAt()) {
                    $em->remove($uc);
                    $em->flush();
                }
                return new ErrorResponse('You already have an initialized active card or signing up with a card.
                Please continue the sign up or load card from <a href="/card-management">Card Management</a> page!',
                    '/card-management', 'Card Management');
            }

            return new ErrorResponse('Loading on the legacy card is disabled.');
        }

        if ($uc->isUsUnlocked()) {
            if (!Util::isAPI() && !Util::isPrivacyRequest() && !Util::isDev()) {
                $step = $request->get('step');
                if (!$step || (int)$step < 5) {
                    return new ErrorResponse('Please load the card in the new consumer portal!');
                }
            }
        }

        $card = $uc->getCard();
        $this->checkCountry($user, $card);
        $reloadable = $card->isReloadable();

        if ($legacy && $reloadable) {
            $uiv = $user->getIdVerify();
            if (!$uiv || !$uiv->isAccepted()) {
                return $this->redirect('/consumer-register/id-verification');
            }
        }

        if ($legacy && $uc->getCurrency() && ($card->getIsAutoCreated() || $user->isTestData())) {
            $ei = $uc->createCard('/consumer-register/card-load');
            if ($ei && $ei->isFailed()) {
                $error = $ei->getError();
                $link = null;
                if ($error === FirstViewAPI::ERROR_ISSUE_DATE_LESS) {
                    $link = '/consumer-register/id-verification';

                    $uiv = $user->getIdVerify();
                    if ($uiv) {
                        $user->setIdVerifyLeftAttempts($user->getIdVerifyLeftAttempts() + 1);
                        $em->persist($user);
                        $em->remove($uiv);
                        $em->flush();
                    }
                } else if (strpos($error, FirstViewAPI::ERROR_GOVT_ID_EXPIRED) !== FALSE) {
                    $link = '/consumer-register/id-verification';
                }
                return new ErrorResponse($error, $link);
            }
        }

        if ($request->query->has('force_reload')) {
            Session::set('force_reload', 'true');
        }
        if (!$load && !$reloadable && !Session::get('force_reload')) {
            $load = $uc->getLastCompletedLoad();
            if ($load) {
                $completedAt = Carbon::instance($load->getCompletedAt());
                if ($completedAt->addMonthsWithoutOverflow(12)->isFuture()) {
                    if (Util::isAPI() || Util::isPrivacyRequest()) {
                        return new ErrorResponse('Only one load per 12 months is allowed for non-reloadable card type.');
                    }
                    return $this->render('@Portal/Register/load-card-error-reload-limit.html.twig', [
                        'uc' => $uc,
                    ]);
                }
            }
        }

        $step = 1;
        if (!$load) {
            /** @var UserCardLoad $load */
            $load = $uc->getLatestValidLoad();
            if ($request->get('loading')) {
                $load = null;
            }
        }

        if ($request->query->has('step')) {
            $step = $request->get('step');
        } else if ($load) {
            $step = $load->getStep();
            $initializedAt = $load->getInitializedAt();
            if ($initializedAt) {
                $initializedAt = Carbon::instance($initializedAt);
                $timeout = Carbon::now()->subMinutes(30);
                if ($initializedAt->lt($timeout)) {
                    $step = 1;
                }
            }
            if ($step === 5) {
                $step = 4;
            }
        }

        if ($load) {
            $load->setReload($user->everLoaded())
                ->persist();
        }

        $user->logIP('load_card_' . $step);

        $method = 'LoadCardPageStep' . $step;
        $data = [
            'step'       => $step,
            'userCard'   => $uc,
            'userCardLoad' => $load,
            'cardName'   => $card->getFullName(),
            'reloadable' => $reloadable,
            'user'       => $user,
        ];
        if (method_exists($this, $method)) {
            return $this->$method($request, $card, $data, $user, $load);
        }
        return $this->render('@Portal/Register/load-card.html.twig', $data);
    }

    protected function LoadCardPageStep1(Request $request, CardProgramCardType $card, array $data, User $user, UserCardLoad $load = null)
    {
        $data['artwork'] = $card->getArtwork();

        $currencies = $card->getCardType()->getCurrencyArray();
        $uc = $user->getLoadingCard();
        $data['currency'] = $uc->getCurrency() ?: 'USD';
        $data['amount'] = $load ? Money::formatAmount($load->getInitialAmount(), $load->getInitialCurrency(), '') : 0;

        $data['currencies'] = [];
        $values = [
            'min' => LoadService::getParsedMinLoadAmount($card->getMinLoad(true)),
            'max' => $card->getMaxLoad(true),
        ];
        if (Session::get(User::LOADING_CARD)) {
            $values['balance'] = $uc->getBalance();
        }
        foreach ($currencies as $c) {
            $data['currencies'][] = Money::convertWithText('USD', $c, $values);
        }

        if (Util::isPrivacyRequest()) {
            return $data;
        }

        return $this->render('@Portal/Register/load-card.html.twig', $data);
    }

    protected function LoadCardPageStep2(Request $request, CardProgramCardType $card, array $data, User $user, UserCardLoad $load)
    {
        $uc = $load->getUserCard();

        $amount = $load->getInitialAmount();
        if (!$amount) {
            return $this->redirect('/consumer-register/card-load?step=1');
        }
        $initialCurrency = $load->getInitialCurrency();
        $data['amount_text'] = Money::format($amount, $initialCurrency);

        $cp = $card->getCardProgram();
        $usu = $cp->isUsUnlocked();
        $em = Util::em();
        $repo = $em->getRepository(\CoreBundle\Entity\CardProgram::class);
        $all = $repo->getLoadMethods($cp, $user->getCountryid());

        foreach ($all as &$item) {
            $item['pay_amount'] = $repo->calculatePayAmount($load, $item['name'], $item['currency']);
            if ($usu) {
                $item['load_fee'] = $repo->getFinalLoadFeeItemText($uc, $item['name'], $amount);
            } else {
                $item['load_fee'] = $repo->getFeeItemText($cp, $item['name'], $user,
                    $initialCurrency, $amount);
            }
        }
        unset($item);
        // Filter duplicate payment methods
        $str = 'transfer';
       // var_dump($all);
        $onlyMehods = array_filter($all,function($t) use ($str){ return strpos(strtolower($t['name']),$str) === false;} );
       // var_dump($onlyMehods);
        $repeatMehods = array_filter($all,function($t) use ($str){ return strpos(strtolower($t['name']),$str) !== false;} );
      //  var_dump($onlyMehods);
        // get max fee for transfer repeat Mehods
        $maxMethod = !empty($repeatMehods) ? array_values($repeatMehods)[0] : [];
        foreach($repeatMehods as $repeatMehod) {
          if ($repeatMehod['pay_amount'] > $maxMethod ['pay_amount']) {
            $maxMethod = $repeatMehod;
          }
        }

        $data['loadMethods'] = array_merge($onlyMehods,[$maxMethod]);

        $balance = $uc->getBalanceWithPendingPayments($load);
        $max = $card->getMaxBalance(true);
        if ($balance + $amount > $max) {
            $data['pendingExceed'] = Money::format($max, $uc->getCurrency());
        }

        $velocityResult = VelocityLoadsService::precheck($load);
        if ($velocityResult) {
            return new ErrorResponse($velocityResult->getVelocity()->getThresholdText() . ' is not allowed');
        }

        if (Util::isPrivacyRequest()) {
            return $data;
        }
        if (Util::isAPI()) {
            return new SuccessResponse($data);
        }

        return $this->render('@Portal/Register/load-card.html.twig', $data);
    }

    protected function LoadCardPageStep3(Request $request, CardProgramCardType $card, array $data, User $user, UserCardLoad $load)
    {
        $em = Util::em();
        $initialCurrency = $load->getInitialCurrency();
        $data['method'] = $load->getMethod();
        $data['partner'] = $load->getPartner();
        $data['initialCurrency'] = $initialCurrency;
        $data['currency'] = $load->getPayCurrency();
        if (!$data['method'] || !$data['currency']) {
            return $this->redirect('/consumer-register/card-load?step=2');
        }

        $amount = $load->getInitialAmount();
        $data['amountText'] = Money::format($amount, $initialCurrency);
        $data['country'] = $em->getRepository(\CoreBundle\Entity\Country::class)->find($data['user']->getCountryid());

        $discount = [
            'loadFeeText' => '',
            'membershipFeeText' => '',
            'totalFeeText' => '',
        ];

        $discountSum = 0;
        $uc = $load->getUserCard();

        $methodName = $load->getMethodName();
        $user->updateAffiliateDiscount($methodName);

        $comments = [];
        // Load Fee
        $loadFee = $uc->calculateFee($methodName, $amount);
        if (($p = Promotion::applyUsuLoadFeePromotion($uc, null, $loadFee)) !== false) {
            $loadFee = $p;
        }
        if ($loadFee) {
            $loadFeeDiscount = $loadFee['discount'];
            if ($loadFeeDiscount) {
                $discountSum += $loadFeeDiscount;
                $discount['loadFee'] = $loadFeeDiscount;
                $discount['loadFeeText'] = Money::format($loadFeeDiscount, $initialCurrency);
            }
            $comments = array_merge($comments, $loadFee['comments'] ?? []);
        } else {
            $loadFee = [
                'origin' => 0,
                'final' => 0,
            ];
        }
        $data['loadFee'] = $loadFee['origin'];
        $data['loadFeeText'] = Money::format($loadFee['origin'], $initialCurrency);

        // Membership Fee
        $membershipFee = $uc->calculateFee(FeeGlobalName::ONE_TIME_MEMBERSHIP_FEE, $amount);
        $user->saveTotalMembershipFeeToPay($membershipFee['final'] ?? 0);
        $unpaid = $user->getUnpaidMembershipFee();

        if ($membershipFee && (empty($membershipFee['final']) || $unpaid)) {
            if ($unpaid) {
                $membershipFee['discount'] = 0;
                $membershipFee['final'] = $unpaid;
            }

            $membershipFeeDiscount = $membershipFee['discount'];
            if ($membershipFeeDiscount) {
                $discountSum += $membershipFeeDiscount;
                $discount['membershipFee'] = $membershipFeeDiscount;
                $discount['membershipFeeText'] = Money::format($membershipFeeDiscount, $initialCurrency);
            }

            $comments = array_merge($comments, $membershipFee['comments'] ?? []);
        } else {
            $membershipFee = [
                'origin' => 0,
                'final'  => 0,
            ];
        }
        $data['membershipFee'] = $membershipFee['origin'];
        $data['membershipFeeText'] = Money::format($membershipFee['origin'], $initialCurrency);

        // Replacement Fee
        $replacementFee = 0;
        if ($load->needToChargeReplacementFee()) {
            $replacementFee += $uc->calculateFinalFee(FeeGlobalName::REPLACEMENT_FEE_ACTIVE_CARD, $amount);
            $replacementFee += $uc->calculateFinalFee(FeeGlobalName::REPLACEMENT_FEE_CLOSED_CARD, $amount);
        }
        $data['replacementFee'] = $replacementFee;
        $data['replacementFeeText'] = Money::format($replacementFee, $initialCurrency);

        $discount['totalFee'] = $discountSum;
        $discount['totalFeeText'] = Money::format($discountSum, $initialCurrency);

        // Other Fees
        // TODO: Disabled this in the new USU
        $otherFees = []; // Util::s2j($load->getUserCard()->getPendingFees());

        $initialTotalAmount = array_sum([
            $amount,
            $loadFee['origin'],
            $membershipFee['origin'],
            $replacementFee
        ]);
        foreach ($otherFees as $i => $otherFee) {
            $initialTotalAmount += $otherFee['amount'];
            $otherFees[$i]['text'] = Money::format($otherFee['amount'], $initialCurrency);
        }
        $data['otherFees'] = $otherFees;
        $data['totalAmountInitialCurrency'] = $initialTotalAmount;
        $data['totalAmountInitialCurrencyText'] = Money::format($initialTotalAmount, $initialCurrency);

        $finalTotalAmount = $initialTotalAmount;
        if (isset($discount['loadFee'])) {
            $finalTotalAmount -= $discount['loadFee'];
        }
        if (isset($discount['membershipFee'])) {
            $finalTotalAmount -= $discount['membershipFee'];
        }
        // Using Rapyd to pay requires conversion of Rapyd's rate
        if ($load->getPartner()->is(LoadPartner::RAPYD)) {
            $repo = Util::em()->getRepository(\CoreBundle\Entity\CardProgram::class);
            $cp = $uc->getCard()->getCardProgram();
            $currency = $uc->getCurrency();
            $feeItem = $repo->getFeeItem($cp, $methodName, $this->getUser());
            $totalAmount = Money::convertWithRapyd($finalTotalAmount, $initialCurrency, $data['currency'],$feeItem);
        } else {
            $totalAmount = Money::convertWithExtra($finalTotalAmount, $initialCurrency, $data['currency']);
        }
        $totalAmount = Money::roundUpIfRequired($methodName, $totalAmount, $data['currency']);
        $data['totalAmount'] = Money::formatAmount($totalAmount, $data['currency']);

        if (!$discountSum) {
            $discount = false;
        }
        $data['discount'] = $discount;
        // TCSPAN-774: save the convert rate
        $rate = Money::getConvertRate($initialCurrency, $data['currency']);
        $load->setCurrencyRate($rate)
            ->setPayAmount($totalAmount)
            ->updateFeeStructure([
                'discount'      => $discount,
                'origin'        => [
                    'loadFee'       => $loadFee['origin'],
                    'membershipFee' => $membershipFee['origin'],
                ],
                'loadFee'       => $loadFee['final'],
                'membershipFee' => $membershipFee['final'],
                'replacementFee' => $replacementFee,
                'otherFees'     => $otherFees,
                'totalAmount'   => $finalTotalAmount,
                'comments'      => $comments,
            ])
            ->persist();

        if (GlobalCollectSupported::PAYNAME_IDEAL === $load->getMethodName()) {
            $data['issuers'] = GlobalCollectService::issuersOfiDeal($load);
        }
//        else if (AlternativePaymentSupported::DIRECT_PAY_MAX === $load->getMethodName()) {
//            $data['issuers'] = AlternativePaymentService::listInstitutions($user->getCountry());
//        }

        if (Util::isAPI() || Util::isPrivacyRequest()) {
            return new SuccessResponse($data);
        }

        return $this->render('@Portal/Register/load-card.html.twig', $data);
    }

    protected function LoadCardPageStep4(Request $request, CardProgramCardType $card, array $data, User $user, UserCardLoad $load)
    {
        $data['method'] = $load->getMethod();
        $data['currency'] = $load->getPayCurrency();
        if (!$data['method'] || !$data['currency']) {
            return $this->redirect('/consumer-register/card-load?step=2');
        }
        $data['amount'] = Money::formatAmount($load->getPayAmount(), $load->getPayCurrency());
        $data['response'] = null;
        $data['redirect'] = null;
        $data['instruction'] = null;
        $data['result'] = null;
        $data['qrUrl'] = null;
        $data['qrUrlCustom'] = null;

        $transaction = $load->getTransaction();
        if (!$transaction) {
            return $this->redirect('/consumer-register/card-load?step=3');
        }
        $response = json_decode($transaction->getResponse());

        $partner = $load->getPartner();
        $methodName = $load->getMethodName();
        if ($partner->is(LoadPartner::GLOBAL_COLLECT)) {
            $data['response'] = Util::formatJSON($response);

            $merchantAction = $response->merchantAction;

            if ($merchantAction->actionType === 'REDIRECT') {
                $data['redirect'] = $merchantAction->redirectData->redirectURL;
            }

            if ($merchantAction->actionType === 'SHOW_INSTRUCTIONS') {
                $data['instruction'] = GlobalCollectService::formatShowData($merchantAction->showData);
            }

            if ($merchantAction->actionType === 'SHOW_TRANSACTION_RESULTS') {
                $data['result'] = GlobalCollectService::formatShowData($merchantAction->showData);
            }
            $method  = Util::em()->getRepository(RapydCountryPaymentType::class)->findBy(['name'=>$load->getMethodName()]);
            $data['cusInstruction'] =  $method  ? $method[0]->getInstructions() : '';
        } else if ($partner->is(LoadPartner::ALTERNATIVE_PAYMENT) || $partner->is(LoadPartner::TWO_THOUSAND_CHARGE)) {
            $data['response'] = Util::formatJSON($response);

            if (!empty($response->redirectUrl)) {
                $data['redirect'] = $response->redirectUrl;
            }
            $method  = Util::em()->getRepository(RapydCountryPaymentType::class)->findBy(['name'=>$load->getMethodName()]);
            $data['cusInstruction'] =  $method  ? $method[0]->getInstructions() : '';
        } else if ($partner->is(LoadPartner::CITCON)) {
            $data['response'] = Util::formatJSON($response);

            if (!empty($response->qrUrl)) {
                $data['qrUrl'] = $response->qrUrl;
            }
        } else if ($partner->is(LoadPartner::RAPYD)) {

          $data['response'] = Util::formatJSON($response);

          // Get code or other info for Rapyd
          if ($response->textual_codes) {
              $value =   json_decode(json_encode($response->textual_codes),TRUE);
              $data['instruction'][0] = [
                  'name' => 'textual_codes',
                  'value' => $value // $payTypeName ? array_merge($value, $data['instruction'][0]['value']) : $value
              ];
          }
          // Get instruction info for Rapyd
          if (is_object($response->instructions)) {
            $response->instructions = [$response->instructions];
          }
          // get customer instructions
          $method  = Util::em()->getRepository(RapydCountryPaymentType::class)->findBy(['type'=>$response->payment_method_type]);
         // var_dump( $method);
          $instructions = [
            [
              'name' => 'instructions',
              'steps' => $method ? $method[0]->getInstructions() : $response->instructions,
              'string' =>  $method ? true : false
            ]
            ];
          $data['instruction'] = $data['instruction'] ? array_merge($data['instruction'], $instructions ) : $instructions;
          // if ($response->payment_method_type_category == 'cash' && !$response->instructions) {
          //   $data['instruction'] = array_merge($data['instruction'], [[
          //     'name' => 'instructions',
          //     'steps' => [
          //       ['step1' => 'Go to a participating cash load location and provide the above code in conjunction with the payment to load funds into your account.']
          //     ]
          //   ]]);
          // }
          $visualCodes = [];
          foreach ($response->visual_codes as $key=>$value) {
            $visualCodes[] = $value;
          }
          $data['qrUrl'] = empty($visualCodes) ? null : $visualCodes[0];
          $data['redirect'] = $response->redirect_url;
        } else if ($partner->is(LoadPartner::COINFLOW)) {
            $data['response'] = Util::formatJSON($response);

            if (!empty($response->link)) {
                $data['redirect'] = $response->link;
                $data['iframe'] = 'light';
            } else if (!empty($response->brCode) && $methodName === LoadMethod::PAYNAME_COINFLOW_PIX) {
                $data['qrUrl'] = $response->brCode;
                $data['qrUrlCustom'] = true;
                $data['result'] = [];
                if (!empty($response->quote)) {
                    $data['result'][] = [
                        'key' => 'Quote',
                        'value' => Money::format(
                            $response->quote->cents,
                            $response->quote->currency
                        ),
                    ];
                }
                if (!empty($response->expires)) {
                    $data['result'][] = [
                        'key' => 'Expiration',
                        'value' => Util::formatDateTime($response->expires),
                    ];
                }
            }
        }

        if (Util::isAPI() || Util::isPrivacyRequest()) {
            return new SuccessResponse($data);
        }

        return $this->render('@Portal/Register/load-card.html.twig', $data);
    }

    protected function LoadCardPageStep5(Request $request, CardProgramCardType $card, array $data, User $user, UserCardLoad $load)
    {
        $transaction = $load->getTransaction();
        if (!$transaction) {
            return new ErrorResponse('No more details to this transaction.');
        }
        $data['payment_error'] = null;

        $partner = $transaction->getPartner();
        $key = $transaction->getPaymentId(false);
        if ($partner->is(LoadPartner::GLOBAL_COLLECT)) {
            try {
                $payment = GlobalCollectService::getPayment($key);
                GlobalCollectService::updateLoadStatus($load, $payment);

                $paymentStatus = $payment->status;
            } catch(ReferenceException $exception) {
                Service::log($exception->getMessage(), [], 'error');
                return $this->redirect('/consumer-register/card-load?step=1');
            }
        } else if ($partner->is(LoadPartner::ALTERNATIVE_PAYMENT) || $partner->is(LoadPartner::TWO_THOUSAND_CHARGE)) {
            try {
                $payment = AlternativePaymentService::getPayment($key);
                $paymentStatus = $payment->status;
                self::processAlternativePayment($load, '/p/load/status', $payment);
            } catch (\Throwable $t) {
                $msg = $t->getMessage();
                if (str_contains($msg, 'Requested resource could not be found')) {
                    if (Util::meta($load, '2000chargeCallback') === 'cancel') {
                        $load->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR)
                            ->persist();
                        $declineReason = Util::meta($load, '2000chargeDeclineReason');
                        if ($declineReason) {
                            $msg = 'the transaction has been declined with the reason: ' . $declineReason . '. Please try again.';
                        } else {
                            $msg = 'the transaction has been canceled. Please try again with another bank.';
                        }
                    } else if (Util::isPrivacyRequest()) {
                        throw PortalException::temp('Please click on the above "Continue to Pay" button to complete the payment first.');
                    }
                }
                throw new PortalException('Payment error: ' . $msg, '/p', persist: false);
            }
        } else if ($partner->is(LoadPartner::CITCON)) {
            $service = new CitconService();
            $payment = $service->inquire($key);
            $paymentStatus = $payment['status'];
            $service->onReceivedSuccess($load, $payment);
        } else if ($partner->is(LoadPartner::RAPYD)) {
          $service = new RapydPaymentService();
          $payment = $service->getPayment($key);
          RapydPaymentService::updateLoadStatus($load, $payment);
          $paymentStatus = RapydPaymentService::getStatusByConstant($payment['status']);
        } else if ($partner->is(LoadPartner::COINFLOW)) {
            CoinflowService::syncPaymentId($load);
            CoinflowService::updatePayment($load);
            $paymentStatus = $load->getStatus();
        } else if ($partner->is(LoadPartner::SYSTEM)) {
            $paymentStatus = $load->getStatus();
        } else {
            throw new PortalException('Unknown load partner: ' . $partner->getName());
        }

        $transaction->setStatus($paymentStatus)
            ->persist();

        $data['status'] = ucwords($load->getLoadStatus());
        $data['transaction_id'] = $load->getTransactionNo();
        $data['payment_error'] = $load->getLoadError();

        if ($paymentStatus === 'Isf') {
            $paymentStatus = 'Insufficient Funds';
        }
        $data['nativeStatus'] = $paymentStatus;

        if (Util::isPrivacyRequest()) {
            return $data;
        }
        if (Util::isAPI()) {
            return new SuccessResponse($data);
        }

        $data['_clean_ui_'] = true;
        return $this->render('@Portal/Register/load-card.html.twig', $data);
    }

    /**
     * @Route("/consumer-register/card-load-set-amount", methods={"POST"})
     */
    public function LoadCardSetAmount(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $uc = $user->getLoadingCard();

        $currency = $request->get('currency');
        $amount = Money::normalizeAmount($request->get('amount'), $currency);

        $uc->setCurrency($currency);
        $uc->persist();

        $load = $uc->ensureLoad();
        $load->setInitialAmount($amount);
        $load->setInitialCurrency($currency);
        $load->persist();

        if (Util::isAPI() || Util::isPrivacyRequest()) {
            return $load;
        }

        return $this->redirect('/consumer-register/card-load?step=2');
    }

    /**
     * @Route("/consumer-register/card-load-set-method", methods={"GET"})
     */
    public function LoadCardSetMethod(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $uc = $user->getLoadingCard();
        $load = $uc->ensureLoad();

        $em = Util::em();
        $partner = $em->getRepository(\CoreBundle\Entity\LoadPartner::class)->find($request->get('partner'));
        $method = $em->getRepository(\CoreBundle\Entity\LoadMethod::class)->find($request->get('method'));

        $load->setPartner($partner);
        $load->setMethod($method);
        $load->setPayCurrency($request->get('currency'))
            ->persist();

        return $this->redirect('/consumer-register/card-load?step=3');
    }

    /**
     * @Route("/consumer-register/card-load-create-payment", methods={"POST"})
     * @throws \Exception
     */
    public function LoadCardCreatePayment(Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $uc = $user->getLoadingCard();
        $card = $uc->getCard();
        $load = $uc->ensureLoad();
        if ($load->getInitializedAt()) {
            $msg = 'This load request had been initialized. Please start a new request from beginning!';
            if (Util::isAPI()) {
                throw new PortalException($msg, '/load-card');
            }
            throw new PortalException($msg, '/consumer-register/card-load?step=1&startup' . $card->getId());
        }

        $partner = $load->getPartner();
        if (!$partner) {
            throw PortalException::temp('Failed to find the load partner info. Please create the load again from the 1st step.');
        }
        $param = 'Unsupported load partner: ' . $partner->getName();

        /** @var Transaction $transaction */
        $transaction = null;
        if ($partner->is(LoadPartner::GLOBAL_COLLECT)) {
            $param = GlobalCollectService::generateParamsForSignup($user, $load, $request);
            if (is_array($param)) {
                $response = $this->get('load_partner.global_collect')->createPayment($param, $load, $transaction);
                if ($response instanceof CreatePaymentResponse) {
                    if ($response->payment->status === 'PENDING_APPROVAL') {
                        $method = $load->getMethod();
                        if ($method->is(GlobalCollectSupported::PAYNAME_SEPA)) {
                            $token = Util::meta($load, 'directDebitToken');
                            if (!$token) {
                                return new ErrorResponse('Unknown direct debit token. Please try again!');
                            }

                            /** @var ExternalInvoke $ei */
                            list($ei) = GlobalCollectService::approveSepaDirectDebit($token, Util::maxLength($user->getCity(), 51));
                            if ($ei && $ei->isFailed()) {
                                return new ErrorResponse('Failed to approve the direct debit token: ' . $ei->getError());
                            }
                        }

                        /** @var ExternalInvoke $ei */
                        list($ei) = GlobalCollectService::approvePayment($response->payment->id, $load);
                        if ($ei && $ei->isFailed()) {
                            $response = $ei->getError();
                        }
                    }
                }
            }
        } else if ($partner->is(LoadPartner::ALTERNATIVE_PAYMENT) || $partner->is(LoadPartner::TWO_THOUSAND_CHARGE)) {
            $param = AlternativePaymentService::generateParamsForSignup($user, $load, $request);
            if (is_array($param)) {
                $response = $this->get('load_partner.alternative_payment')->createPayment($param, $load, $transaction);
            }
        } else if ($partner->is(LoadPartner::CITCON)) {
            $param = (new CitconService())->createPayment($load, $transaction);
            $response = $param;
        } else if ($partner->is(LoadPartner::RAPYD)) {
            $param = (new RapydPaymentService())->generateParamsForPayment($user, $load, $request);
            if (is_array($param)) {
              $response = (new RapydPaymentService())->createPayment($user, $load, $transaction, $param);
            }
        } else if ($partner->is(LoadPartner::COINFLOW)) {
            $param = CoinflowService::getCheckoutLink($load, $transaction);
            $response = $param;
        }
        $method = $load->getMethod();
        if ($transaction) {
            $transaction->setPartner($partner);
            $transaction->setMethod($method);
            $transaction->setRequestBy($user);
            $transaction->setProductEntity(ClassUtils::getClass($card));
            $transaction->setProductKey($card->getId());
            $transaction->setProductCount(1);
            $transaction->persist();
        }
        if (is_string($param)) {
            if ($transaction) {
                $transaction->setError($param)->setStatus(Transaction::STATUS_FAILED)->persist();
            }
            return new ErrorResponse($param);
        }
        if (!isset($response)) {
            $error = 'Failed to create payment!';
            if ($transaction) {
                $transaction->setError($error)->setStatus(Transaction::STATUS_FAILED)->persist();
            }
            return new ErrorResponse($error);
        }
        if (is_string($response)) {
            if ($transaction) {
                $transaction->setError($response)->setStatus(Transaction::STATUS_FAILED)->persist();
            }
            return new ErrorResponse($response);
        }

        // https://shinetechchina.atlassian.net/browse/TCSPAN2-176
        /** @var UserDiscount $ud */
        foreach ($load->getUserDiscounts() as $ud) {
            $ud->substractRedemption();
        }

        $load->setTransaction($transaction);
        $load->setStatus(UserCardLoad::STATUS_PENDING)
            ->setLoadStatus(UserCardLoad::LOAD_STATUS_INITIATED)
            ->setInitializedAt(new \DateTime())
            ->persist();

        $uc->setInitializedAt(new \DateTime())
            ->persist();

        Util::persist($user);

        return $this->redirect('/consumer-register/card-load?step=4');
    }

    /**
     * @Route("/consumer-register/card-load-success/{id}", methods={"GET"})
     * @param $id string id or hash
     * @param Request $request
     * @return CardProgramCardType|\Symfony\Component\HttpFoundation\RedirectResponse|Response
     */
    public function LoadCardSuccessAction(Request $request, $id)
    {
        $uc = UserCard::find($id);
        if (!$uc) {
            return new ErrorResponse('Unknown card.');
        }
        $all = $request->query->all();
        $card = $uc->getCard();

        $template = Session::get(User::LOADING_CARD)
            ? 'load-card-succeed-reload.html.twig'
            : 'load-card-succeed.html.twig';

        return $this->render('@Portal/Register/' . $template, [
            '_clean_ui_' => true,
            'userCard'   => $uc,
            'cardName'   => $card->getFullName(),
            'reloadable' => $card->isReloadable(),
            'amount'     => $all['amount'],
            'time'       => date(Util::DATE_TIME_TZ_FORMAT, $all['time']),
        ]);
    }

    /**
     * @Route("/consumer-register/gc-callback", methods={"GET", "POST"})
     * @param Request $request
     * @return CardProgramCardType|\Symfony\Component\HttpFoundation\RedirectResponse|Response
     */
    public function LoadCardGlobalCollectCallback(Request $request)
    {
        Log::debug('GlobalCollect payment callback', Util::jsonRequest($request));

        $all = $request->query->all();
        $all['time'] = time();

        $repo = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class);
        $rs = $repo->findBy([
            'paymentId' => $all['REF'],
        ]);
        /** @var UserCardLoad $load */
        $load = $rs ? $rs[0] : null;

        // The paymentId we saved was truncated because of field length
        // This is fixed. So we shouldn't use __load_id__ now.
        if (!$load) {
            $load = $repo->find($request->get('__load_id__'));
        }
        if (!$load) {
            throw new PortalException('Unknown load request!');
        }

        $uc = $load->getUserCard();
        $transaction = $load->getTransaction();

        $payment = GlobalCollectService::getPayment($all['REF']);
        GlobalCollectService::updateLoadStatus($load, $payment);

        $transaction->setStatus($payment->status)->persist();
        if (!in_array($payment->status, ['CAPTURED', 'PAID'], true)) {
            return $this->redirect('/consumer-register/card-load?step=5&load=' . $load->getId());
        }

        $ei = $uc->completeLoad($load, '/consumer-register/gc-callback');
        if ($ei && $ei->isFailed()) {
            return new ErrorResponse($ei->getError());
        }

        $resp = json_decode($transaction->getResponse() ?: '{}');
        $resp->__callback__ = $all;
        $transaction->setResponse($resp)->persist();

        return $this->redirect('/consumer-register/card-load-success/' . $uc->getHash()
            . '?amount=' . $transaction->formatMoney() . '&time=' . $all['time']);
    }

    /**
     * @Route("/consumer-register/ap-return-callback", methods={"GET", "POST"})
     * @param Request $request
     * @return CardProgramCardType|\Symfony\Component\HttpFoundation\RedirectResponse|Response
     * @throws \Exception
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     */
    public function LoadCardAlternativePaymentCallback(Request $request)
    {
        Log::debug('AP/2000Charge return callback', Util::jsonRequest($request));

        $all = $request->query->all();

        /** @var UserCardLoad $load */
        $load = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)->find($request->get('__load_id__'));
        if ($load) {
            Util::updateMeta($load, [
                '2000chargeCallback' => 'return',
            ]);
        }

        $transaction = $load->getTransaction();
        if ($transaction) {
            $resp = json_decode($transaction->getResponse() ?: '{}');
            $all['time'] = time();
            $resp->__callback__ = $all;
            $transaction->setResponse($resp)->persist();
        }

        $processed = self::processAlternativePayment($load, '/consumer-register/ap-return-callback');
        if ($processed instanceof Response) {
            return $processed;
        }

        $uc = $load->getUserCard();
        return $this->redirect('/consumer-register/card-load-success/' . $uc->getHash()
            . '?amount=' . $transaction->formatMoney() . '&time=' . $all['time']);
    }

    public static function processAlternativePayment(UserCardLoad $load, $fromPage, $payment = null)
    {
        $cacheKey = 'processAlternativePayment_' . $load->getId();
        if (Data::has($cacheKey)) {
            Log::warn('Duplicated processing for AP/2000Charge ' . $cacheKey);
            return null;
        }
        Data::set($cacheKey, true);

        $oldStatus = $load->getLoadStatus();
        Log::debug('Before processing AP/2000Charge change', [
            'load' => $load->getId(),
            'old_status' => $oldStatus,
        ]);
        try {
            if ( ! $payment) {
                $payment = AlternativePaymentService::getPayment($load->getPaymentId());
            }
            Data::del($cacheKey);
            if ( ! $payment) {
                Log::warn('Not found AP/2000Charge tran: ' . $cacheKey);
                return null;
            }
        } catch (\Exception $exception) {
            if ($fromPage === '/webhook/alternative-payments' && Security::getClientIp() === AlternativePaymentService::PARTNER_SERVER_IP) {
                $payment = null;
                $resource = Util::$request->get('resource');
                if ($resource) {
                    $payment = json_decode(json_encode($resource), false);
                }
                if (empty($payment) || empty($payment->id)) {
                    SlackService::alert('Failed to parse the webhook data when it failed to query transaction status', [
                        'load' => $load->getId(),
                        'partner' => $load->getPartnerName(),
                        'method' => $load->getMethodName(),
                        'payment_reference' => $load->getPaymentReference(),
                        'error' => $exception->getMessage(),
                    ], [
                        SlackService::MENTION_HANS,
                    ]);
                    Data::del($cacheKey);
                    throw $exception;
                }

                SlackService::alert('Trusting the webhook data since it failed to query transaction status', [
                    'load' => $load->getId(),
                    'partner' => $load->getPartnerName(),
                    'method' => $load->getMethodName(),
                    'payment_reference' => $load->getPaymentReference(),
                    'error' => $exception->getMessage(),
                ], [
                    SlackService::MENTION_HANS,
                ]);
            } else {
                SlackService::alert('Failed to query transaction status', [
                    'load' => $load->getId(),
                    'partner' => $load->getPartnerName(),
                    'method' => $load->getMethodName(),
                    'payment_reference' => $load->getPaymentReference(),
                    'error' => $exception->getMessage(),
                ]);
                Data::del($cacheKey);
                throw $exception;
            }
        }
        $context = [
            'load' => $load->getId(),
            'partner' => $load->getPartnerName(),
            'method' => $load->getMethodName(),
            'payment_reference' => $load->getPaymentReference(),
            'old_status' => $oldStatus,
            'new_status' => $payment->status,
        ];
        Log::debug('Received AP/2000Charge status change', $context);

        if (in_array($payment->status, [
            AlternativePaymentService::STATUS_FUNDED,
            AlternativePaymentService::STATUS_SETTLED,
        ], true)) {
            $transaction = $load->getTransaction();
            if ($transaction) {
                $transaction->setStatus(Transaction::STATUS_SUCCESS)
                    ->persist();
            }

            $status = $load->getLoadStatus();
            if (!in_array($status, [
                UserCardLoad::LOAD_STATUS_RECEIVED,
                UserCardLoad::LOAD_STATUS_LOADED,
                UserCardLoad::LOAD_STATUS_REFUNDED,
            ], true)) {
                $uc = $load->getUserCard();
                $load->setReceivedCurrency($payment->currency ?? $uc->getCurrency())
                    ->setReceivedAmount($payment->amount)
                    ->persist();

                $load->updateLoadAmountWhenReceived();
                $load->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
                    ->persist();

                SlackService::tada('Added completed payment to load queue', [
                    'Partner' => $load->getPartnerName(),
                    'Method' => $load->getMethodName(),
                    'Amount' => $load->getReceivedAmountText(),
                    'Status' => $payment->status,
                    'TxnNo' => $load->getTransactionNo(),
                    'User' => $uc->getUser()->getId(),
                ]);

                $ei = $uc->completeLoad($load, $fromPage);

                if ($ei && $ei->isFailed()) {
                    $error = $ei->getError();
                    $link = null;
                    if ($error === FirstViewAPI::ERROR_ISSUE_DATE_LESS) {
                        $link = '/consumer-register/id-verification';

                        $em = Util::em();
                        $user = $uc->getUser();
                        $uiv = $user->getIdVerify();
                        if ($uiv) {
                            $user->setIdVerifyLeftAttempts($user->getIdVerifyLeftAttempts() + 1);
                            $em->persist($user);
                            $em->remove($uiv);
                            $em->flush();
                        }
                    } else if (strpos($error, FirstViewAPI::ERROR_GOVT_ID_EXPIRED) !== FALSE) {
                        $link = '/consumer-register/id-verification';
                    }
                    Log::warn('Failed to process the AP/2000Charge tran: ' . $load->getId(), [
                        'error' => $error,
                        'link' => $link,
                    ]);
                    return new ErrorResponse($error, $link);
                }

                // It's better to load in the queue
//                CardService::load($load->getUserCard(), $load->getLoadAmount(), $load);
            } else if ($status === UserCardLoad::LOAD_STATUS_REFUNDED) {
                SlackService::warning('Abnormal load status change', $context,
                    SlackService::MENTION_HANS);
            } else {
                Log::debug('Received payment status change', $context);
            }
        } else if (in_array($payment->status, [
            AlternativePaymentService::STATUS_ABORTED,
            AlternativePaymentService::STATUS_INVALID,
            AlternativePaymentService::STATUS_ISF,
            AlternativePaymentService::STATUS_DECLINED,
        ], true)) {
            $transaction = $load->getTransaction();
            if ($transaction) {
                $transaction->setStatus(Transaction::STATUS_FAILED)
                    ->persist();
            }
            if (in_array($oldStatus, [
                UserCardLoad::LOAD_STATUS_UNKNOWN,
                UserCardLoad::LOAD_STATUS_INITIATED,
                UserCardLoad::LOAD_STATUS_PENDING,
                UserCardLoad::LOAD_STATUS_CONFIRMED,
                UserCardLoad::LOAD_STATUS_PENDING,
                UserCardLoad::LOAD_STATUS_RECEIVED,
                UserCardLoad::LOAD_STATUS_ERROR,
                UserCardLoad::LOAD_STATUS_SKIPPED,
            ])) {
                $load->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR)
                    ->setError($payment->status)
                    ->persist();
            } else {
                SlackService::warning('Illegal load status change', $context,
                    SlackService::MENTION_HANS);
            }
        } else if ( ! in_array($payment->status, [
            AlternativePaymentService::STATUS_ACQUIRER_DOWN,
            AlternativePaymentService::STATUS_AWAITING_AIS_AUTHORIZATION,
            AlternativePaymentService::STATUS_AWAITING_PIS_AUTHORIZATION,
            AlternativePaymentService::STATUS_APPROVED,
            AlternativePaymentService::STATUS_PENDING,
        ])) {
            SlackService::warning('Unprocessed load status', $context,
                SlackService::MENTION_HANS);
        }
        return null;
    }

    /**
     * @Route("/consumer-register/ap-cancel-callback", methods={"GET", "POST"})
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function LoadCardAlternativePaymentCancelCallback(Request $request)
    {
        Log::debug('AP/2000Charge cancel callback', Util::jsonRequest($request));

        $loadId = $request->get('__load_id__');
        if ($loadId) {
            $load = UserCardLoad::find($loadId);
            if ($load) {
                Util::updateMeta($load, [
                    '2000chargeCallback' => 'cancel',
                ]);
            }
        }

        $suffix = $loadId ? ('&load=' . $loadId) : '';

        return $this->redirect('/consumer-register/card-load?step=5' . $suffix);
    }

    /**
     * @Route("/consumer-register/ap-callback", methods={"GET", "POST"})
     * @Route("/consumer-register/ap-webhook", methods={"GET", "POST"})
     * @return Response
     * @throws \Exception
     * @throws \InvalidArgumentException
     */
    public function LoadCardAlternativePaymentGenericCallback()
    {
        Log::debug('AP/2000Charge generic callback', Util::jsonRequest());

        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        if (isset($data['type'])) {
            $status = AlternativePaymentService::getStatusByCode($data['type']);
            if ($status) {
                $txnId = $data['resource']['id'];
                $load = UserCardLoad::findByPaymentId($txnId);
                if ($load) {
                    $oldStatus = $load->getLoadStatus();
                    if (UserCardLoad::isNewStatus($status, $oldStatus)) {
                        Service::log('Updated load status from AP webhook', [
                            'load'      => $load->getId(),
                            'status'    => $status,
                            'oldStatus' => $oldStatus,
                            'paymentId' => $txnId,
                        ]);
                        $uc = $load->getUserCard();
                        if ($status === UserCardLoad::LOAD_STATUS_RECEIVED) {
                            $load->setReceivedCurrency($data['resource']['currency'] ?? $uc->getCurrency())
                                ->setReceivedAmount($data['resource']['amount'])
                                ->persist();

                            $load->updateLoadAmountWhenReceived();
                        }
                        $load->setLoadStatus($status)
                            ->persist();

                        if ($status === UserCardLoad::LOAD_STATUS_RECEIVED) {
                            $ei = $uc->completeLoad($load, '/consumer-register/ap-callback', false);
                            if ($ei) {
                                if ($ei->isFailed()) {
                                    Service::log('Failed create card from API webhook', [
                                        'ei' => $ei->getId(),
                                        'error' => $ei->getError(),
                                        'paymentId' => $txnId,
                                        'load' => $load->getId(),
                                    ], 'error');
                                } else {
                                    if (UserCardLoad::LOAD_STATUS_RECEIVED === $load->getStatus()) {
                                        CardService::load($load->getUserCard(), $load->getLoadAmount(), $load);
                                    }
                                }
                            } else {
                                Service::log('Failed create card from AP webhook', [
                                    'error' => 'Unknown ei',
                                    'paymentId' => $txnId,
                                    'load' => $load->getId(),
                                ], 'error');
                            }
                        }
                    } else {
                        Service::log('Load already has new status from AP webhook', [
                            'load'      => $load->getId(),
                            'status'    => $status,
                            'oldStatus' => $oldStatus,
                            'paymentId' => $txnId,
                        ]);
                    }
                } else {
                    Service::log('Unknown AP transaction from webhook', [
                        'paymentId' => $txnId,
                    ], 'error');
                }
            }
        }

        return new Response();
    }
}
