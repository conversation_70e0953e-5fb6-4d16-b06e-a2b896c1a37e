<?php

namespace PortalBundle\Controller;

use Carbon\Carbon;
use CoreBundle\Entity\LoginAttempt;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserOtherInfo;
use CoreBundle\Entity\UserToken;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\User\LegalService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Session;
use CoreBundle\Utils\Util;
use PortalBundle\Controller\Common\BaseController;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\SpendrBundle;
use Symfony\Component\DependencyInjection\Exception\InvalidArgumentException;
use Symfony\Component\DependencyInjection\Exception\ServiceCircularReferenceException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use UsUnlockedBundle\Services\Coinflow\CoinflowService;
use UsUnlockedBundle\Services\Mautic\MauticEventService;

class LoginController extends BaseController
{
    /**
     * @Route("/login",name="consumer_login", methods={"GET"})
     * @Route("/admin/login", name="login", methods={"GET"})
     * @param Request $request
     *
     * @return Response
     */
    public function indexAction(Request $request, AuthenticationUtils $authenticationUtils) : Response
    {
        Log::debug('login ' . \CoreBundle\Utils\Security::getClientIp() . ' - ' . $this->request->getUri());

        $host = $request->getHost();
         /*
         * TransferMex Consumer Portal only
         */
        if (in_array($host, [
          'app.transfermex.com',
          'transfermex-app-test.virtualcards.us',
          'mex-app.span.hans',
          'mex-app.span.local',
        ])) {
            $url = '';
            $session = $request->getSession();
            $params = [];
            if ($request->query->get('type')) {
              $params[] = 'type=' . $request->query->get('type');
            }
            if ($request->query->get('bank')) {
              $params[] = 'bank=' . $request->query->get('bank');
            }
            if ($request->query->get('cash')) {
              $params[] = 'cash=' . $request->query->get('cash');
            }
            if (count($params)) {
              $url = '?' . implode('&', $params);
            }
            $session->set(Util::TRANSFERMEX_APP_OPTIONS_URL, $url);
        }

        return $this->render('@Portal/Login/index.html.twig', [
            'error'         => $authenticationUtils->getLastAuthenticationError()?->getMessage(),
            'last_username' => $authenticationUtils->getLastUsername(),
            'goto'          => $request->get('goto')
        ]);
    }

    /**
     * @Route("/login", name="consumer_check", methods={"POST"})
     * @throws ServiceCircularReferenceException
     * @throws \InvalidArgumentException
     * @throws InvalidArgumentException
     * @throws \PortalBundle\Exception\PortalException
     */
    public function checkUserAction(Request $request)
    {
        LegalService::validateLegalFields($request);
        \CoreBundle\Utils\Security::recaptchaCheck($request);

        $platform = Util::platform();
        if ($platform && $platform->isDeprecated()) {
            throw new PortalException('The system "' . $platform->getName() . '" has been deprecated!');
        }

        if ($request->getRequestUri() === '/login') {
            Log::debug('login attempt from ' . ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? '') . ' - ' . $request->getUri() . ': ' . $request->get('username'));
        }

        $session = $request->getSession();
        $username = trim($request->request->get('username'));
        $password = trim($request->request->get('password'));
        if (
        	(strrchr($username, '_cow') === '_cow')
          || (strrchr($username, '_spendr') === '_spendr')
        ) {
          $username = substr($username, 0, -4);
        }
        if (!$username || !$password) {
            throw new PortalException('Invalid username or password!');
        }
        Log::debug( $session->get(Util::TRANSFERMEX_APP_OPTIONS_URL));

        [$user, $username, $platform] = UserService::findUserTryingToLogin($username);
        if (!$user) {
            throw new PortalException('Invalid username or password');
        }

        /** @var User $user */
        if ($user->isLocked()) {
            throw new PortalException('Your account has been locked. Please try again later or contact support.');
        }

        // Do not allow consumers to log in the admin domain
        if (!$user->hasNonConsumerRole() && Util::startsWith($request->getHost(), 'admin.')) {
            $appDomain = Bundle::common('getAppDomain');
            if ($appDomain && $appDomain !== $request->getSchemeAndHttpHost()) {
                throw new PortalException('Please login into the app website: ' . $appDomain);
            }
        }

        $error = 'Invalid credentials.';
        if ($user) {
            $encoder  = Util::getPasswordEncoder();
            if ($encoder->isPasswordValid($user, $password)) {
                $error = $this->afterLoggingIn($request, $user);
                if ($error instanceof Response) {
                    return $error;
                }
            }
        }
        LoginAttempt::create($username, false);

        if (!LoginAttempt::check($username)) {
            $respond = true;
            if ($user) {
                if (!$user->isLocked()) {
                    $user->setLockedStatus(User::LOCK_STATUS_LOCK);
                    Util::persist($user);
                } else {
                    $respond = false;
                }
            }
            if ($respond) {
                $error = 'Too many attempts. Your account has been locked. Please try again later.';
                if (!Util::isAPI()) {
                    $error .= ' To reset your password click "Forgot password?" below.';
                }
            }
        }

        throw new PortalException($error);
    }

    protected function afterLoggingIn(Request $request, User $user)
    {
        $error = null;
        $status = $user ? $user->getStatus() : null;
        if ($status === User::STATUS_CLOSED) {
            $error = 'This account is ' . $status . '! ' . $user->getCloseReason();
        } else if ($status === User::STATUS_BANNED) {
            $error = 'This account is ' . $status . "! Banned Reason:\n" . $user->getBannedReason();
        } else if ($status === User::STATUS_INACTIVE && !Util::platform()->isTernCommerce()) {
            $error = 'Your account is ' . $status. '. Please contact support.';
        }

        if (!$error) {
            if ($this->authIP($user)) {
                $user->logLogin();
                if ($user->inTeam(Role::ROLE_TRANSFER_MEX_MEMBER)) {
                  $userOtherInfo = UserOtherInfo::findByUser($user);
                  if (!$userOtherInfo) {
                    $userOtherInfo = new UserOtherInfo();
                    $userOtherInfo->setUser($user);
                  }
                  $now = new \DateTime();
                  Util::updateMeta($user, [
                    'webLastLogin' => Util::formatDateTime($now)
                  ]);
                  $userOtherInfo->setLastWebLogin($now)->persist();
                }
                $username = $user->getUsername();
                $la = LoginAttempt::create($username, true);
                Util::setUser($user);

                Service::identify($user);
                MauticEventService::loginSuccess($user, $la);

                $timestamp = Carbon::now()->getTimestamp();
                Data::set('session_at_' . $user->getId(), $timestamp);
                Data::set(Session::getPlatformActiveSessionKey() . $user->getId(), $timestamp);

                // add more actions here if necessary after logging in
                if (Bundle::isFIS()) {
                    // Clear FIS' current platform so that the default platform will work
                    $user->setFisCurrentPlatform(null)
                        ->persist();
                }

                $goto = $request->get('goto');

                $session = $request->getSession();
                if (
                    $user->isMasterAdmin()
                    || $user->inTeams([
                        Role::ROLE_TRANSFER_MEX_MEMBER,
                        // for transfer mex
                        Role::ROLE_TRANSFER_MEX_ADMIN,
                        Role::ROLE_TRANSFER_MEX_AGENT,
                        Role::ROLE_TRANSFER_MEX_DEPARTMENT,
                        Role::ROLE_TRANSFER_MEX_CAPTURE,
                    ])
                    || Util::hasSuffix($user->getEmail(), [
                        '@ternitup.com',
                        '@terncommerce.com',
                    ])
                    || (Util::platform()?->isFaasPlatforms() && $user->isTwoFAEnabled(Util::platform()))
                ) {
                    $session->set('Need_2FA_' . $user->getId(), TRUE);
                }

                if (Util::isLocal()) {
                    $session->remove('Need_2FA_' . $user->getId());
                }

                // enable 2FA for employer from Sep 18
                $now = Carbon::now();
                $enableDay = Carbon::create( 2023, 9, 18, 0, 0, 0, Util::tzNewYork());
                if ($user->inTeams([
                  Role::ROLE_TRANSFER_MEX_EMPLOYER,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH
                ]) && $now->gte($enableDay)) {
                  $user->setCurrentEmployer(null);
                  $session->set('Need_2FA_' . $user->getId(), true);
                }
                if (Util::isStaging()) {
                  $session->remove('Need_2FA_' . $user->getId());
                }
                if (Util::startsWith($goto, '/user/api/token')) {
                    $goto = '/';
                }
                if (Util::platform() && Util::platform()->isTernCommerce()) {
                    CoinflowService::sendEvents($user, CoinflowService::EVENT_SIGNIN);
                }
                $session->migrate();
                if ($goto) {
                    $response = $this->redirect($goto);
                } else {
                    if ($user->hasNonConsumerRole()) {
						if (
							Util::platform()
							&& Util::platform()->isSpendr()
							&& $user->inTeams(SpendrBundle::getMerchantEmployeeRoles())
						) {
							$response = $this->redirect('/');
						} else {
							$response = $this->redirect($user->getDefaultUrl());
						}
                    } else {
                        $response = $this->redirect('/');
                    }
                }

                if (Util::isAPI() || Util::isPrivacyRequest()) {
                    $ut = UserToken::instance($user);
                    UserToken::updateUseTime($ut);
                    return new SuccessResponse([
                        'token' => $ut->getToken(),
                    ]);
                }

                if ($user->needResetPassword()) {
                    $response = $this->redirect('/account-service/reset-password');
                }

                return $response;
            }

            $error = 'Access restricted.';
        }

        return $error;
    }

    /**
     * @Route("/admin/check", name="check")
     */
    public function checkAction()
    {
        return $this->redirect('/logout');
    }

    /**
     * @Route("/session-timeout")
     * @throws \InvalidArgumentException
     */
    public function timeoutAction()
    {
        return new Response('<meta http-equiv="refresh" content="1.5;URL=/logout" />Session timed out. Please login again.');
    }

    /**
     * @Route("/detect-session")
     */
    public function detectSessionAction()
    {
        $user = Util::user();
        if (!$user) {
            return new FailedResponse();
        }
        $key = 'session_at_' . $user->getId();
        $last = Data::get($key);
        if (!$last) {
            return new FailedResponse();
        }
        $last = Carbon::createFromTimestamp($last);
        $limit = Carbon::now()->subMinutes(20);
        if ($last->lt($limit)) {
            return new FailedResponse();
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/resume-session")
     */
    public function resumeSessionAction()
    {
        $user = Util::user();
        if (!$user) {
            return new FailedResponse();
        }
        $timestamp = Carbon::now()->getTimestamp();
        Data::set('session_at_' . $user->getId(), $timestamp);
        Data::set(Session::getPlatformActiveSessionKey() . $user->getId(), $timestamp);
        return new SuccessResponse();
    }

    /**
     * @Route("/login/reset-password")
     */
    public function resetPasswordAction()
    {
        return $this->render('@Portal/Login/reset.html.twig');
    }

     /**
     * @Route("/cookie-policy")
     */
    public function cookiePolicy()
    {
        return $this->render('@Portal/Login/cookie-policy.html.twig');
    }
}

