<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 12/02/2018
 * Time: 21:05
 */

namespace PortalBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Entity\AffiliateApply;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use PortalBundle\Controller\Common\BaseController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AffiliateApplyController extends BaseController
{
    /**
     * @Route("/affiliate-apply/submit", methods={"POST"})
     * @param Request $request
     * @return Response
     */
    public function submit(Request $request)
    {
        $all = $request->request->all();
        Log::debug('Got Affiliate Apply', $all);
        if (empty($all)) {
            return new Response('Empty input.');
        }

        $platform = Util::platform();
        if ($platform && !$platform->isTernCommerce()) {
            return new Response('Unsupported platform: ' . $platform->getName());
        }

        $maps = [
            '1'    => 'firstName',
            '2'    => 'lastName',
            '22'   => 'email',
            '5'    => 'companyName',
            '29'   => 'skype',
            '10'   => 'phone',
            '23'   => 'website',
            '24_1' => 'streetAddress',
            '24_2' => 'addressLine2',
            '24_3' => 'city',
            '24_4' => 'state',
            '24_5' => 'zip',
            '24_6' => 'country',
            '18'   => 'audience',
            '17'   => 'monthlyVisitors',
            '25'   => 'facebook',
            '26'   => 'linkedIn',
            '27'   => 'twitter',
            '28'   => 'instagram',
        ];
        $a = new AffiliateApply();
        foreach ($maps as $key => $method) {
            $method = 'set' . ucfirst($method);
            $a->$method($all['input_' . $key] ?? '');
        }

        $acceptedAt = $request->get('accepted_at');
        if ($acceptedAt) {
            $a->setAcceptAt(Carbon::createFromTimestamp($acceptedAt / 1000));
        }

        $a->setTime(new \DateTime())
            ->setIp(Security::getClientIp())
            ->setUserAgent($request->headers->get('User-Agent', ''));
        Util::persist($a);

        $recipients = [];
        if (Util::isLive()) {
            $recipients = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
        } else {
             $recipients = [
                '<EMAIL>',
                '<EMAIL>',
            ];
        }
        Email::sendWithTemplateToAdmins(Email::TEMPLATE_AFFILIATE_APPLY, [
            'company' => $a->getCompanyName(),
            'action_url' => (Util::isLive() ? 'https://usunlocked.virtualcards.us' : 'https://usunlocked-test.virtualcards.us' ). '/admin/tenant/affiliate-apply/' . $a->getId() . '/detail',
        ], CardProgram::usunlocked(), $recipients);

        $response = new SuccessResponse($a->getId());
        $response->headers->add(Util::getCorsHeaders());
        return $response;
    }
}
