<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/4/28
 * Time: 上午11:32
 */

namespace PortalBundle\Controller;


use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\UserPin;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\MsgResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserPinService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Image;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use PortalBundle\Controller\Common\AuthenticatedController;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Rain\RainService;
use UsUnlockedBundle\Services\Sumsub\SumsubAPI;
use UsUnlockedBundle\Services\UserService as ServicesUserService;


class AccountServiceController extends AuthenticatedController
{
    /**
     * @Route("/account-service")
     */
    public function indexAction()
    {
        if (Bundle::isTransferMex()) {
            return $this->redirect('/');
        }

        $user = $this->getUser();
        if (!$user->isConsumer() && $user->hasNonConsumerRole()) {
            $session =  Util::request()->getSession();
            $session->set('Need_2FA_' . $user->getId(), true);
            if (Util::isLocal() || Util::isStaging()) {
                $session->remove('Need_2FA_' . $user->getId());
            }
            return $this->redirect($user->getDefaultUrl());
        }

        if (Bundle::isUsUnlocked()) {
            if (!$user->getEmailVerified()) {
                return $this->redirect('/consumer-register/confirm-email');
            }
        }

        Bundle::bind(__CLASS__, 'fixUserCountry');

        $country = $user->getCountry();
        if (!$country) {
            $fixPage = $user->isLegacyOnly() ? '/consumer-register/personal-info' : '/consumer-register/billing-address';
            return $this->redirect($fixPage);
        }
        $cps = [];
        if (Bundle::isUsUnlocked()) {
            $cps = [
                CardProgram::usunlocked()->getId(),
            ];
        }
        $countryList = Util::em()->getRepository(\CoreBundle\Entity\Country::class)
            ->getCountriesWithCardPrograms($cps);
        $statesList = Util::em()->getRepository(\CoreBundle\Entity\State::class)
            ->getItemsForPortalByCountryId($country->getId());
        $year = date('Y');
        $kycStatus = $user->getSumsubApplicantStatus();
        $data = array(
            'user'        => $user,
            'countryList' => $countryList,
            'disabled'    => $this->getDisabledFields(),
            'stateList'   => $statesList,
            'year'        => $year,
            'countrycode' => $country->getIsoCode(),
            'kycStatus'   => $kycStatus ? ( $kycStatus === SumsubAPI::STATUS_GREEN ? 'Approved' : 'Reject' ): '',
            'paypalSubscriptionStatus' => ServicesUserService::getPayPalSubscription($user)
        );

        Bundle::overwrite(__CLASS__, __FUNCTION__, [
            'data' => $data,
        ]);

        return $this->render('@Portal/Default/account-service/index.html.twig', $data);
    }

    protected function getDisabledFields(User $user = null)
    {
        $user = $user ?: $this->getUser();
        $disabled = [];
        $idv = $user->getIdVerify();
        if ($idv) {
            $disabled = json_decode($idv->getDisabledFields(), true) ?: [];
        }
        $usu = Bundle::isUsUnlocked();
        if (!$usu && $user->getIssuedCards()->count()) {
            $disabled = array_merge($disabled, [
                'user.firstName', 'user.lastName',
            ]);
        }
        if ($usu) {
            $disabled = array_merge($disabled, [
                'user.firstName',
                'user.lastName',
                'user.email',
                // 'user.birthday',
                'user.address',
            ]);
        }
        return $disabled;
    }

    /**
     * @Route("/account-service/send-verification-code", methods={"POST"})
     * @param Request $request
     * @return SuccessResponse
     */
    public function verificationCodeAction(Request $request) {
        UserPinService::createForEmail(
            $this->getUser(),
            durationLimitInMinutes: 3
        );
        return new SuccessResponse();
    }

    /**
     * @Route("/account-service/unlock")
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function unlockAction(Request $request) {
        $code = $request->get('code');
        if (!$code) {
            return new FailedResponse();
        }
        $token = Util::guid();
        $user = $this->getUser();
        $em = Util::em();
        $pin = $em->getRepository(\CoreBundle\Entity\UserPin::class)
            ->findOneBy(['user' => $user], ['createdAt' => 'desc']);
        if ($pin && $pin->getPin() === $code) {
            Data::set('usu_unlock_phones_' . $user->getId(), $token, 600);
            return new SuccessResponse($token);
        }
        $uiv = $user->getIdVerify();
        if ($uiv && $uiv->getNumber()) {
            $number = substr($uiv->getNumber(), -4, 4);
            if ($number === $code) {
                Data::set('usu_unlock_phones_' . $user->getId(), $token, 600);
                return new SuccessResponse($token);
            }
        }
        return new FailedResponse();
    }

    /**
     * @Route("/account-service/update", name="account_service_update", methods={"POST"})
     * @param Request   $request
     * @param User|null $user
     *
     * @return ErrorResponse|MsgResponse|\Symfony\Component\HttpFoundation\RedirectResponse
     * @throws PortalException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function updateAccountInfo(Request $request, User $user = null) {
        Security::recaptchaCheck($request, 'update_profile');

        Bundle::overwrite(__CLASS__, __FUNCTION__, [$request]);

        /** @var User $user */
        $user = $user ?: $this->getUser();
        $emailCode = trim($request->get('email_code'));
        $pass = UserPinService::verify($user, $emailCode);
        if ( ! $pass) {
            throw PortalException::tempPage('Invalid email verification code!');
        }

        $address = trim($request->request->get('address'));
        $addressline = trim($request->request->get('addressline'));
        $city = trim($request->request->get('city'));
        if (!Util::testLatinName($address) || !Util::testLatinName($addressline) || !Util::testLatinName($city)) {
            throw new PortalException('Address and city name can only include latin characters!');
        }

        $firstName = trim($request->request->get('firstName'));
        $lastName = trim($request->request->get('lastName'));
        $emailAddress = trim($request->request->get('emailAddress'));
        $birthDay = trim($request->request->get('birthDay'));
        $birthMonth = trim($request->request->get('birthMonth'));
        $birthYear = trim($request->request->get('birthYear'));
        $state = trim($request->request->get('state'));
        $zip = trim($request->request->get('zip'));
        $phone = trim($request->request->get('phone'));
        $mobilePhone = trim($request->request->get('mobilephone'));
        $workPhone = trim($request->request->get('workphone'));
        $password = trim($request->get('password'));
        $knownAddress = trim($request->request->get('knownAddress'));

        $em = Util::em();

        $isOldUser = $user->getId() > 0;

        $emailAddress = $emailAddress ?: $user->getEmail();
        $oldEmail = $user->getEmail();
        $user->changeEmail($emailAddress);

        $disabled = $this->getDisabledFields($user);
        if (!in_array('user.firstName', $disabled, true)) {
            if (!Util::testLatinName($firstName)) {
                throw new PortalException('First name can only include latin characters!');
            }
            $user->setFirstName($firstName ?: $user->getFirstName());
        }
        if (!in_array('user.lastName', $disabled, true)) {
            if (!Util::testLatinName($lastName)) {
                throw new PortalException('Last name can only include latin characters!');
            }
            $user->setLastName($lastName ?: $user->getLastName());
        }
        if (!in_array('user.birthday', $disabled, true)) {
            if ($birthYear !== '' && $birthMonth !== '' && $birthDay !== '') {
                $birthAt = new \DateTime();
                $birthAt->setDate((int)$birthYear, (int)$birthMonth, (int)$birthDay);
                $user->setBirthday($birthAt);
            }
        }

        $user->setAddress($address ?: $user->getAddress());

        if ($request->request->has('addressline')) {
            $user->setAddressline($addressline);
        }

        if (Bundle::isUsUnlocked()) {
            $user->setCountryid(trim($request->request->get('country')) ?: $user->getCountryid());
        }

        $user->setStateid($state ?: $user->getStateid());
        $user->setCity($city ?: $user->getCity());
        $user->setZip($zip ?: $user->getZip());

        $unlockToken = trim($request->get('unlock_phones_token'));
        if ($unlockToken && $unlockToken === Data::get('usu_unlock_phones_' . $user->getId())) {
            if (Bundle::isClf()) {
                $user->setPhone($phone)
                    ->setMobilephone($mobilePhone)
                    ->setWorkphone($workPhone);
            } else {
                $country = $user->getCountry();

                if ($phone) {
                    $user->setPhone(Util::inputPhone($phone, $country));
                }
                if ($mobilePhone) {
                    $user->setMobilephone(Util::inputPhone($mobilePhone, $country));
                }
                if ($workPhone) {
                    $user->setWorkphone(Util::inputPhone($workPhone, $country));
                }
            }
        }

        if ($password) {
            $checked = UserService::checkNewPasswordSecurity($password, $user);
            if (is_string($checked)) {
                $msg = 'Failed to update password! ' . $checked;
                throw PortalException::tempPage($msg);
            }

            $user->setPlainPassword($password);
            $user->setPassword(Util::encodePassword($user, $password));
        }
        if ($request->get('profile_picture_file_delete')) {
            $user->setProfilePicture(null);
        } else {
            /** @var File $file */
            $file = $request->files->get('profile_picture_file_file');
            if ($file) {
                $type = $file->getMimeType();
                if (!in_array($type, ['image/png', 'image/jpeg', 'image/gif', 'image/jpg'], true)) {
                    throw new PortalException('Invalid profile image format. Please upload jpg/png/gif file!',
                        alert: false);
                }
                Image::correctImageOrientation($file->getPathname());
                $user->setProfilePictureFile($file);
            }
        }

        $user->setKnownAddress($knownAddress === '1');

        $em->persist($user);
        $em->flush();

        Util::updateJson($user, 'meta', [
            'passwordExpiresDisabled' => !!$request->get('passwordExpiresDisabled'),
        ]);

        if ($password) {
            UserService::postActionsAfterChangingPassword($user, $password);
        }

        if ($isOldUser) {
            $recipients = [
                $emailAddress => $user->getFullName(),
            ];
            if ($oldEmail !== $emailAddress) {
                $recipients[$oldEmail] = $user->getFullName();
            }
            Email::sendWithTemplate($recipients, Email::TEMPLATE_PROFILE_UPDATED, [
                'action_url' =>  Util::host() . '/account-service',
            ]);
        }
        if (Bundle::isUsUnlocked() && $user->isRegisterRainUser()) {
          // update rain user
          RainService::updateUser($user);
        }
        return $this->redirect('/account-service?saved=true');
    }

    /**
     * @Route("/account-service/reset-password", methods={"GET"})
     * @param Request $request
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function resetPassword(Request $request)
    {
        $user = $this->getUser();
        if (!$user->needResetPassword()) {
            return $this->redirect('/');
        }
        $reason = 'Welcome!';
        $expired = $user->isPasswordExpired();
        if ($expired) {
            $reason = 'Your password is expired!';
        }
        return $this->render('@Portal/Default/account-service/reset-password.html.twig', [
            'reason' => $reason,
            'expired' => $expired,
        ]);
    }

    /**
     * @Route("/account-service/reset-password-submit", methods={"POST"})
     * @param Request $request
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function resetPasswordSubmit(Request $request)
    {
        $this->authCsrfToken();

        $user = $this->getUser();
        if (!$user->needResetPassword()) {
            return $this->redirect('/');
        }
        $password = $request->get('password');
        $checked = UserService::checkNewPasswordSecurity($password, $user);
        if (is_string($checked)) {
            $msg = 'Failed to update password! ' . $checked;
            throw PortalException::tempPage($msg);
        }
        $user->setPlainPassword($password)
            ->setPassword(Util::encodePassword($user, $password));
        Util::persist($user);

        $user->resetPasswordDone();
        UserService::postActionsAfterChangingPassword($user, $password);
        Util::updateMeta($user, [
          'forceUpdatePassword' => false
        ]);
        return new MsgResponse('Successfully changed password!', '/', 'Reset Password', 'Continue');
    }
}
