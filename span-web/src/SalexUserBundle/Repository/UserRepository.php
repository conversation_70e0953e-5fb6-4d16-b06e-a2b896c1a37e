<?php

namespace SalexUserB<PERSON>le\Repository;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\KycProvider;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Repository\BaseRepository;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Log;
use SalexUserBundle\Entity\User;
use function Functional\pluck;
use UsUnlockedBundle\Entity\PayPalSubscription;
/**
 * UserRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class UserRepository extends BaseRepository
{
    public function findLikeUserName($searchInfo)
    {
        return $this->createQueryBuilder('p')
            ->where('p.username LIKE :username')
            ->setParameter('username', '%'.$searchInfo.'%')
            ->getQuery()
            ->getResult();
    }

    public function getDeletedIds()
    {
        Util::disableSoftDeletable();
        $rs = $this->createQueryBuilder('u')
            ->where('u.deletedAt is not null')
            ->getQuery()
            ->getResult();
        Util::enableSoftDeletable();

        return array_map(function (User $user) {
            return $user->getId();
        }, $rs);
    }

    public function getDeletedIdsForDQL()
    {
        $ids = $this->getDeletedIds();
        if ($ids) {
            return '(' . implode(', ', $ids) . ')';
        }
        return null;
    }

    public function getNonConsumerUserIds()
    {
        $statement = $this->getEntityManager()
            ->getConnection()
            ->executeQuery("select distinct(user_id) from user_role where role_id <>
                (select id from role where name = '" . Role::ROLE_CONSUMER . "');");
        $rs = $statement->fetchAll();
        return array_column($rs, 'user_id');
    }

    public function advancedSearch($option)
    {
        $qb = $this->createQueryBuilder('u')
            ->leftJoin('u.cards', 'c')
            ->leftJoin('c.card', 'cpct')
            ->leftJoin('cpct.cardProgram', 'cp')
            ->leftJoin('u.teams', 'r')
        ;
        $expr = $qb->expr();

        //ToDo Need consider about the role
        //ToDo Once have search conditions with card type, id verification ..
        //ToDo May not show other users except consumer
        //Deal with table join
        //Card Program related Processor, ProgramManager, CardType, Program, NetWorkType
        if(!empty($option['processor']) || !empty($option['programManager'])
            || !empty($option['cardType']) || !empty($option['cardProgram'])
            || !empty($option['netWork']) || !empty($option['idIssuedDateStatus'])
            || !empty($option['idExpireDateStatus']))
        {
            $qb->leftJoin('cpct.cardType', 'ct');
            if(!empty($option['processor']))
            {
                $qb->leftJoin('ct.processor', 'ctpr');
            }
            if(!empty($option['programManager']))
            {
                $qb->leftJoin('ct.programManager', 'ctpm');
            }
        }
        $cl = false;
        //Transaction related LoadPaymentsDateRange, CardTransactionDateRange, MCC
        if(!empty($option['loadDateFrom'])|| !empty($option['loadDateTo']))
        {
            $cl = true;
            $qb->leftJoin('c.loads', 'cl');
        }
        $ctxn = false;
        if(!empty($option['transactionDateFrom']) || !empty($option['transactionDateTo'])
            || !empty($option['mcc']))
        {
            $ctxn = true;
            $qb->leftJoin('c.usages', 'ctxn');
            if(!empty($option['mcc']))
            {
                $qb->leftJoin('ctxn.merchant', 'm');
                $qb->leftJoin('m.merchantType', 'mt');
            }
        }

        //Table user_id_verify column IDDocumentNumber, IDVerifyStatus, IDIssueDate, IDExpireDate
        if(!empty($option['idDocumentNumber']) || !empty($option['idStatus'])
           || !empty($option['idIssuedDateStatus']) || !empty($option['idExpireDateStatus'])
           || !empty($option['kycProvider']))
        {
            $qb->leftJoin('u.idVerifies', 'i');
        }
        //Table usu_paypal_subscription
        if(!empty($option['paypalSubscriptionType']) || !empty($option['paypalSubscriptionId']) || !empty($option['paypalSubscriptionStatus']))
        {
            $qb->leftJoin('u.payPalSubscriptions', 'paypal');
        }

        //Table user_config column
        if(!empty($option['rainRegisterDateStart']) || !empty($option['rainRegisterDateEnd']) || !empty($option['rainRegister']) || !empty($option['sumsubStatus']))
        {
            $qb->leftJoin('u.config', 'config');
        }

        //Table user_shipper_address column BillingAddressType, BillingAddressStatus
        if(!empty($option['billingAddressType']))
        {
            $qb->leftJoin('u.reshipperAddresses', 'b');
            $qb->leftJoin('b.reshipper', 'sh');
        }

        //Deal with search conditions
        $qb->where('1 = 1');
        //Table user column UserID, FirstName, LastName, Email, Address, Phone, City, AccountStatus, Flags, Gender
        $index = 1;
        $parameters = [];

        if (Bundle::isFIS()) {
            $qb->andWhere($expr->neq('u.email', "?$index"));
            $parameters[$index] = '<EMAIL>';
            $index++;
        }

        $em = Util::em();
        $user = Util::user();
        $sa = $user->isSuperAdmin();
        $platform = Util::platform();
        if (!$sa) {
            $visibleRoles = [
                Role::ROLE_CONSUMER,
            ];
            if ($platform && $user->isPlatformOwner()) {
                $visibleRoles = array_merge($visibleRoles, [
                    Role::ROLE_API_INVOKER,
                    Role::ROLE_PLATFORM_OWNER,
                    Role::ROLE_PROGRAM_OWNER,
                ]);
            }
            $qb->andWhere($expr->in('r.name', "?$index"));
            $parameters[$index] = $visibleRoles;
            $index++;

            $otherUsers = $em->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('ure')
                ->join('ure.teams', 't')
                ->select('ure.id')
                ->where($expr->notIn('t.name', $visibleRoles))
                ->distinct()
                ->getQuery()
                ->getArrayResult();
            $otherUsers = pluck($otherUsers, 'id');
            $qb->andWhere($expr->notIn('u.id', $otherUsers));
        }

        if ($user->needAuthCardProgram()) {
            $nextIndex = $index + 1;
            $thirdIndex = $nextIndex + 1;
            $ors = [];
            if (Bundle::isRoot() || $user->isConsumerServiceAgent()) {
                $uucDQL = $em->getRepository(\CoreBundle\Entity\UserCard::class)
                    ->createQueryBuilder('uuc')
                    ->select('IDENTITY(uuc.user)')
                    ->distinct()
                    ->getQuery()
                    ->getDQL();
                $ors = [
                    // Or user has no any card info (imported?)
                    $expr->neq('u.id', $expr->all($uucDQL)),
                ];
            }

            $ors = array_merge($ors, [
                $expr->in('cp', "?$index"),
                $expr->in('u', "?$nextIndex"),
            ]);

            $parameters[$index] = $user->getOpenCardPrograms(false);
            $parameters[$nextIndex] = $em->getRepository(\CoreBundle\Entity\CardProgram::class)
                ->getUserIdsWithDeletedCards($parameters[$index]);
            $index += 2;

            if ($platform && ($sa || $user->isPlatformRelated())) {
                $qb->leftJoin('u.accessiblePlatforms', 'platforms');
                $ors[] = $expr->in('platforms', "?$thirdIndex");
                $parameters[$thirdIndex] = [
                    $platform,
                ];
                $index++;
            }

            $orX = call_user_func_array([$expr, 'orX'], $ors);
            $qb->andWhere($orX);
        }

        if ($ctxn) {
            $qb->andWhere("ctxn.actualTranCode = ?$index");
            $parameters[$index] = '00040';
            $index++;
        }

        if ($cl) {
            $qb->andWhere("cl.type = ?$index");
            $parameters[$index] = UserCardLoad::TYPE_LOAD_CARD;
            $index++;

            $qb->andWhere("cl.loadStatus = ?$index");
            $parameters[$index] = UserCardLoad::LOAD_STATUS_LOADED;
            $index++;
        }

        //UserIDs
        if(!empty($option['userIds']))
        {
            $ids = explode(',', $option['userIds']);
            $qb->andWhere(
                $qb->expr()->in('u.id', "?$index")
            );
            $parameters[$index] = $ids;
            $index++;
        }

        if (!empty($option['user_id'])) {
            $qb->andWhere("u.id = ?$index");
            $parameters[$index] = $option['user_id'];
            $index++;
        }

        //First Name
        if(!empty($option['firstName']))
        {
            $qb->andWhere(
                $qb->expr()->like($qb->expr()->lower('u.firstName'), "?$index")
            );
            $parameters[$index] = '%'. strtolower(trim($option['firstName'])) .'%';
            $index++;
        }
        //Last Name
        if(!empty($option['lastName']))
        {
            $qb->andWhere(
                $qb->expr()->like($qb->expr()->lower('u.lastName'), "?$index")
            );
            $parameters[$index] = '%'. strtolower(trim($option['lastName'])) .'%';
            $index++;
        }
        //Email
        if(!empty($option['email']))
        {
            $qb->andWhere(
                $qb->expr()->like($qb->expr()->lower('u.email'), "?$index")
            );
            $parameters[$index] = strtolower(trim($option['email']));
            $index++;
        }
        //Address
        if(!empty($option['address']))
        {
            $qb->andWhere(
                $qb->expr()->like($qb->expr()->lower('u.address'), "?$index")
            );
            $parameters[$index] = '%'. strtolower(trim($option['address'])) .'%';
            $index++;
        }
        //Phone
        if(!empty($option['phone']))
        {
            $qb->andWhere(
                $qb->expr()->like($qb->expr()->lower('u.phone'), "?$index")
            );
            $parameters[$index] = '%'. strtolower(trim($option['phone'])) .'%';
            $index++;
        }
        //City
        if(!empty($option['city']))
        {
            $qb->andWhere(
                $qb->expr()->like($qb->expr()->lower('u.city'), "?$index")
            );
            $parameters[$index] = '%'. strtolower(trim($option['city'])) .'%';
            $index++;
        }
        //Account Status
        if(!empty($option['accountStatus']))
        {
            $accountStatus = $option['accountStatus'];
            if (strtolower($accountStatus) === 'under review') {
                $accountStatus = User::STATUS_UNDER_REVIEW;
            }
            
            if (in_array($accountStatus, [
                'active',
                'active_no_kyc',
                'active_no_load',
            ])) {
                $qb->andWhere(  
                    $qb->expr()->eq('u.status', "?$index")
                );
                $parameters[$index] = User::STATUS_ACTIVE;
                $index++;
                $qb->andWhere(  
                    $qb->expr()->eq('u.register_step', "?$index")
                );
                $parameters[$index] = $accountStatus;
                $index++;
            } else {  
                $qb->andWhere(
                    $qb->expr()->eq('u.status', "?$index")
                );
                $parameters[$index] = $accountStatus;
                $index++;
            }
        }
        //Flag
        if(!empty($option['flag']))
        {
            $qb->andWhere(
                $qb->expr()->like('u.flagsname', "?$index")
            );
            $parameters[$index] = '%'.$option['flag'].'%';
            $index++;
        }
        //Gender
        if(!empty($option['gender']))
        {
            if('not_specified' == $option['gender'])
            {
                $qb->andWhere(
                    $qb->expr()->notIn('u.gender', "?$index")
                );
                $parameters[$index] = array('Male', 'Female');
                $index++;
            }else
            {
                $qb->andWhere(
                    $qb->expr()->eq('u.gender', "?$index")
                );
                $parameters[$index] = $option['gender'];
                $index++;
            }
        }
        //Table user column Country, State, Region, Age, RegisterDateRange
        //Country
        if(!empty($option['country']))
        {
            $qb->andWhere(
                $qb->expr()->eq('u.countryid', "?$index")
            );
            $parameters[$index] = $option['country'];
            $index++;
        }
        //State
        if(!empty($option['state']))
        {
            $qb->andWhere(
                $qb->expr()->eq('u.stateid', "?$index")
            );
            $parameters[$index] = $option['state'];
            $index++;
        }
        //Region
        if(!empty($option['region']))
        {
            $regionQuery = $this->getEntityManager()->getRepository(\CoreBundle\Entity\Country::class)
                ->createQueryBuilder('co');
            $regionQuery->select('co.id')
                ->where(
                    $regionQuery->expr()->eq('co.region', "?$index")
                );
            $parameters[$index] = $option['region'];
            $index++;
            $qb->andWhere(
                $qb->expr()->eq('u.countryid', $qb->expr()->any($regionQuery->getDQL()))
            );
        }
        // VIP
        if(!empty($option['vip']))
        {
            $qb->andWhere(
                $qb->expr()->eq('u.vip', "?$index")
            );
            $parameters[$index] = $option['vip'] === 'yes' ? 1 : 0;
            $index++;
        }

        // Register Step
        if(!empty($option['register_step']))
        {
            $qb->andWhere(
                $qb->expr()->eq('u.register_step', "?$index")
            );
            $parameters[$index] = $option['register_step'];
            $index++;
        }

         // Register Step
         if(!empty($option['legacy']))
         {
            if ($option['legacy'] === 'yes') {
              $qb->andWhere(
                  $qb->expr()->lt('u.createdAt', "?$index")
              );
            } else {
              $qb->andWhere(
                $qb->expr()->gt('u.createdAt', "?$index")
            );
            }
             $parameters[$index] = Carbon::create(2018, 10, 1);
             $index++;
         }

        //Age
        if(!empty($option['age']))
        {
            $ags = explode('_', $option['age']);
            $fromBirthday = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime('now'), Util::DATE_FORMAT_SEARCH))
                ->subYearsWithoutOverflow($ags[1])->startOfDay();
            $toBirthday = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime('now'), Util::DATE_FORMAT_SEARCH))
                ->subYearsWithoutOverflow($ags[0] == 1 ? 0 : $ags[0])->endOfDay();
            $qb->andWhere(
                $qb->expr()->gte('u.birthday', "?$index")
            );
            $parameters[$index] = $fromBirthday;
            $index++;
            $qb->andWhere(
                $qb->expr()->lte('u.birthday', "?$index")
            );
            $parameters[$index] = $toBirthday;
            $index++;
        }
        //RegisterDateRange
        if(!empty($option['registerDateFrom']))
        {
            $qb->andWhere(
                $qb->expr()->gte('u.createdAt', "?$index")
            );
            $parameters[$index] = Util::timeUTC($option['registerDateFrom']);
            $index++;
        }
        if(!empty($option['registerDateTo']))
        {
            $qb->andWhere(
                $qb->expr()->lte('u.createdAt', "?$index")
            );
            $parameters[$index] = Util::timeUTC($option['registerDateTo'])->addDay();
            $index++;
        }

        //Table user_card column CardAccountNumber, CardIssued, CardStatus, CardBalanceRange, CardLocalBalanceRange
        //CardAccountNumber
        if(!empty($option['accountNumber']))
        {
            $qb->andWhere(
                $qb->expr()->like('c.accountNumber', "?$index")
            );
            $parameters[$index] = '%'.trim($option['accountNumber']).'%';
            $index++;
        }
        //CardIssued
        if(!empty($option['cardIsIssued']))
        {
            if('y' == $option['cardIsIssued'])
            {
                $qb->andWhere(
                    $qb->expr()->eq('c.issued', "?$index")
                );
            }elseif ('n' == $option['cardIsIssued'])
            {
                $cardsQuery = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                    ->createQueryBuilder('u_issued');
                $cardsQuery->select('u_issued.id')
                    ->leftJoin('u_issued.cards', 'u_issued_card')
                    ->where('u_issued_card.id is null');
                $qb->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->neq('c.issued', "?$index"),
                        $qb->expr()->in('u.id', $cardsQuery->getDQL())
                    )

                );
            }
            $parameters[$index] = 1;
            $index++;
        }

        if(!empty($option['ucCreatedFrom']))
        {
            $qb->andWhere(
                $qb->expr()->gte('c.createdAt', "?$index")
            );
            $parameters[$index] = Util::timeUTC($option['ucCreatedFrom']);
            $index++;
        }

        if(!empty($option['ucCreatedTo']))
        {
            $qb->andWhere(
                $qb->expr()->lte('c.createdAt', "?$index")
            );
            $parameters[$index] = Util::timeUTC($option['ucCreatedTo'])->addDay();
            $index++;
        }

        //CardStatus
        if(!empty($option['cardStatus']))
        {
            if('active' == $option['cardStatus'])
            {
                $qb->andWhere(
                    $qb->expr()->eq('c.status', "?$index")
                );
            }elseif ('inactive' == $option['cardStatus'])
            {
                $qb->andWhere(
                    $qb->expr()->neq('c.status', "?$index")
                );
            }
            $parameters[$index] = 'active';
            $index++;
        }
        //CardBalanceRange
        if(isset($option['cardBalanceFrom']) && $option['cardBalanceFrom'] !== '')
        {
            //ToDo Need to get the specific currency
            //ToDo Deal with Null for balance field
            $fromBalance = Money::normalizeAmount($option['cardBalanceFrom'], 'USD');
            $qb->andWhere(
                $qb->expr()->gte('c.balance', "?$index")
            );
            $parameters[$index] = $fromBalance;
            $index++;
        }
        if(isset($option['cardBalanceTo']) && $option['cardBalanceTo'] !== '')
        {
            //ToDo Need to get the specific currency
            //ToDo Deal with Null for balance field
            $toBalance = Money::normalizeAmount($option['cardBalanceTo'], 'USD');
            $qb->andWhere(
                $qb->expr()->lte('c.balance', "?$index")
            );
            $parameters[$index] = $toBalance;
            $index++;
        }
        //CardLocalBalanceRange
        if(isset($option['localBalanceFrom']) && $option['localBalanceFrom']!=='')
        {
            //ToDo Need to get the specific currency
            //ToDo Deal with Null for balance field
            $fromLocalBalance = Money::normalizeAmount($option['localBalanceFrom'], 'USD');
            $qb->andWhere(
                $qb->expr()->gte('c.localBalance', "?$index")
            );
            $parameters[$index] = $fromLocalBalance;
            $index++;
        }
        if(isset($option['localBalanceTo']) && $option['localBalanceTo']!=='')
        {
            //ToDo Need to get the specific currency
            //ToDo Deal with Null for balance field
            $toLocalBalance = Money::normalizeAmount($option['localBalanceTo'], 'USD');
            $qb->andWhere(
                $qb->expr()->lte('c.localBalance', "?$index")
            );
            $parameters[$index] = $toLocalBalance;
            $index++;
        }

        if (!empty($option['legacyBalanceFrom']) || !empty($option['legacyBalanceTo'])) {
            $legacyBalanceQb = $em->getRepository(User::class)
                ->createQueryBuilder('lbu')
                ->join('lbu.cards', 'lbuc')
                ->join('lbuc.card', 'lbucc')
                ->where($expr->eq('lbucc.cardProgram', "?$index"));
            $parameters[$index++] = CardProgram::usunlockedLegacy()->getId();

            if (!empty($option['legacyBalanceFrom'])) {
                $legacyBalanceQb->andWhere($expr->gte('lbuc.balance + lbuc.localBalance', "?$index"));
                $parameters[$index++] = Money::normalizeAmount($option['legacyBalanceFrom'], 'USD');
            }

            if (!empty($option['legacyBalanceTo'])) {
                $legacyBalanceQb->andWhere($expr->lte('lbuc.balance + lbuc.localBalance', "?$index"));
                $parameters[$index++] = Money::normalizeAmount($option['legacyBalanceTo'], 'USD');
            }

            $qb->andWhere($expr->eq('u', $expr->any($legacyBalanceQb->getDQL())));
        }

        if (!empty($option['accountBalanceFrom']) || !empty($option['accountBalanceTo'])) {
            $accountBalanceQb = $em->getRepository(User::class)
                ->createQueryBuilder('abu')
                ->join('abu.cards', 'abuc')
                ->join('abuc.card', 'abucc')
                ->where($expr->neq('abucc.cardProgram', "?$index"));
            $parameters[$index++] = CardProgram::usunlockedLegacy()->getId();

            if (!empty($option['accountBalanceFrom'])) {
                $accountBalanceQb->andWhere($expr->gte('abuc.balance', "?$index"));
                $parameters[$index++] = Money::normalizeAmount($option['accountBalanceFrom'], 'USD');
            }

            if (!empty($option['accountBalanceTo'])) {
                $accountBalanceQb->andWhere($expr->lte('abuc.balance', "?$index"));
                $parameters[$index++] = Money::normalizeAmount($option['accountBalanceTo'], 'USD');
            }

            $qb->andWhere($expr->eq('u', $expr->any($accountBalanceQb->getDQL())));
        }

        //Table user_id_verify column IDDocumentNumber, IDVerifyStatus, IDIssueDate, IDExpireDate
        if(!empty($option['idDocumentNumber']) || !empty($option['idStatus']) || !empty($option['kycProvider']))
        {
           $idVerifyQuery = $this->getEntityManager()->getRepository(\CoreBundle\Entity\UserIdVerify::class)
               ->createQueryBuilder('uiv');
           $idVerifyQuery->select(
               $idVerifyQuery->expr()->max('uiv.id')
           )->groupBy('uiv.user');
           $qb->andWhere(
               $qb->expr()->eq('i.id', $qb->expr()->any($idVerifyQuery->getDQL()))
           );
        }
        //IDDocumentNumber
        if(!empty($option['idDocumentNumber']))
        {
            $qb->andWhere(
                $qb->expr()->like('i.number', "?$index")
            );
            $parameters[$index] = '%'.trim($option['idDocumentNumber']).'%';
            $index++;
        }
        //IDVerifyStatus
        if(!empty($option['idStatus']))
        {
            //ToDo For status none need check records without id verification information and null value
            $qb->andWhere(
                $qb->expr()->eq('i.status', "?$index")
            );
            $parameters[$index] = $option['idStatus'];
            $index++;
        }
        // KYC Provider
        if(!empty($option['kycProvider']))
        {
            $qb->andWhere(
                $qb->expr()->eq('i.provider', "?$index")
            );
            $parameters[$index] = Util::em()->getRepository(KycProvider::class)
                ->find($option['kycProvider']);
            $index++;
        }
        if(!empty($option['idIssuedDateStatus']) || !empty($option['idExpireDateStatus']))
        {

        }
        //IDIssueDate
        if(!empty($option['idIssuedDateStatus']))
        {
            $idVerifyIssueQuery = $this->getEntityManager()->getRepository(\CoreBundle\Entity\UserIdVerify::class)
                ->createQueryBuilder('uiv_issue');
            $idVerifyIssueQuery->select(
                $idVerifyIssueQuery->expr()->max('uiv_issue.id')
            )->groupBy('uiv_issue.user');
            $idVerifyIssueInvalidQuery = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('user_issue');
            $idVerifyIssueInvalidQuery->select('user_issue.id')
                ->leftJoin('user_issue.cards', 'userCard_issue')
                ->leftJoin('userCard_issue.card', 'cardProgramCardType_issue')
                ->leftJoin('cardProgramCardType_issue.cardType', 'cardType_issue')
                ->leftJoin('user_issue.idVerifies', 'idVerify_issue')
                ->where(
                    $idVerifyIssueInvalidQuery->expr()->andX(
                        //Invalid user
                        //Need but without value
                        $idVerifyIssueInvalidQuery->expr()->like('cardType_issue.idVerificationRequirements', "?$index"),
                        $idVerifyIssueInvalidQuery->expr()->eq('idVerify_issue.id', $qb->expr()->any($idVerifyIssueQuery->getDQL())),
                        $idVerifyIssueInvalidQuery->expr()->isNull('idVerify_issue.issueAt')
                    )
                )
                ->distinct();
            $parameters[$index] = '%'.'ID_ISSUE_DATE'.'%';
            $index++;
            if('valid' == $option['idIssuedDateStatus']){
                $qb->andWhere(
                    $qb->expr()->neq('u.id', $qb->expr()->all($idVerifyIssueInvalidQuery->getDQL()))
                );
            }elseif ('invalid' == $option['idIssuedDateStatus'])
            {
                $qb->andWhere(
                    $qb->expr()->eq('u.id', $qb->expr()->any($idVerifyIssueInvalidQuery->getDQL()))
                );
            }
        }
        //IDExpireDate
        if(!empty($option['idExpireDateStatus']))
        {
            $idVerifyExpireQuery = $this->getEntityManager()->getRepository(\CoreBundle\Entity\UserIdVerify::class)
                ->createQueryBuilder('uiv_expire');
            $idVerifyExpireQuery->select(
                $idVerifyExpireQuery->expr()->max('uiv_expire.id')
            )->groupBy('uiv_expire.user');
            $idVerifyExpireInvalidQuery = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('user_expire');
            $idVerifyExpireInvalidQuery->select('user_expire.id')
                ->leftJoin('user_expire.cards', 'userCard_expire')
                ->leftJoin('userCard_expire.card', 'cardProgramCardType_expire')
                ->leftJoin('cardProgramCardType_expire.cardType', 'cardType_expire')
                ->leftJoin('user_expire.idVerifies', 'idVerify_expire')
                ->where(
                    $idVerifyExpireInvalidQuery->expr()->andX(
                        //Invalid user
                        //Need but without value
                        $idVerifyExpireInvalidQuery->expr()->like('cardType_expire.idVerificationRequirements', "?$index"),
                        $idVerifyExpireInvalidQuery->expr()->eq('idVerify_expire.id', $qb->expr()->any($idVerifyExpireQuery->getDQL())),
                        $idVerifyExpireInvalidQuery->expr()->isNull('idVerify_expire.expireAt')
                    )
                )
                ->distinct();
            $parameters[$index] = '%'.'ID_EXPIRY_DATE'.'%';
            $index++;
            if('valid' == $option['idExpireDateStatus']){
                $qb->andWhere(
                    $qb->expr()->neq('u.id', $qb->expr()->all($idVerifyExpireInvalidQuery->getDQL()))
                );
            }elseif ('invalid' == $option['idExpireDateStatus'])
            {
                $qb->andWhere(
                    $qb->expr()->eq('u.id', $qb->expr()->any($idVerifyExpireInvalidQuery->getDQL()))
                );
            }
        }
        //Table user_shipper_address column BillingAddressType, BillingAddressStatus
        //BillingAddressType
        if(!empty($option['billingAddressType']))
        {
            $qb->andWhere(
                $qb->expr()->eq('sh.id', "?$index")
            );
            $parameters[$index] = $option['billingAddressType'];
            $index++;
        }
        //BillingAddressStatus
        if(!empty($option['billingAddressStatus']))
        {
            $billingAddressQuery = $this->getEntityManager()
                ->getRepository(\CoreBundle\Entity\UserBillingAddress::class)
                ->createQueryBuilder('uba')->select('user.id')
                ->leftJoin('uba.user', 'user')
                ->distinct();
            if('valid' == $option['billingAddressStatus'])
            {
                $qb->andWhere(
                    $qb->expr()->eq('u.id', $qb->expr()->any($billingAddressQuery->getDQL()))
                );
            }elseif ('invalid' == $option['billingAddressStatus'])
            {
                $qb->andWhere(
                    $qb->expr()->neq('u.id', $qb->expr()->all($billingAddressQuery->getDQL()))
                );
            }
        }
        //Table user_role Role
        if(!empty($option['role']))
        {
            $qb->andWhere(
                $qb->expr()->eq('r.id', "?$index")
            );
            $parameters[$index] = $option['role'];
            $index++;
        }
        //Card Program related Processor, ProgramManager, CardType, Program, NetWorkType
        //Processor
        if(!empty($option['processor']))
        {
            $qb->andWhere(
                $qb->expr()->eq('ctpr.id', "?$index")
            );
            $parameters[$index] = $option['processor'];
            $index++;
        }
        //ProgramManager
        if(!empty($option['programManager']))
        {
            $qb->andWhere(
                $qb->expr()->eq('ctpm.id', "?$index")
            );
            $parameters[$index] = $option['programManager'];
            $index++;
        }
        //CardType
        if(!empty($option['cardType']))
        {
            $qb->andWhere(
                $qb->expr()->eq('ct.id', "?$index")
            );
            $parameters[$index] = $option['cardType'];
            $index++;
        }
        //Program
        if(!empty($option['cardProgram']))
        {
            $qb->andWhere(
                $qb->expr()->eq('cp.id', "?$index")
            );
            $parameters[$index] = $option['cardProgram'];
            $index++;
        }
        //NetWorkType
        if(!empty($option['netWork']))
        {
            $qb->andWhere(
                $qb->expr()->eq('ct.network', "?$index")
            );
            $parameters[$index] = $option['netWork'];
            $index++;
        }
        //Paypal subscription

        if(!empty($option['paypalSubscriptionType']) || !empty($option['paypalSubscriptionId']) || !empty($option['paypalSubscriptionStatus']))
        {
           $paypalSubscriptionQuery = $this->getEntityManager()->getRepository(PayPalSubscription::class)
               ->createQueryBuilder('pb');
           $paypalSubscriptionQuery->select(
               $paypalSubscriptionQuery->expr()->max('pb.id')
           )->groupBy('pb.user');
           $qb->andWhere(
               $qb->expr()->eq('paypal.id', $qb->expr()->any($paypalSubscriptionQuery->getDQL()))
           );
        }

        if(!empty($option['paypalSubscriptionType']))
        {
            $qb->andWhere(
                $qb->expr()->eq('paypal.planType', "?$index")
            );
            $parameters[$index] = trim($option['paypalSubscriptionType']);
            $index++;
        }

        if(!empty($option['paypalSubscriptionId']))
        {
            $qb->andWhere(
                $qb->expr()->like('paypal.subscriptionId', "?$index")
            );
            $parameters[$index] = '%' . trim($option['paypalSubscriptionId']) . '%';
            $index++;
        }

        if(!empty($option['paypalSubscriptionStatus']))
        {
            $qb->andWhere(
                $qb->expr()->eq('paypal.status', "?$index")
            );
            $parameters[$index] = trim($option['paypalSubscriptionStatus']);
            $index++;
        }

        // sumsub

        if(!empty($option['sumsubStatus']))
        {
            $qb->andWhere(
                $qb->expr()->eq('config.sumsubStatus', "?$index")
            );
            // $qb->andWhere('i.type', "Sumsub");
            $parameters[$index] = trim($option['sumsubStatus']);
            $index++;
        }

        // sumsub
        if(!empty($option['rainRegisterDateStart'])) {
            $fromRainRegisterDate = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime($option['rainRegisterDateStart']), Util::DATE_FORMAT_SEARCH))->startOfDay();
            $qb->andWhere(
                $qb->expr()->gte('config.rainRegisterDate', "?$index")
            );
            $parameters[$index] = $fromRainRegisterDate;
            $index++;
        }

        if (!empty($option['rainRegisterDateEnd'])) {
            $toRainRegisterDate = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime($option['rainRegisterDateEnd']), Util::DATE_FORMAT_SEARCH))->endOfDay();
            $qb->andWhere(
                $qb->expr()->lte('config.rainRegisterDate', "?$index")
            );
            $parameters[$index] = $toRainRegisterDate;
            $index++;
        }
        if (!empty($option['rainRegister'])) {
            if ($option['rainRegister'] === 'yes') {
                $qb->andWhere(
                    $qb->expr()->isNotNull('config.rainUserId')
                );
            } else {
                $qb->andWhere(
                    $qb->expr()->isNull('config.rainUserId')
                );
            }
        }

        //Transaction related LoadPaymentsDateRange, CardTransactionDateRange, MCC
        //LoadPaymentsDateRange
        if(!empty($option['loadDateFrom']))
        {
            $fromLoadDate = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime($option['loadDateFrom']), Util::DATE_FORMAT_SEARCH))->startOfDay();
            $qb
                ->andWhere(
                $qb->expr()->gte('cl.loadAt', "?$index")
            );
            $parameters[$index] = $fromLoadDate;
            $index++;
        }
        if(!empty($option['loadDateTo']))
        {
            $toLoadDate = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime($option['loadDateTo']), Util::DATE_FORMAT_SEARCH))->endOfDay();
            $qb->andWhere(
                $qb->expr()->lte('cl.loadAt', "?$index")
            );
            $parameters[$index] = $toLoadDate;
            $index++;
        }
        //CardTransactionDateRange
        if(!empty($option['transactionDateFrom']))
        {
            $fromTxnDate = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime($option['transactionDateFrom']), Util::DATE_FORMAT_SEARCH))->startOfDay();
            $qb->andWhere(
                $qb->expr()->gte('ctxn.txnTime', "?$index")
            );
            $parameters[$index] = $fromTxnDate;
            $index++;
        }
        if(!empty($option['transactionDateTo']))
        {
            $toTxnDate = Carbon::createFromFormat(Util::DATE_FORMAT_SEARCH,
                Util::formatDateTime(new \DateTime($option['transactionDateTo']), Util::DATE_FORMAT_SEARCH))->endOfDay();
            $qb->andWhere(
                $qb->expr()->lte('ctxn.txnTime', "?$index")
            );
            $parameters[$index] = $toTxnDate;
            $index++;
        }
        //MCC
        if(!empty($option['mcc']))
        {
            $ids = explode(',', $option['mcc']);
            $qb->andWhere(
                $qb->expr()->in('mt.mcc', "?$index")
            );
            $parameters[$index] = $ids;
            $index++;
        }

        if (!isset($option['LoadPaymentsTimesB']) || $option['LoadPaymentsTimesB'] === '0') {
            $option['LoadPaymentsTimesA'] = '';
        }
        if (!isset($option['LoadPaymentsAmountB']) || $option['LoadPaymentsAmountB'] === '0') {
            $option['LoadPaymentsAmountA'] = '';
        }
        if (!isset($option['CardTransactionsTimesB']) || $option['CardTransactionsTimesB'] === '0') {
            $option['CardTransactionsTimesA'] = '';
        }
        if (!isset($option['CardTransactionsAmountB']) || $option['CardTransactionsAmountB'] === '0') {
            $option['CardTransactionsAmountA'] = '';
        }

        if(isset($option['cardBalanceTo']) && $option['LoadPaymentsTimesA'] !== '') {
            $option['LoadPaymentsTimesA'] = (int)$option['LoadPaymentsTimesA'] ?: 0;
            $us = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us');
            $us->select('us.id')
                ->leftJoin('us.cards', 'userCard_expire')
                ->leftJoin('userCard_expire.loads', 'userLoads_expire')
                ->where($us->expr()->isNotNull('userLoads_expire.id'))
                ->andWhere("userLoads_expire.type = ?$index");
            $parameters[$index] = UserCardLoad::TYPE_LOAD_CARD;
            $index++;

            $us->andWhere("userLoads_expire.loadStatus = ?$index");
            $parameters[$index] = UserCardLoad::LOAD_STATUS_LOADED;
            $index++;
            if(!empty($option['LoadPaymentsTimesDateA']))
            {
                $us->andWhere(
                    $us->expr()->gte('userLoads_expire.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsTimesDateA']);
                $index++;
            }
            if(!empty($option['LoadPaymentsTimesDateB']))
            {
                $us->andWhere(
                    $us->expr()->lte('userLoads_expire.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsTimesDateB'])->addDay();
                $index++;
            }

            $us->groupBy('us.id');
            $us->having( $us->expr()->gte('count(userLoads_expire.id)', "?$index"));
            $qb->andWhere(
                $qb->expr()->eq('u.id', $qb->expr()->any($us->getDQL()))
            );
            $parameters[$index] = $option['LoadPaymentsTimesA'];
            $index++;
        }
        if(isset($option['LoadPaymentsTimesB']) && $option['LoadPaymentsTimesB'] !== '') {
            $option['LoadPaymentsTimesB'] = (int)$option['LoadPaymentsTimesB'] ?: 0;
            $us1 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us1');
            $us1->select('us1.id')
                ->leftJoin('us1.cards', 'userCard_expire1')
                ->leftJoin('userCard_expire1.loads', 'userLoads_expire1')
                ->where($us1->expr()->isNotNull('userLoads_expire1.id'))
                ->andWhere("userLoads_expire1.type = ?$index");
            $parameters[$index] = UserCardLoad::TYPE_LOAD_CARD;
            $index++;

            $us1->andWhere("userLoads_expire1.loadStatus = ?$index");
            $parameters[$index] = UserCardLoad::LOAD_STATUS_LOADED;
            $index++;
            if(!empty($option['LoadPaymentsTimesDateA']))
            {
                $us1->andWhere(
                    $us1->expr()->gte('userLoads_expire1.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsTimesDateA']);
                $index++;
            }
            if(!empty($option['LoadPaymentsTimesDateB']))
            {
                $us1->andWhere(
                    $us1->expr()->lte('userLoads_expire1.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsTimesDateB'])->addDay();
                $index++;
            }
            $us1->groupBy('us1.id');
            if ($option['LoadPaymentsTimesB']) {
                $us1->having( $us1->expr()->lte('count(userLoads_expire1.id)', "?$index"));
                $qb->andWhere($qb->expr()->eq('u.id', $qb->expr()->any($us1->getDQL())));
            } else {
                $us1->having( $us1->expr()->gt('count(userLoads_expire1.id)', "?$index"));
                $qb->andWhere($qb->expr()->neq('u.id', $qb->expr()->all($us1->getDQL())));
            }
            $parameters[$index] = $option['LoadPaymentsTimesB'];
            $index++;
        }
        if(isset($option['LoadPaymentsAmountA']) && $option['LoadPaymentsAmountA'] !== '') {
            $option['LoadPaymentsAmountA'] = (int)$option['LoadPaymentsAmountA'] ?: 0;
            $lpAmountA = Money::normalizeAmount($option['LoadPaymentsAmountA'], 'USD');
            $us2 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us2');
            $us2->select('us2.id')
                ->leftJoin('us2.cards', 'userCard_expire2')
                ->leftJoin('userCard_expire2.loads', 'userLoads_expire2')
                ->where('1=1')
                ->andWhere("userLoads_expire2.type = ?$index");
            $parameters[$index] = UserCardLoad::TYPE_LOAD_CARD;
            $index++;

            $us2->andWhere("userLoads_expire2.loadStatus = ?$index");
            $parameters[$index] = UserCardLoad::LOAD_STATUS_LOADED;
            $index++;
            if(!empty($option['LoadPaymentsAmountDateA']))
            {
                $us2->andWhere(
                    $us2->expr()->gte('userLoads_expire2.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsAmountDateA']);
                $index++;
            }
            if(!empty($option['LoadPaymentsAmountDateB']))
            {
                $us2->andWhere(
                    $us2->expr()->lte('userLoads_expire2.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsAmountDateB'])->addDay();
                $index++;
            }
            $us2->groupBy('us2.id');
            $us2->having( $us2->expr()->gte('sum(userLoads_expire2.receivedAmount)', "?$index"));
            $qb->andWhere(
                $qb->expr()->eq('u.id', $qb->expr()->any($us2->getDQL()))
            );
            $parameters[$index] = $lpAmountA;
            $index++;
        }
        if(isset($option['LoadPaymentsAmountB']) && $option['LoadPaymentsAmountB'] !== '') {
            $option['LoadPaymentsAmountB'] = (int)$option['LoadPaymentsAmountB'] ?: 0;
            $lpAmountB= Money::normalizeAmount($option['LoadPaymentsAmountB'], 'USD');
            $us3 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us3');
            $us3->select('us3.id')
                ->leftJoin('us3.cards', 'userCard_expire3')
                ->leftJoin('userCard_expire3.loads', 'userLoads_expire3')
                ->where('1=1')
                ->andWhere("userLoads_expire3.type = ?$index");
            $parameters[$index] = UserCardLoad::TYPE_LOAD_CARD;
            $index++;

            $us3->andWhere("userLoads_expire3.loadStatus = ?$index");
            $parameters[$index] = UserCardLoad::LOAD_STATUS_LOADED;
            $index++;
            if(!empty($option['LoadPaymentsAmountDateA']))
            {
                $us3->andWhere(
                    $us3->expr()->gte('userLoads_expire3.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsAmountDateA']);
                $index++;
            }
            if(!empty($option['LoadPaymentsAmountDateB']))
            {
                $us3->andWhere(
                    $us3->expr()->lte('userLoads_expire3.loadAt', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['LoadPaymentsAmountDateB'])->addDay();
                $index++;
            }
            $us3->groupBy('us3.id');
            if ($lpAmountB) {
                $us3->having( $us3->expr()->lte('sum(userLoads_expire3.receivedAmount)', "?$index"));
                $qb->andWhere($qb->expr()->eq('u.id', $qb->expr()->any($us3->getDQL())));
            } else {
                $us3->having( $us3->expr()->gt('sum(userLoads_expire3.receivedAmount)', "?$index"));
                $qb->andWhere($qb->expr()->neq('u.id', $qb->expr()->all($us3->getDQL())));
            }
            $parameters[$index] = $lpAmountB;
            $index++;
        }

        if(isset($option['CardTransactionsTimesA']) && $option['CardTransactionsTimesA'] !== '') {
            $option['CardTransactionsTimesA'] = (int)$option['CardTransactionsTimesA'] ?: 0;
            $us4 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us4');
            $us4->select('us4.id')
                ->leftJoin('us4.cards', 'userCard_expire4')
                ->leftJoin('userCard_expire4.usages', 'userUsages_expire')
                ->where($us4->expr()->isNotNull('userUsages_expire.id'))
                ->andWhere("userUsages_expire.actualTranCode = ?$index");
            $parameters[$index] = '00040';
            $index++;
            if(!empty($option['CardTransactionsTimesDateA']))
            {
                $us4->andWhere(
                    $us4->expr()->gte('userUsages_expire.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsTimesDateA']);
                $index++;
            }
            if(!empty($option['CardTransactionsTimesDateB']))
            {
                $us4->andWhere(
                    $us4->expr()->lte('userUsages_expire.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsTimesDateB'])->addDay();
                $index++;
            }
            $us4->groupBy('us4.id');
            $us4->having( $us4->expr()->gte('count(us4.id)', "?$index"));
            $qb->andWhere(
                $qb->expr()->eq('u.id', $qb->expr()->any($us4->getDQL()))
            );
            $parameters[$index] = $option['CardTransactionsTimesA'];
            $index++;
        }
        if(isset($option['CardTransactionsTimesB']) && $option['CardTransactionsTimesB'] !== '') {
            $option['CardTransactionsTimesB'] = (int)$option['CardTransactionsTimesB'] ?: 0;
            $us5 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us5');
            $us5->select('us5.id')
                ->leftJoin('us5.cards', 'userCard_expire5')
                ->leftJoin('userCard_expire5.usages', 'userUsages_expire1')
                ->where($us5->expr()->isNotNull('userUsages_expire1.id'))
                ->andWhere("userUsages_expire1.actualTranCode = ?$index");
            $parameters[$index] = '00040';
            $index++;
            if(!empty($option['CardTransactionsTimesDateA']))
            {
                $us5->andWhere(
                    $us5->expr()->gte('userUsages_expire1.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsTimesDateA']);
                $index++;
            }
            if(!empty($option['CardTransactionsTimesDateB']))
            {
                $us5->andWhere(
                    $us5->expr()->lte('userUsages_expire1.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsTimesDateB'])->addDay();
                $index++;
            }
            $us5->groupBy('us5.id');
            if ($option['CardTransactionsTimesB']) {
                $us5->having( $us5->expr()->lte('count(us5.id)', "?$index"));
                $qb->andWhere($qb->expr()->eq('u.id', $qb->expr()->any($us5->getDQL())));
            } else {
                $us5->having( $us5->expr()->gt('count(us5.id)', "?$index"));
                $qb->andWhere($qb->expr()->neq('u.id', $qb->expr()->all($us5->getDQL())));
            }
            $parameters[$index] = $option['CardTransactionsTimesB'];
            $index++;
        }

        if(isset($option['CardTransactionsAmountA']) && $option['CardTransactionsAmountA'] !== '') {
            $option['CardTransactionsAmountA'] = (int)$option['CardTransactionsAmountA'] ?: 0;
            $ctAmountA= Money::normalizeAmount($option['CardTransactionsAmountA'], 'USD');
            $us6 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us6');
            $us6->select('us6.id')
                ->leftJoin('us6.cards', 'userCard_expire6')
                ->leftJoin('userCard_expire6.usages', 'userUsages_expire2')
                ->where('1=1')
                ->andWhere("userUsages_expire2.actualTranCode = ?$index");
            $parameters[$index] = '00040';
            $index++;
            if(!empty($option['CardTransactionsAmountDateA']))
            {
                $us6->andWhere(
                    $us6->expr()->gte('userUsages_expire2.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsAmountDateA']);
                $index++;
            }
            if(!empty($option['CardTransactionsAmountDateB']))
            {
                $us6->andWhere(
                    $us6->expr()->lte('userUsages_expire2.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsAmountDateB'])->addDay();
                $index++;
            }
            $us6->groupBy('us6.id');
            $us6->having( $us6->expr()->gte('sum(userUsages_expire2.txnAmount)', "?$index"));
            $qb->andWhere(
                $qb->expr()->eq('u.id', $qb->expr()->any($us6->getDQL()))
            );
            $parameters[$index] = $ctAmountA;
            $index++;
        }

        if(isset($option['CardTransactionsAmountB']) && $option['CardTransactionsAmountB'] !== '') {
            $option['CardTransactionsAmountB'] = (int)$option['CardTransactionsAmountB'] ?: 0;
            $ctAmountB= Money::normalizeAmount($option['CardTransactionsAmountB'], 'USD');
            $us7 = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
                ->createQueryBuilder('us7');
            $us7->select('us7.id')
                ->leftJoin('us7.cards', 'userCard_expire7')
                ->leftJoin('userCard_expire7.usages', 'userUsages_expire3')
                ->where('1=1')
                ->andWhere("userUsages_expire3.actualTranCode = ?$index");
            $parameters[$index] = '00040';
            $index++;
            if(!empty($option['CardTransactionsAmountDateA']))
            {
                $us7->andWhere(
                    $us7->expr()->gte('userUsages_expire3.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsAmountDateA']);
                $index++;
            }
            if(!empty($option['CardTransactionsAmountDateB']))
            {
                $us7->andWhere(
                    $us7->expr()->lte('userUsages_expire3.txnTime', "?$index")
                );
                $parameters[$index] = Util::timeUTC($option['CardTransactionsAmountDateB'])->addDay();
                $index++;
            }
            $us7->groupBy('us7.id');
            if ($ctAmountB) {
                $us7->having( $us7->expr()->lte('sum(userUsages_expire3.txnAmount)', "?$index"));
                $qb->andWhere($qb->expr()->eq('u.id', $qb->expr()->any($us7->getDQL())));
            } else {
                $us7->having( $us7->expr()->gt('sum(userUsages_expire3.txnAmount)', "?$index"));
                $qb->andWhere($qb->expr()->neq('u.id', $qb->expr()->all($us7->getDQL())));
            }
            $parameters[$index] = $ctAmountB;
            $index++;
        }

        // Affiliate
        if(!empty($option['affiliate_tenant_type']))
        {
            $affQuery = Util::em()->getRepository(\CoreBundle\Entity\Affiliate::class)
                ->createQueryBuilder('aff')
                ->select('aff.id')
                ->where("aff.affType = ?$index")
                ->getDQL();
            $qb->andWhere(
                $qb->expr()->eq('u.affiliate', $qb->expr()->any($affQuery))
            );
            $parameters[$index] = $option['affiliate_tenant_type'];
            $index++;
        }

        if (!empty($option['affiliate_select'])) {
            $qb->andWhere($expr->eq('u.affiliate', "?$index"));
            $parameters[$index] = $option['affiliate_select'];
            $index++;
        }

        // Merchant Custom Name
        if(!empty($option['merchant_custom_name']))
        {
            $qb->join('u.cards', 'm_uc')
                ->join('m_uc.usages', 'm_u')
                ->join('m_u.merchant', 'm_m')
                ->andWhere("m_m.merchantCustName like ?$index");
            $parameters[$index] = '%' . $option['merchant_custom_name'] . '%';
            $index++;
        }

        // Quick search keyword in header
        if(!empty($option['keyword']))
        {
            $qb->andWhere(
                $expr->orX(
                    $expr->eq('u.id', "?$index"),
                    $expr->eq('u.email', "?$index"),
                    $expr->eq('u.username', "?$index"),
                    $expr->eq('u.firstName', "?$index"),
                    $expr->eq('u.lastName', "?$index"),
                    $expr->eq(
                        $expr->concat(
                            'u.firstName',
                            $expr->concat($expr->literal(' '), 'u.lastName')
                        ),
                        "?$index"
                    )
                )
            );
            $parameters[$index] = $option['keyword'];
            $index++;
        }

//        if($option['LoadPaymentsTimesA']!==''||$option['LoadPaymentsTimesB']!=='') {
//            $us = $this->getEntityManager()->getRepository(\SalexUserBundle\Entity\User::class)
//                ->createQueryBuilder('us');
//            $us->select('us.id')
//                ->leftJoin('us.cards', 'userCard_expire')
//                ->leftJoin('userCard_expire.loads', 'userLoads_expire')
//                ->groupBy('us.id');
//                if($option['LoadPaymentsTimesA']!==''&&$option['LoadPaymentsTimesB']==='') {
//                    $us->having("count(us.id)>=".$option['LoadPaymentsTimesA']);
//                }
//                if ($option['LoadPaymentsTimesA']===''&&$option['LoadPaymentsTimesB']!==''){
//                    $us->having("count(us.id)<=".$option['LoadPaymentsTimesB']);
//                }
//                if($option['LoadPaymentsTimesA']!==''&&$option['LoadPaymentsTimesB']!==''){
//                    $us->having("count(us.id)>=".$option['LoadPaymentsTimesA']." and count(us.id)<=".$option['LoadPaymentsTimesB']);
//                }
//
//            $qb->andWhere(
//                $qb->expr()->eq('u.id', $qb->expr()->any($us->getDQL()))
//            );
//        }

        //Deal with parameter values
        foreach ($parameters as $key => $value)
        {
            $qb->setParameter($key, $value);
        }

        $qb->distinct()
            ->orderBy('u.id', 'desc');

        return $qb;
    }
}
