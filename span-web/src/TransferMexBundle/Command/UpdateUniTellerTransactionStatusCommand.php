<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UniTellerTransaction;
use CoreBundle\Entity\Transfer;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TransferMexBundle\Services\UniTellerAPI;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\Services\SlackService;
use CoreBundle\Utils\Data;
use TransferMexBundle\TransferMexBundle;

class UpdateUniTellerTransactionStatusCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:mex:update-uniteller-transfers-status')
            ->setDescription('Pull and fill uniteller transactions')
        ;
    }

    public function getSingletonKey()
    {
        $name = implode('_', [
            $this->getName()
        ]);

        return $name;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, true);

        if (!Util::isLive()) {
          $this->done();
          return;
        }

        if (TransferMexBundle::checkMaintenance(Transfer::PARTNER_UNITELLER)) {
          $this->done();
          return 0;
        }
        try {
          $repo = $this->em->getRepository(UniTellerTransaction::class);

          $repoTransfer = $this->em->getRepository(Transfer::class);
          $ts = $repoTransfer->createQueryBuilder('t')
              ->andWhere(Util::expr()->eq('t.partner', ':partner'))
              ->andWhere(Util::expr()->notIn('t.status', ':status'))
              ->setParameter('status', [
                  Transfer::STATUS_ERROR,
                  Transfer::STATUS_COMPLETED,
                  Transfer::STATUS_CANCELED,
              ])
              ->setParameter('partner', Transfer::PARTNER_UNITELLER)
              ->orderBy('t.sendAt', 'desc')
              ->getQuery()
              ->getResult();
          $count = count($ts);
        } catch (\Exception $ex) {
          $this->done();
          return 0;
        }
        $this->line('Found ' . $count . ' UniTeller transactions to need to be updated the details...');

        $errors = 0;
        /** @var Transfer $transfer */
        foreach ($ts as $i => $transfer) {
            Util::usleep(500);
            $this->line($i . '/' . $count . ': Updating UniTeller transaction detail for ' . $transfer->getPartnerId());

            /** @var UniTellerTransaction $rt */
            $rt = $repo->findOneBy([
                'txnId' => $transfer->getPartnerId(),
            ]);
            if (!$rt) {
                continue;
            }

            try {
              /** @var ExternalInvoke $ei */
              [$ei, $data] = UniTellerRemittanceService::getTransactionDetail($transfer->getSender(), $rt->getTxnId(), false);
              if ($ei && $ei->isFailed()) {
                  $this->line('Failed to get UniTeller transaction detail: ' . $ei->getError(), 'error');
                  $errors++;
                  if ($errors > 5) {
                      $this->em->flush();

                      SlackService::alert('Failed to query UniTeller transaction details: ' . $ei->getError(), [
                          'transfer' => $transfer->getId(),
                      ], SlackService::GROUP_DEVS);
                      $this->done();
                      return 0;
                  }
                  continue;
              }
              $data = $data['transactionDetail'] ?? [];
              Util::updateMeta($rt, [
                  'uniTellerDetail' => $data,
              ], false);
              $rt->setCurrency($transfer->getSendCurrency())
                  ->setAmount(Money::normalizeAmount($data['actualTotalTxAmount'], $transfer->getSendCurrency() ))
                  ->setTransfer($transfer);
              UniTellerRemittanceService::updateTransferByPayout($data, $transfer);

          } catch (\Exception $ex) {
            $this->line('Failed to update UniTeller transfer ' . $transfer->getId() . ' detail: ' . $ex->getMessage(), 'comment');
            continue;
          }
        }
        $this->em->flush();
        $this->line('Updated all transactions\' details');

        $this->done();
        return 0;
    }

}
