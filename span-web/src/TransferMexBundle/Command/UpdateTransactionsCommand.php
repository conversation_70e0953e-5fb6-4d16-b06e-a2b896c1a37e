<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseHostedCommand;
use CoreB<PERSON>le\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Services\APIServices\TwilioService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\BOTM\BotmService;
use FaasBundle\Services\ClientService;
use FaasBundle\Services\IProcessor;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\Console\Input\InputOption;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\SlackService;

class UpdateTransactionsCommand extends BaseHostedCommand
{
    protected $hasNewAchCredits = [];
    protected $hasDuplicatedAchCredits = [];
    protected $forceNotify = false;

    protected function configure()
    {
        $this
            ->setName('span:mex:update-transactions')
            ->setDescription('Pull and fill processor transactions. Update card balance if it has new transactions.')
            ->addOption('total', null, InputOption::VALUE_NONE, 'Total mode with max 6 hrs limit')
            ->addOption('uc', null, InputOption::VALUE_REQUIRED, 'The user card to update the transactions')
            ->addOption('days', null, InputOption::VALUE_REQUIRED, 'Query transactions from how many days ago', 1)
            ->addOption('processor', null, InputOption::VALUE_REQUIRED, 'Rapid or BOTM')
            ->addOption('from-date', null, InputOption::VALUE_REQUIRED, 'Query transactions from this date: MM/DD/YYYY')
            ->addOption('to-date', null, InputOption::VALUE_REQUIRED, 'Query transactions to this date: MM/DD/YYYY')
        ;
        $this->currentPlatform = Platform::transferMex();
    }

    public function getSingletonKey()
    {
        return implode('_', array_filter([
            $this->getName(),
            $this->input->getOption('uc'),
        ]));
    }

    public function alertForDuplicatedCall(): bool
    {
        if ($this->input->getOption('uc')) {
            return false;
        }
        return true;
    }

    protected function hostedExecute()
    {
        $input = $this->input;
        $totalMode = $input->getOption('total');
        $totalModeKey = 'mex_cron_updateAllTransactions_1';
        if ($totalMode) {
            if (Data::get($totalModeKey)) {
                SlackService::prepareForPlatform(Platform::transferMex());
                SlackService::alert('Skip updating all transactions since the last execution is not completed yet.');
                $this->done();
                return 0;
            }
            Data::set($totalModeKey, date('Y-m-d H:i:s'), true, 3600 * 6); // avoid duplicated call in 6 hours
        }

        $uc = new UserCard();
        if ($input->getOption('uc')) {
            $uc = UserCard::find($input->getOption('uc'));
        }

        // Fetch transactions
        $tz = Util::tzCentral();
        $days = max((int)$input->getOption('days'), 1);
        if ($days > 20) {
            $this->forceNotify = true;
        }

        $subDays = $days;
        $now = Carbon::now($tz);
        $yesterdayKey = 'rapid_consumer_transactions_fetched_' . $now->copy()->subDay()->format(Util::DATE_FORMAT_SEARCH);
        if ($totalMode && $days <= 1 && !Data::has($yesterdayKey)) {
            $subDays = $days + 1;
        }
        $startAt = $now->copy()->subDays($subDays - 1)->startOfDay();

        if ($input->getOption('from-date')) {
            $startAt = Carbon::createFromFormat('m/d/Y', $input->getOption('from-date'), $tz)->startOfDay();
        }

        if ($input->getOption('to-date')) {
            $endAt = Carbon::createFromFormat('m/d/Y', $input->getOption('to-date'), $tz)->startOfDay();
        } else {
            $endAt = Carbon::tomorrow()->startOfDay();
        }

        $msgPrefix = '-' . $this->execKey . '- ';
        $msgPrefix .= $uc->getAccountNumber() ? ('Account ' . $uc->getAccountNumber() . ': ') : '';
        $this->line($msgPrefix . 'Updating transactions between ' . $startAt->format('m/d/Y')
                    . ' and ' . $endAt->format('m/d/Y'), 'info');

        $processor = $this->input->getOption('processor');

        $platforms = [];
        $apis = [];
        $accountIds = [];
        if ($uc->getId()) {
            $apis[] = $uc->getProcessorApiImpl();
        } else {
            $apis = ProcessorHub::getAllApis(false);
        }

        $this->line('Before fetching transactions from processors...');

        $fetchedKeys = [];
        /** @var IProcessor $api */
        foreach ($apis as $api) {
            if ($processor && $api->getKey() !== $processor) {
                continue;
            }

            $platform = $api->getPlatform();
            $pid = $platform->getId();
            if (empty($platforms[$pid])) {
                $platforms[$pid] = [
                    'platformId' => $pid,
                    'total' => 0,
                    'news' => [],
                ];
            }
            $pendings = [];

            $fetchKey = ProcessorHub::getConsumerTransactionKey($api, $uc);
            $apiName = $api->getName(true);
            if (in_array($fetchKey, $fetchedKeys)) {
                $this->line($msgPrefix . ' ---- ' . $apiName . ' : Skip updating consumer transactions for the same key...');
                continue;
            }

            try {
                $this->line($msgPrefix . ' ---- ' . $apiName . ' : Updating consumer transactions...');
                $before = $platforms[$pid]['total'];
                if ($uc->getId()) {
                    Data::singletonThrowable('update_transaction_lock_' . $uc->getId(), function () use (&$platforms, $api, $uc, $startAt, $endAt, $pid, &$msgPrefix, &$pendings) {
                        $platforms[$pid]['total'] += ProcessorHub::updateConsumerTransactionsForCard(
                            $api, $uc, $startAt, $endAt, $platforms[$pid]['news'], $msgPrefix, $pendings);
                    }, 3600);
                } else {
                    $platforms[$pid]['total'] += ProcessorHub::updateConsumerTransactionsForCard(
                        $api, $uc, $startAt, $endAt, $platforms[$pid]['news'], $msgPrefix, $pendings);
                }
                $increased = $platforms[$pid]['total'] - $before;
                $this->line($msgPrefix . ' ---- ' . $apiName . ' : Increased transactions ' . $increased, 'comment');
            } catch (\Throwable $pe) {
                $msg = 'Failed to update processor account (' . $apiName . ') consumer transactions: ' . $pe->getMessage();
                SlackService::$channel = null;
                SlackService::prepareForPlatform($platform);
                SlackService::alert($msg);
                $this->line($msg, 'error');
            }

            $fetchedKeys[] = $fetchKey;

            if ($uc->getId()) {
                if ($api instanceof BotmAPI) {
                    $accountIds[] = $uc->getUser()->ensureConfig()->getBotmUserAccountId();
                }

                foreach ($uc->getLegacyAccountNumbers() as $an) {
                    $this->line('Fetching the transactions of legacy account ' . $an);
                    $legacyUc = new UserCard();
                    $legacyUc->setAccountNumber($an);
                    $legacyUc->setUser($uc->getUser());
                    $legacyUc->setCard($uc->getCard());
                    $legacyAPI = $legacyUc->getProcessorApiImpl();
                    try {
                        if ($legacyAPI instanceof BotmAPI && BotmAPI::isAccountNumberValid($an)) {
                            $legacyAccountId = BotmService::getCachedAccountIdByProxyValue($an);
                            if (in_array($legacyAccountId, $accountIds)) {
                                $this->line('Skip for the same BOTM account ID ' . $legacyAccountId);
                                continue;
                            }
                        }

                        $news = [];
                        $legacyFound = ProcessorHub::updateConsumerTransactionsForCard(
                            $legacyAPI, $legacyUc, $startAt, $endAt, $news, 'Account ' . $an . ': ', $pendings);
                        $platforms[$pid]['total'] += $legacyFound;
                        $this->line('Found ' . $legacyFound . ' legacy transactions of legacy account ' . $an);
                    } catch (PortalException $pe) {
                        $msg = 'Failed to sync processor legacy account (' . $an . ' @ ' . $legacyAPI->getName(true)
                               . ') consumer transactions: ' . $pe->getMessage();
                        SlackService::$channel = null;
                        SlackService::prepareForPlatform($platform);
                        SlackService::alert($msg, [
                            'uc' => $uc->getId(),
                            'legacy_account' => $an,
                            'invoke_id' => $pe->getExternalInvokeId(),
                        ]);
                        $this->line($msg, 'error');
                    }
                }
            }
        }
        $this->line($msgPrefix . 'Updated transactions ' . $days . ' days ago...');

        if ($totalMode && $days <= 1) {
            Data::set($yesterdayKey, true);
        }

        $this->postUpdatingTransactions(
            $platforms, $uc, $msgPrefix,
            $startAt, $endAt, $totalMode
        );

        if ($totalMode) {
            Data::del($totalModeKey);
        }

        return $this->done();
    }

    public function postUpdatingTransactions(array $platforms, UserCard $uc, string $msgPrefix, Carbon $startAt, Carbon $endAt, $totalMode)
    {
        foreach ($platforms as $p) {
            $this->processPlatformData($p, $uc, $startAt, $endAt);
        }

        $this->line($msgPrefix . 'Check & Send ACH credits summary');
        try {
            foreach ($this->hasNewAchCredits as $employerName => $_) {
                $batchKey = 'transfermex_ach_batch_' . $employerName;
                $batchCache = Data::getArray($batchKey, []);
                if (empty($batchCache['count'])) {
                    continue;
                }
                $platform = Platform::find($batchCache['platform']);
                $mentions = [];
                if ($platform->isTransferMex()) {
                    SlackService::$channel = SlackService::CHANNEL_CLIENT;
                    $mentions = SlackService::GROUP_TRANSFER_MEX_EXT;
                }
                SlackService::prepareForPlatform($platform);
                SlackService::eyes('ACH credits summary for `' . $employerName . '` in latest 24 hrs', [
                    'Date' => $batchCache['date'],
                    'Employer' => $batchCache['employer'],
                    'Total amount' => Money::formatUSD($batchCache['amount']),
                    'Total payments' => $batchCache['count'],
                    'Distinct employees' => count($batchCache['members']),
                ], $mentions);
            }
        } catch (\Exception $e) {
            Log::exception('Failed to send the ACH credits batch summary alert: ' . $e->getMessage(), $e);
        }


        $this->line($msgPrefix . 'Check & send duplicated ACH credit alerts');
        try {
          foreach ($this->hasDuplicatedAchCredits as $memberId => $item) {
            $member = User::find($memberId);
            $platform = Platform::find($item['platform']);
            /** @var UserCardTransaction $t */
            foreach ($item['list'] as $t) {
              $uc = $t->getUserCard();
              SlackService::prepareForPlatform($platform);
              SlackService::eyes('Duplicated ACH credits summary for `' . $member . '` in latest 24 hrs', [
                  'Date' => Carbon::now()->format(Util::DATE_FORMAT_SEARCH),
                  'Member' =>  $member->getSignature(),
                  'Duplicated Amount' => Money::formatUSD($t->getTxnAmount()),
                  'Barcode number' => $uc->getAccountNumber()
                  //'Transfer Id' => $t->getTranId(),
              ], SlackService::GROUP_TRANSFER_MEX_DUPLICATE_PAYMENTS);
            }
          }
          // send email to tren for the Duplicated payment
          $this->sentDuplicatedPaymentEmail();
        } catch (\Exception $e) {
          Log::exception('Failed to send the duplicated ACH credits batch summary alert: ' . $e->getMessage(), $e);
        }

        if ($totalMode) {
            Data::set('transfermex_update_all_transactions_at', time());
            // Service::sendAsync('/t/cron/mex/employer/update-monthly-activity-report');
        }
    }

    public function processPlatformData(array $p, UserCard $inputUc, Carbon $startAt, Carbon $endAt)
    {
        $platform = Platform::find($p['platformId']);
        if (!$platform) {
            $this->line('Unknown platform when processing transactions', 'error', $p);
            return;
        }

        $this->line('Start to process the platform: ' . $platform->getName(), 'info');

        $tz = Util::tzCentral();
        $context = [
            'from' => Util::formatDateTime($startAt),
            'to' => Util::formatDateTime($endAt),
        ];
        if ($inputUc->getId()) {
            $context['user'] = $inputUc->getUser()->getSignature();
            $context['account number'] = $inputUc->getAccountNumber();
        }
        $context['transactions count'] = $p['total'];
        $context['new transactions'] = [];

        $appDomain = Bundle::common('getAppDomain', [], null, $platform) ??
                     $platform->getDomain() ??
                     $platform->getSubDomainFull();
        $transferMex = $platform->isTransferMex();
        $twilio = new TwilioService();
        foreach ($p['news'] as $tid) {
            $t = UserCardTransaction::find($tid);
            if (!$t) {
                $this->line('Failed to find the user_card_transaction with ID ' . $tid, 'error');
                continue;
            }

            $uc = $t->getUserCard();
            $txnTime = $t->getTxnTime();
            $member = $uc->getUser();
            $amount = Money::format($t->getTxnAmountUSD(), 'USD');
            $tid = $t->getId();
            $status = $t->getStatus();
            $statusSuffix = $status === 'Declined' ? (' (' . $t->getUserField2() . ')') : '';
            $context['new transactions'][$tid] = [
                'id' => $tid,
                'platform' => $platform->getName(),
                'employer' => $member->getPrimaryGroupName(),
                'member' => $member->getSignature(),
                'account' => $t->getCustomerAccountId(),
                'description' => $t->getTranDesc(),
                'time' => Util::formatLocalDate($txnTime, $tz),
                'type' => $t->getTranCode(),
                'amount' => $amount,
                'status' => $status . $statusSuffix,
            ];
            $group = $member->getPrimaryGroup();
            $isCredit = $t->isCreditTransactionFromEmployer();
            Log::debug('New transactions notification', [
                'context' => $context['new transactions'][$tid],
                'group' => $group ? $group->getName() : null,
                'fundingType' => $group ? $group->getFundingType() : null,
                'creditFromEmployer' => $isCredit,
            ]);

            if ($isCredit && $txnTime && Carbon::instance($txnTime)->addDays(7)->isFuture()) {
                try {
                    $manualLoad = strpos($t->getTranDesc(), 'manual_load') !== false;
                    if (!$manualLoad) {
                        SlackService::$channel = 'client';
                        SlackService::prepareForPlatform($platform);
                        SlackService::check('New credit transaction', $context['new transactions'][$tid]);

                        $employerName = $group ? $group->getName() : $platform->getName();
                        $batchKey = 'transfermex_ach_batch_' . $employerName;
                        $batchCache = Data::getArray($batchKey, []);
                        $batchCache['date'] = Carbon::now()->format(Util::DATE_FORMAT_SEARCH);
                        $batchCache['platform'] = $platform->getName();
                        $batchCache['employer'] = $employerName;
                        $batchCache['amount'] = ($batchCache['amount'] ?? 0) + $t->getTxnAmountUSD();
                        $batchCache['count'] = ($batchCache['count'] ?? 0) + 1;

                        $members = $batchCache['members'] ?? [];
                        $members[] = $member->getId();
                        $batchCache['members'] = array_values(array_unique($members));
                        Data::setArray($batchKey, $batchCache, 86400, false);

                        $this->hasNewAchCredits[$employerName] = $batchCache;

                        $actualTranCode = null;
                        if ($t->isBOTM() && in_array($t->getActualTranCode(), [
                            'ach_transactions',
                        ])) {
                            $actualTranCode = $t->getActualTranCode();
                        }
                        // set the ACH record as the last record to redis for the employee
                        Data::setArray('employee_last_ach_' . $member->getId(), [
                            'id' => $t->getId(),
                            'txnTime'   => Util::formatDateTime($t->getTxnTime(), Util::DATE_TIME_FORMAT, Util::tzNewYork()),
                            'txnAmount' => $t->getTxnAmount(),
                            'meta'  => [],
                          ]);
                        // detect duplicated payments
                        if ($this->checkDuplicatedPayment($uc, $t->getTxnAmount(), $t->getTranDesc(), $actualTranCode)) {
                          $theContext = $context['new transactions'][$tid];
                          $theDesc = $theContext['description'] ?? '';
                          $mentions = SlackService::GROUP_TRANSFER_MEX_INTERNAL_MORE;
                          if (str_contains($theDesc, 'API ~~ ACH Transfer')) {
                              $mentions = SlackService::GROUP_TRANSFER_MEX_INTERNAL;
                          }
                          // set alert message
                          SlackService::$channel = null;
                          SlackService::prepareForPlatform($platform);
                          SlackService::alert('Duplicated credit transaction on the same date!',
                              $theContext, $mentions);
                          $this->hasDuplicatedAchCredits[$member->getId()]['platform'] = $platform->getName();
                          $this->hasDuplicatedAchCredits[$member->getId()]['list'][] = $t;
                        }
                    }

                    $notified = false;
                    if ($platform->isFaasPlatforms()) {
                      $fullName = $member->getFullName();
                      $client = null;
                      $cardProgram = $uc->getCard()->getCardProgram();
                      if (!$cardProgram) {
                        $cardProgram = CardProgram::usunlocked();
                      }
                      $host = $cardProgram->getPlatform()->host();
                      $platform = $cardProgram->getPlatform();
                      // Log::debug('Default host');
                      // Log::debug($host);
                      if ($member->inTeam(Role::ROLE_FAAS_CLIENT)) {
                          $client = $member;
                      } else if ($member->inTeam(Role::ROLE_FAAS_MEMBER)) {
                          $client = $member->getPrimaryGroupAdmin();
                      }
                      $group = $member->getAdminGroup();
                      $isReloadable = $group ?  $group->getMemberLoadType() : false;
                      $platformMeta = Util::meta($platform);
                      if (!$client) {
                          $client = ClientService::getClientByDomainOrReferer();
                      }
                      $host = ClientService::getPreferredHost($client, $host);
                      $body = '';
                      $foundName = '';
                      if (!$client) {
                        $foundName =  $cardProgram->getName();
                      } else {
                        $foundName = $client->getEmployerName();
                      }
                      if ($member->getEmail() && Email::isValid($member->getEmail()) && !$platform->isAmericaVoice()) {
                        $body = '<p>Your card account has been funded.</p><p style="margin: 0">Amount: '. $amount.'</p><p>Funded By: '. $foundName .'</p>';
                        // Log::debug($isReloadable);
                        // Log::debug($platformMeta['enableLoadTypeConfigure']);
                        // Log::debug($host);
                        if (!$isReloadable && $platformMeta['enableLoadTypeConfigure']) {
                          $member->setConfirmationToken(Util::guid())
                                ->setPasswordRequestedAt(Carbon::now()->addDay())
                                ->persist();
                          $actionUrl = $host . '/resetting/reset/' . $member->getConfirmationToken();
                          $body .= '<p style="margin-top:10px">You will receive your card in the mail in the next 7-10 business days.</p>
                          <p style="margin-top:10px">Click on the below link to set a password for your portal access. The link will expire in 48 hours. You can view your balance, transaction history and manage your funds online. FAQs can be found in the pamphlet you will receive with your card. If you require further assistance, please call us at ************. </p>
                          <div style="text-align:center">
                            <a href="'. $actionUrl .'" class="button button--" target="_blank">Set Password</a>
                          </div>';
                          $body .= "<p>For reference, here's your login information:</p>";
                          $body .='<table class="attributes" width="100%" cellpadding="0" cellspacing="0">
                                  <tr>
                                    <td class="attributes_content">
                                      <table width="100%" cellpadding="0" cellspacing="0">
                                        <tr>
                                          <td class="attributes_item"><strong>Login Page:</strong> ' .  $host . '/login</td>
                                        </tr>
                                        <tr>
                                          <td class="attributes_item"><strong>Username:</strong> '. $member->getEmail() . '</td>
                                        </tr>
                                      </table>
                                    </td>
                                  </tr>
                                </table><br/><br/>';

                        }

                        $notified = Email::sendWithTemplateToUser($member, Email::TEMPLATE_SIMPLE_LAYOUT, [
                            'body' => $body,
                            'Product_Team' =>  $foundName,
                            'subject' => $fullName . ', welcome to the ' . $foundName,
                        ], $cardProgram);
                      }
                      if ( !$notified && $member->getMobilephone() && !$platform->isAmericaVoice()) {
                        $msg = $fullName . ', you received ' . $amount .' on your card from '. $foundName. '. You can expect to receive your card in the mail in the next 7-10 business days. After you receive your card, you may activate your card at ' . $host . '/login or call the number in the pamphlet you will receive.';
                        $info = array(
                          'to' => $member->getMobilephone(),
                          'msg' => $msg,
                        );
                        $twilio->sendSMSMsg($info);
                      }
                    } else {
                      // send firebase message
                      $msg = $transferMex
                      ? ('Se ha acreditado ' . $amount . ' en su tarjeta TransferMex')
                      : ('Your ' . $platform->getName() . ' card has been credited ' . $amount);
                      $messageInfo = [
                        'title' =>  $transferMex ? 'Notificación' : 'Notification',
                        'message' => $msg
                      ];
                      $notified = MemberService::sentFirebase($member, $messageInfo, true);

                      if ( !$notified && $member->getEmail() && Email::isValid($member->getEmail())) {
                          $template = $transferMex
                              ? Email::TEMPLATE_TRANSFER_MEX_CREDIT_MEMBER_SPANISH
                              : Email::TEMPLATE_TRANSFER_MEX_CREDIT_MEMBER;
                          $notified = Email::sendWithTemplateToUser($member, $template, [
                              'amount' => $amount,
                              'url' => $appDomain,
                          ], Util::$cardProgram);
                      }

                      if (!$manualLoad && !$notified && $member->getMobilephone()) {
                          $msg = $transferMex
                              ? ('Se ha acreditado ' . $amount . ' en su tarjeta TransferMex')
                              : ('Your ' . $platform->getName() . ' card has been credited ' . $amount);
                          $info = array(
                              'to' => $member->getMobilephone(),
                              'msg' => $msg,
                          );
                          $twilio->sendSMSMsg($info);
                      }
                    }
                } catch (\Exception $ex) {
                    Log::warn('Failed to send notifications for new credit transaction '
                              . $tid . ': ' . $ex->getMessage());
                }
            }
        }

        $msg = 'Updated processor transactions ' . ($inputUc->getId() ? 'for a single account' : ' `totally`');
        $ucs = [];
        if ($p['news']) {
            $tiCount = count($p['news']);
            $this->line('Start to update the balance & status for ' . $tiCount
                        . ' cards that have new transactions...', 'info');
            foreach ($p['news'] as $ti => $tid) {
                $t = UserCardTransaction::find($tid);
                if (!$t) {
                    $this->line('Failed to find the user_card_transaction again with ID ' . $tid, 'error');
                    continue;
                }
                $uc = $t->getUserCard();
                if (isset($ucs[$uc->getId()])) {
                    continue;
                }
                if ($ti && $ti % 20 === 0) {
                    $this->line($ti . '/' . $tiCount . ': Updating the balance & status');
                }
                $ucs[$uc->getId()] = $uc;
                try {
                    ProcessorHub::updateBalanceAndStatus($uc);
                } catch (\Exception $e) {
                    $this->line('Failed to update balance & status for card ' . $uc->getAccountNumber()
                                . ': ' . $e->getMessage(), 'error');
                }
            }
            $context['updated balance on cards'] = count($ucs);
        }

        $context['new transactions'] = count($context['new transactions']);
        if (!$this->forceNotify && $inputUc->getId()) {
            $this->line($msg, 'comment', $context);
        } else {
            SlackService::$channel = 'client';
            SlackService::prepareForPlatform($platform);
            SlackService::clock($msg, $context);
        }
    }

    public function checkDuplicatedPayment($userCard, $amount, $desc, $actualTranCode = null) {
      $startTime = Carbon::now()->subHours(24);
      $query = Util::em()->getRepository(UserCardTransaction::class)
                     ->createQueryBuilder('uct')
                     ->where(Util::expr()->eq('uct.txnAmount', ':amount'))
                     ->andWhere(Util::expr()->gte('uct.txnTime',':txnTime'))
                     ->andWhere(Util::expr()->eq('uct.userCard',':userCard'))
                     ->andWhere(Util::expr()->eq('uct.tranCode',':tranCode'))
                     ->andWhere(Util::expr()->eq('uct.tranDesc',':tranDesc'))
                     ->setParameter('amount', $amount)
                     ->setParameter('userCard', $userCard)
                     ->setParameter('txnTime', $startTime)
                     ->setParameter('tranCode', 'CREDIT')
                     ->setParameter('tranDesc', $desc);

      if ($actualTranCode !== null) {
          $query->andWhere('uct.actualTranCode = :actual_tran_code')
              ->setParameter('actual_tran_code', $actualTranCode);
      }

      $res = $query->select('count(distinct uct)')
                     ->distinct()
                     ->getQuery()
                     ->getSingleScalarResult();
       return $res > 1 ? true : false;
    }

    public function sentDuplicatedPaymentEmail () {
      $list = [];
      foreach ($this->hasDuplicatedAchCredits as $memberId => $item) {
        $member = User::find($memberId);
        /** @var UserCardTransaction $t */
        foreach ($item['list'] as $t) {
            $uc = $t->getUserCard();
            if ( ! RapidAPI::isAccountNumberValid($uc->getAccountNumber())) {
                continue;
            }
          $list[] = [
            'Member' => $member->getSignature(),
            'Date' => Util::formatDateTime( $t->getTxnTime(), Util::DATE_FORMAT, 'America/New_York'),
            'Amount'  => Money::format($t->getTxnAmount(), 'USD'),
            // 'TransferId' => $t->getTranId()
            'Barcode number' => $uc->getAccountNumber()
          ];
        }
      }
      if (empty($list)) {
        return;
      }
      $subject = 'Duplicated ACH payments from Rapid';
      $body = Util::render('@TransferMex/Rapid/duplicated_payment_summary.html.twig', [
            'params' => $list
      ]);
      if (Util::isLive() && !Util::isDev()) {
        $to = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];
      } else {
          $to = [
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
          ];
      }

      Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, [
          'name' => 'Support Team',
          'subject' => $subject,
          'body' => $body
      ]);
    }
}
