<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use Core<PERSON>undle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UniTellerTransaction;
use CoreBundle\Entity\Transfer;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TransferMexBundle\Services\UniTellerAPI;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\TransferMexBundle;

class UpdateUniTellerTransactionsCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:mex:update-uniteller-transactions')
            ->setDescription('Pull and fill uniteller transactions')
            ->addOption('days', null, InputOption::VALUE_OPTIONAL, 'Update from how many days ago', 150)
           // ->addOption('detailDays', null, InputOption::VALUE_OPTIONAL, 'Update details from how many days ago', 30)
        ;
    }

    public function getSingletonKey()
    {
        return implode('_', [
            $this->getName()
        ]);
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, true);
        if (TransferMexBundle::checkMaintenance(Transfer::PARTNER_UNITELLER)) {
          $this->done();
          return 0;
        }
        // Query all the transactions
        $api = new UniTellerAPI();
        $page = 0;
        $pageSize = 50;
        $days = $input->getOption('days') ?: 30;
        $earliest = Util::timeUTC()->startOfDay()->subDays($days);
        $transactions = [];
        while (true) {
            // Due to API rate limit
            sleep(2);

            $this->line('Query page ' . ($page + 1) . ' from ' . date('c', $earliest->getTimestamp()));
           try {
              /** @var ExternalInvoke $ei */
              [$ei, $data] = $api->getAllTransactions(null, $page, $pageSize, Util::formatDateTime($earliest, 'mdY'), null , true);
              if ($ei && $ei->isFailed()) {
                  SlackService::alert('Failed to update UniTeller transactions: ' . $ei->getError(), [
                      'from' => $earliest->format('c'),
                      'page' => $page,
                      'pageSize' => $pageSize,
                  ], SlackService::GROUP_DEVS);
                  // $this->done();
                  // return;
              }
              $data = isset($data['results']) ? $data['results'] : [];
            } catch (\Exception $ex) {
               $this->line('Failed to query UniTeller transfers: ' . $ex->getMessage(), 'comment');
               $data = [];
            }
            if (!count($data)) {
                break;
            }
            $transactions[] = $data;
            $page++;
        }
        if (count($transactions)) {
          $transactions = array_merge(...$transactions);
        }

        $this->line('Found ' . count($transactions) . ' uniteller transactions in recent ' . $days . ' days');

        $repo = $this->em->getRepository(UniTellerTransaction::class);

        foreach ($transactions as $t) {
            if (empty($t['txNumber'])) {
                continue;
            }

            /** @var UniTellerTransaction $rt */
            $rt = $repo->findOneBy([
                'platform' => Util::$platform,
                'txnId' => $t['txNumber'],
            ]);
            if (!$rt) {
                $rt = new UniTellerTransaction();
                $rt->setPlatform(Util::$platform)
                    ->setTxnId($t['txNumber']);
            }
            $rt->setCreatedAt(empty($t['createTime']) ? null : Util::toUTC(Carbon::create(substr($t['createTime'], 4 ,4), substr($t['createTime'], 0, 2), substr($t['createTime'], 2 ,2), substr($t['createTime'], 8 ,2), substr($t['createTime'], 10 ,2), substr($t['createTime'], 12 ,2), Util::tzNewYork())))
                ->setStatus($t['status']['code'] ?? null)
            ;
            $this->em->persist($rt);
        }
        $this->em->flush();
        $this->line('Filled all the transactions');
        $this->done();
        return 0;

        // // Update details
        // $detailDays = $input->getOption('detailDays') * 1;
        // if ($detailDays > 1) {
        //   $startTime = Util::timeUTC()->subDays($detailDays)->startOfDay();
        // } else if ($detailDays  < 0) {
        //   $startTime = Util::timeUTC()->subHour();
        // } else if ($detailDays == 1) {
        //   $startTime = Util::timeUTC()->subHour(2);
        // } else {
        //   $startTime = Util::timeUTC()->subDays(30)->startOfDay();
        // }

        // $repoTransfer = $this->em->getRepository(Transfer::class);
        // $ts = $repoTransfer->createQueryBuilder('t')
        //     ->where('t.sendAt > :earliest')
        //     ->andWhere(Util::expr()->eq('t.partner', ':partner'))
        //     ->andWhere(Util::expr()->notIn('t.status', ':status'))
        //     ->setParameter('earliest', $startTime)
        //     ->setParameter('status', [Transfer::STATUS_ERROR, Transfer::STATUS_COMPLETED,  Transfer::STATUS_CANCELED])
        //     ->setParameter('partner', Transfer::PARTNER_UNITELLER)
        //     ->orderBy('t.sendAt', 'desc')
        //     ->getQuery()
        //     ->getResult();
        // $count = count($ts);
        // $this->line('Found ' . $count . ' UniTeller transactions to need to be updated the details...');

        // $errors = 0;
        // /** @var Transfer $transfer */
        // foreach ($ts as $i => $transfer) {
        //     Util::usleep(500);
        //     $this->line($i . '/' . $count . ': Updating UniTeller transaction detail for ' . $transfer->getPartnerId());

        //     /** @var UniTellerTransaction $rt */
        //     $rt = $repo->findOneBy([
        //         'txnId' => $transfer->getPartnerId(),
        //     ]);
        //     if (!$rt) {
        //         continue;
        //     }
        //     // we don't need to get UniTeller transaction detail when the transfer is cancelled or completed
        //     // if (in_array($transfer->getStatus(), [Transfer::STATUS_COMPLETED, Transfer::STATUS_CANCELED])) {
        //     //   continue;
        //     // }

        //     try {
        //       /** @var ExternalInvoke $ei */
        //       [$ei, $data] = UniTellerRemittanceService::getTransactionDetail($transfer->getSender(), $rt->getTxnId());
        //       if ($ei && $ei->isFailed()) {
        //           $this->line('Failed to get UniTeller transaction detail: ' . $ei->getError(), 'error');
        //           $errors++;
        //           if ($errors > 5) {
        //               $this->em->flush();

        //               SlackService::alert('Failed to query UniTeller transaction details: ' . $ei->getError(), [
        //                   'transfer' => $transfer->getId(),
        //               ], SlackService::GROUP_DEVS);
        //               $this->done();
        //               return;
        //           }
        //           continue;
        //       }
        //       $data = $data['transactionDetail'] ?? [];
        //       Util::updateMeta($rt, [
        //           'uniTellerDetail' => $data,
        //       ], false);
        //       $rt->setCurrency($transfer->getSendCurrency())
        //           ->setAmount(Money::normalizeAmount($data['actualTotalTxAmount'], $transfer->getSendCurrency() ))
        //           ->setTransfer($transfer);
        //       UniTellerRemittanceService::updateTransferByPayout($data, $transfer);

        //   } catch (\Exception $ex) {
        //     $this->line('Failed to update UniTeller transfer ' . $transfer->getId() . ' detail: ' . $ex->getMessage(), 'comment');
        //     continue;
        //   }
        // }
        // $this->em->flush();
        // $this->line('Updated all transactions\' details');

        // $this->done();
        return 0;
    }

}
