<?php

namespace TransferMexBundle\Command\Dev;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use Core<PERSON><PERSON>le\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use FOS\UserBundle\Util\TokenGenerator;
use FOS\UserBundle\Util\TokenGeneratorInterface;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Mailer\SwiftMailer;
use SpendrBundle\Services\UserService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\RapydAPI;
use TransferMexBundle\Services\SlackService;

class DeductDuplicatedRefundsCommand extends BaseCommand
{
    protected $slackChannel = null;

    protected function configure()
    {
        $this
            ->setName('span:dev:mex:deduct-dup-refunds')
            ->setDescription('Deduct the member balance if he has duplicated refunds caused by canceled transfers')
            ->addOption('transaction', null, InputOption::VALUE_REQUIRED, 'User card transaction id to refund. If it\'s omitted, it will find all the duplications')
            ->addOption('method', null, InputOption::VALUE_REQUIRED, 'Custom method to execute')
            ->addOption('silent', null, InputOption::VALUE_NONE, 'Only check but do not alert in the public Slack channels')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, TRUE);

        $this->slackChannel = $input->getOption('silent') ? 'hans' : null;

        $txnId = $input->getOption('transaction');
        if ($txnId) {
            $this->fix($txnId);
            $this->done();
            return 0;
        }

        $method = $input->getOption('method');
        if ($method) {
            if (method_exists($this, $method) && in_array($method, [
                'checkDupPayouts',
                'checkDupRefunds',
                'checkDupTransfers',
                'checkRawDupTransfers',

                'findLocalDupRefunds',
                'findLocalDupTransferDeductions',

                'queryStatusOfDupTransfers',
                'updateMetaPinAtKey',
                'resetTmxWebappUsers',
                'resetTmxRecipientCredentials',
                'resetFaasMemberCredentials',
                'resetSpendrAdminCredentials',
            ])) {
                $result = $this->$method();
                $this->line('Method ' . $method . ' result: ' . json_encode($result));
            } else {
                $this->line('Unknown method: ' . $method, 'error');
            }
            $this->done();
            return 0;
        }

        $local = $this->findLocalDupRefunds();
        $remote = $this->findRemoteDupRefunds();
        $transfers = $this->findRemoteDupTransfers();
        $deductions = $this->findLocalDupTransferDeductions();

        if ($local <= 0 && $remote <= 0 && $transfers <= 0 && $deductions <= 0) {
            SlackService::$channel = $this->slackChannel;
            SlackService::eyes('No new duplicated refunds or transfers.');
        }

        $this->done();
        return 0;
    }

    protected function resetFaasMemberCredentials()
    {
        $us = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where('t.name = :name')
            ->setParameter('name', Role::ROLE_FAAS_MEMBER)
            ->getQuery()
            ->getResult();
        $count = count($us);
        $this->line('Found ' . $count . ' FaaS members...');

        sleep(5);

        $changed = 0;
        /** @var User $u */
        foreach ($us as $u) {
            if ( ! Util::meta($u, 'passwordUpdatedAt')) {
                $u->setPlainPassword(Util::generatePassword());
                $changed++;
            }
            $this->em->persist($u);
        }
        $this->em->flush();
        return $changed;
    }

    protected function resetTmxRecipientCredentials()
    {
        $us = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where('t.name = :name')
            ->setParameter('name', Role::ROLE_TRANSFER_MEX_RECIPIENT)
            ->getQuery()
            ->getResult();
        $count = count($us);
        $this->line('Found ' . $count . ' TransferMex recipients...');

        sleep(5);

        $changed = 0;
        /** @var User $u */
        foreach ($us as $u) {
            if ( ! Util::meta($u, 'passwordUpdatedAt')) {
                $u->setPlainPassword(Util::generatePassword());
                $changed++;
            }
            $this->em->persist($u);
        }
        $this->em->flush();
        return $changed;
    }

    protected function resetSpendrAdminCredentials()
    {
        $us = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->where($this->expr->in('u.email', ':emails'))
            ->setParameter('emails', array_merge(
                UserService::SPENDR_BALANCE_ADMIN_EMAILS,
                UserService::SPENDR_BALANCE_ADMIN_EMAILS_FOR_TEST,
            ))
            ->getQuery()
            ->getResult();
        $count = count($us);
        $this->line('Found ' . $count . ' Spendr admins...');

        sleep(5);

        $changed = 0;
        /** @var User $u */
        foreach ($us as $u) {
            if ( ! Util::meta($u, 'passwordUpdatedAt')) {
                $u->setPlainPassword(Util::generatePassword());
                $changed++;
            }
            $this->em->persist($u);
        }
        $this->em->flush();
        return $changed;
    }

    protected function updateMetaPinKey()
    {
        $ucs = $this->em->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.accountNumber is not null')
            ->andWhere('uc.meta like :meta')
            ->setParameter('meta', '%"updatedPIN"%')
            ->getQuery()
            ->getResult();
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $value = Util::meta($uc, 'updatedPIN');
            Util::updateMeta($uc, 'updatedPIN', false);

            $last4 = $uc->getLast4();
            Util::updateMeta($uc, [
                'updatedPIN_' . $last4 => $value,
            ], false);
        }
        $this->em->flush();
    }

    protected function resetTmxWebappUsers()
    {
        Email::$disabled = false;
        Util::$platform = Platform::transferMex();

        $rs = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.cards', 'uc')
            ->where('uc.card = :card')
            ->andWhere($this->expr->like('u.meta', ':meta'))
            ->setParameter('card', CardProgramCardType::getForCardProgram(CardProgram::transferMexUSD()))
            ->setParameter('meta', '%"passwordHistory"%')
            ->getQuery()
            ->getResult();
        $count = count($rs);
        $this->line('Found ' . $count . ' users that used ever set their password...');

        sleep(10);
        $tokenGenerator = new TokenGenerator();

        /** @var User $user */
        foreach ($rs as $i => $user) {
            $this->line($i . '/' . $count . ': Sending reset password to ' . $user->getEmail());

            $user->setConfirmationToken($tokenGenerator->generateToken());
            $user->setPasswordRequestedAt(new \DateTime());
            $user->setPlainPassword(Util::generatePassword());
            Util::persist($user);

            SwiftMailer::sendResettingEmail($user, true, true);
        }
    }

    protected function updateMetaPinAtKey()
    {
        $ucs = $this->em->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.accountNumber is not null')
            ->andWhere('uc.meta like :meta')
            ->setParameter('meta', '%"updatedPinAt"%')
            ->getQuery()
            ->getResult();
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $value = Util::meta($uc, 'updatedPinAt');
            Util::updateMeta($uc, 'updatedPinAt', false);

            $last4 = $uc->getLast4();
            Util::updateMeta($uc, [
                'updatedPinAt_' . $last4 => $value,
            ], false);
        }
        $this->em->flush();
    }

    protected function findRemoteDupRefunds()
    {
        $rs = $this->em->getRepository(ExternalInvoke::class)
            ->createQueryBuilder('ei')
            ->where('ei.type = :type')
            ->andWhere($this->expr->orX(
                $this->expr->isNull('ei.status'),
                $this->expr->eq('ei.status', ':status')
            ))
            ->andWhere($this->expr->orX(
                $this->expr->like('ei.request', ':request1'),
                $this->expr->like('ei.request', ':request2')
            ))
            ->setParameter('type', 'rapid_POST_prepaidservices/rest/external-bulk-cards/nokyc/loadAmount')
            ->setParameter('request1', '%rapyd_transfer_reverse - %')
            ->setParameter('request2', '%Payout TranID:%')
            ->setParameter('status', ExternalInvoke::STATUS_SUCCESS)
            ->select('ei.id, ei.request')
            ->getQuery()
            ->getArrayResult();
        $count = count($rs);
        $this->line('Found ' . $count . ' remote loads to check...', 'info');

        $reverses = [];
        $payouts = [];
        foreach ($rs as $i => $r) {
            $req = json_decode($r['request'], true) ?? [];
            $desc = $req['txnDescription'] ?? '';
            if (!$desc) {
                continue;
            }
            $matches = [];
            preg_match('|rapyd_transfer_reverse -.* (\d+)|', $desc, $matches);
            if (!empty($matches[1])) {
                $id = $matches[1];
                $reverses[$id] = $reverses[$id] ?? [];
                $reverses[$id][] = $r['id'] . ': ' . $r['request'];
                continue;
            }
            $matches = [];
            preg_match('|Payout TranID: ?(\d+)|', $desc, $matches);
            if (!empty($matches[1])) {
                $id = $matches[1];
                $payouts[$id] = $payouts[$id] ?? [];
                $payouts[$id][] = $r['id'] . ': ' . $r['request'];
                continue;
            }
        }

        foreach ($reverses as $id => $ts) {
            if (count($ts) < 2) {
                unset($reverses[$id]);
            }
        }
        $this->line('Found ' . count($reverses) . ' remote duplicated transfer refunds...', 'info', $reverses);
        $currentCount = count($reverses);

        // ---- 18: Before ----
        // 9, 10, 13, 14, 20, 146, 164, 172, 194, 13428,
        // 13569, 13573, 13618, 13660, 21943, 21944, 23350, 23383

        // ---- 1: Added in July 2022 ----
        // 36271

        // ---- 3: Added in Aug 7th, 2022, due to Rapyd creation error. No lose. ----
        // 43542, 43316, 43545

        // ---- 51/73816: wrong manual load comment
        // ---- 90287: duplicated deductions because of Rapyd under review error, so duplicated refunds
        // ---- 149118: the transfer changed to Error twice. See https://terncommerce.slack.com/archives/C017HLZ2U2Z/p1693356080478059
        // ---- 161333: duplicated refunds. TransferMex unloaded it manually. See https://ternitup.atlassian.net/browse/SPAN-75
        // ---- 224257: failed transfer due to Rapyd maintenance error. charged twice so refunded twice.

        $knownCount = 27;
        if ($currentCount > $knownCount) {
            SlackService::$channel = $this->slackChannel;
            SlackService::alert(
                'Found ' . ($currentCount - $knownCount) . ' unknown duplicated transfer refunds. Refer to the log for more details.',
                null,
                SlackService::MENTION_HANS
            );
        }

        foreach ($payouts as $id => $ts) {
            if (count($ts) < 2) {
                unset($payouts[$id]);
            }
        }
        $this->line('Found ' . count($payouts) . ' remote duplicated prefunding payouts...', 'info', $payouts);
        $currentCount = count($payouts);
        // manual unload the money for Payout TranID:2346087
        $knownCount = 404;
        if ($currentCount > $knownCount) {
            SlackService::$channel = $this->slackChannel;
            SlackService::alert(
                'Found ' . ($currentCount - $knownCount) . ' unknown duplicated prefunding payouts. Refer to the log for more details.',
                null,
                SlackService::MENTION_HANS
            );
        }
        return $currentCount - $knownCount;
    }

    protected function findRemoteDupTransfers()
    {
        $rs = $this->em->getRepository(ExternalInvoke::class)
            ->createQueryBuilder('ei')
            ->where('ei.type = :type')
            ->andWhere($this->expr->orX(
                $this->expr->isNull('ei.status'),
                $this->expr->eq('ei.status', ':status')
            ))
            ->setParameter('type', 'rapyd_POST_/v1/payouts')
            ->setParameter('status', ExternalInvoke::STATUS_SUCCESS)
            ->select('ei.id, ei.request')
            ->getQuery()
            ->getArrayResult();
        $count = count($rs);
        $this->line('Found ' . $count . ' remote transfers to check...', 'info');

        $transfers = [];
        foreach ($rs as $i => $r) {
            $req = json_decode($r['request'], true) ?? [];
            $id = $req['merchant_reference_id'] ?? '';
            if (!$id) {
                continue;
            }
            $transfers[$id] = $transfers[$id] ?? [];
            $transfers[$id][] = $r['id'];
        }

        foreach ($transfers as $id => $ts) {
            if (count($ts) < 2) {
                unset($transfers[$id]);
            }
        }
        $this->line('Found ' . count($transfers) . ' remote duplicated transfers...', 'info', $transfers);
        $currentCount = count($transfers);
        $knownCount = 158;
        if ($currentCount > $knownCount) {
            SlackService::$channel = $this->slackChannel;
            SlackService::alert(
                'Found ' . ($currentCount - $knownCount) . ' unknown duplicated transfers. Refer to the log for more details.',
                null,
                SlackService::MENTION_HANS
            );
        }
        return $currentCount - $knownCount;
    }

    protected function findLocalDupRefunds() {
        $rs = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.productName = :productName')
            ->andWhere($this->expr->like('uct.tranDesc', ':tranDesc'))
            ->setParameter('productName', 'TransferMex')
            ->setParameter('tranDesc', '%rapyd_transfer_reverse - %')
            ->getQuery()
            ->getResult();
        $duplications = [];
        /** @var UserCardTransaction $r */
        foreach ($rs as $r) {
            $matches = [];
            preg_match('|rapyd_transfer_reverse - (\d+)|', $r->getTranDesc(), $matches);
            if (!empty($matches[1])) {
                $rid = $matches[1];
                $transfer = Transfer::find($rid);
                if ($transfer) {
                    if (Util::meta($transfer, 'fixForRapydMaintenance') ||
                        Util::meta($transfer, 'fixForKnownDuplicate')) {
                        continue;
                    }
                }
                if (Util::meta($r, 'deductedDuplication')) {
                    continue;
                }
                if (empty($duplications[$rid])) {
                    $duplications[$rid] = [];
                }
                $duplications[$rid][] = [
                    'txn_id' => $r->getId(),
                    'desc' => $r->getTranDesc(),
                    'transfer_id' => $rid,
                    'amount' => $r->getTxnAmountUSD(),
                    'member' => $r->getUserCard()->getUser()->getSignature(),
                ];
            }
        }

        $currentCount = 0;
        foreach ($duplications as $items) {
            if (count($items) <= 1) {
                continue;
            }
            $first = $items[0];
            $this->line('found duplications on member: ' . $first['member'] . ' for transfer ' . $first['transfer_id']);
            $currentCount++;
        }

        Log::debug('duplicated refunds', [
            'all' => $duplications,
            'duplications' => array_filter($duplications, function ($items) {
                return count($items) > 1;
            }),
        ]);

        // ----- Fixed -----
        // 500164182, Ignacio Jimenez Hernandez, <EMAIL> for transfer 36271
        // 500153689, SERGIO GOMEZ MENDEZ, <EMAIL> for transfer 13573

        // ----- Pending to fix -----
        // 500143873, JESUS ISRAEL VARGAS RUIZ, <EMAIL> for transfer 13569
        // 500135872, CLEMENTE ZOQUITECATL MACUIXTLE, <EMAIL> for transfer 13428

        // ----- Known transfers to fix Rapyd creation error, no lose ----
        // 43542, 43316, 43545

        // ----- Mis-operation ----
        // 51/73816 - Wrong manual load comment

        // Duplicate record for transfer 72025

        // ---- transfer 90287: duplicated deductions because of Rapyd under review error, so duplicated refunds
        // ---- transfer 149118: the transfer changed to Error twice. See https://terncommerce.slack.com/archives/C017HLZ2U2Z/p1693356080478059
        // ---- transfer 161333: duplicated refunds. TransferMex unloaded it manually. See https://ternitup.atlassian.net/browse/SPAN-75
        // ---- transfer 221694: bad manual operation. Has been fixed at that time.
        // ---- transfer 272036: bad manual operation. Should use manual unload instead
        // ---- transfer 900: wrong manual comment for 500333346

        $knownCount = 13;
        if ($currentCount > $knownCount) {
            SlackService::$channel = $this->slackChannel;
            SlackService::alert(
                'Found ' . ($currentCount - $knownCount) . ' unknown duplicated local refunds. Refer to the log for more details.',
                null,
                SlackService::MENTION_HANS
            );
        }
        return $currentCount - $knownCount;
    }

    protected function findLocalDupTransferDeductions() {
        $since = Carbon::today()->subYear();
        $repo = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.productName = :productName')
            ->andWhere('uct.tranCode = :tranCode')
            ->andWhere('uct.txnTime >= :txnTime')
            ->andWhere($this->expr->like('uct.tranDesc', ':tranDesc'))
            ->setParameter('productName', 'TransferMex')
            ->setParameter('txnTime', $since);
        $query = $repo->setParameter('tranDesc', '%rapyd_transfer - %')
            ->setParameter('tranCode', 'DEBIT');
        $count = $query->select('count(uct)')
            ->getQuery()
            ->getSingleScalarResult();
        $this->line('Found ' . $count . ' transfers');

        $duplications = [];
        $hasChanges = false;
        /** @var UserCardTransaction $r */
        foreach ($query->select('uct')->getQuery()->toIterable() as $i => $r) {
            if ($i % 1000 === 0) {
                $this->line($i . '/' . $count);
                if ($hasChanges) {
                    Util::completeDoctrineBatch();
                    $hasChanges = false;
                }
            }

            $matches = [];
            preg_match('|rapyd_transfer - (\d+)|', $r->getTranDesc(), $matches);
            if (!empty($matches[1])) {
                $rid = $matches[1];
                $transfer = Transfer::find($rid);
                if ($transfer) {
                    if (Util::meta($transfer, 'fixForRapydMaintenance') ||
                        Util::meta($transfer, 'fixForKnownDuplicate')) {
                        continue;
                    }
                }
                if (Util::meta($r, 'refundedDuplication')) {
                    continue;
                }
                $meta = Util::meta($r, 'rapidData');
                $rapidId = $meta['txnRefNum'] ?? null;
                if (empty($duplications[$rid])) {
                    $duplications[$rid] = [];
                } else if ($rapidId) {
                    foreach ($duplications[$rid] as $d) {
                        if ($d['rapid_id'] === $rapidId) {
                            $this->line('Delete the record. Simply dup local uct record ' . $r->getId() . ' for transfer ' . $rid);
                            $this->em->remove($r);
                            $hasChanges = true;
                            continue 2;
                        }
                    }
                }
                $duplications[$rid][] = [
                    'txn_id' => $r->getId(),
                    'desc' => $r->getTranDesc(),
                    'transfer_id' => $rid,
                    'amount' => $r->getTxnAmountUSD(),
                    'member' => $r->getUserCard()->getUser()->getSignature(),
                    'rapid_id' => $rapidId,
                ];
            }
        }
        if ($hasChanges) {
            Util::completeDoctrineBatch();
        }

        $currentCount = 0;
        foreach ($duplications as $rid => $items) {
            $count = count($items);
            if ($count <= 1) {
                continue;
            }

            $first = $items[0];
            if (in_array((int)$rid, [
                23336, 23392, 90036, 90287, // Were manually fixed before
            ], true)) {
                $uct = UserCardTransaction::find($first['txn_id']);
                Util::updateMeta($uct, [
                    'refundedDuplication' => time(),
                ]);
                $this->line('Mark the manual transfer fixed: ' . $first['txn_id'] . ' for transfer ' . $rid);
                continue;
            }

            if ($count === 2) {
                $cnt = (int)$repo->setParameter('tranDesc', '%rapyd_transfer_reverse - ' . $rid . ',%')
                    ->setParameter('tranCode', 'CREDIT')
                    ->select('count(uct)')
                    ->getQuery()
                    ->getSingleScalarResult();
                $cnt += (int)$repo->setParameter('tranDesc', '%rapyd_transfer_reverse - ' . $rid)
                    ->setParameter('tranCode', 'CREDIT')
                    ->select('count(uct)')
                    ->getQuery()
                    ->getSingleScalarResult();
                if ($cnt === 1) {
                    $uct = UserCardTransaction::find($first['txn_id']);
                    Util::updateMeta($uct, [
                        'refundedDuplication' => time(),
                    ]);
                    $this->line('Mark the transfer fixed: ' . $first['txn_id'] . ' for transfer ' . $rid);
                    continue;
                }
                if ($cnt === 2) {
                    $transfer = Transfer::find($first['transfer_id']);
                    if ($transfer->isError()) {
                        $uct = UserCardTransaction::find($first['txn_id']);
                        Util::updateMeta($uct, [
                            'refundedDuplication' => time(),
                        ]);
                        $this->line('Mark the error transfer fixed: ' . $first['txn_id'] . ' for transfer ' . $rid);
                        continue;
                    }
                }
            }
            $this->line('found deduction duplications on member: ' . $first['member'] . ' for transfer ' . $first['transfer_id']);
            $currentCount++;
        }

        Log::debug('duplicated transfer deductions', [
            'all' => $duplications,
            'duplications' => array_filter($duplications, function ($items) {
                return count($items) > 1;
            }),
        ]);

        // 2023: Transfer 148018: User 500165484, dup deduction $51. to be fixed.
        // 2025: Transfer 350388: User 500448845, rapid operation queue. normal.
        // 2025: Transfer 353397: User 500451902, rapid operation queue. normal.

        $knownCount = 2;
        if ($currentCount > $knownCount) {
            SlackService::$channel = $this->slackChannel;
            SlackService::alert(
                'Found ' . ($currentCount - $knownCount) . ' unknown duplicated local transfer deductions. Refer to the log for more details.',
                null,
                SlackService::MENTION_HANS
            );
        }
        return $currentCount - $knownCount;
    }

    public function fix($txnId)
    {
        /** @var UserCardTransaction $uct */
        $uct = $this->em->getRepository(UserCardTransaction::class)
            ->find($txnId);

        $uc = $uct->getUserCard();
        $this->line('member: ' . $uc->getUser()->getSignature());
        $this->line('old balance: ' . $uc->getBalance());

        RapidService::updateBalanceBy($uct->getUserCard(), -$uct->getTxnAmountUSD(),
            'rapyd_transfer_deduct_reverse', $uct->getId(), 'Deduct duplicated transfer reverses');

        $this->line('new balance: ' . $uc->getBalance());
        Util::updateMeta($uct, [
            'deductedDuplication' => true,
        ]);

        return null;
    }

    protected function checkDupTransfers()
    {
        $all = json_decode(file_get_contents(__DIR__ . '/../../../../../db/json/dup_transfers_all.json'), true);
        $found = json_decode(file_get_contents(__DIR__ . '/../../../../../db/json/dup_transfers_found.json'), true);
        $found2 = [];
        foreach ($found as $item) {
            $found2[$item['Transfer ID']] = $item;
        }
        foreach ($all as $key => $eis) {
            if (empty($found2[$key])) {
                $this->line('Unknown duplicated transfers: ' . $key);
            }
        }

        // Result:
        // Only found one more (transfer 26452) which was tested by Hans on 2022-05-25 for Rapyd's idempotency support
    }

    protected function checkDupRefunds()
    {
        $all = json_decode(file_get_contents(__DIR__ . '/../../../../../db/json/dup_refunds_all.json'), true);
        $ids = [];
        $transfers = [];
        foreach ($all as $tid => $eis) {
            $transfers[] = $tid;
            foreach ($eis as $ei) {
                $parts = explode(': ', $ei);
                $ids[] = $parts[0];
            }
        }
        $this->line('select * from external_invoke where id in (' . implode(', ', $ids) . ')');
        $this->line('select * from transfer where id in (' . implode(', ', $transfers) . ')');

        // Result:
        // Most the duplicated refunds have been corrected, except the items in the Rapid operation queue
    }

    protected function checkDupPayouts()
    {
        $all = json_decode(file_get_contents(__DIR__ . '/../../../../../db/json/dup_payouts_all.json'), true);
        $ids = [];
        foreach ($all as $tid => $eis) {
            foreach ($eis as $ei) {
                $parts = explode(': ', $ei);
                $ids[] = $parts[0];
            }
        }
        $this->line('select * from external_invoke where id in (' . implode(', ', $ids) . ')');

        // Result:
        // All the duplicated payouts have been corrected before. Currently, all are from the accident on 2021-12-23
    }

    protected function queryStatusOfDupTransfers()
    {
        $duplicated = Util::s2j(file_get_contents(__DIR__ . '/../../../../../db/json/dup_transfers.json') ?: '[]');
        $count = count($duplicated);
        $api = new RapydAPI();
        $completed = [];
        foreach ($duplicated as $i => $item) {
            $tid = $item['Transfer ID'];
            $this->line($i . '/' . $count . ': Checking ' . $tid);

            $payouts = $item['All Payouts'] ?? [];
            foreach ($payouts as $pid => $status) {
                /** @var ExternalInvoke $ei */
                [$ei, $data] = $api->getPayout($pid);
                if ($ei && $ei->isFailed()) {
                    $this->line(' ===== Payout ' . $pid . ' error: ' . $ei->getErrorSignature(), 'error');
                } else {
                    $this->line(' ===== Payout ' . $pid . ' status: ' . $data['status'], $data['status'] === 'Completed' ? 'info' : 'error');
                    if ($data['status'] === 'Completed') {
                        $completed[] = [
                            'id' => $pid,
                            'status' => $data['status'],
                            'sender_amount' => $data['sender_amount'],
                            'merchant_reference_id' => $tid,
                        ];
                    }
                }
                $payouts[$pid][] = $data['status'];
                $payouts[$pid][] = $data['sender_amount'];
            }
            $duplicated[$i]['All Payouts'] = $payouts;
        }
        return $completed;
    }

    protected function checkRawDupTransfers()
    {
        Util::$platform = Platform::transferMex();
        $transfers = file_get_contents(__DIR__ . '/../../../../../db/json/dup_transfers.txt');
        $transfers = array_filter(explode("\n", $transfers));
        $duplicated = [];
        $all = [];
        $total = 0;
        foreach ($transfers as $transfer) {
            $t = json_decode($transfer, true);
            $id = $t['transfer'];
            if (empty($all[$id])) {
                $all[$id] = $t;
            } else {
                $t['cent'] = Money::normalizeAmountFromUsdFormat($t['amount']);
                $total += $t['cent'];
                $duplicated[$id] = $t;
            }
        }
        $result = [];
        $fixed = 0;
        $count = count($duplicated);
        foreach ($duplicated as $i => $item) {
            $t = Transfer::find($item['transfer']);
            $tid = $t->getId();
            $sender = $t->getSender();
            $re = [
                'Transfer ID' => $tid,
                'Amount' => str_replace('USD ', '', $item['amount']),
                'Status' => $t->getStatus(),
                'Type' => $item['type'],
                'Method' => $item['method'],
                'Rapyd Payout ID' => $t->getPartnerId(),
                'Employer' => $item['employer'],
                'Sender User ID' => $sender->getId(),
                'Sender User Name' => $sender->getFullName(),
                'Sender User Email' => $sender->getEmail(),
                'Recipient Name' => $t->getRecipient()->getFullName(),
            ];
            $result[] = $re;

//            try {
//                $balance = $uc->getBalance(true);
//            } catch (\Exception $ex) {
//                $balance = $uc->getBalance();
//            }
//            $cent = $item['cent'];
//            $reverse = $balance >= $cent ? $cent : $balance;
//            $prefix = $i . '/' . $count . ': ';
//            if ($reverse > 0) {
//                try {
//                    $api = RapidAPI::getForUserCard($uc);
//                    ExternalInvoke::host($api->reverseFunds($uc, $reverse, 'Fix duplicated transfer ' . $t->getId()));
//                    $this->line($prefix . 'Reversed funds ' . $cent . ' for transfer ' . $t->getId());
//                    $result[$tid] = ['success' => true, 'transfer' => $tid, 'amount' => $cent, 'balance' => $balance, 'reversed' => $reverse];
//                    $fixed += $reverse;
//                } catch (\Exception $ex) {
//                    $this->line($prefix . 'Failed to reverse ' . $cent . ' for transfer ' . $t->getId() . ': ' . $ex->getMessage(), 'error');
//                    $result[$tid] = ['success' => false, 'transfer' => $tid, 'amount' => $cent, 'balance' => $balance, 'error' => $ex->getMessage()];
//                }
//            } else {
//                $this->line($prefix . 'Balance ' . $balance . ' is not enough for reversing transfer ' . $t->getId() . ' with amount ' . $cent, 'error');
//                $result[$tid] = ['success' => false, 'transfer' => $tid, 'amount' => $cent, 'balance' => $balance, 'error' => 'Balance is not enough'];
//            }
        }
        $this->line(json_encode(compact('total', 'fixed', 'count', 'result')), 'info');
    }
}
