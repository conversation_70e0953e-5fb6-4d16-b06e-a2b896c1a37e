<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Transfer;
use CoreBundle\Utils\Util;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Core<PERSON><PERSON>le\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Utils\Log;
use SalexUserBundle\Entity\User;
use TransferMexBundle\TransferMexBundle;
use Symfony\Component\Console\Input\InputOption;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\SlackService;

class ChargeMonthFeeCommand extends BaseCommand
{
    public $memberIds = [];

    protected function configure()
    {
        $this
            ->setName('span:mex:charge-month-fee')
            ->setDescription("Check the member and charge for month fees")
            ->addOption('memberId', null, InputOption::VALUE_OPTIONAL, 'Member ID for test')
            ->addOption('charge', null, InputOption::VALUE_NONE, 'Whether to charge')
            ->addOption('alert', null, InputOption::VALUE_NONE, 'Whether to send alerts to Slack')
            ->addOption('isDebug', null, InputOption::VALUE_NONE, 'Whether to log debug info')

        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, true);
        $this->line('Start charge month fee');

        $charge = $this->input->getOption('charge');
        $alert = $this->input->getOption('alert');
        $memberId = $this->input->getOption('memberId');
        $isDebug = $this->input->getOption('isDebug');
        $oneYearAgo = Carbon::now()->subYear();
        $this->line('Query date: ' . $oneYearAgo->format('Y-m-d H:i:s'));

        $usersWithoutTransfers = $this->findUsersWithoutTransfers($oneYearAgo);
        $this->line('Found ' . count($usersWithoutTransfers) . ' users need to charge fee');
        $success = 0;
        $count = count($usersWithoutTransfers);
        $this->memberIds = [];
        foreach ($usersWithoutTransfers as $i => $user) {
          if ($memberId && $memberId != $user['id']) {
            continue;
          }
          if ($this->chargeFee($user['id'], $charge)) {
            $success++;
            usleep(200);
          }
          if ($i % 100 === 0) {
            $this->line('Processed ' . $i . '/' . $count . ' records...');
          }
        }
        $this->line('Successful: ' . $success);
        $this->line('End charge month fee');
        if ($alert) {
            SlackService::tada('Finished charging the monthly fee on ' . $success . ' users...');
        }
        if ($isDebug) {
           Log::debug('Members list:', $this->memberIds);
        }
        $this->done();
        return 0;
    }

    private function chargeFee($userId, $charge = false)
    {
        $monthlyFee = Config::get('transfermex_month_fee', 200);
        $user = Util::em()->getRepository(User::class)->find($userId);
        $uc = $user->getCurrentPlatformCard();
        if (!$uc) {
            Log::debug("Card not found for user: {$userId}. Skipped.");
            return;
        }
        $an = $uc ? $uc->getAccountNumber() : null;
        if (!$an) {
             Log::debug("Card number not found for user: {$userId}. Skipped.");
            return;
        }
        $config = $user->ensureConfig();
        if (!$config->getBotmUserId()) {
            Log::debug("BOTM user found for user: {$userId}. Skipped.");
            return;
        }
        $balance = $uc->getBalance();
        if ($balance <= 0) {
            Log::debug("Insufficient balance for user: {$userId}. Skipped.");
            return;
        }
        $fees = $balance && ($balance - $monthlyFee) > 0 ? $monthlyFee : $balance;

        $this->memberIds[] = $userId;
        try {
            if ($charge) {
                // RapidService::updateBalanceBy($uc, -$fees, 'transfermex_month_fee', $user->getId(), 'Charging month fee', $user);

                UserFeeHistory::create($user, $fees, FeeGlobalName::MONTHLY_FEE, $user, 'Charging month fee', Platform::transferMex());
                Log::debug('Charging month fee ' . $fees . ' to user:  ' . $userId);
                Util::flush();
                return true;
            }
        } catch(\Exception $e) {
            Log::debug('Exception occurred when charging month fee ' . $fees . ' to user:  ' . $userId);

            Log::error($e->getMessage());
            return false;
        }
        Log::debug('Failed to charging month fee ' . $fees . ' to user:  ' . $userId);

        return false;
    }
    private function findUsersWithoutTransfers($cutoffDate)
    {
        $em = Util::em();
        $expr = Util::expr();
        $cp = CardProgram::usunlocked();
        $feeName = FeeGlobalName::MONTHLY_FEE;
        $date = Carbon::now()->subMonthNoOverflow()->endOfDay();
        $subQuery = $em->getRepository(UserFeeHistory::class)
              ->createQueryBuilder('ufh')
              ->join('ufh.user', 'uf')
              ->where('ufh.feeName = :feeName')
              ->andWhere('ufh.time >= :feeAt')
              ->andWhere($expr->like('ufh.meta', ':transferMex'))
              ->select('uf.id')
              ->getDQL()
          ;
        $qb = $em->getRepository(UserCard::class)->createQueryBuilder('uc')
                  ->join('uc.user', 'u')
                  ->join('u.teams', 'r')
                  ->join('u.config', 'cf')
                  ->leftJoin(Transfer::class, 't', 'WITH', 't.sender = u and t.sendAt >= :cutoffDate')
                  ->leftJoin(UserCardTransaction::class, 'uct', 'WITH', 'uct.userCard = uc and uct.txnTime >= :txnTime')
                  ->andWhere('uct.id IS NULL')
                  ->andWhere('t.id IS NULL')
                  ->andWhere($expr->neq('u.id', $expr->all($subQuery)))
                  ->andWhere(Util::expr()->in('r.name', ':roles'))
                  ->andWhere(Util::expr()->isNotNull('cf.botmUserId'))
                  ->andWhere(Util::expr()->lte('u.createdAt', ':createdAt'))
                  ->setParameter('createdAt', Util::timeUTC($cutoffDate))
                  ->setParameter('txnTime', Util::timeUTC($cutoffDate))
                  ->setParameter('transferMex', '%transferMex%')
                  ->setParameter('cutoffDate', Util::timeUTC($cutoffDate))
                  ->setParameter('roles', TransferMexBundle::getMemberRoles())
                  ->setParameter('feeName', $feeName)
                  ->setParameter('feeAt', $date);

        $qb->andWhere('u.deletedAt IS NULL');

        return $qb->select('u.id')->getQuery()->getResult();
    }
}
