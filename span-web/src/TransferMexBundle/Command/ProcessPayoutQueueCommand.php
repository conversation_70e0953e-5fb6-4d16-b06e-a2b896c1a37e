<?php

namespace TransferMexBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Transfer;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TransferMexBundle\Controller\Mobile\TransferController;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\TransferMexBundle;
use TransferMexBundle\Services\TransferService;
use TransferMexBundle\Services\UniTellerRemittanceService;

class ProcessPayoutQueueCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:mex:process-payout-queue')
            ->setDescription('Run the queued(previously failed or disabled instance transfer) Rapyd payouts')
            ->addOption('payout', null, InputOption::VALUE_REQUIRED, 'Payout to process. If not specified, it will process all queued payouts.')
            ->addOption('total', null, InputOption::VALUE_OPTIONAL, 'Maximum payouts to process', 1000)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::transferMex();
        $this->prepare($input, $output, true);
        SlackService::prepareForPlatform(Util::$platform);

        Util::usleep(50, 1000);
        // store the API error count
        $errorCount = 0;
        $singleKey = 'transfer_mex_payout_queue_single';
        if (Data::lockedHas($singleKey)) {
            $this->done('Skip duplication call to process payout queue.');
            return 0;
        }
        Data::set($singleKey, true, true);
        $enabled = TransferMexBundle::isTransferEnabled();
        $enabledCashPickup = TransferMexBundle::isCashPickupTransferEnabled();
        $enableIntermex = TransferMexBundle::isIntermexEnabled();
        $instantEnabled = TransferMexBundle::isTransferInstantEnabled();
        $cashPickupEnabled = TransferMexBundle::isCashPickTransferInstantEnabled();
        $intermexInstantEnabled = TransferMexBundle::isIntermexTransferInstantEnabled();

        if (TransferMexBundle::checkMaintenance('rapyd') && TransferMexBundle::checkMaintenance('uniteller')) {
            SlackService::warning('Skip processing the payout queue since the Rapyd and UniTeller is under maintenance.');
            Data::del($singleKey);
            $this->done();
            return 0;
        }

        $payoutId = $input->getOption('payout');
        if ( ! $payoutId ) {
            if ((! $enabled || ! $instantEnabled) && (!$enabledCashPickup || !$cashPickupEnabled) && (!$enableIntermex || !$intermexInstantEnabled)) {
                SlackService::warning('Skip processing the payout queue since (instant & cash pickup) transfer is disabled.');
                Data::del($singleKey);
                $this->done();
                return 0;
            }
        }

        $statuses = [
            Transfer::STATUS_QUEUED,
            Transfer::STATUS_PROCESSING,
        ];
        $partnerList = [];
        if ( ! $payoutId ) {
          if (TransferMexBundle::checkMaintenance(Transfer::PARTNER_RAPYD)) {
            $this->line('Skip processing the payout queue since Rapyd is under maintenance.');
          } else {
            if ($instantEnabled && $enabled) {
              $partnerList = array_merge($partnerList, [Transfer::PARTNER_RAPYD]);
            } else {
              $this->line('Skip processing bank payout since Bank transfer is disabled');
            }
          }
          if (TransferMexBundle::checkMaintenance(Transfer::PARTNER_UNITELLER)) {
            $this->line('Skip processing the payout queue since UniTeller is under maintenance.');
          } else {
            if ($cashPickupEnabled && $enabledCashPickup) {
              $partnerList = array_merge($partnerList, [Transfer::PARTNER_UNITELLER]);
            } else {
              $this->line('Skip processing cash pickup payout since cash pickup transfer is disabled');
            }
          }
          if ($enableIntermex && $intermexInstantEnabled) {
            $partnerList = array_merge($partnerList, [Transfer::PARTNER_INTERMEX]);
          } else {
            $this->line('Skip processing intermex payout since intermex transfer is disabled');
          }
        } else {
          $partnerList = [Transfer::PARTNER_RAPYD, Transfer::PARTNER_UNITELLER, Transfer::PARTNER_INTERMEX];
        }
        $q = $this->em->getRepository(Transfer::class)
            ->createQueryBuilder('t')
            ->where($this->expr->in('t.partner', ':partner'))
            ->andWhere($this->expr->in('t.status', ':statuses'))
            ->setParameter('partner', $partnerList)
            ->setParameter('statuses', $statuses)
            ->orderBy('t.id', 'ASC');

        $force = false;
        if ($payoutId) {
            $force = true;
            $q->andWhere('t.id = :id')
                ->setParameter('id', $payoutId);
        }

        $rs = $q->setMaxResults((int)($input->getOption('total') ?? '1000'))
            ->select('t.id')
            ->getQuery()
            ->getArrayResult();

        if ( ! $rs) {
            SlackService::info('No queued transfers need to be processed...');
            Data::del($singleKey);
            $this->done();
            return 0;
        }

        // Check partner balances before the queue
        $partners = [];
        $utLowBalance = UniTellerRemittanceService::getLowestBalanceForRegularTransfer();
        foreach (Transfer::PARTNERS as $pn) {
            $value = true;
            if ($pn === Transfer::PARTNER_RAPYD) {
                try {
                    $balance = RapydDisburseService::getWalletBalance();
                    if ($balance < RapydDisburseService::LOW_BALANCE_FOR_INSTANT_TRANSFER) {
                        $value = false;
                    }
                } catch (\Throwable $e) {
                    $this->line('Failed to get Rapyd balance: ' . Util::exceptionBrief($e));
                    $value = false;
                }
            } else if ($pn === Transfer::PARTNER_UNITELLER) {
                $balance = TransferService::getUniTellerBalance();
                if ($balance < $utLowBalance) {
                    $value = false;
                }
            }
            if (!$value) {
                $this->line('Skip processing partner due to insufficient balance: ' . $pn);
            }
            $partners[$pn] = $value;
        }
        $hasBalancedPartner = false;
        foreach ($rs as $t) {
            /** @var Transfer $r */
            $r = Transfer::find($t['id']);
            $pn = $r->getPartner();
            if (isset($partners[$pn])) {
                $hasBalancedPartner = true;
                break;
            }
        }
        if ( ! $hasBalancedPartner) {
            SlackService::info('No queued transfers have enough balance to processed');
            Data::del($singleKey);
            $this->done();
            return 0;
        }

        $total = $q->setMaxResults((int)($input->getOption('total') ?? '1000'))
                  ->select('sum(t.sendAmount)')
                  ->getQuery()
                  ->getSingleScalarResult();
        $hasTransferred = 0;
        Log::debug('There are ' . count($rs) . ' transfers with total ' . Money::formatWhen($total). ' waiting to be transferred');
        $count = count($rs);
        SlackService::wave('Start processing the queued transfers...', [
            'batch_size' => $count,
        ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
        $date =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT, 'America/New_York');
        $dailyLimit = Config::get('max_transfer_amount_limit_daily', 250000);
        $monthlyLimit = Config::get('max_transfer_amount_limit_monthly', 1000000);
        $onceAmountLimit = TransferMexBundle::getMaxTransferAmount();

        $results = [];
        foreach ($rs as $i => $t) {
            $rid = $t['id'];
            /** @var Transfer $r */
            $r = Transfer::find($rid);
            $partner = $r->getPartner();
            $prefix = ($i + 1) . '/' . $count . ': ' . $rid . ' ---- ';

            if (empty($partners[$partner])) {
                $this->line($prefix . 'skip for partner ' . $partner);
                continue;
            }

            if ( ! in_array($r->getStatus(), $statuses)) {
                $this->line('Skip processing payout when status changes: ' . $rid);
                $results['status_changed'] = $results['status_changed'] ?? 0;
                $results['status_changed']++;
                continue;
            }

            $key = 'process_payout_queue_' . $rid;
            if (Data::lockedHas($key)) {
                $this->line('Skip processing payout queue again: ' . $rid);
                $results['skip_recent'] = $results['skip_recent'] ?? 0;
                $results['skip_recent']++;
                continue;
            }
            Data::set($key, true, true, 3600);

            if ($partner === Transfer::PARTNER_RAPYD) {
                try {
                    $balance = RapydDisburseService::getWalletBalance();
                } catch (\Throwable $e) {
                    $partners[$partner] = false;
                    SlackService::alert('Failed to fetch the latest Rapyd balance: ' . $e->getMessage(), [],
                        SlackService::GROUP_TRANSFER_MEX_INTERNAL);
                    continue;
                }
                if (
                    $balance < RapydDisburseService::LOW_BALANCE_FOR_INSTANT_TRANSFER ||
                    $balance - $r->getSendAmount() < RapydDisburseService::MIN_BALANCE_FOR_INSTANT_TRANSFER
                ) {
                    $partners[$partner] = false;
                    SlackService::alert('Rapyd balance is too low. Skip the transfer from the payout queue.', [
                        'Rapyd Balance' => Money::usdFormat($balance),
                        'Transfer Id'   => $r->getId(),
                    ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
                    continue;
                }
            }

            if ($partner === Transfer::PARTNER_UNITELLER) {
                $balance = TransferService::getUniTellerBalance();
                if ($balance < $utLowBalance) {
                    $partners[$partner] = false;
                    SlackService::alert('UniTeller balance is too low. Skip the transfer from the payout queue.', [
                        'UniTeller Balance' => Money::usdFormat($balance),
                        'Transfer Id'       => $r->getId(),
                    ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
                    continue;
                }
            }
            if ($hasTransferred > $total) {
              SlackService::alert('The amount transferred is greater than the total amount.',
              [
                  'Total' => Money::usdFormat($total),
                  'Has transferred' => Money::usdFormat($hasTransferred),
              ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
              break;
            }
            $dailyAmountKey = 'daily_transfer_amount_' . $r->getSender()->getId() . '_' . $date;
            $dailyAmount = Data::get($dailyAmountKey) ?? 0;
            if (($r->getSendAmount() + $dailyAmount) > $dailyLimit) {
              $message = "This transfer triggers the user's maximum daily limit!";
              SlackService::alert($message,
              [
                  'Transfer' => $r->getId(),
                  'Send Amount' => Money::usdFormat($r->getSendAmount()),
                  'Daily Limit' => Money::usdFormat($dailyLimit),
              ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
              continue;
            }
            $month =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT_MONTH, 'America/New_York');
            $monthlyAmountKey = 'monthly_transfer_amount_' . $r->getSender()->getId() . '_' . $month;
            $monthlyAmount = Data::get($monthlyAmountKey) ?? 0;
            if (($r->getSendAmount() + $monthlyAmount) > $monthlyLimit) {
              $message = "This transfer triggers the user's maximum monthly limit!";
              SlackService::alert($message,
              [
                  'Transfer' => $r->getId(),
                  'Send Amount' => Money::usdFormat($r->getSendAmount()),
                  'Monthly Limit' => Money::usdFormat($monthlyLimit),
              ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
              continue;
            }

            if ($r->getSendAmount() > $onceAmountLimit) {
              $message = "This transfer triggers the maximum of a single transfer!";
              SlackService::alert($message,
              [
                  'Transfer' => $r->getId(),
                  'Send Amount' => Money::usdFormat($r->getSendAmount()),
                  'Once Limit' => Money::usdFormat($onceAmountLimit),
              ], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
              continue;
            }

            $this->line($prefix . 'processing transfer with status ' . $r->getStatus());
            try {
                $ret = TransferController::doConfirmTransfer($r, $force);
            } catch (\Throwable $e) {
                $errorCount++;
                $this->line('Failed to do confirm transfer ' . $rid . ' when processing queue: ' . $e->getMessage(), 'error');
                $ret = 'exception';
            }
            $ret = $ret ?: 'unknown';
            $this->line($prefix . '--------- result: ' . $ret . ' and status: ' . $r->getStatus());
            $hasTransferred += $r->getSendAmount();
            $results[$ret] = $results[$ret] ?? 0;
            $results[$ret]++;

            if ($ret !== 'exception' && $r->isError()) {
                $errorCount++;
                $this->line('Failed to do confirm transfer ' . $rid . ' due to error ' . $r->getError(), 'error');
            }

            if ($errorCount > 10) {
                SlackService::alert('Too many failures. Exiting from the payout queue. Please check the API maintenance time both Rapyd and UniTeller.',
                    [], SlackService::GROUP_TRANSFER_MEX_INTERNAL);
                break;
            }

            Util::usleep(400, 700);
        }

        Service::sendAsync('/t/cron/mex/rapyd/monitor-instant-transfer/alert');

        $created = $results['created'] ?? 0;
        SlackService::wave('Completed processing *' . $created . '* queued transfers', $results,
            SlackService::GROUP_TRANSFER_MEX_INTERNAL);

        Data::del($singleKey);
        $this->done();
        return 0;
    }
}
