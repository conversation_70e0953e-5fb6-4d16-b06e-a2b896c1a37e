<?php

namespace TransferMexBundle\Controller\Admin\Reports;

use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Controller\Admin\BaseController;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\RapidService;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserFeeHistory;

class InactiveFeeReportController extends BaseController
{
    use UserTransferMexTrait;

    public function __construct()
    {
        parent::__construct();

        $this->authSuperAdmin();
    }

    /**
     * @Route("/admin/mex/reports/inactive_fee/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {

        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $result['quick'] = [
            'count' => $result['count'],
            'total' => $this->query($request)->distinct()->select('sum(ufh.amount)')->getQuery()->getSingleScalarResult(),
            'members' => $this->query($request)->select('count(distinct ufh.user)')->getQuery()->getSingleScalarResult(),
        ];

        return new SuccessResponse($result);
    }

    protected function query(Request $request, $type = 'default')
    {
        $query = $this->em->getRepository(UserFeeHistory::class)
            ->createQueryBuilder('ufh')
            ->join('ufh.user', 'u')
            ->join('u.teams', 't')
            ->join('u.userGroups', 'g')
            ->join('g.adminConfigs', 'ac')
            ->join('u.config', 'config')
            ->join('ac.user', 'e')
            ->where(Util::expr()->eq('ufh.feeName', ':feeName'))
            ->andWhere(Util::expr()->like('ufh.meta', ':meta'))
            ->setParameter('feeName', FeeGlobalName::MONTHLY_FEE)
            ->setParameter('meta', '%transferMex%')
        ;

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'ufh', 'count(distinct ufh)');
        $params->distinct = true;
        $params->fixRangeMoney = true;
        $params->orderBy = [
            'ufh.id' => 'desc',
        ];
        return $params;
    }

    /**
     * @param UserFeeHistory $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        return [
            'Member ID' => $entity->getUser()->getId(),
            'First Name'   =>  $entity->getUser()->getFirstName(),
            'Last Name'    =>  $entity->getUser()->getLastName(),
            'Fee Amount' => Money::usdFormat($entity->getAmount()),
            'Fee Month' => Util::formatDateTime($entity->getTime(), Util::DATE_FORMAT_MONTH_DASH),
        ];
    }

    /**
     * @Route("/admin/mex/reports/inactive_fee/filters")
     * @return SuccessResponse
     */
    public function filtersAction() {
        return new SuccessResponse([
            'employers' => $this->listForSelection($this->getCurrentEmployerRole()),
        ]);
    }

    /**
     * @Route("/admin/mex/reports/inactive_fee/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $title = 'TransferMex Inactive Fee Report';

        return $this->commonExport($request,  $title , [
            'Member ID'    => 20,
            'First Name'   => 20,
            'Last Name'    => 20,
            'Fee Amount'   => 20,
            'Fee Month'    => 40
        ]);
    }
}
