<?php


namespace TransferMexBundle\Controller\Admin;


use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UniTellerTransaction;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Services\RapydAPI;

class UniTellerTransactionsController extends BaseController
{
    /**
     * UniTellerTransactionsController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authSuperAdmin();
    }

    /**
     * @Route("/admin/mex/uniteller-transactions/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        return $this->em->getRepository(UniTellerTransaction::class)
            ->createQueryBuilder('t')
            ->andWhere('t.platform = :platform')
            ->setParameter('platform', Platform::transferMex())
            ;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 't', 'count(distinct t)');
        $params->distinct = true;
        $params->orderBy = [
            't.createdAt' => 'desc',
        ];
        $params->searchFields = [
            't.id',
            't.txnId',
            't.type',
            't.status',
            't.reason',
        ];
        return $params;
    }

    /**
     * @param UniTellerTransaction $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $transfer = $entity->getTransfer();
        return [
            'Date' => Util::formatDateTime($entity->getCreatedAt(), Util::DATE_TIME_FORMAT, $this->tz),
            'Transaction ID' => $entity->getTxnId(),
            'Amount' => Money::format($entity->getAmount(), $entity->getCurrency() ?? 'USD', false),
            // 'Type' => Util::humanize($entity->getType()),
            'Status' => Util::humanize($entity->getStatus()),
            // 'Reason' => $entity->getReason(),
            'Transfer' => $transfer ? $transfer->getId() : null,
            'Transfer Amount' => $transfer ? Money::formatUSD($transfer->getSendAmount(), false) : null,
            'UniTeller Fee' => $transfer ? Money::formatUSD($transfer->getCost(), false) : null,
            'Revenue' => $transfer ? Money::formatUSD($transfer->getSendAmount() - $transfer->getCost() - $entity->getAmount(), false) : null,
        ];
    }

    /**
     * @Route("/admin/mex/uniteller-transactions/filters")
     * @return SuccessResponse
     */
    public function filtersAction() {
        return new SuccessResponse([]);
    }

    /**
     * @Route("/admin/mex/uniteller-transactions/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
        $fields = [
            'Date' => 20,
            'Transaction ID' => 20,
            'Amount' => 12,
            // 'Type' => 24,
            'Status' => 12,
            // 'Reason' => 20,
            'Transfer' => 20,
            'Transfer Amount' => 18,
            'UniTeller Fee' => 16,
            'Revenue' => 16
        ];
        return $this->commonExport($request, 'UniTeller Transactions', $fields);
    }
}
