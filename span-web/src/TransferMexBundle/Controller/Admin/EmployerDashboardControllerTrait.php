<?php

namespace TransferMexBundle\Controller\Admin;

use Carbon\Carbon;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\Role;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\AbstractQuery;
use Symfony\Component\HttpFoundation\Request;
use TransferMexBundle\Entity\EmployerPayout;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use CoreBundle\Utils\Log;
use TransferMexBundle\TransferMexBundle;
use CoreBundle\Utils\Money;
use FaasBundle\Services\BOTM\BotmAPI;
use TransferMexBundle\Entity\EmployerTransactions;

trait EmployerDashboardControllerTrait
{
	public function traitGetProgramBalanceChart(Request $request)
	{
    if ($this->user->inTeam(Role::ROLE_TRANSFER_MEX_EMPLOYER)) {
      $user = $this->user;
    } else {
      $user = $this->user->getAdminGroup() ? $this->user->getAdminGroup()->getPrimaryAdmin() : $this->user;
    }
    // <EMAIL> start 2021-08-20
    if ($user->getId() === 500125260) {
      $this->earliest = Carbon::create(2021, 8, 1, 0, 0, 0, $this->tz);
    }

    $period = $request->get('period');
    switch ($period) {
      case 'all':
      case 'today':
      case 'custom_range':
        $this->format = Util::DATE_FORMAT_SEARCH;
        $date = Carbon::now($this->tz)->startOfDay();
        break;
      case 'week':
        $this->format = Util::DATE_FORMAT_WEEK_DASH;
        $date = Carbon::now($this->tz)->startOfDay();
        break;
      case 'year':
        $this->format = Util::DATE_FORMAT_YEAR_DASH;
        $date = Carbon::now($this->tz)->startOfYear();
                $this->earliest = Carbon::create(2021, 1, 1, 0, 0, 0, $this->tz);
        break;
      default:
        $this->format = Util::DATE_FORMAT_MONTH_DASH;
        $date = Carbon::now($this->tz)->startOfMonth();
        break;
    }

    $key     = [];
    $res     = [];
    $data    = $this->getTransactionData($request);
    // $group   = $this->user->getAdminGroup();
    if ($this->group->isBOTM()) {
        $oldData = $this->getOldDataForBOTMEmployer($request);
    }
		while ($date->gte($this->earliest)) {
			$month = Util::formatDateTime($date, $this->format, $this->tz);
			$monthNum = Util::formatDateTime($date, 'm', $this->tz);
			$yearNum = Util::formatDateTime($date, 'Y', $this->tz);
			$weekNum = Util::formatDateTime($date, 'W', $this->tz);

			if($this->format === Util::DATE_FORMAT_WEEK_DASH && ($monthNum == 12 || $monthNum == 1)) {
				if($weekNum == 1 && $monthNum == '12') {
					$month = ($yearNum + 1) . '-' . $weekNum;
				}
				if($weekNum >= 52 && $monthNum == '01') {
					$month = ($yearNum - 1) . '-' . $weekNum;
				}
			}

			if(!in_array($month, $key)) {
				$key[] = $month;
			}

			$res['chartCount']['deposits'][$month] = (isset($data['count']['deposits'][$month]) ? $data['count']['deposits'][$month] : 0);
			$res['chartCount']['payouts'][$month] = (isset($data['count']['payouts'][$month]) ? $data['count']['payouts'][$month] : 0);
			$res['chartCount']['adjustment'][$month] = (isset($data['count']['adjustment'][$month]) ? $data['count']['adjustment'][$month] : 0);
			$res['chartData']['deposits'][$month] = (isset($data['data']['deposits'][$month]) ? $data['data']['deposits'][$month] : 0);
			$res['chartData']['payouts'][$month] = (isset($data['data']['payouts'][$month]) ? -$data['data']['payouts'][$month] : 0);
			$res['chartData']['adjustment'][$month] = (isset($data['data']['adjustment'][$month]) ? $data['data']['adjustment'][$month] : 0);

            if (!$this->group->isBOTM() && Util::isLive() && $this->platform->isTransferMex() && $user->getId() === ********* ) {
                // add the over paid 1124.9 for 2021-12-23 issue for FH
                if ($month === '2021-12-23' || $month === '2021-51' || $month === '2021-12' || $month === '2021') {
                  $res['chartData']['payouts'][$month] -= 112449;
                }
            }

            if (!$this->group->isBOTM() && Util::isLive() && $this->platform->isTransferMex() && $user->getId() === 500125260 ) {
                // load this money from base by Indigo
                if ($month === '2023-11-04' || $month === '2023-44' || $month === '2023-11' || $month === '2023') {
                  $res['chartData']['payouts'][$month] += 1500;
                }
            }
            if ($this->group->isBOTM()) {
            $res['chartCount']['deposits'][$month] += (isset($oldData['count']['deposits'][$month]) ? $oldData['count']['deposits'][$month] : 0);
            $res['chartCount']['payouts'][$month] += (isset($oldData['count']['payouts'][$month]) ? $oldData['count']['payouts'][$month] : 0);
            $res['chartCount']['adjustment'][$month] += (isset($oldData['count']['adjustment'][$month]) ? $oldData['count']['adjustment'][$month] : 0);
            $res['chartData']['deposits'][$month] += (isset($oldData['data']['deposits'][$month]) ? $oldData['data']['deposits'][$month] : 0);
            $res['chartData']['payouts'][$month] += (isset($oldData['data']['payouts'][$month]) ? $oldData['data']['payouts'][$month] : 0);
            $res['chartData']['adjustment'][$month] += (isset($oldData['data']['adjustment'][$month]) ? $oldData['data']['adjustment'][$month] : 0);
            }
            $res['chartData']['balance'][$month] = $res['chartData']['deposits'][$month]
				+ $res['chartData']['payouts'][$month] + $res['chartData']['adjustment'][$month];

			$res['chartAvg']['deposits'][$month] = $res['chartCount']['deposits'][$month] ? round($res['chartData']['deposits'][$month] / $res['chartCount']['deposits'][$month], 2) : 0;
			$res['chartAvg']['payouts'][$month] = $res['chartCount']['payouts'][$month] ? round($res['chartData']['payouts'][$month] / $res['chartCount']['payouts'][$month], 2) : 0;
			$res['chartAvg']['adjustment'][$month] = $res['chartCount']['adjustment'][$month] ? round($res['chartData']['adjustment'][$month] / $res['chartCount']['adjustment'][$month], 2) : 0;

			switch ($period) {
				case 'all':
				case 'today':
				case 'custom_range':
					$date->subDay();
					break;
				case 'week':
					$date->subWeek();
					break;
				case 'year':
					$date->subYearWithOverflow();
					break;
				default:
					$date->subMonthNoOverflow();
					break;
			}
		}
		ksort($res['chartData']['deposits']);
		ksort($res['chartData']['payouts']);
		ksort($res['chartData']['adjustment']);
		ksort($res['chartData']['balance']);
		ksort($res['chartCount']['deposits']);
		ksort($res['chartCount']['payouts']);
		ksort($res['chartCount']['adjustment']);
		// ksort($res['chartCount']['refund']);
		ksort($res['chartAvg']['deposits']);
		ksort($res['chartAvg']['payouts']);
		ksort($res['chartAvg']['adjustment']);
		sort($key);
		$res['key'] = array_values($key);
		foreach ($res['chartData']['balance'] as $k => $item) {
			$offset = array_search($k, array_keys($res['chartData']['balance']));
			if($offset) {
				$res['chartData']['balance'][$k] += array_sum(array_slice($res['chartData']['balance'], $offset - 1, 1));
			}
		}
		// $group = $this->user->getAdminGroup();
		$rapidBalance = 0;
		if(Util::meta($this->group, 'rapidAgentNumber')) {
			$api = RapidAPI::getForUserGroup($this->group);
			$rapidBalance = $api->getCachedBalance();
            $rapidBalance -= EmployerService::getHoldingPayoutsAmount($this->getGroupAdminUser($this->user));
		}
		// $res['balance'] = $rapidBalance;
		$res['chartData']['deposits'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartData']['deposits']);
		$res['chartData']['payouts'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartData']['payouts']);
		$res['chartData']['adjustment'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartData']['adjustment']);
		$res['chartData']['balance'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartData']['balance']);
		$res['chartAvg']['deposits'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartAvg']['deposits']);
		$res['chartAvg']['payouts'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartAvg']['payouts']);
		$res['chartAvg']['adjustment'] = array_map(function ($v) {
			return round($v / 100, 2);
		}, $res['chartAvg']['adjustment']);

		$res['deposits'] = round(array_sum($res['chartData']['deposits']), 2);
		$res['payouts'] = round(array_sum($res['chartData']['payouts']), 2);
		$res['adjustment'] = round(array_sum($res['chartData']['adjustment']), 2);
        $botmBalance = 0;
        if (BotmAPI::hasUserGroupAccount($this->group)) {
          if ($request->get('isRefresh')) {
            try {
              $botmBalance = EmployerService::getBOTMAvailableBalance($user, true);
            } catch(\Exception $exception) {
              Log::debug('Update the BOTM balance error ' . $exception->getMessage());
              $api = BotmAPI::getForUserGroup($this->group);
              $botmBalance = $api->getCachedBalance();
            }
          } else {
            $api = BotmAPI::getForUserGroup($this->group);
            $botmBalance = $api->getCachedBalance();
          }
        }
        $rapidBalance = 0;
        $oldRapidEmployer = false;
        if (RapidAPI::hasUserGroupAccount($this->group)) {
            $oldRapidEmployer = true;
            $lastOldData = Util::em()->getRepository(EmployerTransactions::class)
                                      ->createQueryBuilder('et')
                                      ->join('et.userGroup', 'ug')
                                      ->andWhere(Util::expr()->eq('ug.id', ':id'))
                                      ->setParameter('id',  $this->user->getAdminGroup()->getId())
                                      ->orderBy('et.dateTime', 'desc')
                                      ->setMaxResults(1)
                                      ->getQuery()
                                      ->getResult();
           $rapidBalance = count($lastOldData) ? $lastOldData[0]->getEndingBalance() : 0;
        }
        $res['oldRapidEmployer'] = $oldRapidEmployer;
        $res['isBotm'] = $this->group->isBOTM();
        $res['rapidBalance'] = $rapidBalance;
        $res['botmBalance'] = $botmBalance;
        $res['realBalance'] = $rapidBalance + $botmBalance;
        $res['balance'] = round(($res['deposits'] + $res['payouts'] + $res['adjustment']), 2) * 100;
        if ($botmBalance != $res['balance'] && $request->get('isRefresh') && !Data::get('pull_agent_transaction_' . $this->group->getId())) {
          Data::set('pull_agent_transaction_' . $this->group->getId(), true, 10 * 60);
          Util::executeCommand('span:mex:update-botm-agent-transactions', [
            '--agent' => 'botm_' . $this->group->getBotmBusinessAccountId(),
            '--days' => 1,
            '--notify' => null,
            '--type' => 'program_to_business'
          ]);
        }
		return new SuccessResponse($res);
	}

  protected function getOldDataForBOTMEmployer($request) {
      $query = Util::em()->getRepository(EmployerTransactions::class)
                          ->createQueryBuilder('et')
                          ->join('et.userGroup', 'ug')
                          ->andWhere(Util::expr()->eq('ug.id', ':id'))
                          ->setParameter('id',  $this->user->getAdminGroup()->getId());
      $query->orderBy('et.dateTime', 'desc');
      $query->addOrderBy('et.id', 'desc');
      $query = $this->queryByDateRange($query, 'et.dateTime', $request);
      $params = new QueryListParams(
        $query,
        $request,
        'et.id, et.dateTime, et.deposit, et.depositCount, et.payout, et.payoutCount, et.adjustment, et.adjustmentCount, et.balance',
        'count(distinct et)'
      );
      $params->pagination = false;
      $params->hydrationMode = AbstractQuery::HYDRATE_ARRAY;
      $allList = $this->queryListForDataOnly($params);
      Log::debug('Find ' . count($allList) . ' old data need to add.');
      $lists   = [];
      $counts  = [];
      foreach ($allList as $item) {
        $month = Util::formatDateTime($item['dateTime'], $this->format, $this->tz);
        $monthNum = Util::formatDateTime($item['dateTime'], 'm', $this->tz);
        $yearNum = Util::formatDateTime($item['dateTime'], 'Y', $this->tz);
        $weekNum = Util::formatDateTime($item['dateTime'], 'W', $this->tz);
        if ($this->format === Util::DATE_FORMAT_WEEK_DASH && ($monthNum == 12 || $monthNum == 1)) {
            if ($weekNum == 1 && $monthNum == '12') {
                $month = ($yearNum + 1) . '-' . $weekNum;
            }
            if ($weekNum >= 52 && $monthNum == '01') {
                $month = ($yearNum - 1) . '-' . $weekNum;
            }
        }


        if (!isset($lists['payouts'][$month])) {
            $lists['payouts'][$month] = 0;
        }
        $lists['payouts'][$month] += $item['payout'];
        if (!isset($counts['payouts'][$month])) {
            $counts['payouts'][$month] = 0;
        }
        $counts['payouts'][$month] += $item['payoutCount'];

        if (!isset($lists['deposits'][$month])) {
          $lists['deposits'][$month] = 0;
        }
        $lists['deposits'][$month] += $item['deposit'];
        if (!isset($counts['deposits'][$month])) {
            $counts['deposits'][$month] = 0;
        }
        $counts['deposits'][$month] += $item['depositCount'];

        if (!isset($lists['adjustment'][$month])) {
          $lists['adjustment'][$month] = 0;
        }
        $lists['adjustment'][$month] += $item['adjustment'];
        if (!isset($counts['adjustment'][$month])) {
            $counts['adjustment'][$month] = 0;
        }
        $counts['adjustment'][$month] += $item['adjustmentCount'];
      }

      return [
          'count' => $counts,
          'data'  => $lists,
      ];
  }

  protected function getTransactionData($request)
  {
      $allList = $this->getTransactionRecord($request);
      $lists   = [];
      $counts  = [];
      // $group = $this->user->getAdminGroup();
      if ($this->group->isBOTM()) {
        foreach ($allList as $item) {
          $month = Util::formatDateTime($item['txnTime'], $this->format, $this->tz);
          $monthNum = Util::formatDateTime($item['txnTime'], 'm', $this->tz);
          $yearNum = Util::formatDateTime($item['txnTime'], 'Y', $this->tz);
          $weekNum = Util::formatDateTime($item['txnTime'], 'W', $this->tz);
          if ($this->format === Util::DATE_FORMAT_WEEK_DASH && ($monthNum == 12 || $monthNum == 1)) {
              if ($weekNum == 1 && $monthNum == '12') {
                  $month = ($yearNum + 1) . '-' . $weekNum;
              }
              if ($weekNum >= 52 && $monthNum == '01') {
                  $month = ($yearNum - 1) . '-' . $weekNum;
              }
          }

          $amount = $item['txnAmount'];
          if ($item['tranCode'] == 'CREDIT') {
              if (in_array($item['actualTranCode'], ['user_to_business'])) {
                if (!isset($lists['adjustment'][$month])) {
                    $lists['adjustment'][$month] = 0;
                }
                $lists['adjustment'][$month] += $amount;
                if (!isset($counts['adjustment'][$month])) {
                    $counts['adjustment'][$month] = 0;
                }
                $counts['adjustment'][$month] += 1;
              }
              if (in_array($item['actualTranCode'], ['program_to_business', 'ach_transaction'])) {
                if (!isset($lists['deposits'][$month])) {
                    $lists['deposits'][$month] = 0;
                }
                $lists['deposits'][$month] += $amount;
                if (!isset($counts['deposits'][$month])) {
                    $counts['deposits'][$month] = 0;
                }
                Log::debug('=========');
                Log::debug($item['id']);
                $counts['deposits'][$month] += 1;
              }
          }
          if ($item['tranCode'] == 'Pay_employee'
          && (
              $item['accountStatus'] === EmployerPayout::PAYOUT_COMPLETE
              || $item['accountStatus'] === EmployerPayout::PAYOUT_SYNC_PENDING
              || $item['accountStatus'] === EmployerPayout::PAYOUT_REVERSED
          ) && $item['actualTranCode'] == 'botm'){ // in_array($item['actualTranCode'], ['business_to_user'])) {
            if (!isset($lists['payouts'][$month])) {
              $lists['payouts'][$month] = 0;
            }
            $lists['payouts'][$month] += $amount;
            if (!isset($counts['payouts'][$month])) {
                $counts['payouts'][$month] = 0;
            }
            $counts['payouts'][$month] += 1;
            if ($item['accountStatus'] == EmployerPayout::PAYOUT_REVERSED) {
              if (!isset($lists['adjustment'][$month])) {
                $lists['adjustment'][$month] = 0;
              }
              $lists['adjustment'][$month] += $amount;
              if (!isset($counts['adjustment'][$month])) {
                  $counts['adjustment'][$month] = 0;
              }
              $counts['adjustment'][$month] += 1;
            }
          }
          if ( $item['tranCode'] == 'DEBIT' && in_array($item['actualTranCode'], ['business_to_program'])) {
            if (!isset($lists['adjustment'][$month])) {
              $lists['adjustment'][$month] = 0;
            }
            $lists['adjustment'][$month] += $amount;
            if (!isset($counts['adjustment'][$month])) {
                $counts['adjustment'][$month] = 0;
            }
            $counts['adjustment'][$month] += 1;
          }
        }
      } else {
        foreach ($allList as $item) {
            $month = Util::formatDateTime($item['txnTime'], $this->format, $this->tz);
            $monthNum = Util::formatDateTime($item['txnTime'], 'm', $this->tz);
            $yearNum = Util::formatDateTime($item['txnTime'], 'Y', $this->tz);
            $weekNum = Util::formatDateTime($item['txnTime'], 'W', $this->tz);
            if ($this->format === Util::DATE_FORMAT_WEEK_DASH && ($monthNum == 12 || $monthNum == 1)) {
                if ($weekNum == 1 && $monthNum == '12') {
                    $month = ($yearNum + 1) . '-' . $weekNum;
                }
                if ($weekNum >= 52 && $monthNum == '01') {
                    $month = ($yearNum - 1) . '-' . $weekNum;
                }
            }
            if (in_array($item['actualTranCode'], ['business_to_user', 'user_to_business', 'program_to_business', 'ach_transaction'])) {
              continue;
            }
            $amount = $item['txnAmount'];
            if ($item['tranCode'] == 'CREDIT') {
                if (Util::startsWith($item['tranDesc'], 'Wire ')) {
                    if (!isset($lists['deposits'][$month])) {
                        $lists['deposits'][$month] = 0;
                    }
                    $lists['deposits'][$month] += $amount;
                    if (!isset($counts['deposits'][$month])) {
                        $counts['deposits'][$month] = 0;
                    }
                    $counts['deposits'][$month] += 1;
                } else {
                    if (!isset($lists['adjustment'][$month])) {
                        $lists['adjustment'][$month] = 0;
                    }
                    $lists['adjustment'][$month] += $amount;
                    if (!isset($counts['adjustment'][$month])) {
                        $counts['adjustment'][$month] = 0;
                    }
                    $counts['adjustment'][$month] += 1;
                }
            }
            if ((
                $item['tranCode'] == 'DEBIT'
                || (
                    $item['tranCode'] == 'Pay_employee'
                    && (
                        $item['accountStatus'] === EmployerPayout::PAYOUT_COMPLETE
                        || $item['accountStatus'] === EmployerPayout::PAYOUT_SYNC_PENDING
                    ) &&  $item['actualTranCode'] !==  'botm'
                )
            )) {
                if ($amount > 0 && $item['actualTranCode'] !== 'MANUAL_AGENT_DEBIT') {
                    if (!isset($lists['payouts'][$month])) {
                        $lists['payouts'][$month] = 0;
                    }
                    $lists['payouts'][$month] += $amount;
                    if (!isset($counts['payouts'][$month])) {
                        $counts['payouts'][$month] = 0;
                    }
                    $counts['payouts'][$month] += 1;
                } else {
                    if (!isset($lists['adjustment'][$month])) {
                        $lists['adjustment'][$month] = 0;
                    }
                    $lists['adjustment'][$month] -= $amount;
                    if (!isset($counts['adjustment'][$month])) {
                        $counts['adjustment'][$month] = 0;
                    }
                    $counts['adjustment'][$month] += 1;
                }
            }
        }
      }

      return [
          'count' => $counts,
          'data'  => $lists,
      ];
  }

	protected function getTransactionRecord($request)
	{

			$query = Util::em()->getRepository(UserCardTransaction::class)
				->createQueryBuilder('uct')
				->join('uct.userCard', 'uc')
				->join('uc.user', 'u')
				->andWhere(Util::expr()->eq('u.id', ':id'))
				->setParameter('id', $this->getGroupAdminUser($this->user)->getId())
				->andwhere(Util::expr()->eq('uc.type', ':ucType'))
        ->andwhere(Util::expr()->notLike('uct.tranDesc', ':desc'))
        ->andwhere(Util::expr()->notLike('uct.tranDesc', ':descPayroll'))
        ->andwhere(Util::expr()->notLike('uct.tranDesc', ':descMoveFunds'))
        ->setParameter('descPayroll', '%Transfer Payroll money from%')
        ->setParameter('descMoveFunds', 'Transfer per HZ%')
				->setParameter('desc', '%Transfer Payout money from%')
				->setParameter('ucType', PrivacyAPI::CARD_TYPE_DUMMY)
        // hide refund transactions for 2021-12-23 issues
        ->andwhere(Util::expr()->notIn('uct.id', ':hideIds'))
        ->setParameter('hideIds',TransferMexBundle::hideTransactions());
		$query->orderBy('uct.txnTime', 'desc');
		$query->addOrderBy('uct.id', 'desc');
		$query = $this->queryByDateRange($query, 'uct.txnTime', $request);
		$params = new QueryListParams(
			$query,
			$request,
			'uct.id, uct.txnTime, uct.txnAmount, uct.tranDesc, uct.actualTranCode, uct.tranCode, uct.accountStatus',
			'count(distinct uct)'
		);
		$params->pagination = false;
		$params->hydrationMode = AbstractQuery::HYDRATE_ARRAY;
		$allList = $this->queryListForDataOnly($params);
		return $allList;
	}

	public function traitGetStaticData(Request $request)
	{
		// $group = $this->user->getAdminGroup();
		$totalDeposits = $this->getEmployeeTotalDeposits();
		$res['employee'] = [
			'total' => $this->group->getUserCount(),
			'active' => $this->group->getUserCount(MemberService::STEP_ACTIVE),
			'totalDeposits' => $totalDeposits,
			'avgDeposits' => $this->group->getUserCount() ? round($totalDeposits / $this->group->getUserCount()) : 0
		];
		$res['depositDetails'] = [
			'name' => 'Axiom Bank',
			'address' => '5991 S. Goldenrod Rd, Orlando, FL 32822',
			'account' => '**********',
			'rounting' => '*********'
		];
		$res['payouts'] = $this->getRecentPayout($this->getGroupAdminUser($this->user)->getId(), $request);
		$res['deposits'] = $this->getRecentDeposits($this->getGroupAdminUser($this->user)->getId(), $request);
		return new SuccessResponse($res);
	}

	protected function getEmployeeTotalDeposits()
	{
		$res = Util::em()->getRepository(UserCardTransaction::class)
			->createQueryBuilder('uct')
			->join('uct.userCard', 'uc')
			->join('uc.user', 'u')
      ->andwhere(Util::expr()->eq('u.id', $this->getGroupAdminUser($this->user)->getId()))
			->andWhere(Util::expr()->eq('uct.tranCode', ':uctType'))
			->setParameter('uctType', 'Pay_employee')
			->andwhere(Util::expr()->eq('uc.type', ':ucType'))
			->andWhere(Util::expr()->in('uct.accountStatus', ':status'))
			->setParameter('status', [EmployerPayout::PAYOUT_COMPLETE, EmployerPayout::PAYOUT_SYNC_PENDING])
			->setParameter('ucType', PrivacyAPI::CARD_TYPE_DUMMY)
      ->andwhere(Util::expr()->notIn('uct.id', ':hideIds'))
      ->setParameter('hideIds', TransferMexBundle::hideTransactions())
      ->andwhere(Util::expr()->gt('uct.txnAmount', ':txnAmount'))
      ->setParameter('txnAmount', 0)
			->select('sum(uct.txnAmount)')
			->getQuery()
			->getSingleScalarResult();
      $user = null;
      if ($this->user->inTeam(Role::ROLE_TRANSFER_MEX_EMPLOYER)) {
        $user = $this->user;
      } else {
        $user = $this->user->getAdminGroup() ? $this->user->getAdminGroup()->getPrimaryAdmin() : $this->user;
      }
      if (Util::isLive() && $this->platform->isTransferMex() && $user->getId() === ********* ) {
        $res += 112449;
      }
		return $res;
	}

	protected function getRecentDeposits($id, $request)
	{
		$query = Util::em()->getRepository(UserCardTransaction::class)
			->createQueryBuilder('uct')
			->join('uct.userCard', 'uc')
			->join('uc.user', 'u')
			->andWhere(Util::expr()->eq('u.id', ':id'))
			->setParameter('id', $id)
			->andwhere(Util::expr()->eq('uc.type', ':ucType'))
			->andWhere(Util::expr()->eq('uct.tranCode', ':uctType'))
      ->andwhere(Util::expr()->notIn('uct.id', ':hideIds'))
      ->setParameter('hideIds', TransferMexBundle::hideTransactions())
			->setParameter('ucType', PrivacyAPI::CARD_TYPE_DUMMY)
			->setParameter('uctType', 'CREDIT')
			->orderBy('uct.txnTime', 'desc');
		$list = $this->queryByDateRange($query, 'uct.txnTime', $request)
			->getQuery()
			->setFirstResult(0)
			->setMaxResults(5)
			->getResult();
		$res = [];
    // $group = $this->user->getAdminGroup();
		foreach ($list as $item) {
      if (!$this->group->isBOTM() && in_array($item->getActualTranCode(), ['user_to_business', 'program_to_business', 'ach_transaction'])) {
        continue;
      }
			$res[] = [
				'sendDate' => Util::formatDateTime($item->getTxnTime() ? $item->getTxnTime() : $item->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
				'amount' => $item->getTxnAmount(),
				'balance' => $item->getCurBalance()
			];
		}
		return $res;
	}

	protected function getRecentPayout($id, $request)
	{
        $str = $this->platform->isFaasPlatforms() ? 'Member' : 'Employee';

		$query = Util::em()->getRepository(UserCardTransaction::class)
			->createQueryBuilder('uct')
			->join('uct.userCard', 'uc')
			->join('uc.user', 'u')
			->where(Util::expr()->eq('u.id', $id))
			->andwhere(Util::expr()->eq('uc.type', ':ucType'))
			->andWhere(Util::expr()->in('uct.tranCode', ':uctType'))
			->andWhere(Util::expr()->in('uct.accountStatus', ':status'))
			->andwhere(Util::expr()->like('uct.tranDesc', ':employeeId'))
			->setParameter('ucType', PrivacyAPI::CARD_TYPE_DUMMY)
			->setParameter('employeeId', '%'. $str . ':%')
			->setParameter('status', [EmployerPayout::PAYOUT_COMPLETE, EmployerPayout::PAYOUT_SYNC_PENDING])
			->setParameter('uctType', ['DEBIT', 'Pay_employee'])
			->orderBy('uct.txnTime', 'desc');
		$list = $this->queryByDateRange($query, 'uct.txnTime', $request)
			->getQuery()
			->setFirstResult(0)
			->setMaxResults(5)
			->getResult();
		$res = [];
		foreach ($list as $item) {
			$desc = $item->getTranDesc();
            if (!$this->platform->isFaasPlatforms()) {
              $employeeId = strpos($desc, 'Employee:') !== FALSE ? substr($desc, strpos($desc, 'Employee:') + 9) : null;
            } else {
              $employeeId = strpos($desc, 'Member:') !== FALSE ? substr($desc, strpos($desc, 'Member:') + 7) : null;
            }

			if(strpos($employeeId, ';') !== FALSE) {
				$employeeId = substr($employeeId, 0, strpos($employeeId, ';'));
			}

            $employee = null;
			if($employeeId) {
				$employee = User::find($employeeId);
			}
			$res[] = [
				'id' => $item->getId(),
				'sendDate' => Util::formatDateTime($item->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
				'amount' => $item->getTxnAmount(),
				'balance' => $item->getCurBalance(),
				'employee' => $employee ? $employee->toApiArray() : null
			];
		}
		return $res;
	}
}
