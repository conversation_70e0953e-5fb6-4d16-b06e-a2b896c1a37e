<?php

namespace TransferMexBundle\Controller\Admin\Employer;

use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserGroup;
use SalexUserBundle\Entity\User;
use TransferMexBundle\Controller\Admin\AgentsController;
use TransferMexBundle\Entity\UserTransferMexTrait;

class BaseController extends AgentsController
{
   use UserTransferMexTrait;
    /** @var User */
    public $employer;

    /** @var UserGroup */
    public $group;

    /**
     * BaseController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->employer = $this->getGroupAdminUser($this->user);
        $this->group = $this->employer->getAdminGroup();
    }

    protected function getAccessibleRoles()
    {
        if ($this->user && in_array($this->user->getTeamName(), [
          Role::ROLE_TRANSFER_MEX_EMPLOYER,
          Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN
          ])) {
          $this->group  = $this->user->getAdminGroup();
          if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH) {
            return [];
          }
        }
        return [
            Role::ROLE_TRANSFER_MEX_EMPLOYER,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
            Role::ROLE_FAAS_CLIENT,
            Role::ROLE_FAAS_CLIENT_ADMIN,
            Role::ROLE_FAAS_CLIENT_AGENT
        ];
    }
}
