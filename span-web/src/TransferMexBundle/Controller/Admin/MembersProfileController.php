<?php


namespace TransferMexBundle\Controller\Admin;


use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Notes;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\AuthService;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use FaasBundle\Services\BOTM\BotmAPI;
use FaasBundle\Services\ProcessorHub;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\ProcessorMemberService;
use TransferMexBundle\Services\RapidService;
use CoreBundle\Entity\NotificationEmails;
use CoreBundle\Entity\Platform;
use FaasBundle\Services\BOTM\BotmService;
use TransferMexBundle\Entity\EmployeeTransferFreeConfig;
use TransferMexBundle\Entity\EmployerTransferFreeConfig;

class MembersProfileController extends MembersController
{
    use UserTransferMexTrait;
    /**
     * @Route("/admin/mex/members/{user}/profile")
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     */
    public function profileAction(Request $request, User $user)
    {
        $this->validateUser($user);

        $data = $this->getRowData($user);
        $uc = $user->getCurrentPlatformCard();

        if ($request->get('force') === 'true') {
            try {
                $an = $uc->getAccountNumber();
                if ($an && BotmAPI::isAccountNumberValid($an)) {
                    $accountId = $user->ensureConfig()->getBotmUserAccountId();
                    if (!$accountId) {
                        return new FailedResponse('The card has not been enrolled yet! No card account found.');
                    }
                }
                ProcessorHub::updateBalanceAndStatus($uc);
            } catch (\Throwable $t) {
                if (RapidAPI::isAccountNumberError($t->getMessage())) {
                    $api = RapidAPI::getForUserCard($uc);
                    $api->getBalance($uc, true);
                    RapidService::updateBalanceAndStatus($uc);
                } else {
                    throw $t;
                }
            }
        }

        $config = $user->ensureConfig();

        $data['Role'] = 'Member';
        $data['Balance'] = $uc->getBalance();
        $data['Country'] = $user->getCountryName();
        $data['Date of Birth'] = Util::formatDateTime($user->getBirthday(), Util::DATE_FORMAT_LONG, Util::tzUTC());
        $data['Mailing Address'] = $user->getAddresses();
        $data['City'] = $user->getCity();
        $data['State / Province'] = $user->getStateName();
        $data['Postal Code'] = $user->getZip();
        $data['KYC Passed'] = $user->isAllKycPassed();
        $data['Employer'] = $user->getPrimaryGroupName();
        $data['Barcode Number'] = $uc->getAccountNumber();
        $data['Card Number'] = $uc->getMaskedCardNumber();
        $data['Card Status'] = $uc->getIssueStatus();
        $data['Free-First Transfer'] = MemberService::checkIsNextTransferFree($user) ? 'Not In Use' : MemberService::getFirstPromoStatus($user);
        $data['Free Promo'] = MemberService::getUserPromo($user, 'notUse') . ' promos available, ' . MemberService::getUserPromo($user, 'used') .' used';
        $legacies = $uc->getLegacyAccountNumbers();
        $data['Legacy Account Numbers'] = $legacies;
        $data['isBotmCard'] = $uc && $uc->isBotmCard() ? true : false;
        $data['Botm User Id'] = $user->ensureConfig()->getBotmUserId();
        $data['Botm Account Id'] = $user->ensureConfig()->getBotmUserAccountId();
        $data['ans'] = [];
        $ans = array_filter(array_merge([
            $uc->getAccountNumber(),
        ], $legacies));
        $apis = RapidService::getAllApis();
        foreach ($ans as $an) {
            $agent = RapidService::getCachedEnrolledAgentNumber($an);
            if ($agent) {
                $api = $apis[$agent] ?? null;
                if ($api) {
                    $data['ans'][$an] = $api->getName();
                }
            }
        }
        $data['employers'] = EmployerService::listForSelection($this->getCurrentEmployerRole());

        $data['BOTM User ID'] = $config->getBotmUserId();
        $data['BOTM Account ID'] = $config->getBotmUserAccountId();
        $botmKycStatus = Util::meta($user, 'botm_kyc_status');
        if (!$botmKycStatus) {
          $botmKycStatus = BotmService::getBotmKycStatus($user);
          Util::updateMeta($user,[
            'botm_kyc_status' => $botmKycStatus
          ]);
        }
        $data['BOTM KYC Status'] = $botmKycStatus;
        $data['Free Period'] = EmployeeTransferFreeConfig::getPeriodByMemberIds($user);
        $data['Employer Free Period'] = EmployerTransferFreeConfig::getPeriodByMemberId($user);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/mex/members/{user}/notes")
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     */
    public function notesAction(Request $request, User $user)
    {
        $this->validateUser($user);
        $data = Util::toApiArray($user->getNotes());
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/mex/members/{user}/notes-del/{note}")
     * @param Request $request
     * @param User    $user
     * @param Notes   $note
     *
     * @return SuccessResponse
     */
    public function notesRemoveAction(Request $request, User $user, Notes $note)
    {
        $this->validateUser($user);
        $toThisUser = $note->getToname() === (string)$user->getId();
        $createdByMe = Util::eq($note->getCreatedBy(), $this->user);
        if ($toThisUser && $createdByMe) {
            $this->em->remove($note);
            $this->em->flush();
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/notes-add", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return SuccessResponse
     * @throws PortalException
     */
    public function notesAddAction(Request $request, User $user)
    {
        $this->validateUser($user);
        $note = $user->addNote($request->get('content'), true, $this->user->getId(), $this->user);
        return new SuccessResponse($note->toApiArray());
    }

    /**
     * @Route("/admin/mex/members/{user}/activate-card", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function activateCard(Request $request, User $user)
    {
        $this->validateUser($user);

        $uc = $user->getCurrentPlatformCard();
        if (!$uc->getAccountNumber()) {
            return new FailedResponse('Unknown account number!');
        }

        try {
            MemberService::enroll($uc);
        } catch (PortalException $pe) {
            return new FailedResponse($pe->getMessage());
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/change-card-status", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function changeCardStatus(Request $request, User $user)
    {
        $this->validateUser($user);

        $uc = $user->getCurrentPlatformCard();
        if (!$uc->getAccountNumber()) {
            return new FailedResponse('Unknown account number!');
        }

        $status = $request->get('cardStatus');
        if (!$status) {
            return new FailedResponse('Unknown status!');
        }

        $old = $uc->getNativeStatus();
        $dba = $request->get('dba');
        $reason = $request->get('reason');
        if ($dba) {
            $an = $request->get('accountNumber');
            $api = ProcessorHub::getForUserCardAccountNumber($uc, $an);
            if ($an !== $uc->getAccountNumber()) {
                $tempUc = new UserCard();
                $tempUc->setAccountNumber($an);
                $cards = $api->getCardDetailsData($tempUc);
                if ($cards) {
                    $old = $cards[0]['cardStatus'];
                }
            }

            /** @var ExternalInvoke $ei */
            [$ei] = $api->updateStatus($api->getUserCard(), $status, substr($dba, -4));
            ProcessorHub::updateBalanceAndStatusSilently($uc);
            if ($ei && $ei->isFailed()) {
                $error = $ei->getError();
                if ($api instanceof BotmAPI && $an === $uc->getAccountNumber() && BotmAPI::isCardClosedError($error)) {
                    if (in_array($uc->getNativeStatus(), [
                        BotmAPI::CARD_STATUS_PENDING_FULFILLMENT,
                        BotmAPI::CARD_STATUS_PENDING_ACTIVATION,
                    ])) {
                        return new FailedResponse('Card status is ' . $uc->getNativeStatus() . ' and cannot be changed now. Please enroll it first.');
                    }
                    if ($uc->getNativeStatus() === BotmAPI::CARD_STATUS_CLOSED) {
                        return new FailedResponse('Card status is ' . $uc->getNativeStatus() . ' and cannot be changed. Please assign a new card.');
                    }
                }
                return new FailedResponse($error);
            }
            $reasonStr = $reason ? ' with the reason that ' . $reason : '';
            $user->addNote('Changed the card ' . $an . ' (ending with ' . substr($dba, -2) . ') status from ' . $old . ' to ' . $status . $reasonStr,
                TRUE, $this->user->getId());
        } else {
            ProcessorMemberService::changeStatusWithLegacies($uc, $status);
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/verify-support-id", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function verifySupportId(Request $request, User $user)
    {
        $this->validateUser($user);

        $verified = AuthService::verify($user, 'supportId2Fa', $request->get('code'));
        if (!$verified) {
            return new FailedResponse('Invalid Support ID!');
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/mex/members/{user}/return-balance", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function returnBalance(Request $request, User $user)
    {
        $this->validateUser($user);
        $this->authRoles([Role::ROLE_TRANSFER_MEX_ADMIN]);
        $type = $request->get('type', 'card');
        $amount = Money::normalizeAmount($request->get('amount', 0), 'USD');
        if ($amount <= 0) {
            return new FailedResponse('Invalid amount!');
        }
        $legacyGroup = null;
        if ($type == 'employer') {
          $eid = $request->get('employer');
          if (!$eid) {
              return new FailedResponse('Invalid employer!');
          }

          $employer = $this->em->getRepository(User::class)
              ->find($eid);
          if (!$employer) {
              return new FailedResponse('Unknown employer ' . $eid . '!');
          }
          $legacyGroup = $employer->getAdminGroup();
        }
        $uc = $user->getCurrentPlatformCard();
        $legacies = $uc->getLegacyAccountNumbers();

        // send email to Rapid to move funds
        if (Util::isLive() && !Util::isDev()) {
          $to = [
              '<EMAIL>',
              '<EMAIL>',
          ];
          $cc = [
              '<EMAIL>',
              '<EMAIL>'
              // '<EMAIL>'
          ];
          $cc = array_merge($cc, NotificationEmails::findByPlatform(Platform::transferMex()));
        } else {
            $to = '<EMAIL>';
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
            ];
        }
        $userGroup = $user->getPrimaryGroup();

        $body = '<p>User: ' . $user->getId() . '</p>' .
                '<p>New barcode number: ' . $uc->getAccountNumber() . '</p>' .
                '<p>Legacy Balance: ' .  Money::formatWhen($amount) . '</p>';
        if ($type == 'card') {
          $agentNumber = RapidService::getCachedEnrolledAgentNumber($legacies[count($legacies) - 1]);
          $body = '<p>Our below account changed his card </p>' .
                  $body .
                  '<p>We reversed his balance to the employer agent account ' . $agentNumber . '. Can you please load his balance to the new account from the agent ' . $agentNumber . ' directly?</p>';
        } else {
          $agentNumber = RapidService::getCachedEnrolledAgentNumber($uc->getAccountNumber());
          $employerAgent = Util::meta($legacyGroup, 'rapidAgentNumber');
          $loadAgent = null;
          if ($agentNumber != $employerAgent) {
            $loadAgent = $agentNumber;
          } else {
            $loadAgent = $legacyGroup->getName() . ' ' .$employerAgent;
          }

          $body = '<p>Our below account was moved from ' . $legacyGroup->getName() . ' to ' . $userGroup->getName() . ':</p>' .
                  $body .
                  '<p>We reversed his legacy balance to his legacy employer agent ' . $loadAgent . '. Can you please load his legacy balance to the new account from the agent ' . $loadAgent . ' directly?</p>';
        }
        $body = $body . '<p>Remember to use a description like "Transfer per HZ ' . $user->getId() . '" for the transaction. Thanks.</p>';
        $params = [
          'name' => 'RFS Team',
          'subject' => 'Please move funds to the account ' . $uc->getAccountNumber(),
          'body' => $body,
          '_cc' => $cc,
         ];

        Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, $params, null, CardProgram::transferMexUSD());
        return new SuccessResponse();
    }
}
