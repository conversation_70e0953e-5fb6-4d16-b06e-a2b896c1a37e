<?php


namespace TransferMexBundle\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserGroup;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\DeniedException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\EmployerPayout;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\TransferMexBundle;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use TransferMexBundle\Controller\Admin\Employer\BaseController;

class EmployerPaymentController extends BaseController
{
   use UserTransferMexTrait;
   public  $cacheKey = 'Employer_Pay_Employee';
    protected function getAccessibleRoles()
    {
        if ($this->user && in_array($this->user->getTeamName(), [
          Role::ROLE_TRANSFER_MEX_EMPLOYER,
          Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN
          ])) {
          $this->group = $this->user->getAdminGroup();
          if ($this->group->getFundingType() == UserGroup::FUNDING_TYPE_ACH) {
            return [];
          }
        }
        return [
            Role::ROLE_TRANSFER_MEX_EMPLOYER,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
            Role::ROLE_FAAS_CLIENT,
            Role::ROLE_FAAS_CLIENT_ADMIN,
            Role::ROLE_FAAS_CLIENT_AGENT
		];
    }

    /**
     * @Route("/admin/mex/employer/payments/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = parent::search($request, $page, $limit);
        $result = $resp->getResult();

        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->join('uct.userCard', 'uc')
            ->join('uc.user', 'u')
            ->where(Util::expr()->eq('u.id', $this->getGroupAdminUser($this->user)->getId()))
            // ->andWhere(Util::expr()->gte('uct.txnAmount', ':account'))
            ->andWhere(Util::expr()->in('uct.accountStatus', ':status'))
            ->andwhere(Util::expr()->eq('uc.type',':ucType'))
            ->andwhere(Util::expr()->neq('uct.tranCode',':reverseType'))
            ->andwhere(Util::expr()->notLike('uct.tranDesc', ':desc'))
            ->andwhere(Util::expr()->notLike('uct.tranDesc', ':descPayroll'))
            ->andwhere(Util::expr()->notLike('uct.tranDesc', ':descMoveFunds'))
            //->andwhere(Util::expr()->neq('uct.actualTranCode', ':toBusiness'))
            // hide refund transactions for 2021-12-23 issues and change card issue
            ->andwhere(Util::expr()->notIn('uct.id', ':hideIds'))
            ->setParameter('hideIds', TransferMexBundle::hideTransactions())
            ->setParameter('descPayroll', '%Transfer Payroll money from%')
            ->setParameter('descMoveFunds', 'Transfer per HZ%')
            ->setParameter('desc', '%Transfer Payout money from%')
            // ->setParameter('toUser', 'business_to_user')
            // ->setParameter('toBusiness', 'user_to_business')
            ->setParameter('reverseType', 'Reverse_employee')
            ->setParameter('ucType', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('status', [
            	UserCardTransaction::STATUS_EXECUTED,
              UserCardTransaction::STATUS_REVERSED,
              EmployerPayout::PAYOUT_INIT,
              EmployerPayout::PAYOUT_COMPLETE,
              EmployerPayout::PAYOUT_SYNC_PENDING,
              UserCardTransaction::STATUS_CANCLED,
              UserCardTransaction::STATUS_REVERSED,
              UserCardTransaction::STATUS_DUPLICATED
            ]);
          // $group = $this->user->getAdminGroup();
          if ($this->group && $this->group->isBOTM()) {
            $expr = Util::expr();
            $query->andwhere(
                    $expr->orX($expr->neq('uct.actualTranCode', ':actualTranCode'),
                    $expr->isNull('uct.actualTranCode'))
                    )
                  ->setParameter('actualTranCode', 'business_to_user');
          }
          $paymentType = $request->get('paymentType');
          $roleType = $request->get('roleType');
          if ($paymentType || $roleType) {
            if ($paymentType === 'payout' || $roleType === 'employee') {
              $query->andWhere(Util::expr()->in('uct.tranCode', ':paymentType'))
                   ->setParameter('paymentType', ['DEBIT', 'Pay_employee']);
            }
            if ($paymentType === 'deposit' || $roleType === 'employer') {
              $query->andWhere(Util::expr()->eq('uct.tranCode', ':paymentType'))
              ->setParameter('paymentType', 'CREDIT');
            }
            if ($paymentType === 'reversed') {
              $query->andWhere(Util::expr()->lt('uct.txnAmount', ':txnAmount'))
              ->setParameter('txnAmount', 0);
            }
          }
          $str = $this->platform->isFaasPlatforms() ? 'Member' : 'Employee';
          // check not
          $not = $request->get('__not');
          if (isset($not['title'])) {
              $employee = User::findByExternalId($not['title'], $this->getGroupAdminUser($this->user));
              if ($employee) {
                  $query->andwhere(Util::expr()->notLike('uct.tranDesc', ':title'))
                      ->setParameter('title', '%' . $str . ':' . $employee->getId() . '%');
              }
          }
        if (isset($not['firstName'])) {
            $employee = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['firstName' => $not['firstName']]);
            if ($employee) {
                $query->andwhere(Util::expr()->notLike('uct.tranDesc', ':firstName'))
                    ->setParameter('firstName', '%' . $str . ':' . $employee->getId() . '%');
            }
        }
        if (isset($not['lastName'])) {
            $employee = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['lastName' => $not['lastName']]);
            if ($employee) {
                $query->andwhere(Util::expr()->notLike('uct.tranDesc', ':lastName'))
                    ->setParameter('lastName', '%' . $str . ':' . $employee->getId() . '%');
            }
        }
        if (isset($not['email'])) {
            $employee = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['email' => $not['email']]);
            if ($employee) {
                $query->andwhere(Util::expr()->notLike('uct.tranDesc', ':email'))
                    ->setParameter('email', '%' . $str . ':' . $employee->getId() . '%');
            }
        }
        if (isset($not['transferTo'])) {
                $query->andwhere(Util::expr()->notLike('uct.tranDesc', ':transferTo'))
                    ->setParameter('transferTo', '%' . $str . ':' . $not['transferTo'] . '%');
        }
        if ($request->get('title')) {
            $employee = User::findByExternalId($request->get('title'), $this->getGroupAdminUser($this->user));
            $query->andwhere(Util::expr()->like('uct.tranDesc', ':title'))
            ->setParameter('title', '%' . $str . ':' . ($employee ? $employee->getId() : $request->get('title')) . '%');
          }
        if ($request->get('email')) {
            $employee = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['email' => $not['email']]);
            $query->andwhere(Util::expr()->like('uct.tranDesc', ':email'))
                    ->setParameter('email', '%' . $str . ':' . ($employee ? $employee->getId() : $request->get('email')) . '%');
        }
        if ($request->get('firstName')) {
            $employee = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['firstName' => $not['firstName']]);
            $query->andwhere(Util::expr()->like('uct.tranDesc', ':firstName'))
                    ->setParameter('firstName', '%' . $str . ':' . ($employee ? $employee->getId() : $request->get('firstName')) . '%');
        }
        if ($request->get('lastName')) {
            $employee = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(['lastName' => $not['lastName']]);
            $query->andwhere(Util::expr()->like('uct.tranDesc', ':lastName'))
                    ->setParameter('lastName', '%' . $str . ':' . ($employee ? $employee->getId() : $request->get('lastName')) . '%');
        }
          if ($request->get('transferTo')) {
              $str = $this->platform->isFaasPlatforms() ? 'Member' : 'Employee';
            $query->andwhere(Util::expr()->like('uct.tranDesc', ':transferTo'))
            ->setParameter('transferTo', '%' . $str . ':' . $request->get('transferTo') . '%');
          }
          $as = $request->get('accountStatus');
          if ($as) {
            $accountStatus = $as === 'Executed'
                ? ['Executed', EmployerPayout::PAYOUT_SYNC_PENDING, EmployerPayout::PAYOUT_COMPLETE]
                : [$as];
            $query->andWhere(Util::expr()->in('uct.accountStatus', ':accountStatus'))
              ->setParameter('accountStatus', $accountStatus);
          }
        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'uct', 'count(distinct uct)');
        $params->distinct = true;
        $params->orderBy = [
            'uct.txnTime' => 'desc',
            'uct.id' => 'desc'
        ];
        $params->searchFields = [
          'uct.tranDesc'
        ];
        return $params;
    }

    /**
     * @param UserCardTransaction $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        // $flag = rand(0,10);
        $employeeId = null;
        $employee = null;
        // if ($entity->getTranCode() === 'DEBIT' || $entity->getTranCode() === 'Pay_employee') {
        $desc = $entity->getTranDesc();
        if ($this->platform->isFaasPlatforms()) {
          $employeeId = strpos($desc, 'Member:') !== FALSE ? substr($desc, strpos($desc, 'Member:') + 7) : null;
        } else {
          $employeeId = strpos($desc, 'Employee:') !== FALSE ? substr($desc, strpos($desc, 'Employee:') + 9) : null;
        }
        if (strpos($employeeId, ';') !== FALSE) {
          $employeeId = substr($employeeId, 0, strpos($employeeId, ';'));
        }
        $payoutId = $this->getPayoutId($entity);
        if ($employeeId) {
          $employee = User::find($employeeId);
        }
        // if ($this->platform->isFaasPlatforms()) {
        //     $str = 'Member';
        //     $strC = 'Client';
        // } else {
        //     $str = 'Employee';
        //     $strC = 'Employer';
        // }
        $patmentType = '';
        if (($entity->getTranCode() === 'DEBIT' || $entity->getTranCode() ===  'Pay_employee') && $entity->getActualTranCode() !== 'MANUAL_AGENT_DEBIT') {
          $patmentType = $entity->getTxnAmount() > 0 ? $this->getEmployeeText() .' Payout' : $this->getEmployeeText() .' Reversal';
        } else if ($entity->getTranCode() === 'DEBIT' && $entity->getActualTranCode() === 'MANUAL_AGENT_DEBIT') {
          $patmentType = $this->getEmployerText() . ' Adjustment';
        } else {
          $patmentType = $this->getEmployerText() . ' Deposit';
        }
        return [
          'ID' => $entity->getId(),
          'Payment Type' => $patmentType,
          'Transfer To' => $entity->getTranCode() === 'CREDIT' ? ($employeeId ? $employeeId : 'Funding Account') : ($employeeId ? $employeeId : 'DEBIT'),
          'Role Type'=> (($entity->getTranCode() === 'DEBIT' && $entity->getActualTranCode() !== 'MANUAL_AGENT_DEBIT') || $entity->getTranCode() ===  'Pay_employee') ? $this->getEmployeeText() : $this->getEmployerText(),
          'Email' => $employee ? $employee->getEmail() : null,
          'First Name' => $employee ? $employee->getFirstName() : null,
          'Last Name' => $employee ? $employee->getLastName() : null,
          'External Employee ID' => $employee ? $employee->getExternalId() : null,
          'Amount' => Money::formatWhen($entity->getTxnAmount()),
          'amount' => $entity->getTxnAmount() / 100,
          'Employee' => $employee ? $employee->toApiArray() : null,
          'Date & Time' => Util::formatDateTime($entity->getTxnTime() ? $entity->getTxnTime() : $entity->getCreatedAt(), Util::DATE_TIME_FORMAT, 'EST'),
          'Status' => ($entity->getAccountStatus() === 'syncPending' || $entity->getAccountStatus() === 'completed') ? 'Executed' : $entity->getAccountStatus(),
          'isExecuted' => $payoutId ? Data::get($this->cacheKey . $payoutId) : false
        ];
    }

    /**
     * @Route("/admin/mex/employer/payments/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function export(Request $request)
    {
    	$title = 'TransferMex Employees';
		if ($this->platform->isFaasPlatforms()) {
			$title = $this->cardProgram->getName() . 'Members';
		}
        return $this->commonExport($request, $title . ' Payments & Deposits', [
          'Payment Type' => 20,
          'Transfer To' => 20,
          'Email' => 35,
          'First Name' => 30,
          'Last Name' => 30,
          'External Employee ID' => 20,
          'Role Type' => 20,
          'Amount' => 20,
          'Date & Time' => 20,
          'Status' => 20,
        ]);
    }

    /**
     * @Route("/admin/mex/employer/payment/reverse", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function reverse(Request $request)
    {
        $this->authRoles([
          Role::ROLE_TRANSFER_MEX_EMPLOYER,
          Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
          Role::ROLE_FAAS_CLIENT,
          Role::ROLE_FAAS_CLIENT_ADMIN
        ]);
        if (Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $id = $request->get('id');
        $user = $this->getGroupAdminUser($this->user);
        $platform = Util::platform();
        // $uc = $user->getCurrentPlatformCard();
        $uc = EmployerService::getMexDummyCard($user);
        if (!$id) {
          return new FailedResponse('Unknown transaction!');
        }
        $uct = UserCardTransaction::find($id);
        if (!$uct) {
          return new FailedResponse('Unknown transaction!');
        }
        $payoutId = null;
        if (in_array($uct->getAccountStatus(), [EmployerPayout::PAYOUT_INIT, UserCardTransaction::STATUS_DUPLICATED]) && $uct->getTranCode() === 'Pay_employee') {
          $uct->setAccountStatus(UserCardTransaction::STATUS_CANCLED)->persist();
          // find employee payout and set status to cancel
          if (strpos($uct->getTranDesc(), 'Employee Payout ID:') !== false) {
            $payoutId = substr($uct->getTranDesc(), strpos($uct->getTranDesc(), 'Payout ID:') + 10, (strpos($uct->getTranDesc(), ';Employee') - strpos($uct->getTranDesc(), 'Payout ID:') - 10));
          } else if (strpos($uct->getTranDesc(), 'Member Payout ID:') !== false) {
            $payoutId = substr($uct->getTranDesc(), strpos($uct->getTranDesc(), 'Payout ID:') + 10, (strpos($uct->getTranDesc(), ';Member') - strpos($uct->getTranDesc(), 'Payout ID:') - 10));
          }

          /** @var EmployerPayout $payout */
          $payout = EmployerPayout::find($payoutId);
          if ($payout) {
            $payout->setStatus(EmployerPayout::PAYOUT_CANCEL)->persist();
          }
        }

        if (($uct->getAccountStatus() === EmployerPayout::PAYOUT_COMPLETE || $uct->getAccountStatus() === EmployerPayout::PAYOUT_SYNC_PENDING) && $uct->getTranCode() === 'Pay_employee') {
          $amount = Money::normalizeAmount($request->get('amount', 0), 'USD');
          if ($amount <= 0) {
              return new FailedResponse('Invalid amount!');
          }
          $employee = User::find($request->get('employee'));
          if (!$employee) {
            return new FailedResponse('Invalid ' . $this->getEmployeeText() . '!');
          }
          $employeeUc = $employee->getCurrentPlatformCard();
          $employeeOldBalance = $employeeUc->getBalance() ?: 0;
          if ($employeeOldBalance < $amount) {
            return new FailedResponse('The ' . $this->getEmployeeText() . ' balance is not enough to refund!');
          }


          $transaction = new UserCardTransaction();
          $transaction->setUserCard($uc)
                         ->setTxnAmount( $amount)
                         ->setStatus(EmployerPayout::PAYOUT_INIT)
                         ->setTranCode('Reverse_employee')
                         ->setProductName(UserCardTransaction::PRODUCT_TRANSFER_MEX)
                         ->setTxnTime(Carbon::now())
                         ->setTranDesc($this->getEmployeeText() . ':' . $employee->getId())
                         ->persist();

          $res = RapidService::ReversePayout($uct, $amount, $employee, $transaction, $platform->isFaasPlatforms());
          if (is_string($res)) {
            return new FailedResponse($res);
          }
          $transaction->setAccountStatus(EmployerPayout::PAYOUT_SYNC_PENDING)->persist();
          $uct->setAccountStatus(UserCardTransaction::STATUS_REVERSED)->persist();
        }
        if (Util::getImpersonatingUser()) {
          SlackService::alert(Util::getImpersonatingUser()->getFullName() . ' login as employer and try to reverse the payment.', [
              'employer' => $this->user->getEmployerName(),
              'employer Id' => $this->user->getId(),
              'uct' => $uct->getId()
          ], SlackService::GROUP_DEVS);
        }
        return new SuccessResponse();
    }

     /**
     * @Route("/admin/mex/employer/payments/re-execute-complote", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function reExecuteOrComplete(Request $request) {
        if (Util::meta($this->user, 'readOnlyAdmin')) {
          throw new DeniedException();
        }
        $id = $request->get('id');
        $type = $request->get('type');
        // $this->authRoles(Role::ROLE_MASTER_ADMIN);
        if (!$id) {
          return new FailedResponse('Unknown transaction!');
        }
        $uct = UserCardTransaction::find($id);
        if (!$uct) {
          return new FailedResponse('Unknown transaction!');
        }
        $payoutId = $this->getPayoutId($uct);
        // clear the cache flag, the script will execute the payment again
        if ($payoutId && $type === 'execute') {
          Data::set($this->cacheKey . $payoutId, false);
        }
        // update payment record status when the payment is complete
        if ($payoutId && $type === 'complete') {
          $payout = EmployerPayout::find($payoutId);
          $uct->setStatus(EmployerPayout::PAYOUT_SYNC_PENDING)->persist();
          $payout->setStatus(EmployerPayout::PAYOUT_COMPLETE)->persist();
        }
        // execute duplicated payment manually
        if ($payoutId && $type === 'repeat') {
          $payout = EmployerPayout::find($payoutId);
          $key = 'Employer_Pay_Employee' . $payout->getId();
          Data::set($key, false);
          $uct->setStatus(EmployerPayout::PAYOUT_INIT)->persist();
          $payout->setStatus(EmployerPayout::PAYOUT_INIT)->setPassCheckDuplicate(true)->persist();
          if(!Data::get('Mex_employer_pay_employee_confirm')) {
            Data::set('Mex_employer_pay_employee_confirm', 1, true);
            Service::sendAsync('/t/cron/mex/rapid/execute-all-pay-employee');
          }
        }
        return new SuccessResponse();
    }

    /**
     * @param UserCardTransaction $uct
     * @return int
     */
    protected function getPayoutId($uct) {
        $payoutId = null;
        if (strpos($uct->getTranDesc(), 'Employee Payout ID:') !== false) {
          $payoutId = substr($uct->getTranDesc(), strpos($uct->getTranDesc(), 'Payout ID:') + 10, (strpos($uct->getTranDesc(), ';Employee') - strpos($uct->getTranDesc(), 'Payout ID:') - 10));
        } else if (strpos($uct->getTranDesc(), 'Member Payout ID:') !== false) {
          $payoutId = substr($uct->getTranDesc(), strpos($uct->getTranDesc(), 'Payout ID:') + 10, (strpos($uct->getTranDesc(), ';Member') - strpos($uct->getTranDesc(), 'Payout ID:') - 10));
        }
        return $payoutId;
    }
}
