<?php
/**
 * Created by Vs Code.
 * User: Abel
 * Date: 2021/4/9
 * Time: 下午4:48
 */

namespace TransferMexBundle\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\AbstractQuery;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\UserTransferMexTrait;
use TransferMexBundle\Services\EmployerService;
use TransferMexBundle\Services\MemberService;
use CoreBundle\Utils\Log;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserGroup;
use TransferMexBundle\Entity\EmployerPayout;
use TransferMexBundle\Services\RapidAPI;
use TransferMexBundle\Services\RapidService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class EmployerDashboardController extends BaseController
{
	  use EmployerDashboardControllerTrait;
    use UserTransferMexTrait;
     /** @var Carbon */
     protected $earliest;
     protected $format = Util::DATE_FORMAT_MONTH_DASH;
    /** @var User */
    public $employer;
    /** @var UserGroup */
    public $group;
    /**
     * AgentsController constructor.
     */
      public function __construct()
      {
          parent::__construct();
          $this->tz = 'EST';//$this->user->getTimezone() ? $this->user->getTimezone() : date_default_timezone_get();

          // $this->tz = Util::tzUTC();
          $this->earliest = Carbon::create(2021, 9, 1, 0, 0, 0, $this->tz);
          $this->authRoles($this->getAccessibleRoles());
          $this->employer = $this->getGroupAdminUser($this->user);
          $this->group = $this->employer->getAdminGroup();
      }

      protected function getAccessibleRoles()
      {
          if ($this->user && in_array($this->user->getTeamName(), [
            Role::ROLE_TRANSFER_MEX_EMPLOYER,
            Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN
            ])) {
            $currentEmployerId = Util::meta($this->user, 'currentEmployer');
            $group = null;
            if ($currentEmployerId) {
              $currentEmployer = User::find($currentEmployerId);
              $group = $currentEmployer->getAdminGroup();
            } else {
              $group = $this->user->getAdminGroup();
            }
            if (!$group || $group->getFundingType() == UserGroup::FUNDING_TYPE_ACH) {
              return [];
            }
          }
          return [
              Role::ROLE_TRANSFER_MEX_EMPLOYER,
              Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN,
			        Role::ROLE_FAAS_CLIENT,
              Role::ROLE_FAAS_CLIENT_ADMIN,
              Role::ROLE_FAAS_CLIENT_AGENT
          ];
      }


    /**
     * @Route("/admin/mex/employer/employerBalanceChart", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getProgramBalanceChart(Request $request) {
     return $this->traitGetProgramBalanceChart($request);
    }

    /**
     * @Route("/admin/mex/employer/dashboard/static", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function getStaticData(Request $request) {
		return $this->traitGetStaticData($request);
    }

   /**
     * @Route("/admin/mex/employer/refresh-agent-balance")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function refreshAgentBalanceAction(Request $request)
    {
        $balance = EmployerService::getAvailableBalance($this->user, true);
        return new SuccessResponse($balance);
    }
}
