<?php

namespace TransferMexBundle\Controller;

use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Exception\FailedException;
use CoreBundle\Exception\NotLoggedInException;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;

class BaseController extends \PortalBundle\Controller\Common\BaseController
{
    /** @var User */
    public $user;

    public $protected = true;

    public $tz;

    public $acceptNoTokenRoute = ['location'];


    /**
     * BaseController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        if (!Util::$platform) {
            Util::$platform = Platform::get(Platform::NAME_TERN_COMMERCE);
        }
        $this->platform = Util::$platform;

        if (!Util::isCommand() && !$this->platform->isTransferMex() && !$this->platform->isFaasPlatforms()) {
            throw PortalException::tempPage('Access denied to ' . Platform::NAME_TRANSFER_MEX);
        }

        if (!Util::$cardProgram) {
            Util::$cardProgram = CardProgram::transferMexUSD();
        }
        $this->cardProgram = Util::$cardProgram;

        if ($this->protected && !Util::isCommand()) {
            $this->user = $this->getUser();
            $route = Util::request()->query->get("route");
            if (!$this->user && !in_array($route, $this->acceptNoTokenRoute)) {
                throw new NotLoggedInException();
            }

        }

        $this->tz = Util::tzNewYork();
    }

    protected function validatePinTokenCode(Request $request)
    {
        if ($this->user->isMasterAdmin()) {
            return;
        }

        $token = $this->getPostData('token');
        if (!$token) {
            throw new FailedException('Unknown PIN token!');
        }
        $verified = $this->user->validatePinToken($token);
        if (!$verified) {
            throw new FailedException('Invalid PIN code!');
        }
    }



    public function createCacheKey($request, $method = '', $prefix = 'CORE')
    {
        $p = $request->query->all();
        $cacheKey = $prefix . '.' . $method . '.' . str_replace('/', '.', $request->getPathInfo());
        $cacheKey = str_replace('..', '.', $cacheKey);
        $cacheKey = ltrim($cacheKey, '.');
        foreach ($p as $key => $value) {
            if ($key != '_t' && $key != 'force') {
                try {
                    $cacheKey = $cacheKey . '.' . $key . '.' . $value;
                } catch (\Exception $e) {
                    $cacheKey = $cacheKey . '.' . $key . '.' . implode(".", $value);
                }
            }
        }

        return $cacheKey;
    }
}
