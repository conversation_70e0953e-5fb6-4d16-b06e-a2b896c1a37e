<?php


namespace TransferMexBundle\Controller\Mobile;


use CoreBundle\Entity\Transfer;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Services\IntermexRemittanceService;
use TransferMexBundle\Services\UniTellerRemittanceService;

class TransactionController extends BaseController
{
	use TransactionControllerTrait;

    /**
     * @Route("/mex/m/transaction/list/{page}/{pageSize}")
     * @param Request $request
     * @param int     $page
     * @param int     $pageSize
     *
     * @return FailedResponse|SuccessResponse
     */
    public function list(Request $request, $page = 1, $pageSize = 30)
    {
		$data = $this->traitList($request, $this->user, $page, $pageSize);
		return new SuccessResponse($data);
    }

    /**
     * @Route("/mex/m/transaction/{id}/detail")
     * @param $id
     *
     * @return FailedResponse|SuccessResponse
     */
    public function detail($id)
    {
        $transfer = Transfer::find($id);
        if (!$transfer) {
          return new DeniedResponse();
        }
        if ($transfer->getStatus() == Transfer::STATUS_CREATED && $transfer->getPartner() === Transfer::PARTNER_UNITELLER) {
          // update uniteller status first
          try {
            [$ei, $data] = UniTellerRemittanceService::getTransactionDetail($transfer->getSender(), $transfer->getPartnerId());
            if ($ei && $ei->isFailed()) {
              throw new PortalException('Update transfer detail error!');
            }
            $data = $data['transactionDetail'] ?? [];
            UniTellerRemittanceService::updateTransferByPayout($data, $transfer);
            // sync the status to UniTeller
            if ($data['transactionStatus'] == Transfer::UNITELLER_STATUS_CANCELPENDING && !Util::meta($transfer, 'unitellerCancelPending')) {
              UniTellerRemittanceService::updateTransactions('VOID', $transfer);
              Util::updateMeta($transfer, [
                'unitellerCancelPending' => true
              ]);
            }
          } catch (\Exception $ex) {
            Log::debug('Failed to update UniTeller transfer ' . $transfer->getId() . ' detail: ' . $ex->getMessage());
          }
          $transfer = Transfer::find($id);
        }
        if (($transfer->getStatus() == Transfer::STATUS_CREATED || Util::isStaging()) && $transfer->getPartner() === Transfer::PARTNER_INTERMEX) {
          // update Intermex status first
          $needAddCompliance1025Form = Util::meta($transfer, 'needAddCompliance1025Form');
          try {
            // add 1025 auto
            $res = false;
            if ($needAddCompliance1025Form) {
              $complianceError = null;
              $attachment = IntermexRemittanceService::createForm1025($transfer);
              if ($attachment) {
                $params = [
                  'pinNumber' => $transfer->getPartnerId(),
                  'documentType' => 'COMP_FORM1025',
                  'documentImages' => [Util::getFileBase64($attachment->getPath())]
                ];
                $res = IntermexRemittanceService::addComplianceFile($params, $complianceError);
              }
              Util::updateMeta($transfer, [
                'needAddCompliance1025Form' => !$res,
                'compliance1025FormError' => $complianceError,
              ]);
            }
              IntermexRemittanceService::updateTransfer($transfer);
            } catch (\Exception $ex) {
            Log::debug('Failed to update Intermex transfer ' . $transfer->getId() . ' detail: ' . $ex->getMessage());
          }
          $transfer = Transfer::find($id);
        }
        $data = $transfer->toApiArray(true);
        $data['type'] = 'transfer';
        return new SuccessResponse($data);
    }
}
