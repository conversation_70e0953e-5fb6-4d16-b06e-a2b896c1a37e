<?php


namespace TransferMexBundle\Controller\Mobile;


use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\Location;
use TransferMexBundle\Entity\LocationRequest;
use TransferMexBundle\Services\UniTellerRemittanceService;
use CoreBundle\Entity\Transfer;
use TransferMexBundle\Entity\Recipient;

class LocationController extends BaseController
{
    public $protected = false;

    /**
     * @Route("/mex/m/location/list")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function list(Request $request)
    {
        $page = (int)$request->get('page_number', 1);
        $size = (int)$request->get('page_size', 50);
        $latitude = (float)$request->get('latitude', '19.39068');
        $longitude = (float)$request->get('longitude', '-99.2836992');
        $radius = (float)$request->get('radius', 10);
        $merchant = (string)$request->get('merchant');
        $location = (string)$request->get('location');
        $address = (string)$request->get('address');
        $transferId = $request->get('transfer');
        $transfer = null;
        if ($transferId) {
            $transfer = Transfer::find($transferId);
        }
        $query = $this->em->getRepository(Location::class)
            ->createQueryBuilder('l')
            ->join('l.state', 's')
            ->select('l as loc, s.name as state,
                SQRT(
                    POW(69.1 * (l.latitude - :latitude), 2) +
                    POW(69.1 * (:longitude - l.longitude) * COS(l.latitude / 57.3), 2)
                ) AS distance
            ')
            ->where('l.status = :status')
            ->andWhere(Util::expr()->eq('l.partner', ':partner'));
        if (!empty($merchant)) {
            $query = $query->andWhere(Util::expr()->like('l.name', ':merchant'))->orWhere(Util::expr()->like('l.payer', ':merchant'))->setParameter('merchant', '%' . $merchant . '%');
            foreach (UniTellerRemittanceService::CASH_PICKUP_NAME_LIST as $key => $value) {
                if (strpos($value, $merchant) !== false) {
                    $query = $query->orWhere(Util::expr()->like('l.payer', ':value'))->setParameter('value', '%' . $value . '%');
                }
            }

        }

        if (!empty($location)) {
            $query = $query->andWhere(Util::expr()->like('l.name', ':location'))->orWhere(Util::expr()->like('l.street', ':location'))
                ->orWhere(Util::expr()->like('l.city', ':location'))->orWhere(Util::expr()->like('s.name', ':location'))->setParameter('location', '%' . $location . '%');
        }
        if (!empty($address)) {
            $query = $query->andWhere(Util::expr()->like('l.name', ':address'))->orWhere(Util::expr()->like('l.street', ':address'))
                ->orWhere(Util::expr()->like('l.city', ':address'))->orWhere(Util::expr()->like('s.name', ':address'))->setParameter('address', '%' . $address . '%');
        }


        if ($transfer && $transfer->getPartner() == Transfer::PARTNER_UNITELLER) {
            $user = $transfer->getRecipient();
            /** @var Recipient $recipient */
            $recipient = $user->ensureTransferMexRecipient();
            $query = $query->andWhere('l.payer = :payer')
                ->setParameter('payer', $recipient->getPayoutMethodType());
            if (in_array($recipient->getPayoutMethodType(), ['INTERMEX', 'WLD97'])) {
                $query = $query->andWhere('l.exterior = :exterior')
                    ->setParameter('exterior', Util::meta($recipient, 'payerBranch'));
            }
        }

        $rs = $query->having('distance < :distance')
            ->orderBy('distance')
            ->setParameter('latitude', $latitude)
            ->setParameter('longitude', $longitude)
            ->setParameter('distance', $radius)
            ->setParameter('status', 'active')
            ->setParameter('partner', 'UniTeller')
            ->setFirstResult(($page - 1) * $size)
            ->setMaxResults($size)
            ->getQuery()
            ->getArrayResult();

        $data = [];
        foreach ($rs as $r) {
            $loc = $r['loc'];
            $data[] = [
                'id' => '' . $loc['id'],
                'alias' => $loc['name'],
                'latitude' => (float)$loc['latitude'],
                'longitude' => (float)$loc['longitude'],
                'address' => [
                    'alias' => $loc['name'],
                    'full_name' => $loc['name'],
                    'address_line1' => $loc['street'],
                    'address_line2' => '',
                    'city' => $loc['city'],
                    'state' => $r['state'],
                    'zip_code' => $loc['zip'],
                    'phone_number' => $loc['phone'] ?? '',
                    'country' => 'Mexico',
                ],
                'currency_code' => 'MXN',
                'distance' => round((float)$r['distance'], 2),
                'location_type' => 'Active',
                'partner' => $loc['payer'],
                'logo' => isset(UniTellerRemittanceService::CASH_PICKUP_LOGO_LIST[$loc['payer']]) ? 'static/mex/img/uniteller/' . UniTellerRemittanceService::CASH_PICKUP_LOGO_LIST[$loc['payer']] : '',
                'name' => isset(UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$loc['payer']]) ? UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$loc['payer']] : $loc['payer'],
                'partnerType' => $loc['partner'],
                'exterior'    => $loc['exterior'],
                'hours'        => $loc['hours'],
                'hoursWeekend' => $loc['hoursWeekend']
            ];
        }

        return new SuccessResponse($data);
    }

    /**
     * @Route("/mex/m/location/request")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function request(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();

        $code = $request->get('postalCode');
        if (!$code) {
            return new FailedResponse('Empty postal code!');
        }
        $lr = new LocationRequest();
        $lr->setRequestAt(new \DateTime())
            ->setRequestBy($this->user)
            ->setPostalCode($code);
        Util::persist($lr);
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/merchant/names")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function merchantNames(Request $request)
    {
        $result = $this->em->getRepository(Location::class)->createQueryBuilder('l')
            ->select('DISTINCT(l.payer)')->andWhere('l.partner = :partner')
            ->setParameter('partner', 'UniTeller')->getQuery()->getArrayResult();
        $values = array_map(function ($item) {
            return array_values($item)[0];
        }, $result);
        $names = [];
        foreach ($values as $row) {
          if (isset(UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$row]) ) {
            $names[] = UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$row];
          }
          // $names[] = isset(UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$row]) ? UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$row] : $row;
        }
        return new SuccessResponse($names);
    }
}
