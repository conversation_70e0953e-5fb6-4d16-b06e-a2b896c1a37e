<?php


namespace TransferMexBundle\Controller\Mobile;


use Carbon\Carbon;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Processor;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Transfer;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\APIServices\TwilioService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use DateTime;
use FaasBundle\Services\BOTM\BotmAPI;
use mikehaertl\wkhtmlto\Pdf;
use MobileBundle\MobileBundle;
use PortalBundle\Exception\PortalException;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\EmployeePromo;
use TransferMexBundle\Entity\UniTellerPayers;
use TransferMexBundle\Services\IntermexRemittanceService;
use TransferMexBundle\Services\MemberService;
use TransferMexBundle\Services\RapidService;
use TransferMexBundle\Services\RapydAPI;
use TransferMexBundle\Services\RapydDisburseService;
use TransferMexBundle\Services\SlackService;
use TransferMexBundle\Services\TransactionService;
use TransferMexBundle\Services\TransferPrepareService;
use TransferMexBundle\Services\TransferReviewService;
use TransferMexBundle\Services\TransferService;
use TransferMexBundle\Services\UniTellerRemittanceService;
use TransferMexBundle\TransferMexBundle;

class TransferController extends BaseController
{
    protected function checkTransferEnabled($type = 'bank', $checkIntermex = false, $isUniTellerBank = false)
    {
        if ($checkIntermex && !TransferMexBundle::isIntermexEnabled()) {
          $msg = 'The intermex transfer service is disabled. Please retry later.';
          throw new FailedException($msg);
        }

        $msg = 'The transfer service is disabled. Please retry later.';
        if ($type === 'bank' && ! TransferMexBundle::isTransferEnabled() && !$checkIntermex && !$isUniTellerBank) {
            $msg = 'This feature is currently being updated. Please use the cash pickup option in the interim.';
            throw new FailedException($msg);
        }

        if (($type === 'cash' || ($type === 'bank' && $isUniTellerBank)) && ! TransferMexBundle::isCashPickupTransferEnabled()) {
          $msg = 'The UniTeller transfer service is disabled. Please retry later.';
          throw new FailedException($msg);
        }

        $group = $this->user->getPrimaryGroup();
        if ($group && ! $group->getEnableTransfer()) {
            throw new FailedException($msg);
        }
    }

    /**
     * @Route("/mex/m/transfer/init")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function init(Request $request)
    {
        $this->validateAppVersion();

        $uc = $this->user->getCurrentPlatformCard();
//        if ($uc->isRapidCard()) {
//            return new FailedResponse('Sorry that the old card is not supported to create transfers anymore. Please migrate to the new card.');
//        }
        $recipient = User::find($request->get('recipient_id'));
        return new SuccessResponse([
            'payoutOptions' => RapydDisburseService::getPayoutOptions(uc: $uc, recipient: $recipient),
            'payoutOptionsUniTeller' => UniTellerRemittanceService::getPayoutOptions($recipient),
            'isNextTransferFree' => MemberService::checkIsNextTransferFree($this->user),
            'isBankFree' => MemberService::checkTransferFree($this->user, 'bank'),
            'isCashFree' => MemberService::checkTransferFree($this->user, 'cash')
        ]);
    }

    /**
     * @Route("/mex/m/transfer/get-cost-fee", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function getCostAndFee(Request $request)
    {
        $recipient = User::find($request->get('id'));
        if (!$recipient || !$recipient->inTeam(Role::ROLE_TRANSFER_MEX_RECIPIENT)) {
            return new FailedResponse('Unknown recipient!');
        }
        $rr = $recipient->ensureTransferMexRecipient();


        if (!$rr->getPayoutType()) {
            return new FailedResponse('The recipient\'s payout options are not configured yet. Please edit the recipient details first.');
        }

        if (!$rr->getPayoutMethodType()) {
            return new FailedResponse('The recipient\'s payout options are not configured yet. Please edit the recipient details first.');
        }
        
        $amount = (double)$request->get('amount');
        if (!$amount || $amount <= 0) {
            return new FailedResponse('Invalid transfer amount!');
        }

        if ($rr->getIsIntermex()) {
          $payoutOptions = IntermexRemittanceService::getPayoutOptions($recipient);
        } else {
          $payoutOptions = UniTellerRemittanceService::getPayoutOptions($recipient);
          // get state fee
          $feeList = UniTellerRemittanceService::ExternalQuickQuotes($this->user, $rr, $amount);

          $payoutOptions['stateFee'] = Money::normalizeAmount($feeList['stateFee'], 'USD'); // $feeList['stateFee'] * 100;
        }
        return new SuccessResponse([
            // 'transfer' => $t->toApiArray(true),
            'payoutOptionsUniTeller' => $payoutOptions,
        ]);
    }

     /**
     * @Route("/mex/m/transfer/get-rate", methods={"GET"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function getRateByRecipient(Request $request)
    {
        $id = $request->get('id');
        if ($id) {
            $recipient = User::find($request->get('id'));
            if (!$recipient || !$recipient->inTeam(Role::ROLE_TRANSFER_MEX_RECIPIENT)) {
                return new FailedResponse('Unknown recipient!');
            }
        } else {
            $recipient = null;
        }

        $payoutOptions = IntermexRemittanceService::getPayoutOptions($recipient);

        return new SuccessResponse([
            // 'transfer' => $t->toApiArray(true),
            'payoutOptionsIntermex' => $payoutOptions,
        ]);
    }

    /**
     * @Route("/mex/m/transfer/create")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function create(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->disableWebPortalAccess();
        $this->validatePasscode($request);
        $this->validateSecureSession();
        $this->validateEmployerMemberStatus($this->user);

		    // return new FailedResponse('The transfer service is in maintenance now. Please try again later.');

        $recipient = User::find($request->get('recipient'));
        if (!$recipient || !$recipient->inTeam(Role::ROLE_TRANSFER_MEX_RECIPIENT)) {
            return new FailedResponse('Unknown recipient!');
        }

        if ( $this->validatePendingTransfer($this->user, $recipient)) {
            return new FailedResponse('The recipient has a pending transfer, please finish this first.');
        }

        $rr = $recipient->ensureTransferMexRecipient();
        if (!$rr->getPayoutMethodType()) {
            return new FailedResponse('The recipient\'s payout options are not configured yet. Please edit the recipient details first.');
        }
        // add city before creating transfer
        if ($rr->getIsIntermex()) {
          $city = $recipient->getCity();
          if ( $recipient->getMethodsGroup()) {
            $receiver = $recipient->getMethodsGroup()->getPrimaryAdmin();
            $city = $receiver->getCity();
          }
          if (preg_match('/[^\x{0020}-\x{0080}:;$#!<>&@+*%\[\]\/^,."]+/', $city) || strlen($city) >= 40) {
            throw PortalException::temp('The recipient\'s city cannot contain the special character and cannot exceed 40 characters, please use only letters (A-Z), numbers (0-9), and standard punctuation. Special characters like , #, $, ñ, etc., are not allowed!');
          }
        }

        if (((!Util::meta(Util::platform(), 'enableIntermexCash') || Util::meta($this->user, 'disableIntermexCash')) && $rr->getPayoutType() == 'Cash Pickup' && $rr->getIsIntermex())
          || ((!Util::meta(Util::platform(), 'enableUniTellerCashPickup') || Util::meta($this->user, 'disableCashPickup')) && $rr->getPayoutType() == 'Cash Pickup' && !$rr->getIsIntermex())
          || ((!Util::meta(Util::platform(), 'enableIntermexBank') || Util::meta($this->user, 'disableIntermexBank')) && $rr->getPayoutType() == 'Bank Transfer' && $rr->getIsIntermex() && !$rr->getIsUniTellerBank())
          || ((!Util::meta(Util::platform(), 'enableRapydBank') || Util::meta($this->user, 'disableRapydBank')) && $rr->getPayoutType() == 'Bank Transfer' && !$rr->getIsIntermex() && !$rr->getIsUniTellerBank())
          || (((!Util::meta(Util::platform(), 'enableUniTellerBank') && !$this->checkEmployerUniTellerBankMethod($this->user)) || Util::meta($this->user, 'disableUniTellerBank')) && $rr->getPayoutType() == 'Bank Transfer' && $rr->getIsUniTellerBank() && !$rr->getIsIntermex())
        ) {
          throw PortalException::temp('The recipient\'s payout method is no longer supported. Please edit the recipient details first.');
        }
        if ($rr->getIsIntermex() && (!$recipient->getAddress() || !$recipient->getState() || !$recipient->getCity())) {
          return new FailedResponse('The recipient\'s address options of this method are not configured yet. Please edit the recipient details first.');
        }
        $this->checkTransferEnabled(Transfer::parsePayoutType($request->get('transferMethod')), $rr->getIsIntermex(), $rr->getIsUniTellerBank());

        $methodType = $request->get('methodType') ?: $rr->getPayoutMethodType();
        if (TransferMexBundle::isTransferInstantEnabled()) {
          TransferService::precheck($rr, $methodType, $rr->getIsIntermex());
        }

        if (TransferMexBundle::isIntermexTransferInstantEnabled()) {
          TransferService::precheck($rr, $methodType, $rr->getIsIntermex());
        }

        $amount = (double)$request->get('amount');
        if (!$amount || $amount <= 0) {
            return new FailedResponse('Invalid transfer amount!');
        }
        $uniTellerTransferKey = 'uniteller_transfer_to_' .str_replace(' ', '_', $recipient->getCompleteFullName()) . '_from_' . $this->user->getId();
        if (Util::isLive() && Util::meta($rr, 'useUniTeller') && Data::get($uniTellerTransferKey)) {
          return new FailedResponse('The '. $recipient->getCompleteFullName() . ' has a pending transfer, please finish this first.');
        }
        $payoutType = Transfer::parsePayoutType($request->get('transferMethod'));
        $uc = $this->user->getCurrentPlatformCard();
        if (!Util::isDev()) {
            if ($rr->getIsIntermex()) {
              $payer = $rr->getPayoutMethodType();
              $minAmount = IntermexRemittanceService::getMinTransferAmount($payoutType, $payer,  $recipient->getCountry()->getIsoCode()) ?? 4800;
              $maxAmount = TransferMexBundle::getIntermexMaxTransferAmount();
            } else {
              if (Util::meta($rr, 'useUniTeller')) {
                $payer = $rr->getPayoutMethodType();
                $minAmount = UniTellerRemittanceService::getMinAmountByType($payer, $payoutType, $recipient->getCountry()->getIsoCode());
                $uniTellerMax = Data::get('uniteller_cash_pickup_' . $payer . '_max_limit');
                $maxAmount =  $uniTellerMax ?  $uniTellerMax * 100 : TransferMexBundle::getMaxUniTellerTransferAmount();
              } else {
                $minAmount = TransferMexBundle::getMinTransferAmount($uc);
                $maxAmount = TransferMexBundle::getMaxTransferAmount();
              }
            }
            if ($amount < $minAmount) {
                return new FailedResponse('The minimal transfer amount is ' . Money::formatUSD($minAmount) . ' (Updated)!');
            }

            if ($amount > $maxAmount) {
                return new FailedResponse('The maximal transfer amount is ' . Money::formatUSD($maxAmount) . '!');
            }
            $date =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT, 'America/New_York');
            $month =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT_MONTH, 'America/New_York');
            $dailyLimit = Config::get('max_transfer_amount_limit_daily', 250000);
            $monthlyLimit = Config::get('max_transfer_amount_limit_monthly', 1000000);
            $dailyAmountKey = 'daily_transfer_amount_' . $this->user->getId() . '_' . $date;
            $monthlyAmountKey = 'monthly_transfer_amount_' . $this->user->getId() . '_' . $month;
            $dailyAmount = Data::get($dailyAmountKey) ?? 0;
            if (($amount + $dailyAmount) > $dailyLimit) {
                return new FailedResponse('The maximal daily transfer amount is ' . Money::formatUSD($dailyLimit) .
                                          ' while you have transferred ' . Money::formatUSD($dailyAmount) . ' !');
            }
            $monthlyAmount = Data::get($monthlyAmountKey) ?? 0;
            if (($amount + $monthlyAmount) > $monthlyLimit) {
                return new FailedResponse('The maximal monthly transfer amount is ' . Money::formatUSD($monthlyLimit) .
                                          ' while you have transferred ' . Money::formatUSD($monthlyLimit) . ' !');
            }

        }

        TransferPrepareService::updateBalanceAndStatus($uc);


        if ($payoutType === 'bank' && !$recipient->getBirthday() && TransferReviewService::everHadHoldPayoutForRecipient($recipient)) {
            return new FailedResponse('Please fill in the full name and birthday on the recipient detail page and try again, because the recipient had Hold payouts before.');
        }

        $disablePayerList = UniTellerPayers::getDisableList($payoutType);
        if (Util::meta($rr, 'useUniTeller') && in_array($methodType, $disablePayerList)) {
          return new FailedResponse('Sorry, We no longer support this merchant, please edit the recipient and choose another merchant.');
        }
        $amount = (int)$amount;
        // use intermex as the trnsfer provider
        if ($rr->getIsIntermex()) {
          if (MemberService::checkIsNextTransferFree($this->user))  {
            $costFee = 0;
          } else {
            $costFee = MemberService::checkTransferFree($this->user, $payoutType) ? 0 : IntermexRemittanceService::feeByType($payoutType, $rr->getUser()->getCountry()->getIsoCode());
          }
          // check the payer is available
          if (!IntermexRemittanceService::checkPayerIsAvailable($recipient, $methodType, $payoutType)) {
            return new FailedResponse('Sorry, We no longer support this payer, please edit the recipient and choose another payer.');
          }
          $fee = $costFee;
          if (Config::get('use_intermex_rate_version_1', 1)) {
            $rate = IntermexRemittanceService::getDailyRate(true, $recipient->getCountry());
          } else {
            $rate = $rr->getCurrency() == 'USD' ? 1 :IntermexRemittanceService::getDailyRateV2($rr, true);
          }
          $payoutOptions = IntermexRemittanceService::getPayoutOptions($recipient);
          $payoutOptions['stateFee'] = 0;
          if ($rr->getCurrency() == 'USD') {
            $payoutOptions['dailyRate'] = 1;
            $payoutOptions['intermex_real_rate'] = 1;
          }
          $meta = [
              'payoutOptions' => $payoutOptions,
              'fxRateMarkup' => [
                $rr->getCurrency() == 'USD' ? 0: IntermexRemittanceService::RATE_MARK_UP_PARTNER,
                IntermexRemittanceService::RATE_MARK_UP_PLATFORM,
              ],
          ];
        } else {
          if (Util::meta($rr, 'useUniTeller')) {
              $feeList = UniTellerRemittanceService::ExternalQuickQuotes($this->user, $rr, $amount);

              if (MemberService::checkIsNextTransferFree($this->user))  {
                $costFee = 0;
              } else {
                $costFee = MemberService::checkTransferFree($this->user, $payoutType) ? 0 : UniTellerRemittanceService::feeByType($payoutType, $rr->getUser()->getCountry()->getIsoCode());
              }
              $fee = $costFee + $feeList['stateFee'] * 100;

              $rate = $rr->getCurrency() == 'USD' ? 1 : UniTellerRemittanceService::getDailyRate(true, $recipient->getCountry()->getIsoCode(),  $recipient->getCountry()->getCurrency());
              $payoutOptions = UniTellerRemittanceService::getPayoutOptions($recipient);
              $payoutOptions['stateFee'] = $feeList['stateFee'] * 100;
              if ($rr->getCurrency() == 'USD') {
                $payoutOptions['dailyRate'] = 1;
                $payoutOptions['intermex_real_rate'] = 1;
              }
              $meta = [
                  'payoutOptions' => $payoutOptions,
                  'fxRateMarkup' => [
                      $rr->getCurrency() == 'USD' ? 0 : UniTellerRemittanceService::RATE_MARK_UP_PARTNER,
                      UniTellerRemittanceService::RATE_MARK_UP_PLATFORM,
                  ],
              ];

          } else {
              if (MemberService::checkIsNextTransferFree($this->user)) {
                $fee = 0;
              } else {
                $fee = MemberService::checkTransferFree($this->user, $payoutType) ? 0 : RapydDisburseService::feeByType($payoutType);
              }
              $rate = RapydDisburseService::getDailyRate(true);
              $payoutOptions = RapydDisburseService::getPayoutOptions(uc: $uc);
              $meta = [
                  'payoutOptions' => $payoutOptions,
                  'fxRateMarkup' => [
                      $rr->getCurrency() == 'USD' ? 0 :RapydDisburseService::getPartnerRateMarkup(),
                      RapydDisburseService::getPlatformRateMarkup(),
                  ],
              ];
          }
        }

        TransferPrepareService::validateBalance($uc, $amount + $fee);

        $rateKey = 'mex_create_transfer_rate_' . $this->user->getId();
        if (Util::isLive() && Data::lockedHas($rateKey, 300, true)) {
            return new FailedResponse('Please create a new transfer after 5 minutes.');
        }
        Data::set($rateKey, true, true, 300);

        if (!Util::isLive()) {
            $meta['__sandbox'] = true;
        }

        $isTransferFree = MemberService::checkIsNextTransferFree($this->user) || MemberService::checkTransferFree($this->user, $payoutType, true);

        $t = new Transfer();
        $partner = Util::meta($rr, 'useUniTeller') ? Transfer::PARTNER_UNITELLER : Transfer::PARTNER_RAPYD;
        $cost = Util::meta($rr, 'useUniTeller') ? UniTellerRemittanceService::costByType($payoutType, $methodType, $recipient->getCountry()->getIsoCode()) : RapydDisburseService::costByType($payoutType);
        $revenue = Util::meta($rr, 'useUniTeller') ? UniTellerRemittanceService::revenueByType($payoutType, $amount, $methodType, $rr, $isTransferFree) : RapydDisburseService::revenueByType($payoutType, $amount, $t, $isTransferFree);
        $platformRevenue = Util::meta($rr, 'useUniTeller') ? UniTellerRemittanceService::platformRevenueByType($amount, $t) : RapydDisburseService::platformRevenueByType($amount, $t);
        if ($rr->getIsIntermex()) {
          $partner = 'intermex';
          $cost = IntermexRemittanceService::costByType($payoutType, $methodType, $recipient->getCountry()->getIsoCode());
          $revenue = IntermexRemittanceService::revenueByType($payoutType, $amount, $methodType, $rr, $isTransferFree);
          $platformRevenue = IntermexRemittanceService::platformRevenueByType($amount, $t);
        }
        $t->setSender($this->user)
            ->setSenderCard($uc)
            ->setRecipient($recipient)
            ->setPartner($partner)
            ->setStatus(Transfer::STATUS_PENDING)
            ->setPayoutType($payoutType)
            ->setPayoutMethodType($methodType)
            ->setSendAmount($amount)
            ->setSendCurrency('USD')
            ->setPayoutAmount((int)round($amount * $rate))
            ->setPayoutCurrency($rr->getCurrency())
            ->setReceiveCurrency($rr->getCurrency())
            ->setTransferFee((int)round($fee))
            ->setFromAccountNumber($uc->getTransferFromAccountNumber())
            ->setFxRate($rate)
            ->setCost($cost)
            ->setRevenue($revenue)
            ->setPlatformRevenue($platformRevenue)
            ->setMeta(Util::j2s($meta))
            ->persist()
        ;
        TransactionService::updateTransactionByTransfer($t);

        try {
          self::doConfirmTransfer($t);
        } catch (\Throwable $exception) {
            $msg = $t->getError() ?: 'Failed to create transfer!';
            if (strpos($msg, 'Error occurred when call the rapyd API') !== false) {
                return new FailedResponse($t->getFilteredError());
            }
            if (strpos($msg, 'Error occurred when call the UniTeller API') !== false) {
              return new FailedResponse('Sorry that the operation failed due to an error (code: 06). We are investigating. Please reach out to our support if it failed constantly.');
            }
            throw $exception;
        }

        if ($cost == 0) {
          SlackService::warning('This transaction fee is 0, please check!', [
            'transfer' => $t->getId(),
            'sender' => $this->user->getSignature(),
            'recipient' => $t->getRecipient()->getSignature(),
            'amount' => Money::format($t->getSendAmount(), $t->getSendCurrency()),
          ], [
            SlackService::MENTION_ABEL,
            SlackService::MENTION_HANS,
          ]);
        }
        if ($isTransferFree) {
          if (!MemberService::checkIsNextTransferFree($this->user, $t)) {
            $promo = MemberService::checkTransferFree($this->user, $payoutType, true);
            if ($promo && $promo !== true) {
              $promo->setTransfer($t)
                    ->setStatus(Util::meta($rr, 'useUniTeller') ? EmployeePromo::STATUS_PENDING : EmployeePromo::STATUS_COMPLETE)->persist();
              Util::updateMeta($t, [
                'employeePromoId' =>  $promo->getId(),
                'promoType' =>  $promo->getType()
              ]);
            }
            $tz = Util::tzNewYork();
            $now = Carbon::now($tz);
            if ($promo === true && $now < Carbon::create(2024,4,30,23,59,59,$tz)) {
              $date =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT, 'America/New_York');
              $feeKey = 'transfer_free_for_mirage_' . $date . '_' . $this->user->getId();
              $feeCountOnOneDay = Data::get($feeKey) ? (int)Data::get($feeKey) : 0;
              Data::set($feeKey, $feeCountOnOneDay + 1);
            }
          } else {
            $context = UniTellerRemittanceService::getTransferContext($t);
            $type = $t->getPayoutTypeName();;
            if ($t->isIntermex()) {
              $type = $type . '(Intermex)';
            }
            if ($t->isUniTeller() && $type == 'Bank Transfer') {
              $type = $type . '(UniTeller)';
            }
            $msg = 'Created First Free ' . $type;
            Util::updateMeta($t, [
              'promoType' =>  'first_transfer'
            ]);
            SlackService::tada($msg, $context);
          }
        }
        if (Util::meta($rr, 'useUniTeller')) {
          Data::set($uniTellerTransferKey, true, false, 60 * 30);
        }
        if (Util::isStaging()) {
          Log::debug('Create transfer success!');
        }
        return new SuccessResponse([
            'transfer' => $t->toApiArray(true),
            'payoutOptions' => $payoutOptions,
        ]);
    }

    /**
     * @Route("/mex/m/transfer/confirm/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function confirm(Request $request, Transfer $transfer)
    {
        $this->checkTransferEnabled($transfer->getPayoutType());
        $this->checkReadonlyWhenLoggingAs();
        $this->validateAppVersion();
        $this->validateEmployerMemberLevelStatus($this->user);

		if (!Util::eq($transfer->getSender(), $this->user)) {
            return new DeniedResponse();
        }
        if (!in_array($transfer->getStatus(), [
            Transfer::STATUS_PENDING,
            Transfer::STATUS_CONFIRMATION,
            Transfer::STATUS_QUEUED,
            Transfer::STATUS_PROCESSING,
        ])) {
            $singleKey = 'uniteller_confirm_payout_' . $transfer->getId();
            if (Data::has($singleKey)) {
              return new FailedResponse('This transaction is being confirmed, please refresh or try again later.');
            }
            return new FailedResponse('This transfer is not confirmable!');
        }

        $uc = $this->user->getCurrentPlatformCard();
        TransferPrepareService::updateBalanceAndStatus($uc);
        if (!$uc->isActive()) {
            return new FailedResponse('Your card is not active!');
        }

        if ($transfer->getPartner() === Transfer::PARTNER_RAPYD) {
            TransferPrepareService::validateBalance($uc, $transfer->getTotalAmount());
        }

        self::doConfirmTransfer($transfer);
        return new SuccessResponse($transfer->toApiArray(true));
    }

    /**
     * @return string available values: processing/queued/created/skipped
     */
    public static function doConfirmTransfer(Transfer $transfer, $force = false)
    {
        $uc = $transfer->getSenderCard();
        $sender = $uc->getUser();
        $singleKey = 'mex_transfer_flying_' . $sender->getId();
        if (Data::has($singleKey)) {
            SlackService::warning('Skip processing the transfer since there is already a processing one.', [
                'transfer' => $transfer->getId(),
                'sender' => $sender->getSignature(),
                'recipient' => $transfer->getRecipient()->getSignature(),
                'amount' => Money::format($transfer->getSendAmount(), $transfer->getSendCurrency()),
            ]);
            throw PortalException::temp('There is already a processing transfer. Please try again later.');
        }
        Data::set($singleKey, true, true, 600);

		    $created = false;
        if ($transfer->getPartner() === Transfer::PARTNER_RAPYD) {
            $amount = $transfer->getTotalAmount();
            $type = 'rapyd_transfer';
            $id = '' . $transfer->getId();
            $comment = 'Transfer ' . Money::formatWhen($amount) . ' in ' . $id;
            $status = $transfer->getStatus();
            $continue = in_array($status, [
                Transfer::STATUS_PENDING,
                Transfer::STATUS_QUEUED,
                Transfer::STATUS_PROCESSING,
            ]) || ($status === Transfer::STATUS_CONFIRMATION && $transfer->getPartnerId());
            if ($continue) {
                if ( ! in_array($status, [
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                ])) { // Queued/processing transfers already have funds debited.
                    try {
                        TransferPrepareService::prepare($transfer, $uc);

                        Util::em()->refresh($uc); // reload from database
                        [$balanceProcessor] = TransferPrepareService::validateBalance($uc, $amount);
                        $api = null;
                        if ($balanceProcessor === Processor::NAME_BOTM) { // Use BOTM instead of the current processor
                            $transfer->setFromAccountNumber($uc->getBotmTransferFromAccountNumber());
                            $api = new BotmAPI();
                        } else {
                            $transfer->setFromAccountNumber($uc->getTransferFromAccountNumber());
                        }
                        RapidService::updateBalanceBy($uc, -$amount, $type, $id, $comment, $transfer, api: $api);
                        Log::debug('Completed deducting transfer amount from balance: ' . $transfer->getId() . ' with processor ' . $balanceProcessor);
                    } catch (\Throwable $exception) {
                        $msg = 'Failed to deduct transfer amount from the card';
                        $transfer->setStatus(Transfer::STATUS_CANCELED)
                            ->setCancelAt(Carbon::now())
                            ->setError($msg . ': ' . $exception->getMessage())
                            ->fillEiFromException($exception)
                            ->persist();
                        TransactionService::updateTransactionByTransfer($transfer);
                        SlackService::exception($msg, $exception, [
                            'user' => $uc->getUser()->getSignature(),
                            'transfer' => $transfer->getId(),
                        ]);
                        Data::push('failed_transfer_deduction', $transfer->getId());
                        Data::del($singleKey);
                        throw $exception;
                    }
                    $transfer->setDeducted();
                }

                $instant = TransferMexBundle::isTransferInstantEnabled();
                if ( !$force && ! $instant) {
                    $transfer->setStatus(Transfer::STATUS_PROCESSING)
                        ->persist();
                    SlackService::info('Queue the transfer since the Rapyd instant transfer option is off.', [
                        'transfer' => $transfer->getId(),
                        'member' => $uc->getUser()->getSignature(),
                        'amount' => Money::formatUSD($amount),
                    ]);
                    Data::del($singleKey);
                    return 'processing';
                }

                $isMaintenance = TransferMexBundle::checkMaintenance(Transfer::PARTNER_RAPYD);
                if ($isMaintenance) {
                    $transfer->setStatus(Transfer::STATUS_PROCESSING)
                        ->persist();
                    SlackService::info('Queue the transfer since Rapyd is under maintenance.', [
                        'transfer' => $transfer->getId(),
                        'member' => $uc->getUser()->getSignature(),
                        'amount' => Money::formatUSD($amount),
                    ]);
                    Data::del($singleKey);
                    return 'processing';
                }

                $action = null;
                if (in_array($status, [
                    Transfer::STATUS_PENDING,
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                ])) {
                    $action = 'create';
                } else if ($status === Transfer::STATUS_CONFIRMATION && $transfer->getPartnerId()) {
                    $action = 'confirm';
                }
                $cacheKey = 'rapid_' . $action . '_payout_' . $id; // typo, should be rapyd_
                try {
                    Data::once($cacheKey, function () use ($transfer, $action, &$created) {
                        if ($action === 'create') {
                            RapydDisburseService::createPayout($transfer);
                            $created = true;
                        } else if ($action === 'confirm') {
                            RapydDisburseService::confirmPayout($transfer);
                            $created = true;
                        }
                    });
                } catch (\Throwable $exception) {
                    $msg = $exception->getMessage() ?: '';
                    $context = [
                        'error' => $msg,
                        'transfer' => $transfer->getId(),
                        'transfer_amount' => Money::format($transfer->getSendAmount(), $transfer->getSendCurrency()),
                        'member' => $transfer->getSender()->getSignature(),
                    ];

                    if (strpos($msg, 'NOT_ENOUGH_FUNDS') !== false) {
                        $transfer->setStatus(Transfer::STATUS_PROCESSING)
                            ->setError($msg)
                            ->persist();
                        TransactionService::updateTransactionByTransfer($transfer);

                        $context['wallet_balance'] = RapydDisburseService::getWalletBalance();
                        SlackService::alert('Added the failed payout to the queue.', $context);

                        Service::sendAsync('/t/cron/mex/rapyd/monitor-instant-transfer/alert');

                        Data::del($cacheKey);
                        Data::del($singleKey);
                        return 'queued';
                    }

                    $mentions = [];
                    if ( ! Util::containsSubString($msg, [
                        'ERROR_BANK_IDENTIFIER_MISMATCH',
                        'ERROR_CHECK_DIGIT_NOT_VALID',
                        'ERROR_CREATE_PAYOUT',
                        'ERROR_PAYOUT_AMOUNT_NOT_WITHIN_RANGE_OF_PAYOUT_METHOD_TYPE',
                        'ERROR_UNSUPPORTED_PAYOUT_CURRENCY_OR_COUNTRY',
                        'ERROR_UPDATE_FUNDS',
                        'INVALID_AMOUNT',
                        'SERVICE_TEMPORARILY_UNAVAILABLE',
                    ])) {
                        $mentions = [
                            SlackService::MENTION_ABEL,
                            SlackService::MENTION_HANS,
                        ];
                    }
                    SlackService::alert('Failed to create/confirm the Rapyd payout.', $context, $mentions);

                    $transfer->setStatus(Transfer::STATUS_ERROR)
                        ->setCancelAt(Carbon::now())
                        ->setError($msg)
                        ->persist();
                    // return the promo if the transfer use
                    TransferService::updatePromo($transfer, true);
                    TransactionService::updateTransactionByTransfer($transfer);

                    // All the known statuses that we should refund it back to the sender immediately.
                    if ( ! Util::containsSubString($msg, [
                        'ERROR_BANK_IDENTIFIER_MISMATCH', // The request attempted an operation that requires both an account number and a bank identifier, but the bank portion of the account number does not match the bank identifier. The request was rejected. Corrective action: Verify the account number and bank identifier and retry.
                        'ERROR_CHECK_DIGIT_NOT_VALID', // The request attempted an operation with an alphanumeric identifier, but the check digit was not valid. The request was rejected. Corrective action: Verify the identifier and retry.
                        'ERROR_CREATE_PAYOUT', // The request tried to create a payout, but a required field was missing or a field had an incorrect value. The request was rejected. Corrective action: Run 'Get Payout Required Fields' and check all values.
                        'ERROR_PAYOUT_AMOUNT_NOT_WITHIN_RANGE_OF_PAYOUT_METHOD_TYPE', // The amount provided was not within the range of the payout method type. The request was rejected. Corrective action: Ensure that the amount provided is within the range of the payout method type.
                        'ERROR_UNSUPPORTED_PAYOUT_CURRENCY_OR_COUNTRY', // The request attempted a payout operation, but there was a problem with the country or currency, or a mismatch in entity types. The request was rejected. Corrective action: Run 'List Payout Method Types'. Use only supported countries, and use only currencies that are supported for each country. If you are using saved entities for payer or beneficiary, make sure the entity type in the request matches the entity type in the saved entity.
                        'ERROR_CREATE_PAYOUT_BENEFICIARY_COUNTRY_AND_BANK_COUNTRY_MISMATCH', // The request tried to create a payout, but the beneficiary’s country did not match the country of the bank. The request was rejected. Corrective action: Set 'beneficiary_country' to the country specified in the bank account details.
                        'ERROR_UPDATE_FUNDS', // The request tried to add funds to or remove funds from a wallet, but the wallet was not found. The request was rejected. Corrective action: Set 'ewallet' to the ID of a valid wallet that has not been blocked or deleted. The ID is a string starting with 'ewallet_'.
                        'INVALID_AMOUNT', // The request attempted an operation that requires an amount, but the amount was not found, was not a valid number or was out of bounds. The request was rejected. Corrective action: Use the correct amount.
                        'UNAUTHENTICATED_API_CALL', // https://app.asana.com/0/****************/****************/f
                        'SERVICE_TEMPORARILY_UNAVAILABLE', // The request attempted an operation, but the service is temporarily unavailable. The request was rejected. Corrective action: Retry in a few minutes. If the problem persists, contact Rapyd Client Support.

                        // Other undocumented errors
                        'ERROR_GET_PAYOUT_METHOD_TYPES', // Rapyd's internal error. No Rapyd transactions were created at all so we should refund it.
                        'ERROR_GET_PAYOUT', // The request tried to retrieve a payout, but the payout was not found. The request was rejected. Corrective action: Use the ID of a valid payout.
                        'ERROR_GET_USER', // The request attempted an operation that requires a wallet, but the wallet was not found. The request was rejected. Corrective action: Use the ID of a valid wallet that has not been deleted. The ID is a string starting with 'ewallet_'.
                        'MISSING_AUTHENTICATION_HEADERS', // The request did not contain the required headers for authentication. The request was rejected. Corrective action: Add authentication headers.
                        'REQUEST_DECLINED', // Your request was declined. For more information, please contact Rapyd Client Service.
                    ])) {
                        Util::updateMeta($transfer, [
                            'unhandledRapydError' => $msg,
                        ], false);
                        $transfer->setRefunded(true);
                    }

                    Log::debug('agent balance: ' .  $uc->getProcessorApiImpl()->getAgentAvailableBalance());

                    if ($transfer->isDeducted() && !$transfer->isRefunded()) {
                        try {
                            RapidService::updateBalanceBy($uc, $amount, $type . '_reverse', $id, $comment, $transfer, true);
                        } catch (\Throwable $exception) {
                            SlackService::exception('Failed to refund transfer amount to user: ' . $exception->getMessage(), $exception, [
                                'user' => $uc->getUser()->getSignature(),
                                'transfer' => $transfer->getId(),
                            ]);
                            Data::del($singleKey);
                            throw $exception;
                        }
                        $transfer->setRefunded(true);
                    }

                    if (str_contains($msg, 'ERROR_PAYOUT_RATE_EXPIRED')) {
                        $transfer->setStatus(Transfer::STATUS_EXPIRED)
                            ->persist();
                        try {
                            RapydDisburseService::updateTransfer($transfer);
                        } catch (\Throwable $exception) {
                            // It's strange that the get payout API will return the internal error.
                            $msg = $exception->getMessage() ?: '';
                            if (Util::isLive() || !str_contains($msg, 'Internal error')) {
                                Data::del($singleKey);
                                throw $exception;
                            }
                        }
                    } else {
                        Data::del($singleKey);
                        throw $exception;
                    }
                }
            }
        } else if ($transfer->getPartner() === Transfer::PARTNER_UNITELLER){
            $uc = $transfer->getSenderCard();
            $amount = $transfer->getTotalAmount();
            $type = 'rapyd_transfer';
            $id = '' . $transfer->getId();
            $comment = 'Transfer ' . Money::formatWhen($amount) . ' in ' . $id;
            $status = $transfer->getStatus();
            $continue = in_array($status, [
                    Transfer::STATUS_PENDING,
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                ]) || ($status === Transfer::STATUS_CONFIRMATION && Util::meta($transfer, 'transactionInternalReference'));
            if ($continue) {
                if ( ! in_array($status, [
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                    Transfer::STATUS_CONFIRMATION
                ])) { // Queued/processing confirmation transfers already have funds debited.
                    try {
                        TransferPrepareService::prepare($transfer, $uc);

                        Util::em()->refresh($uc); // reload from database
                        [$balanceProcessor] = TransferPrepareService::validateBalance($uc, $amount);
                        $api = null;
                        if ($balanceProcessor === Processor::NAME_BOTM) { // Use BOTM instead of the current processor
                            $transfer->setFromAccountNumber($uc->getBotmTransferFromAccountNumber());
                            $api = new BotmAPI();
                        } else {
                            $transfer->setFromAccountNumber($uc->getTransferFromAccountNumber());
                        }
                        RapidService::updateBalanceBy($uc, -$amount, $type, $id, $comment, $transfer, api: $api);
                        Log::debug('Completed deducting transfer amount from balance: ' . $transfer->getId() . ' with processor ' . $balanceProcessor);
                    } catch (\Throwable $exception) {
                        $msg = 'Failed to deduct transfer amount from the card';
                        $transfer->setStatus(Transfer::STATUS_CANCELED)
                            ->setCancelAt(Carbon::now())
                            ->setError($msg . ': ' . $exception->getMessage())
                            ->fillEiFromException($exception)
                            ->persist();
                        TransactionService::updateTransactionByTransfer($transfer);
                        SlackService::exception($msg, $exception, [
                            'user' => $uc->getUser()->getSignature(),
                            'transfer' => $transfer->getId(),
                        ]);
                        Data::push('failed_transfer_deduction', $transfer->getId());
                        Data::del($singleKey);
                        throw $exception;
                    }
                    $transfer->setDeducted();
                }

                $instant = TransferMexBundle::isCashPickTransferInstantEnabled();
                if ( !$force && ! $instant) {
                    $transfer->setStatus(Transfer::STATUS_PROCESSING)
                        ->persist();
                    SlackService::info('Queue the transfer since the Cash Pickup transfer option is off.', [
                        'transfer' => $transfer->getId(),
                        'member' => $uc->getUser()->getSignature(),
                        'amount' => Money::formatUSD($amount),
                    ]);
                    Data::del($singleKey);
                    return 'processing';
                }

                $isMaintenance = TransferMexBundle::checkMaintenance(Transfer::PARTNER_UNITELLER);
                if ($isMaintenance) {
                    $transfer->setStatus(Transfer::STATUS_PROCESSING)
                        ->persist();
                    SlackService::info('Queue the transfer since the UniTeller is under maintenance.', [
                        'transfer' => $transfer->getId(),
                        'member' => $uc->getUser()->getSignature(),
                        'amount' => Money::formatUSD($amount),
                    ]);
                    Data::del($singleKey);
                    return 'processing';
                }

                $uniTellerBalance = TransferService::getUniTellerBalance();
                if ($uniTellerBalance < UniTellerRemittanceService::getLowestBalanceForRegularTransfer()) {
                    $transfer->setStatus(Transfer::STATUS_PROCESSING)
                        ->persist();
                    SlackService::info('Queue the transfer since the UniTeller balance is too low.', [
                        'transfer' => $transfer->getId(),
                        'member' => $uc->getUser()->getSignature(),
                        'amount' => Money::formatUSD($amount),
                        'UniTeller balance' => Money::formatUSD($uniTellerBalance),
                    ]);
                    Data::del($singleKey);
                    return 'processing';
                }

                $action = null;
                if (in_array($status, [
                    Transfer::STATUS_PENDING,
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                ])) {
                    $action = 'create';
                } else if ($status === Transfer::STATUS_CONFIRMATION && Util::meta($transfer, 'transactionInternalReference')) {
                    $action = 'confirm';
                }

                $cacheKey = 'uniteller_' . $action . '_payout_' . $id; // typo, should be rapyd_
                try {
                    Data::once($cacheKey, function () use ($transfer, $action, &$created) {
                        if ($action === 'create') {
                            UniTellerRemittanceService::sendMoneyPreview($transfer);
                            UniTellerRemittanceService::sendMoneyConfirm($transfer);
                            $created = true;
                        } else if ($action === 'confirm') {
                            UniTellerRemittanceService::sendMoneyConfirm($transfer);
                            $created = true;
                        }
                    });
                } catch (\Throwable $exception) {
                    $msg = $exception->getMessage() ?: '';
                    $context = [
                        'error' => $msg,
                        'transfer' => $transfer->getId(),
                        'transfer_amount' => Money::format($transfer->getSendAmount(), $transfer->getSendCurrency()),
                        'member' => $transfer->getSender()->getSignature(),
                    ];

                    $message = 'Failed to create/confirm the UniTeller payout.';
                    if (Util::containsSubString($msg, [
                        'The amount exceeds the maximum transaction limit',
                    ])) {
                        SlackService::warning($message, $context);
                    } else {
                        SlackService::alert($message, $context, SlackService::GROUP_DEV);
                    }

                    $transfer->setStatus(Transfer::STATUS_ERROR)
                        ->setCancelAt(Carbon::now())
                        ->setError($msg)
                        ->persist();
                    // return the promo if the transfer use
                    TransferService::updatePromo($transfer, true);
                    TransactionService::updateTransactionByTransfer($transfer);

                    if ($transfer->isDeducted() && !$transfer->isRefunded()) {
                        try {
                            RapidService::updateBalanceBy($uc, $amount, $type . '_reverse', $id, $comment, $transfer, true);
                        } catch (\Throwable $exception) {
                            SlackService::exception('Failed to refund transfer amount to user: ' . $exception->getMessage(), $exception, [
                                'user' => $uc->getUser()->getSignature(),
                                'transfer' => $transfer->getId(),
                            ]);
                            throw $exception;
                        }
                        $transfer->setRefunded(true);
                    }
                    Data::del($singleKey);
                    throw $exception;
                }
            }
        } else if ($transfer->getPartner() === Transfer::PARTNER_INTERMEX){
          $uc = $transfer->getSenderCard();
          $amount = $transfer->getTotalAmount();
          $type = 'rapyd_transfer';
          $id = '' . $transfer->getId();
          $comment = 'Transfer ' . Money::formatWhen($amount) . ' in ' . $id;
          $status = $transfer->getStatus();
          $continue = in_array($status, [
                  Transfer::STATUS_PENDING,
                  Transfer::STATUS_QUEUED,
                  Transfer::STATUS_PROCESSING,
              ]) || ($status === Transfer::STATUS_CONFIRMATION && Util::meta($transfer, 'transactionPreparationID'));
          if ($continue) {
              if ( ! in_array($status, [
                  Transfer::STATUS_QUEUED,
                  Transfer::STATUS_PROCESSING,
                  Transfer::STATUS_CONFIRMATION
              ])) { // Queued/processing confirmation transfers already have funds debited.
                  try {
                      TransferPrepareService::prepare($transfer, $uc);

                      Util::em()->refresh($uc); // reload from database
                      [$balanceProcessor] = TransferPrepareService::validateBalance($uc, $amount);
                      $api = null;
                      if ($balanceProcessor === Processor::NAME_BOTM) { // Use BOTM instead of the current processor
                          $transfer->setFromAccountNumber($uc->getBotmTransferFromAccountNumber());
                          $api = new BotmAPI();
                      } else {
                          $transfer->setFromAccountNumber($uc->getTransferFromAccountNumber());
                      }
                      RapidService::updateBalanceBy($uc, -$amount, $type, $id, $comment, $transfer, api: $api);
                      Log::debug('Completed deducting transfer amount from balance: ' . $transfer->getId() . ' with processor ' . $balanceProcessor);
                  } catch (\Throwable $exception) {
                      $msg = 'Failed to deduct transfer amount from the card';
                      $transfer->setStatus(Transfer::STATUS_CANCELED)
                               ->setError($msg . ': ' . $exception->getMessage())
                              ->fillEiFromException($exception)
                              ->persist();
                      TransactionService::updateTransactionByTransfer($transfer);
                      SlackService::exception($msg, $exception, [
                          'user' => $uc->getUser()->getSignature(),
                          'transfer' => $transfer->getId(),
                      ]);
                      Data::push('failed_transfer_deduction', $transfer->getId());
                      Data::del($singleKey);
                      throw $exception;
                  }
                  $transfer->setDeducted();
              }

              $instant = TransferMexBundle::isIntermexTransferInstantEnabled();
              if ( !$force && ! $instant) {
                  $transfer->setStatus(Transfer::STATUS_PROCESSING)
                      ->persist();
                  SlackService::info('Queue the transfer since the Intermex transfer option is off.', [
                      'transfer' => $transfer->getId(),
                      'member' => $uc->getUser()->getSignature(),
                      'amount' => Money::formatUSD($amount),
                  ]);
                  Data::del($singleKey);
                  return 'processing';
              }

              $isMaintenance = TransferMexBundle::checkMaintenance(Transfer::PARTNER_INTERMEX);
              if ($isMaintenance) {
                  $transfer->setStatus(Transfer::STATUS_PROCESSING)
                      ->persist();
                  SlackService::info('Queue the transfer since the UniTeller is under maintenance.', [
                      'transfer' => $transfer->getId(),
                      'member' => $uc->getUser()->getSignature(),
                      'amount' => Money::formatUSD($amount),
                  ]);
                  Data::del($singleKey);
                  return 'processing';
              }

              $action = null;
              if (in_array($status, [
                  Transfer::STATUS_PENDING,
                  Transfer::STATUS_QUEUED,
                  Transfer::STATUS_PROCESSING,
              ])) {
                  $action = 'create';
              } else if ($status === Transfer::STATUS_CONFIRMATION && Util::meta($transfer, 'transactionPreparationID')) {
                  $action = $transfer->getPartnerId() ? 'release' : 'confirm';
              }

              $cacheKey = 'intermex_' . $action . '_payout_' . $id; // type, should be intermex_
              try {
                  Data::once($cacheKey, function () use ($transfer, $action, &$created) {
                      if ($action === 'create') {
                          IntermexRemittanceService::createWire($transfer);
                          IntermexRemittanceService::wireConfirm($transfer);
                          $intermexTransaction = Util::meta($transfer, 'intermexTransaction');
                          if (isset($intermexTransaction['stsComplianceOk']) && $intermexTransaction['stsComplianceOk']) {
                            IntermexRemittanceService::wireRelease($transfer);
                            $created = true;
                          } else {
                            // we can add compliance automatically
                            $automaticallAddFlag = false;
                            if (!$intermexTransaction['requireWireAddInf'] && !$intermexTransaction['requiredNationality']) {
                              $automaticallAddFlag = IntermexRemittanceService::addComplianceInfo($transfer);
                              if ($automaticallAddFlag) {
                                IntermexRemittanceService::wireRelease($transfer);
                                $created = true;
                              }
                            }
                            Util::updateMeta($transfer, [
                              'needAddComplianceInfo' => $automaticallAddFlag ? false : ($intermexTransaction['requireWireAddInf'] || $intermexTransaction['requireSS'] || $intermexTransaction['requiredNationality']),
                            ]);
                          }
                          Util::updateMeta($transfer, [
                            'needAddComplianceFile' => $intermexTransaction['requirePhotoID'] || $intermexTransaction['requireIncomeVerif']
                          ]);
                      } else if ($action === 'confirm') {
                          IntermexRemittanceService::wireConfirm($transfer);
                          IntermexRemittanceService::wireRelease($transfer);
                          $created = true;
                      } else if ($action === 'release') {
                          IntermexRemittanceService::wireRelease($transfer);
                          $created = true;
                    }
                  });
              } catch (\Throwable $exception) {
                  $msg = $exception->getMessage() ?: '';
                  $context = [
                      'error' => $msg,
                      'transfer' => $transfer->getId(),
                      'transfer_amount' => Money::format($transfer->getSendAmount(), $transfer->getSendCurrency()),
                      'member' => $transfer->getSender()->getSignature(),
                  ];

                  if ( !Util::containsSubString($msg, [
                      'INVALID ACCOUNT NUMBER, PLEASE RE-ENTER ACCOUNT NUMBER',
                      'ACCOUNT HOLDER DOES NOT MATCH WITH BENEFICIARY NAME',
                      'ACCOUNT NUMBER DOES NOT EXIST, PLEASE RE-ENTER A VALID ACCOUNT',
                      'ACCOUNT INACTIVE, BLOCKED OR CANCELLED. PLEASE CONTACT WITH DESTINATION BANK',
                      'Please enter the Bank Account Number',
                  ])) {
                      SlackService::alert('Failed to create/confirm the Intermex payout.', $context, [
                          SlackService::MENTION_ABEL,
                          SlackService::MENTION_HANS,
                      ]);
                  }

                  $transfer->setStatus(Transfer::STATUS_ERROR)
                      ->setCancelAt(Carbon::now())
                      ->setError($msg)
                      ->persist();
                  // return the promo if the transfer use
                  TransferService::updatePromo($transfer, true);
                  TransactionService::updateTransactionByTransfer($transfer);

                  if ($transfer->isDeducted() && !$transfer->isRefunded()) {
                      try {
                          RapidService::updateBalanceBy($uc, $amount, $type . '_reverse', $id, $comment, $transfer, true);
                      } catch (\Throwable $exception) {
                          SlackService::exception('Failed to refund transfer amount to user: ' . $exception->getMessage(), $exception, [
                              'user' => $uc->getUser()->getSignature(),
                              'transfer' => $transfer->getId(),
                          ]);
                          throw $exception;
                      }
                      $transfer->setRefunded(true);
                  }
                  Data::del($singleKey);
                  throw $exception;
              }
          }
        }

        if ($created && $transfer->getStatus() === Transfer::STATUS_CREATED) {
            $template = null;
            $params = [
                'sender' => $transfer->getSender()->getFullName(),
                'amount' => Money::format($transfer->getPayoutAmount(), $transfer->getPayoutCurrency()),
            ];
            $type = $transfer->getPayoutType();
            $recipient = $transfer->getRecipient();
            $payout = $recipient->ensureTransferMexRecipient();
            if ($type === RapydAPI::PAYOUT_CATEGORY_BANK) {
                $template = Email::TEMPLATE_TRANSFER_MEX_TRANSFER_BANK;
                $params['pan'] = $payout->getAccountNumberLast4();
            } else if ($transfer->getPartner() === Transfer::PARTNER_RAPYD && $type === RapydAPI::PAYOUT_CATEGORY_CASH) {
                $template = Email::TEMPLATE_TRANSFER_MEX_TRANSFER_CASH;
                $params['merchant'] = $payout->getPayoutMethodTypeName();
            }
            if ($template) {
                Email::sendWithTemplateToUser($recipient, $template, $params);
            }
        }

        if ($created && Util::isAPI()) {
            Service::sendAsync('/t/cron/mex/rapyd/monitor-instant-transfer/silent');
        }

        Data::del($singleKey);
        return $created ? 'created' : 'skipped';
    }

    /**
     * @Route("/mex/m/transfer/cancel/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function cancel(Request $request, Transfer $transfer)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->validateAppVersion();
        $this->validateEmployerMemberLevelStatus($this->user);

        if (!Util::eq($transfer->getSender(), $this->user)) {
            return new DeniedResponse();
        }
        // return new FailedResponse("You can't cancel the transfer. Please contact support to cancel!");

        if ($transfer->getStatus() === Transfer::STATUS_CREATED && $transfer->getPartner() === Transfer::PARTNER_RAPYD) {
            $sendAt = $transfer->getSendAt();
            if ($sendAt && Util::isLive() && Carbon::now()->subHours(48)->lt($sendAt)) {
                return new FailedResponse('You can only cancel a payout until 48 hours.');
            }
            $method = RapydDisburseService::getCachedMethodType($transfer->getPayoutMethodType());
            if (isset($method['is_cancelable']) && !$method['is_cancelable']) {
                return new FailedResponse('This method type is not cancelable!');
            }
        }

        if (in_array($transfer->getPartner(), [Transfer::PARTNER_UNITELLER, Transfer::PARTNER_INTERMEX]) && $transfer->getStatus() === Transfer::STATUS_COMPLETED ) {
            return new FailedResponse("You can't cancel the transfer. Please contact support to cancel!");
        }

        // Check uniteller status and the time
        if ($transfer->getPartner() === Transfer::PARTNER_UNITELLER) {
            // before cancel the transfer check uniteller status and time
            $sendAt = $transfer->getSendAt();
            if ($sendAt && Util::isLive() && Carbon::now()->subMinutes(90)->lt($sendAt)) {
                return new FailedResponse("You can only cancel a payout until 1.5 hours.");
            }
            $meta = Util::meta($transfer, 'UniTellerSendMoney');
            $status = $meta['transactionStatus'] ?? (isset($meta['transactionDetail'])
                ? $meta['transactionDetail']['transactionStatus']
                : '');
            if ($status === Transfer::UNITELLER_STATUS_HOLD) {
                Log::debug('Attempt to cancel a Hold UniTeller transfer ' . $transfer->getId()
                           . ' which was synced at ' . (Util::meta($transfer, 'UniTellerSyncAt') ?? ''));
                return new FailedResponse("You can only cancel a payout when the status is not hold.");
            }
            UniTellerRemittanceService::cancelTransactions($transfer);
            if (Util::meta($transfer, 'unitellerCancelPending')) {
              return new SuccessResponse('Operation succeeded, waiting for bank processing');
            }
        } else if ($transfer->getPartner() === Transfer::PARTNER_RAPYD) {
            RapydDisburseService::cancelPayout($transfer);
        } else if ($transfer->getPartner() === Transfer::PARTNER_INTERMEX) {
            return new FailedResponse("Sorry that this payout cannot be canceled now.");
            $reasonId = $request->get('reasonId');
            if (!$reasonId) {
              return new FailedResponse("Please select the reason why you want to cancel the transfer.");
            }
            $sendAt = $transfer->getSendAt();
            if ($sendAt && Util::isLive() && Carbon::now()->subMinutes(30)->lt($sendAt)) {
                return new FailedResponse("You can only cancel a payout until 30 minutes later.");
            }

            IntermexRemittanceService::cancelTransactions($transfer, $reasonId);
        }

        $transfer->setCancelAt($transfer->getCancelAt() ?? Carbon::now()->toDateTime())
                  ->persist();
        // return the promo if the transfer use
        TransferService::updatePromo($transfer, true);
        // update limit
        $date =  Util::formatDateTime($transfer->getSendAt(), Util::DATE_FORMAT, 'America/New_York');
        $dailyAmountKey = 'daily_transfer_amount_' . $transfer->getSender()->getId() . '_' . $date;
        $dailyAmount = Data::get($dailyAmountKey) ?? 0;
        $month =  Util::formatDateTime($transfer->getSendAt(), Util::DATE_FORMAT_MONTH, 'America/New_York');
        $monthlyAmountKey = 'monthly_transfer_amount_' . $transfer->getSender()->getId() . '_' . $month;
        $monthlyAmount = Data::get($monthlyAmountKey) ?? 0;
        if ($dailyAmount) {
          Data::set($dailyAmountKey,  $dailyAmount - $transfer->getSendAmount(), true,  24 * 3600);
        }
        if ($monthlyAmount) {
          Data::set($monthlyAmountKey,  $monthlyAmount - $transfer->getSendAmount(), true,  31 * 24 * 3600);
        }
        return new SuccessResponse();
    }

     /**
     * @Route("/mex/m/transfer/staging/cancel/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function cancelOnStaging(Request $request, Transfer $transfer)
    {
        if (!Util::isStaging()) {
          return new SuccessResponse('You can only cancel transfer on staging server!');
        }
        if ($transfer->getPartner() == Transfer::PARTNER_UNITELLER) {
            UniTellerRemittanceService::cancelTransactions($transfer);
        } else {
            if ($transfer->getStatus() === Transfer::STATUS_PROCESSING) {
                $transfer->setStatus(Transfer::STATUS_QUEUED)
                    ->persist();
            }
            RapydDisburseService::cancelPayout($transfer);
        }
        return new SuccessResponse();
    }

     /**
     * @Route("/mex/m/transfer/receipt/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function viewUniTellerPayoutReceiptAction(Request $request, Transfer $transfer)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->validateAppVersion();

        if (!Util::eq($transfer->getSender(), $this->user) || !in_array($transfer->getPartner(), [Transfer::PARTNER_UNITELLER, Transfer::PARTNER_INTERMEX]) ) {
            return new DeniedResponse();
        }
        if ($transfer->isUniTeller()) {
          $data = UniTellerRemittanceService::getTransactionReceipt($transfer->getSender(), $transfer->getPartnerId());
          $str = $data ? $data['disclaimer'] : '';
        } else {
          $data = IntermexRemittanceService::getTransactionReceipt($transfer->getSender());
          $str = $data && isset($data['disclaimer']) ? $data['disclaimer'] : '';
        }
        if($str) {
          $res = "<!DOCTYPE html>
            <html>
            <head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head>
            <body style='margin: 0; padding: 0;'>
              <div>" . $str . '</div>
              </body>
            </html>';
          return new SuccessResponse($res);
        }
        return new FailedResponse('');
    }

    /**
     * @Route("/mex/m/transfer/sendMessage/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function sendMessageToReceipt(Request $request,Transfer $transfer)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->validateAppVersion();
        $this->validateEmployerMemberLevelStatus($this->user);

        $type = $request->get('type');
        if (!$transfer->isCashPickup() || !Util::eq($transfer->getSender(), $this->user) || !in_array($transfer->getPartner(), [Transfer::PARTNER_UNITELLER, Transfer::PARTNER_INTERMEX])) {
            return new DeniedResponse();
        }

        $cardProgram = CardProgram::transferMexUSD();
        $host = $cardProgram->getPlatform()->host();

        $hash = Uuid::uuid4();
        $oldHash = Util::meta($transfer, 'hash');
        $oldHashList = $oldHash ? explode(',', $oldHash) : [];
        if (count($oldHashList) >=5 ) {
          $hash = $oldHashList[0]?? $oldHashList[1];
        }

        $isSpanish = MobileBundle::isSpanish();
        $language = $isSpanish ? 'es' : 'en';
        $recipient = $transfer->getRecipient();
        if ($recipient->getCountry() && $recipient->getCountry()->isMX()) {
          $language = 'es';
        }
        $urlEn = $host . '/mex/cashpickup?hash=' . $hash . '&type=' . $type . '&language=en';
        $urlEs = $host . '/mex/cashpickup?hash=' . $hash . '&type=' . $type . '&language=es';
        $recipient = $transfer->getRecipient();
        if ($type == 'sms') {
          $msgContentEs = $transfer->getSender()->getFullName() . ' te ha enviado dinero a traves del Servicio Cash Pickup en México de Transfermex (ID: ' . $transfer->getId() . ')! Haz clic aquí para conocer más detalles: ' . $urlEs;
          $msgContentEn = $transfer->getSender()->getFullName() . ' wishes to send you money using Mexico Cash Pickup using TransferMex! Click here for details: ' . $urlEn;
          $msgEs = 'Hola ' . $recipient->getFirstName(). ', '. $msgContentEs;
          $msgEn = 'Hi ' . $recipient->getFirstName(). ', '. $msgContentEn;
          $twilio = new TwilioService();
          $info = array(
            'to' => $recipient->getMobilephone(),
            'msg' => $msgEs . '. ' . $msgEn,
          );
          $sent = $twilio->sendSMSMsg($info);
          if (!($sent instanceof ExternalInvoke)) {
            return new FailedResponse($sent);
          }
        } else {
          $msgEs = '<p>'. $transfer->getSender()->getFullName() . ' te ha enviado dinero a traves del Servicio Envío de dinero en México de Transfermex!</p>' .
            '<p> Haz clic aquí para conocer más detalles:</p>' .
            '<p><a href="' . $urlEs . '">' . 'Ver detalle' . '</a></p>';

          $msgEn = '<p>'. $transfer->getSender()->getFullName() . ' wishes to send you money using Mexico Cash Pickup using TransferMex!</p>' .
            '<p>Click here for details: :</p>' .
            '<p><a href="' . $urlEn . '">' . 'See Detail' . '</a></p>';
          $msg = $msgEs . '<br/>' . $msgEn;

          $email = Email::sendWithTemplate([$recipient->getEmail()], Email::TEMPLATE_SIMPLE_LAYOUT, [
            'name' => $recipient->getFirstName(),
            'subject' => $language == 'es' ? $transfer->getSender()->getFullName() . ' te ha enviado dinero a traves del Servicio Envío de dinero en México de Transfermex (ID: '. $transfer->getPartnerId() . ')' : $transfer->getSender()->getFullName() . ' has sent you a cash pickup request using TransferMex (ID: '. $transfer->getPartnerId() . ')' ,
            'body' => $msg,
          ], null , $cardProgram);
          if (!($email instanceof Email) ||( $email->getStatus() != Email::STATUS_SENT &&  $email->getStatus() != Email::STATUS_PENDING)) {
            return new FailedResponse();
          }
        }

        if (!in_array($hash, $oldHashList)) {
          Util::updateMeta($transfer, [
            'hash' =>  $oldHash ? $oldHash . ',' . $hash : $hash
          ]);
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/mex/m/transfer/getReceipt/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function getReceipt(Request $request, Transfer $transfer) {
      $this->checkReadonlyWhenLoggingAs();
      $this->validateAppVersion();

      $sender = $transfer->getSender();
      if (!Util::eq($sender, $this->user) || !in_array($transfer->getPartner(), [Transfer::PARTNER_UNITELLER, Transfer::PARTNER_INTERMEX]) ) {
          return new DeniedResponse();
      }

      $targetDir = Util::ensureFile(Util::secureDir('mex_receip_pdf'), true);
      $errorMessage = '';
      $roundStr = Util::randString();
      if ($transfer->isUniTeller()) {
        $meta = Util::meta($transfer, 'UniTellerSendMoney');
        $status = isset($meta['transactionStatus']) ? $meta['transactionStatus'] : (isset($meta['transactionDetail']) ? $meta['transactionDetail']['transactionStatus'] : $transfer->getStatus());

        $path = $targetDir . $transfer->getId(). $status . $roundStr .'.pdf';
        if (Util::meta($transfer, 'hasCreateReceiptFor' . $status)) {
          $att = Attachment::find(Util::meta($transfer, 'hasCreateReceiptFor' . $status));
          return new SuccessResponse($att->getAssetUrl());
        }
        if ($status == Transfer::UNITELLER_STATUS_HOLD) {
          $statusStr = 'Processing Request';
          $backgroudColor = '#faf3e1';
          $color = 'orange';
        } elseif ($status == Transfer::UNITELLER_STATUS_PAYABLE) {
          $statusStr = 'Awaiting Pickup';
          $backgroudColor = '#e5f7ff';
          $color = '#37bdff';
        } elseif ($status == Transfer::UNITELLER_STATUS_PAID) {
          $statusStr = 'Completed';
          $backgroudColor = '#ebfaf4';
          $color = '#00de00';
        } elseif (in_array($status, Transfer::STATUSES_ERROR)) {
          $statusStr = 'Error';
          $backgroudColor = '#feeeee';
          $color = '#fc5a5a';
          $errorMessage = $transfer->getFilteredError();
        } else {
          $statusStr = 'Cancelled';
          $backgroudColor = '#eee';
          $color = '#000';
        }
        $data = UniTellerRemittanceService::getTransactionReceipt($sender, $transfer->getPartnerId());
        $str = $data ? $data['disclaimer'] : '';
        $templateUrl = '@TransferMex/Transfer/receipt.html.twig';
      } else {
        $meta = Util::meta($transfer, 'IntermexStatus');
        $status = isset($meta['wireStatusId']) && isset(Transfer::INTERMEX_STATUS_LIST[$meta['wireStatusId']]) ? Transfer::INTERMEX_STATUS_LIST[$meta['wireStatusId']] : $transfer->getStatus();
        if ($status == Transfer::STATUS_CONFIRMATION) {
          return new FailedResponse('The tansfer has not been released and cannot view receipt!');
        }
        $path = $targetDir . $transfer->getId(). $status . $roundStr .'.pdf';
        if (Util::meta($transfer, 'hasCreateReceiptFor' . $status)) {
          $att = Attachment::find(Util::meta($transfer, 'hasCreateReceiptFor' . $status));
          return new SuccessResponse($att->getAssetUrl());
        }
        if ($status == Transfer::STATUS_CREATED || $status == Transfer::STATUS_PENDING ) {
          $statusStr = 'Processing Request';
          $backgroudColor = '#faf3e1';
          $color = 'orange';
        } else if ($status == Transfer::STATUS_COMPLETED) {
          $statusStr = 'Completed';
          $backgroudColor = '#ebfaf4';
          $color = '#00de00';
        } elseif (in_array($status, Transfer::STATUSES_ERROR)) {
          $statusStr = 'Error';
          $backgroudColor = '#feeeee';
          $color = '#fc5a5a';
          $errorMessage = $transfer->getFilteredError();
        } else {
          $statusStr = 'Cancelled';
          $backgroudColor = '#eee';
          $color = '#000';
        }
        $data = IntermexRemittanceService::getTransactionReceipt($sender);
        $str = $data ? $data['disclaimer'] : '';
        $templateUrl = '@TransferMex/Transfer/intermex_receipt.html.twig';
      }
      $recipient = $sender->ensureTransferMexRecipient();

      $recUser = $transfer->getRecipient();
      $state = $recUser->getState();
      $country = $recUser->getCountry();
      $payoutOptions = Util::meta($transfer, 'payoutOptions');
      $senderAddress = $sender->getAddress() . ', ' . Util::field($sender->getState()) . ', ' . Util::field($sender->getCountry());
      if ($transfer->isIntermex() && !$sender->getCountry()->isUSA()) {
        $groupAdmin = $sender->getPrimaryGroupAdmin();
        $senderAddress = $groupAdmin->getAddress() . ', ' . Util::field($groupAdmin->getState()) . ', ' . Util::field($groupAdmin->getCountry());
      }
      $htmlContent = Util::render($templateUrl, [
        'status' => $statusStr,
        'backgroudColor' => $backgroudColor,
        'color' => $color,
        'errorMessage' => $errorMessage,
        'str' => $str,
        'senderId' => $sender->getId(),
        'senderName' => $sender->getFullName(),
        'senderPhone' => $sender->getMobilephone(),
        'senderAddress' => $senderAddress,
        'accountNumber' => $recipient->getDecryptedAccountNumber(),
        'beneficiaryName' => $recUser->getFullName(),
        'beneficiaryPhone' => $recUser->getMobilephone(),
        'beneficiaryAddress' => $recUser->getAddress() . ', ' . Util::field($state) . ', ' . Util::field($country),
        'createdAt' => $transfer->isIntermex() ? Util::formatDateTime($transfer->getSendAt(), Util::DATE_TIME_FORMAT, Util::tzNewYork()) : Util::formatApiDateTime($transfer->getSendAt()),
        'receiveAt' => $transfer->isIntermex() ? Util::formatDateTime($transfer->getReceiveAt(), Util::DATE_TIME_FORMAT, Util::tzNewYork()) : Util::formatApiDateTime($transfer->getReceiveAt()),
        'transferId' => $transfer->getPartnerId(),
        'amount' => $transfer->getSendAmount() / 100,
        'fee' => $transfer->getTransferFee() / 100,
        'stateFee' => $payoutOptions && isset($payoutOptions['stateFee']) ? $payoutOptions['stateFee'] : 0,
        'total' => $transfer->getTotalAmount() / 100,
        'rate' => $transfer->getFxRate(),
        'receive' => $transfer->isIntermex() ? ($transfer->getReceiveAmount() ? $transfer->getReceiveAmount() / 100 : $transfer->getPayoutAmount() / 100) : $transfer->getReceiveAmount() / 100,
        'method' => $transfer->isIntermex() ? $transfer->getPayoutMethodType() : UniTellerRemittanceService::CASH_PICKUP_NAME_LIST[$transfer->getPayoutMethodType()],
        'methodType' => $transfer->getPayoutTypeName(),
        'methodPhone' => $transfer->isIntermex() ? '' : '',
        'methodAddress' => $transfer->isIntermex() ? '' : '',
        'url' => $transfer->isUniTeller() ? Util::host() . '/static/mex/img/receipt.svg' :  Util::host() . '/static/mex/img/intermex_receipt.jpg',
        'currency' => $transfer->getReceiveCurrency(),
        'sendCurrency' => $transfer->getSendCurrency(),
        'mexUrl' => Util::host() . '/static/mex/img/logo_green.png'
      ]);
      $globalOptions = [
        'footer-right' => 'Page [page] of [topage]',
        'binary' => '/usr/local/bin/wkhtmltopdf'
      ];
      $pdf = new Pdf();
      $pdf->setOptions($globalOptions);

      $pdf->addPage($htmlContent);

      // Log::debug($htmlContent);
      $pdfResult  = $pdf->saveAs($path);
      if ($pdfResult) {
        $att = Attachment::createFromHostedFile($path, 'mex_receipt_pdf');
        $att->setCreateBy($this->user)
            ->persist();
        Util::updateMeta($transfer, ['hasCreateReceiptFor' . $status => $att->getId()]);
        return new SuccessResponse($att->getAssetUrl());
      }

      return new FailedResponse($pdf->getError());
    }

    /**
     * @Route("/mex/m/transfer/addComplianceInfo/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function addComplianceInfo(Request $request, Transfer $transfer) {
      $this->checkReadonlyWhenLoggingAs();
      $this->validateAppVersion();
      $intermexTransaction = Util::meta($transfer, 'intermexTransaction');
      $info = [
        'wirePurpose'  => $request->get('wirePurpose', ''),
        'fundSource'   => $request->get('fundSource', ''),
        'citizenship'  => $request->get('citizenship', Util::meta($transfer->getSender(), 'citizenshipValue')),
        'ssValue'      => $request->get('ssValue', Util::meta($transfer->getSender(), 'ssValue')),
        'occupation'   => $request->get('occupation'),
        'relationship' => $request->get('relationship', Util::meta($transfer->getRecipient(), 'relationship')),
      ];
      if ($intermexTransaction['requireWireAddInf'] && ( !$info['wirePurpose'] || !$info['fundSource'])) {
        return new FailedResponse('Please input the Transfer Purpose and Fund Source!');
      }
      if ($intermexTransaction['requiredNationality'] && !$info['citizenship']) {
        return new FailedResponse('Please input the Nationality information!');
      }
      if ($intermexTransaction['requireSS'] && !$info['ssValue']) {
        return new FailedResponse('Requires Social Security Number!');
      }

      $res = IntermexRemittanceService::addComplianceInfo($transfer, $info, true);
      if (!$res) {
        return new FailedResponse('Faild to add compliance info');
      }
      if (Util::meta($transfer, 'automaticallAddComplianceInfoFailed' )) {
        Util::updateMeta($transfer, ['automaticallAddComplianceInfoFailed' => false]);
      }
      Util::updateMeta($transfer, [
        'complianceInfo'        => $info,
        'needAddComplianceInfo' => false,
        'needAddComplianceFile' => $intermexTransaction['requirePhotoID'] || $intermexTransaction['requireIncomeVerif']
      ]);
      self::doConfirmTransfer($transfer);
      if ($intermexTransaction['print1025']) {
        $attachment = IntermexRemittanceService::createForm1025($transfer);
        $res = false;
        $complianceError = null;
        if ($attachment) {
          $params = [
            'pinNumber' => $transfer->getPartnerId(),
            'documentType' => 'COMP_FORM1025',
            'documentImages' => [Util::getFileBase64($attachment->getPath())]
          ];
          $res = IntermexRemittanceService::addComplianceFile($params, $complianceError);
        }
        Util::updateMeta($transfer, [
          'needAddCompliance1025Form' => !$res,
          'compliance1025FormError' => $complianceError,
        ]);
      }
      return new SuccessResponse();
    }

     /**
     * @Route("/mex/m/transfer/addComplianceFile/{transfer}")
     * @param Request  $request
     * @param Transfer $transfer
     *
     * @return FailedResponse|SuccessResponse
     */
    public function addComplianceFile(Request $request, Transfer $transfer) {
      $this->checkReadonlyWhenLoggingAs();
      $this->validateAppVersion();
      $intermexTransaction = Util::meta($transfer, 'intermexTransaction');
      $attachment = null;
      $documentType = $request->get('type');
      $attachmentId = $request->get('attachmentId', null);
      $params = [];
      $sender = $transfer->getSender();
      if (($intermexTransaction['requirePhotoID'] || $intermexTransaction['requireIncomeVerif'] ) && !$attachmentId) {
        return new FailedResponse('Please upload the image!');
      }

      if ($intermexTransaction['requirePhotoID'] && $documentType === 'COMP_SNDID') {
        if (!preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $request->get('idExpirationDate'))){
          return new FailedResponse('Please enter the date in the correct format. eg: 10/28/2033');
        }
        $attachment = Attachment::find($attachmentId);
        $idExpirationDate = DateTime::createFromFormat('d/m/Y', $request->get('idExpirationDate'));
        $params = [
            'senderId'       => $sender->getId(),
            'documentType'   => "COMP_SNDID",
            'idType'         => $request->get('idType'),
            'idTypeDesc'     => IntermexRemittanceService::ID_TYPE_TDESC[$request->get('idType')] ?? '',
            'idNumber'       => $request->get('idNumber'),
            'idCountry'      => Util::ascii($sender->getCountry()->getName()),
            'idState'        => Util::ascii($sender->getState()->getName()),
            'IdExpirationDate' => Util::formatApiDateTime($idExpirationDate),
            'enteredBy'      => $sender->getFullName(),
            'documentImages' => [Util::getFileBase64($attachment->getPath())]
        ];
      }
      if ($intermexTransaction['requireIncomeVerif']  && $documentType === 'COMP_SND_INCOME') {
        $attachment = Attachment::find($attachmentId);
        $params = [
          'senderId'       => $sender->getId(),
          'documentType'   => "COMP_SND_INCOME",
          'enteredBy'      => $sender->getFullName(),
          'incomeTypeId'   => $request->get('incomeTypeId'),
          'documentImages' => [Util::getFileBase64($attachment->getPath())]
        ];
      }

      $complianceError = null;
      $res = IntermexRemittanceService::addComplianceFile($params, $complianceError);
      if (!$res) {
        return new FailedResponse('Failed to add compliance file: ' . $complianceError);
      }
      $intermexTransaction = Util::meta($transfer, 'intermexTransaction');
      if ($documentType === 'COMP_SNDID') {
        $intermexTransaction['requirePhotoID'] = false;
      }
      if ($documentType === 'COMP_SND_INCOME') {
        $intermexTransaction['requireIncomeVerif'] = false;
      }
      Util::updateMeta($transfer, [
       'needAddComplianceFile' => $intermexTransaction['requirePhotoID'] || $intermexTransaction['requireIncomeVerif'],
       'intermexTransaction' => $intermexTransaction
      ]);
      self::doConfirmTransfer($transfer);
      return new SuccessResponse();
    }

     /**
     * @Route("/mex/m/transfer/attachments", methods={"POST"})
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function uploadAction(Request $request)
    {
        $this->checkReadonlyWhenLoggingAs();
        $this->validateAppVersion();
        $category = $request->get('category');
        if (!in_array($category, ['transfer', 'avatar'])) {
          return new FailedResponse('Unknown attachment category!');
        }

        try {
            $result = Attachment::upload($category);
        } catch (PortalException $exception) {
            return new FailedResponse($exception->getMessage());
        }
        /**
         * @var string $key
         * @var Attachment $a
         */
        foreach ($result as $key => $a) {
            $result[$key] = $a->toApiArray();
        }

        return new SuccessResponse($result);
    }
}
