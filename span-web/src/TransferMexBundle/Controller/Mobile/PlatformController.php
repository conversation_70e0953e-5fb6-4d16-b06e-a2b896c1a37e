<?php


namespace TransferMexBundle\Controller\Mobile;


use CoreBundle\Entity\Country;
use CoreBundle\Response\SuccessResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PlatformController extends BaseController
{
    public $protected = false;

    #[Route('/mex/m/platform')]
    public function profile()
    {
        $data = [];
        $data['countries'] = $this->cardProgram->getCountries()->map(function (Country $country) {
            $d = $country->toApiMinArray();
            $d['currency'] = $country->getCurrency();
            $d['phone'] = $country->getPhoneCode();
            return $d;
        })->toArray();
        return new SuccessResponse($data);
    }

    #[Route('/mex/m/signup')]
    public function signup()
    {
        return new Response('Sorry that the signup feature is not available right now.');
    }

    #[Route('/mex/m/reset-password')]
    public function resetPassword()
    {
        return new RedirectResponse('https://script.google.com/a/macros/transfermex.com/s/AKfycbwRf9mx-Bm_Dp8rH5-ebBIUIqOgxzA9xmmCZP-2UK2WnhFcM1ydTlnWMC7odAsaZXjFyw/exec');
    }

    #[Route('/mex/m/help')]
    public function help()
    {
        return new RedirectResponse('https://script.google.com/a/macros/transfermex.com/s/AKfycbxM7A5L4JdJubIB9IdE7F9M-u5LK7aynEIIDJWyGv8Qr5ejzSVN0wHwPoBjJV_vzEQJ/exec');
    }
}
