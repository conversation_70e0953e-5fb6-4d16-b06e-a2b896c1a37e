<?php


namespace TransferMexBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Currency;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Transfer;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use TransferMexBundle\TransferMexBundle;

class RapydDisburseService
{
    public const DATE_FORMAT = 'm/d/Y';
    public const DATE_FORMAT_DMY = 'd/m/Y';

    public const FEE_BANK_TRANSFER = 100; // $1.00 USD
    public const FEE_CASH_PICKUP = 300; // $3.00 USD

    public const OTHER_COUNTRY_FEE = [
      'bank' => [
        'SV' => 500 // $5.00 USD
      ],
      'cash' => [
        'SV' => 500 // $5.00 USD
      ]
    ];
    public const COST_BANK_TRANSFER = 70; // Fee charged by Rapyd
    public const COST_CASH_PICKUP = 300; // Fee charged by Rapyd

    public const RATE_MARK_UP_PARTNER = 0.012; // Partner(TransferMex) and Tern share this revenue, 50% vs 50%
    public const RATE_MARK_UP_PLATFORM = 0.008; // Tern Platform's revenue. Was 0.006 for some initial transfers

    public const LOW_BALANCE_FOR_INSTANT_TRANSFER = 100000;
    public const MIN_BALANCE_FOR_INSTANT_TRANSFER = 50000;

    public const STATUSES_FOR_SETTLEMENT = [
        Transfer::STATUS_CREATED,
        Transfer::STATUS_QUEUED,
        Transfer::STATUS_HOLD,
        Transfer::STATUS_PROCESSING,
        Transfer::STATUS_COMPLETED,
    ];

    public const STATUSES_FOR_SETTLEMENT_REVERSE = [
        Transfer::STATUS_CANCELED,
        Transfer::STATUS_ERROR,
        Transfer::STATUS_RETURNED,
    ];

    // Show input controls in the app to ask values
    public const BENEFICIARY_REQUIRED_FIELDS_MAP = [
        // ['rapyd_field', 'local_field', 'label']
        ['bic_swift', 'bicSwift', 'BIC SWIFT'],
        ['account_number', 'accountNumber', 'Account Number'],
//        ['tipoCuentaBeneficiario', 'clabe', 'CLABE'], // doesn't exist in the live env

        // Will always be sent to the API
        ['address', 'address', 'Address'],
        ['city', 'city', 'City'],
        ['state', 'state', 'State'],
        ['postcode', 'postcode', 'Postcode'],

        // Always required
//        ['email', 'email', 'Email'],
//        ['phone_number', 'mobile', 'Mobile Phone'],
    ];

    public const SENDER_REQUIRED_FIELDS_MAP = [];

    public static function getPartnerRateMarkup()
    {
        $rate = self::RATE_MARK_UP_PARTNER;
        $tz = Util::tzCentral();
        $now = Carbon::now($tz);
        $from = Carbon::create(2023, 6, 28, 0, 0, 0, $tz);
        if ($now->lt($from)) {
            return $rate;
        }
        for ($i = 0; $i < 10; $i++) {
            $rate += 0.0005;
            $from->addWeek();
            if ($now->lt($from)) {
                break;
            }
        }
        return round($rate, 8);
    }

    public static function getPlatformRateMarkup()
    {
        return self::RATE_MARK_UP_PLATFORM;
    }

    public static function feeByType($type)
    {
        return $type === 'bank' ? self::FEE_BANK_TRANSFER : self::FEE_CASH_PICKUP;
    }

    public static function costByType($type)
    {
        return $type === 'bank' ? self::COST_BANK_TRANSFER : self::COST_CASH_PICKUP;
    }

    public static function revenueByType($type, $amount, Transfer $transfer = null, $isTransferFree = false)
    {
        $fee = $isTransferFree ? 0 : self::feeByType($type);
        $cost = self::costByType($type);

        $rate = self::getPartnerRateMarkup();
        if ($transfer) {
            $rates = Util::meta($transfer, 'fxRateMarkup');
            if (isset($rates[0])) {
                $rate = $rates[0];
            }
        }

        $feeRevenue = $fee - $cost;
        $rateRevenue = $amount * $rate;

        return $feeRevenue + $rateRevenue;
    }

    public static function platformRevenueByType($amount, Transfer $transfer = null)
    {
        $rate = self::getPlatformRateMarkup();
        if ($transfer) {
            $rates = Util::meta($transfer, 'fxRateMarkup');
            if (isset($rates[1])) {
                $rate = $rates[1];
            }
        }
        return $amount * $rate;
    }

    protected static function validatePayout(Transfer $transfer)
    {
        if (!$transfer->getPartnerId()) {
            throw PortalException::create('The transfer has not been initialized yet!');
        }
    }

    public static function createApiFromTransfer(Transfer $transfer)
    {
        $live = Util::isLive();
        if ($transfer->isSandbox()) {
            $live = false;
        }
        return new RapydAPI($live);
    }

    public static function getBeneficiaryDataArray(User $recipient, array $extra = [])
    {
        $re = $recipient->ensureTransferMexRecipient();

        if (!empty($extra['default_payout_method_type'])) {
            TransferService::precheck($re, $extra['default_payout_method_type']);
        }

        $country = $recipient->getCountry();
        $phone = Util::formatFullPhoneDigits($recipient->getMobilephone(), $country->getIsoCode());
        $beneficiary = [
            'name'                       => $recipient->getCompleteFullName(),
            'first_name'                 => trim($recipient->getFirstName()),
            'last_name'                  => trim($recipient->getLastName()),
            'company_name'               => Platform::NAME_TRANSFER_MEX,
            'address'                    => trim($recipient->getAddresses()),
            'email'                      => trim($recipient->getEmail()),
            "country"                    => $country->getIsoCode(),
            'currency'                   => 'MXN',
            "city"                       => trim($recipient->getCity()),
            'entity_type'                => RapydAPI::ENTITY_TYPE_INDIVIDUAL,
            'merchant_reference_id'      => '' . $recipient->getId(),
            "postcode"                   => trim($recipient->getZip()),
            'phonenumber'                => $phone,
            'phone_number'               => $phone,
            "state"                      => Util::field($recipient->getState(), 'abbrOrName'),
            'account_number'             => $re->getDecryptedAccountNumber() ?? '',
            'bank_account_type'          => 'clabe',
            'payment_type'               => 'regular',
            'bic_swift'                  => $re->getBicSwift() ?? '',
            'phone_country_code'         => $country->getPhoneCode(),
            'nationality'                => $country->getIsoCode(),

            // Extended Beneficiary parameters
            'category'                   => 'bank',
            'gender'                     => $recipient->getGender(), // male, female, other, not_applicable
            'identification_type'        => $re->getIdentificationType(), // drivers_license, identification_id, international_passport, residence_permit, social_security, work_permit
            'identification_value'       => $re->getIdentificationValue(),
        ];
        if ($recipient->getBirthday()) {
            $beneficiary['date_of_birth'] = Util::formatUtcDate($recipient->getBirthday(), self::DATE_FORMAT_DMY);
            $beneficiary['dob'] = $beneficiary['date_of_birth'];
        }
        foreach (['gender', 'identification_type', 'identification_value'] as $field) {
            if (array_key_exists($field, $beneficiary) && !$beneficiary[$field]) {
                unset($beneficiary[$field]);
            }
        }

        return $beneficiary;
    }

    public static function createPayout(Transfer $transfer, $live = null)
    {
        if (!in_array($transfer->getStatus(), [
            Transfer::STATUS_PENDING,
            Transfer::STATUS_QUEUED,
            Transfer::STATUS_PROCESSING,
        ])) {
            throw PortalException::temp('Invalid transfer status!');
        }

        if ($transfer->getPartnerId()) {
            throw PortalException::temp('The transfer was already sent!');
        }

        $recipient = $transfer->getRecipient();
        $from = $transfer->getSender();
        if (!$recipient || !$from) {
            throw PortalException::temp('Invalid recipient or sender!');
        }

        $type = $transfer->getPayoutMethodType();

        if ($live === null) {
            $live = Util::isLive();
        }
        $api = new RapydAPI($live);

        $country = $from->getCountry();
        $birthday = Util::formatUtcDate($from->getBirthday(), self::DATE_FORMAT);
        $phone = Util::formatFullPhoneDigits($from->getMobilephone(), $country->getIsoCode());
        $sender = [
            "name"                     => trim($from->getFullName()),
            'first_name'               => trim($from->getFirstName()),
            'last_name'                => trim($from->getLastName()),
            'company_name'             => Platform::NAME_TRANSFER_MEX,
            "address"                  => trim($from->getAddresses()),
            "city"                     => trim($from->getCity()),
            "state"                    => Util::field($from->getState(), 'abbrOrName'),
            'country'                  => 'US',// $from->getCountry()->getIsoCode(),
            'currency'                 => RapydAPI::getSenderCurrency($transfer->getPayoutType()),// $transfer->getSendCurrency(),
            'entity_type'              => RapydAPI::getSenderEntityType($transfer->getPayoutType()),
            'postcode'                 => trim($from->getZip()),
            "phonenumber"              => $phone,
            "phone_number"             => $phone,
            'email'                    => trim($from->getEmail()),
            'identification_type'      => RapydAPI::ID_TYPE_SOCIAL_SECURITY,
            'identification_value'     => $from->getCurpDecrypted(),
            'source_of_income'         => 'salary',
            'purpose_code'             => 'salary',
            'phone_country_code'       => $country->getPhoneCode(),
            'nationality'              => $country->getIsoCode(),
            'beneficiary_relationship' => 'customer',
            'occupation'               => 'salesman',

            // Once the `date_of_birth` is added, it will report `Invalid Signature` in the sandbox env
            'date_of_birth'            => $birthday,
            'id_date_of_issue'         => Util::formatUtcDate(
                Carbon::instance($from->getBirthday())->addYearsWithoutOverflow(18),
                self::DATE_FORMAT
            ),
            'id_expiry'                => Util::formatUtcDate(
                Carbon::instance($from->getBirthday())->addYearsWithoutOverflow(80),
                self::DATE_FORMAT
            ),
        ];

        $beneficiary = self::getBeneficiaryDataArray($recipient, [
            'default_payout_method_type' => $type,
        ]);

        $params = [
            'beneficiary' => $beneficiary,
            'beneficiary_country' => $recipient->getCountry()->getIsoCode(),
            'beneficiary_entity_type' => RapydAPI::ENTITY_TYPE_INDIVIDUAL,
            'confirm_automatically' => true,
            'ewallet' => $api->getParameter('rapyd_wallet_id'),
            'merchant_reference_id' => '' . $transfer->getId(),
            'payout_amount' => Money::formatAmountToNumber($transfer->getPayoutAmount(), $transfer->getPayoutCurrency()),
            'payout_currency' => $transfer->getPayoutCurrency(),
            'payout_method_type' => $type,
            'sender' => $sender,
            'description' => 'SPAN - transfer - ' . $transfer->getId(),

            // Cash pickup requires the `sender_country` to be `US`
            'sender_country' => $from->getCountry()->getIsoCode(), // 'US',
            'sender_currency' =>  $transfer->getSendCurrency(), // RapydAPI::getSenderCurrency($transfer->getPayoutType()),
            'sender_entity_type' => RapydAPI::getSenderEntityType($transfer->getPayoutType()),

            'metadata' => [
                'beneficiary' => $recipient->getMetadataForPayout(),
            ],
        ];

        try {
            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->createPayout($params, $transfer->getId());
        } catch (PortalException $ex) {
            if (!empty($ex->ei)) {
                 $transfer->setEi($ex->ei)
                     ->persist();
            }
            throw $ex;
        }
        $transfer->setEi($ei);

        if ($ei && $ei->isFailed()) {
            $transfer->setStatus(Transfer::STATUS_ERROR)
                ->setError($ei->getError())
                ->persist();
            throw PortalException::temp($ei->getError());
        }

        $transfer->setPartnerId($data['id']);

        if (!empty($data['created_at'])) {
            $transfer->setSendAt(Carbon::createFromTimestamp($data['created_at']));
        }

        self::updateTransferByPayout($data, $transfer);
        return $transfer;
    }

    public static function confirmPayout(Transfer $transfer)
    {
        self::validatePayout($transfer);
        $api = self::createApiFromTransfer($transfer);
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->confirmPayout($transfer->getPartnerId(), $transfer->getId());
        if ($ei && $ei->isFailed()) {
            throw PortalException::create($ei->getError());
        }
        return self::updateTransferByPayout($data, $transfer);
    }

    public static function cancelPayout(Transfer $transfer)
    {
        if ( ! in_array($transfer->getStatus(), [
            Transfer::STATUS_PENDING,
            Transfer::STATUS_CONFIRMATION,
            Transfer::STATUS_QUEUED,
            Transfer::STATUS_PROCESSING,
            Transfer::STATUS_CREATED,
        ])) {
            throw new FailedException('This transfer is not cancelable!');
        }

        $user = Util::user();
        $admin = $user->inTeams([
            Role::ROLE_MASTER_ADMIN,
            Role::ROLE_TRANSFER_MEX_ADMIN,
            Role::ROLE_TRANSFER_MEX_AGENT,
        ]);
        if ( ! $admin && $transfer->getStatus() === Transfer::STATUS_PROCESSING) {
            throw new FailedException('This transfer is being processed and cannot be canceled.');
        }

        if ($transfer->getPartner() === Transfer::PARTNER_RAPYD && $transfer->getPartnerId()) {
            try {
                self::updateTransfer($transfer);
                $oldStatus = $transfer->getStatus();

                $api = self::createApiFromTransfer($transfer);
                /** @var ExternalInvoke $ei */
                [$ei, $data] = $api->cancelPayout($transfer->getPartnerId());
                if ($ei && $ei->isFailed()) {
                    throw PortalException::create($ei->getError());
                }
                self::updateTransferByPayout($data, $transfer, true);

                /** @noinspection PhpConditionAlreadyCheckedInspection */
                if (in_array($oldStatus, [
                    Transfer::STATUS_CREATED,
                    Transfer::STATUS_PENDING,
                    Transfer::STATUS_CONFIRMATION,
                    Transfer::STATUS_QUEUED,
                    Transfer::STATUS_PROCESSING,
                    Transfer::STATUS_COMPLETED,
                    Transfer::STATUS_HOLD,
                    Transfer::STATUS_DECLINE,
                ]) && $transfer->getStatus() === Transfer::STATUS_CANCELED && $transfer->isDeducted() && !$transfer->isRefunded()) {
                    RapidService::updateBalanceBy($transfer->getSenderCard(), $transfer->getTotalAmount(),
                        'rapyd_transfer_reverse', $transfer->getId(), 'Cancelled transfer', $transfer, true);
                    $transfer->setRefunded(true);

                    $context = self::getTransferContext($transfer);
                    $context['canceled_by'] = Util::field(Util::user(), 'signature');
                    SlackService::alert('The transfer was cancelled.', $context, SlackService::GROUP_FAAS);
                }
            } catch (\Exception $exception) {
                $msg = $exception->getMessage() ?: '';
                if (strpos($msg, 'not cancelable') !== FALSE
                    && $transfer->getStatus() === Transfer::STATUS_CONFIRMATION)
                {
                    $transfer->setStatus(Transfer::STATUS_CANCELED)
                        ->persist();
                } else {
                    throw $exception;
                }
            }
        } else {
            $oldStatus = $transfer->getStatus();
            if ($transfer->isDeducted() && !$transfer->isRefunded()) {
                RapidService::updateBalanceBy($transfer->getSenderCard(), $transfer->getTotalAmount(),
                    'rapyd_transfer_reverse', $transfer->getId(), 'Cancelled transfer', $transfer, true);
                $transfer->setRefunded(true);

                $context = self::getTransferContext($transfer);
                $context['canceled_by'] = Util::field(Util::user(), 'signature');
                SlackService::alert('The ' . strtolower($oldStatus) . ' transfer was cancelled and refunded to the member.', $context,
                    SlackService::GROUP_FAAS);
            }

            $transfer->setStatus(Transfer::STATUS_CANCELED)
                ->persist();
            TransactionService::updateTransactionByTransfer($transfer);
        }
    }

    public static function simulateCompletingPayout(Transfer $transfer, $amount)
    {
        self::validatePayout($transfer);
        $api = self::createApiFromTransfer($transfer);
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->completePayout($transfer->getPartnerId(), $amount, $transfer->getSendCurrency());
        if ($ei && $ei->isFailed()) {
            throw PortalException::create($ei->getError());
        }
        self::updateTransferByPayout($data, $transfer);
    }

    public static function updateTransfer(Transfer $transfer)
    {
        self::validatePayout($transfer);
        $api = self::createApiFromTransfer($transfer);
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->getPayout($transfer->getPartnerId(), true);
        if ($ei && $ei->isFailed()) {
            throw PortalException::create($ei->getError());
        }
        return self::updateTransferByPayout($data, $transfer);
    }

    public static function updateTransferByPayout($data, Transfer $transfer = null, $initiative = false)
    {
        if ($otherPlatform = self::isFromOtherPlatform($data)) {
            Log::debug('Skip updating transfer by payout for other platforms: ' . $otherPlatform);
            return null;
        }

        if ($transfer === null) {
            $transfer = Transfer::findByRapydId($data['id']);
            if (!$transfer) {
                return $transfer;
            }
        }
        $oldStatus = $transfer->getStatus();
        $didNotRefundDirectly = $transfer->isFailedByRapydAndDidNotRefundDirectly();

        if ( ! empty($data['created_at']) && ! $transfer->getSendAt()) {
            $transfer->setSendAt(Carbon::createFromTimestamp($data['created_at']));
        }

        $paidAmount = Money::normalizeAmount($data['paid_amount'], $transfer->getReceiveCurrency());
        $transfer->setStatus($data['status'])
            ->setReceiveAmount($paidAmount)
            ->setReceiveAt($data['paid_at'] ? Carbon::createFromTimestamp($data['paid_at']) : $transfer->getReceiveAt())
            ->setError($data['error'] ?? null)
            ->setSyncAt(new \DateTime());

        if (!empty($data['beneficiary']['masked_account_number'])) {
            $data['beneficiary']['account_number'] = $data['beneficiary']['masked_account_number'];
            unset($data['beneficiary']['masked_account_number']);
        }
        if (!empty($data['beneficiary']['account_number'])) {
            $data['beneficiary']['account_number'] = Util::maskPan($data['beneficiary']['account_number']);
        }

        // don't store the first change
        if (Util::meta($transfer, 'rapydPayout')) {
            $diff = Util::calculateArrayDiff(Util::meta($transfer, 'rapydPayout') ?? [], $data);
            $transfer->logChangeHistory($diff);
        }
        Util::updateMeta($transfer, [
            'rapydPayout' => $data,
        ]);

        TransactionService::updateTransactionByTransfer($transfer);

        $newStatus = $transfer->getStatus();
        try {
            if ($oldStatus !== $newStatus && in_array($newStatus, [
                Transfer::STATUS_HOLD,
                Transfer::STATUS_DECLINE,
                Transfer::STATUS_DECLINED,
            ])) {
                SlackService::info('Rapyd payout status update', [
                    'transfer' => $transfer->getId(),
                    'partnerId' => $transfer->getPartnerId(),
                    'oldStatus' => $oldStatus,
                    'newStatus' => $newStatus,
                ]);

                // https://ternitup.atlassian.net/browse/SPAN-31
                if ($newStatus === Transfer::STATUS_HOLD) {
                    Util::updateMeta($transfer, [
                        'holdAt' => date('c'),
                    ]);

                    $rapydPayout = Util::meta($transfer, 'rapydPayout') ?? [];
                    if (empty($rapydPayout['metadata']['beneficiary']['date_of_birth'])) {
                        $recipient = $transfer->getRecipient();
                        if ($recipient->getBirthday() === NULL) {
                            $sent = MemberService::sendInstantMessage($transfer->getSender(), [
                                'en' => new MessagePayload(
                                    'Transfer #' . $transfer->getId() . ' needs your action to complete',
                                    'Please ensure your mobile app is up to date and edit the recipient with full name and DOB.',
                                ),
                                'es' => new MessagePayload(
                                    'La transferencia #' . $transfer->getId() . ' necesita de una acción para ser completada',
                                    'Asegúrate que tu aplicación móvil esté actualizada y edita el destinatario con su nombre completo y fecha de nacimiento.',
                                ),
                            ]);
                            if ($sent) {
                                SlackService::$channel = SlackService::CHANNEL_CLIENT;
                                SlackService::point('Reminded the member to fill the DoB in the mobile app for the `Hold` transfer', [
                                    'member' => $transfer->getSender()->getSignature(),
                                    'transfer' => $transfer->getId(),
                                    'recipient' => $recipient->getCompleteFullName(),
                                    'methods' => implode(', ', $sent),
                                ]);
                            }
                        } else {
                            Data::pushToArray('transfermex_rapyd_hold_inquiry', $transfer->getId());
                        }
                    }
                }
            }
        } catch (\Throwable $t) {
            Log::warn('Failed to notify the Rapyd Hold/Decline status: ' . Util::getExceptionBrief($t));
        }

        if ( ! Transfer::isSentStatus($oldStatus) && Transfer::isSentStatus($newStatus)) {
            $context = self::getTransferContext($transfer);
            if (in_array($oldStatus, [
                Transfer::STATUS_DECLINE,
                Transfer::STATUS_DECLINED,
                Transfer::STATUS_ERROR,
                Transfer::STATUS_RETURNED,
                Transfer::STATUS_CANCELED,
                Transfer::STATUS_EXPIRED,
            ])) {
                SlackService::alert('`' . $oldStatus . '` payout becomes `' . $newStatus . '`!', $context, SlackService::GROUP_FAAS);

                if ($didNotRefundDirectly && $transfer->isDeducted()) {
                    SlackService::info(
                        'Skip deducting the amount from the member for the under review/timeout transfer.',
                        $context,
                        SlackService::MENTION_HANS
                    );
                } else {
                    RapidService::updateBalanceBy($transfer->getSenderCard(), -$transfer->getTotalAmount(),
                        'rapyd_transfer', $transfer->getId(), 'Transfer in ' . $transfer->getId(), $transfer, true);
                    SlackService::info('Deducted the transfer amount from the member.', $context, SlackService::MENTION_HANS);
                }

                $pendingToRecheck = $transfer->isPendingToRecheck();
                if ($pendingToRecheck) {
                    Util::updateMeta($transfer, [
                        'legacyUnhandledRapydCreateError' => $pendingToRecheck,
                    ], false);
                    Util::updateMeta($transfer, 'unhandledRapydError');
                }
            } else {
                $msg = 'Completed transfer/payout';
                if (Data::has('process_payout_queue_' . $transfer->getId())) {
                    $msg .= ' *from the payout queue*';
                }
                // update the daily amount
                $date =  Util::formatDateTime(Carbon::now(), Util::DATE_FORMAT, 'America/New_York');
                $dailyAmountKey = 'daily_transfer_amount_' . $transfer->getSender()->getId() . '_' . $date;
                $dailyAmount = Data::get($dailyAmountKey) ?? 0;
                Data::set($dailyAmountKey,  $dailyAmount + $transfer->getSendAmount(), true,  24 * 3600);
                TransferService::updatePromo($transfer);
                SlackService::tada($msg, $context);
            }
        } else if (!$initiative && in_array($oldStatus, [
                Transfer::STATUS_CREATED,
                Transfer::STATUS_PENDING,
                Transfer::STATUS_QUEUED,
                Transfer::STATUS_HOLD,
                Transfer::STATUS_PROCESSING,
                Transfer::STATUS_CONFIRMATION,
                Transfer::STATUS_COMPLETED,
            ]) && in_array($newStatus, [
                Transfer::STATUS_ERROR,
                Transfer::STATUS_RETURNED,
                Transfer::STATUS_DECLINE,
                Transfer::STATUS_DECLINED,
                Transfer::STATUS_CANCELED,
                Transfer::STATUS_EXPIRED,
            ]))
        {
            $context = self::getTransferContext($transfer);
            $mentions = $newStatus === Transfer::STATUS_ERROR ? [] : SlackService::GROUP_FAAS;
            SlackService::alert('`' . $oldStatus . '` payout becomes `' . $newStatus . '`!', $context, $mentions);

            if (Util::isLive()) {
                // Some transfers were completed twice during an accident in 2022-05-10. These transfers should not have a refund.
                $tid = $transfer->getId();
                if ($tid >= 23285 && $tid <= 23442 && !in_array($tid, [
                    // Error transfers that have been fixed manually
                    23304,
                    23336,
                    23350,
                    23392,
                ])) {
                    // Steps to fix it manually
                    // 1. Replace the error partner_id in the transfer with the completed one
                    // 2. Change the transfer status to `Completed`
                    // 3. Call https://www.virtualcards.us/dev/mex/rapyd/disburse/update-payout/new_partner_id
                    // 4. Remove the `refundedToMember` meta, but ensure the `payoutToBase` is kept.
                    $transfer->setRefunded(true);
                    SlackService::info('Skip refunding the transfer. It needs to be fixed manually.', $context, SlackService::MENTION_HANS);
                    return $transfer;
                }
            }

            if ($transfer->isDeducted() && !$transfer->isRefunded()) {
                $api = null;
                $fromBase = false;
                if (Util::meta($transfer, 'payoutToBase')) {
                    $fromBase = true;
                    $api = new RapidAPI(); // Refund from base agent since it was moved there
                }
                $refunded = RapidService::updateBalanceBy(
                    $transfer->getSenderCard(),
                    $transfer->getTotalAmount(),
                    'rapyd_transfer_reverse', $transfer->getId(),
                    $newStatus . ' transfer', $transfer,
                    true,
                    $api
                );
                $context['refunded'] = $refunded;
                $transfer->setRefunded(true);
                Util::updateMeta($transfer, 'payoutToBase');
                $transfer->setPayoutToBase(null)
                        ->persist();
                $msg = 'Refunded the transfer amount to the member';
                if ($fromBase) {
                    $msg .= ' *from the base agent*';
                }
                SlackService::info($msg, $context);

                if ($refunded) {
                    TransferHealthService::saveRefundCache($transfer);
                }
            }
            TransferService::updatePromo($transfer, true);
        }

        return $transfer;
    }

    public static function getTransferContext(Transfer $transfer)
    {
        return [
            'transfer' => $transfer->getId(),
            'employer' => $transfer->getSender()->getPrimaryGroupName(),
            'sender' => $transfer->getSender()->getSignature(),
            'type' => $transfer->getPayoutType(),
            'method' => $transfer->getPayoutMethodType(),
            'amount' => Money::formatWhen($transfer->getSendAmount(), $transfer->getSendCurrency(), true),
            'status' => $transfer->getPartnerStatus(),
        ];
    }

    public static function getDailyRate($force = false, $fromCurrency = 'USD', $toCurrency = 'MXN')
    {
        $args = compact('fromCurrency', 'toCurrency');
        if ($force) {
            Data::del(Data::key(__METHOD__, $args));
        }
        return Data::callback(__METHOD__, $args, function () use ($fromCurrency, $toCurrency) {
            $api = new RapydAPI();
            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->getDailyRate(RapydAPI::RATE_TYPE_PAYOUT, $fromCurrency, $toCurrency);
            if ($ei && $ei->isFailed()) {
                throw PortalException::create($ei->getError());
            }
            if (!isset($data['rate'])) {
                throw PortalException::create('Invalid daily rate when converting ' . $fromCurrency . ' to ' . $toCurrency);
            }
            self::validateDailyRate($data['rate'], $fromCurrency, $toCurrency, RapydAPI::RATE_TYPE_PAYOUT);
            return $data['rate'] * (1 - self::getPartnerRateMarkup() - self::getPlatformRateMarkup());
        }, 3600);
    }

    public static function validateDailyRate(float|int|string $rate, string $fromCurrency, string $toCurrency, string $type = null)
    {
        $valid = true;
        $rate = (float)$rate;
        if ($fromCurrency === 'USD' && $toCurrency === 'MXN') {
            // Currently min & max values: 15.87891687, 21.28565167
            if ($rate < 8 || $rate > 28) {
                $valid = false;
            }
        }
        if ($fromCurrency === 'MXN' && $toCurrency === 'USD') {
            if ($rate < 0.0357143 || $rate > 0.125) { // 1 / 28, 1 / 8
                $valid = false;
            }
        }
        if ( ! $valid) {
            SlackService::alert('Possibly illegal FX rate: `' . $rate . '`.', [],
                SlackService::GROUP_TRANSFER_MEX_DEV);
            throw PortalException::create('FX rate beyonds the valid range!');
        }
    }

    public static function getPayoutOptions($force = false, UserCard $uc = null, User $recipient = null)
    {
        $defaultRateInfo = Currency::find($recipient ? $recipient->getCountry()->getCurrency() : 'MXN');
        $defaultRate = $defaultRateInfo ? $defaultRateInfo->getOneUsd() : 18.511111;
        try {
            $currency = $recipient ? $recipient->getCountry()->getCurrency() : 'MXN';
            $dailyRate = Data::get('intermex_rate_currently_' . $currency) ?? $defaultRate; //self::getDailyRate($force);
        } catch (\Exception $e) {
            Log::warn('Failed to query Rapyd rate: ' . $e->getMessage());
            $dailyRate = $defaultRate;
        }
        $min = TransferMexBundle::getMinTransferAmount($uc);
        $testAccounts = Config::array('intermex_test_account');
        if ($uc && in_array($uc->getUser()->getId(), $testAccounts)) {
          $min = 100;
        }
        // if ($recipient) {
        //   $min = IntermexRemittanceService::getMinTransferAmount('bank', '', $recipient->getCountry()->getIsoCode());
        // }
        return [
            'minAmount' =>  $min,
            'maxAmount' => TransferMexBundle::getMaxTransferAmount(),
            'dailyRate' => $dailyRate * 1.0,
            'feeCash' => self::FEE_CASH_PICKUP,
            'feeBank' => self::FEE_BANK_TRANSFER,
            'otherCountryFee' => self::OTHER_COUNTRY_FEE
        ];
    }

    public static function getCachedFilteredMethodTypes($simple = true)
    {
        $all = self::getCachedMethodTypes($simple);
        $live = Util::isLive();

        $result = [];
        foreach ($all as $key => $items) {
            $is = [];
            foreach ($items as $item) {
                if (Util::startsWith($key, 'cash.')) {
                    if ($live && !Util::startsWith($item['name'], 'Cash disbursement through ')) {
                        continue;
                    }
                    $item['name'] = trim(str_replace([
                        'Cash Transfer to ',
                        'Cash disbursement through ',
                    ], '', $item['name']));
                }
                $is[] = $item;
            }
            $result[$key] = $is;
        }
        return count($result) ? $result : null;
    }

    public static function getCachedMethodTypes($simple = true)
    {
        $key = $simple ? 'transfermex_rapyd_payout_methods_simple' : 'transfermex_rapyd_payout_methods';
        return Data::getArray($key);
    }

    public static function getCachedMethodType($type)
    {
        $all = self::getCachedMethodTypes();
        foreach ($all as $group => $types) {
            foreach ($types as $t) {
                if ($t['payout_method_type'] === $type) {
                    return $t;
                }
            }
        }
        return [];
    }

    public static function getCachedRequireFields($type, $side = 'beneficiary')
    {
        $method = self::getCachedMethodType($type);
        return $method[$side . '_required_fields'] ?? [];
    }

    public static function getRequiredFieldInfo($type, $field, $side = 'beneficiary')
    {
        $all = self::getCachedRequireFields($type, $side);
        foreach ($all as $item) {
            if ($item['name'] === $field) {
                return $item;
            }
        }
        return null;
    }

    public static function isClabeRequired($type, $side = 'beneficiary')
    {
        $bat = self::getRequiredFieldInfo($type, 'bank_account_type', $side);
        if (!empty($bat['regex']) && $bat['regex'] === 'clabe') {
            return true;
        }
        $bat = self::getRequiredFieldInfo($type, 'tipoCuentaBeneficiario', $side);
        if (!empty($bat['regex']) && $bat['regex'] === 'clabe') {
            return true;
        }
        return false;
    }

    public static function isEnableMethod($type) {
      $all = self::getCachedMethodTypes();
      foreach ($all as $group => $types) {
          foreach ($types as $t) {
              if ($t['payout_method_type'] === $type) {
                  return true;
              }
          }
      }
      return false;
    }

    public static function createSender(User $user, $entity_type = 'individual')
    {
        $api = new RapydAPI();
        return $api->createSender([
            'company_name' => trim($user->getFullName()),
            'country' => $user->getCountry()->getIsoCode(),
            'currency' => 'USD',
            'entity_type' => $entity_type,
            'first_name' => trim($user->getFirstName()),
            'identification_type' => RapydAPI::ID_TYPE_SOCIAL_SECURITY,
            'identification_value' => $user->getCurpDecrypted(),
            'last_name' => trim($user->getLastName()),
        ]);
    }

    /**
     * It's not in use right now. We are including the beneficiary object directly when creating payouts
     */
    public static function createBeneficiary(User $user, $category = 'cash', $entity_type = 'individual', $default_payout_method_type = 'mx_banortemadero_cash')
    {
        $api = new RapydAPI();
        return $api->createBeneficiary([
            'category' => $category,
            'company_name' => trim($user->getFullName()),
            'country' => $user->getCountry()->getIsoCode(),
            'currency' => 'MXN',
            'default_payout_method_type' => $default_payout_method_type,
            'entity_type' => $entity_type,
            'first_name' => trim($user->getFirstName()),
            'identification_type' => RapydAPI::ID_TYPE_SOCIAL_SECURITY,
            'identification_value' => $user->getCurpDecrypted(),
            'last_name' => trim($user->getLastName()),
            'merchant_reference_id' => '' . $user->getId(),
        ]);
    }

    /**
     * @return array
     */
    public static function getBrands()
    {
        return [
            "Banorte",
            "Intermex Pue S.A. De C.V",
            "Tiendas Del Sol",
            "Woolworth",
            "Caja Popular Mexicana",
        ];
//        $content = Util::s2j(file_get_contents(__DIR__ . '/../../../../shared/locations-all-mxn-funds-out-live.json'));
//        $all = $content['data']['data'];
//        $brands = [];
//        foreach ($all as $item) {
//            $bs = $item['brands'] ?? [];
//            $brands = array_unique(array_merge($brands, $bs));
//        }
//        return $brands;
    }

    public static function getWalletBalance($currency = 'USD')
    {
        $api = new RapydAPI();
        $walletId = $api->getParameter('rapyd_wallet_id');
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->getWalletAccounts($walletId);
        if ($ei && $ei->isFailed()) {
            throw PortalException::fromEi($ei);
        }
        if (empty($data)) {
            throw PortalException::temp('Empty wallet accounts!');
        }
        $balance = 0;
        foreach ($data as $account) {
            $cur = $account['currency'] ?? '';
            if ($cur === $currency) {
                $balance = $account['balance'] ?? 0;
                break;
            }
        }
        return Money::normalizeAmount($balance, 'USD');
    }

    public static function getWalletTransactions($type = RapydAPI::TRANSACTION_TYPE_TOP_UP_FUNDS_IN, $page_size = null)
    {
        $api = new RapydAPI();
        $walletId = $api->getParameter('rapyd_wallet_id');
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->getWalletTransactions($walletId, $type, $page_size ? 1 : null, $page_size);
        if ($ei && $ei->isFailed()) {
            throw PortalException::fromEi($ei);
        }
        return $data ?: [];
    }

    public static function getWalletTransactionDetail($wtId)
    {
        $api = new RapydAPI();
        $walletId = $api->getParameter('rapyd_wallet_id');
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->getWalletTransactionDetail($walletId, $wtId);
        if ($ei && $ei->isFailed()) {
            throw PortalException::fromEi($ei);
        }
        return $data ?? [];
    }

    public static function getCachedWalletBalance()
    {
        return (int)Data::get('transfermex_rapyd_balance');
    }

    public static function checkAndAlertBalanceChanges($updateTopUps = false)
    {
        $oldBalance = self::getCachedWalletBalance();
        $balance = self::getWalletBalance();

        if ($oldBalance !== $balance) {
            if ($updateTopUps) {
                self::updateTotalTopUpAmount();
            }
            $trans = self::getWalletTransactions(null, 1);
            SlackService::wave('Rapyd wallet balance changed.', [
                'Available Balance' => Money::format($balance, 'USD'),
                'Latest transaction' => $trans ? $trans[0] : null,
            ]);
            Data::set('transfermex_rapyd_balance', $balance);
        }

        return $balance;
    }

    public static function updateTotalTopUpAmount()
    {
        $total = 0;
        $all = self::getWalletTransactions();
        foreach ($all as $t) {
            if (($t['balance_type'] ?? '') !== 'available_balance') {
                continue;
            }
            if (($t['status'] ?? '') !== 'CLOSED') {
                continue;
            }
            $amount = Money::normalizeAmount($t['amount'] ?? 0, 'USD');
            $total += $amount;
        }
        Data::set('transfermex_rapyd_top_up', $total);
        return $total;
    }

    public static function isFromOtherPlatform(array &$data)
    {
        $desc = $data['description'] ?? null;
        if ($desc) {
            if (str_starts_with($desc, 'SPAN - ')) {
                return false;
            }
            if (str_starts_with($desc, 'Payout - ')) { // BOTM uses this as the description
                return 'BOTM like payouts: ' . $desc;
            }
        }
        if (array_key_exists('sender', $data) && is_array($data['sender'])) {
            $sender = $data['sender'];
            if (array_key_exists('company_name', $sender)) {
                $sender_company = $data['sender']['company_name'];
                if ($sender_company !== Platform::NAME_TRANSFER_MEX) {
                    return $sender_company;
                }
            }
        }
        if (array_key_exists('beneficiary', $data) && is_array($data['beneficiary'])) {
            $beneficiary = $data['beneficiary'];
            if (array_key_exists('company_name', $beneficiary)) {
                $beneficiary_company = $beneficiary['company_name'];
                if ($beneficiary_company !== Platform::NAME_TRANSFER_MEX) {
                    return $beneficiary_company;
                }
            }
            if (array_key_exists('merchant_reference_id', $beneficiary)) {
                $uid = $beneficiary['merchant_reference_id'] ?? 0;
                if (!$uid) {
                    return 'empty_beneficiary_merchant_reference_id';
                }
            }
//        $beneficiary = User::find($uid);
//        if (!$beneficiary) {
//            return 'beneficiary_not_found_' . $uid;
//        }
//        if (!$beneficiary->hasRole(Role::ROLE_TRANSFER_MEX_RECIPIENT)) {
//            return 'beneficiary_not_recipient_' . $uid;
//        }
        }
        return false;
    }

    public static function checkAndRefundUnhandledTransfer(Transfer $transfer)
    {
        Data::singletonThrowable('rapyd_refund_unhandled_transfer_' . $transfer->getId(), function () use ($transfer) {
            if ( ! $transfer->isError()) {
                throw new FailedException('The transfer is not Error!');
            }
            $known = $transfer->isFailedByRapydAndDidNotRefundDirectly();
            if (!$known) {
                throw new FailedException('The transfer was created!');
            }
            if (!$transfer->isDeducted()) {
                throw new FailedException('The transfer was not deducted from the member!');
            }
//            $hasReturn = RapydRefundService::hasReturnedInRapydWallet($transfer);
//            if ( ! $hasReturn) {
//                throw new FailedException('No valid return in Rapyd wallet!');
//            }
            $refunded = RapydRefundService::syncAndFindRefundTransaction($transfer);
            if ($refunded) {
                throw new FailedException('The transfer had been refunded in the card transaction ' . $refunded->getId());
            }
            $uc = $transfer->getSenderCard();
            $amount = $transfer->getTotalAmount();
            $type = 'rapyd_transfer';
            $id = '' . $transfer->getId();
            $comment = 'Transfer ' . Money::formatWhen($amount) . ' in ' . $id;
            try {
                RapidService::updateBalanceBy($uc, $amount, $type . '_reverse', $id, $comment, $transfer);
            } catch (\Exception $exception) {
                SlackService::exception('Failed to refund transfer amount to user: ' . $exception->getMessage(), $exception, [
                    'user' => $uc->getUser()->getId(),
                    'transfer' => $transfer->getId(),
                ], SlackService::MENTION_HANS);
                throw $exception;
            }
            $transfer->setRefunded(true);

            $pendingToRecheck = $transfer->isPendingToRecheck();
            Util::updateMeta($transfer, [
                'legacyUnhandledRapydCreateError' => $pendingToRecheck,
            ], false);
            Util::updateMeta($transfer, 'unhandledRapydError');

            SlackService::wave('Refunded the failed transfer due to Rapyd creation error manually', [
                'transfer' => $transfer->getId(),
                'member' => $transfer->getSender()->getId(),
                'previous_error' => $pendingToRecheck,
                'operator' => Util::id(Util::user()),
            ], SlackService::MENTION_HANS);
        });
    }

    public static function sendEmailToRapydSupport($subject, $body, $live = true, $otherCc = [])
    {
        if ($live && Util::isLive() && !Util::isDev()) {
            $to = [
                '<EMAIL>',
            ];
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];

            if ($otherCc) {
                $cc = array_merge($cc, $otherCc);
            }
        } else {
            $to = '<EMAIL>';
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
        }

        Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'name' => 'Rapyd Support',
            'subject' => $subject,
            'body' => $body,
            '_cc' => $cc,
        ], null, CardProgram::transferMexUSD());
    }
}
