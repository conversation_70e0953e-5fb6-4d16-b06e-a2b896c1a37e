<?php

namespace TransferMexBundle\Services;

use CoreBundle\Entity\Transfer;
use CoreBundle\Services\ThrottleService;
use CoreBundle\Entity\UserCard;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;

class TransferHealthService
{
    public static function saveRefundCache(Transfer $transfer)
    {
        $time = Util::randTimeNumber();
        $key = 'payout_refund_' . $transfer->getId() . '_' . $time;
        Data::set($key, 1, true, 8640000); // 100 days
    }

    public static function getErrorThrottleForQueue()
    {
        return new ThrottleService('rapyd_transfer_queue_throttle', 20, 3600);
    }

    public static function syncTransaction(UserCard $uc, int $days = 10)
    {
        $params = [
            '--uc' => $uc->getId(),
            '--days' => $days,
        ];
        $output = Util::executeCommand('span:mex:update-transactions', $params);
        if (!$output || str_contains($output, 'Failed to')) {
            throw PortalException::temp('Error when syncing transactions to decide the health', [
                'uc' => $uc->getId(),
                'caller' => Util::getCaller(true),
            ]);
        }
    }
}
