<?php


namespace TransferMexBundle\Services;


use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use CoreBundle\Utils\Data;
use SalexUserBundle\Entity\User;
use CoreBundle\Entity\Country;
use MobileBundle\MobileBundle;
use CoreBundle\Utils\Security;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\CardProgram;
class UniTellerAPI
{
    public $live = true;
    protected $retry = false;
    protected $retryCount = 0;
    protected $retryAvailable = false;
    protected $ipLimit = false;
    protected $getClientTokenRetry = false;
    protected $getUserTokenRetry = false;
    protected $getAdminTokenRetry = false;

    /**
     * Error Code
     *  01 => get client token error
     *  02 => get user token error
     *  03 => get admin token error
     *  04 => get externalQuickQuotes
     *  05 => get rate
     *  06 => create transfer error
     *  07 => cancel transfer error
     *  08 => get security Questions error
     *  09 => check dupllcate User error
     *  10 => get method error
     */


    /**
     * RapydDisburseService constructor.
     *
     * @param null|false $live
     */
    public function __construct($live = null)
    {
        if ($live === null) {
            $live = Util::isLive();
        }
        $this->live = $live;
    }

    public function getParameter($key)
    {
        return Util::getKmsParameter($this->live ? $key : ($key . '_test'));
    }


    public function getPlatform() {
        $platform = MobileBundle::extractEnvVar('os');
        $res = 'WEB';
        if ($platform === 'ios') {
          $res = 'IOS';
        } else if ($platform === 'android') {
          $res = 'ANDROID';
        }
        if (!Util::isLive()) {
          Log::debug('client platform: ' . $res);
        }
        return $res;
    }

    public function getIP() {
        $res = Security::getClientIp();
        if ($res) {
            Log::debug('client IP: ' . $res);
        }
        return $this->ipLimit || $this->retryAvailable ? Security::getServerIp() : $res;
    }

    public function getVersion() {
        $res = MobileBundle::extractEnvVar('version', '1.0');
        if (!Util::isLive()) {
          Log::debug('client app version: ' . $res);
        }
        return $res;
    }

    public function getClientToken($update = false)
    {
        $key = 'uniteller_client_token_' . ($this->live ? 'live' : 'test');
        if (!$update) {
            $token = Data::get($key);
            if ($token) {
                return $token;
            }
        }
        $url = Util::getConfigKey('uniteller_base_url');
        $client = new Client();

        try {
            $partnerSecret = $this->getParameter('uniteller_partner_secret');
            $signature = 'Basic ' . $partnerSecret;
            $ip = Security::getServerIp(); // isset($_SERVER["REMOTE_ADDR"]) ? $_SERVER["REMOTE_ADDR"] : Security::getClientIp();
            $response = $client->post($url . 'oauth/token', [
                'headers' => [
                    'Content-Type'        => 'application/x-www-form-urlencoded',
                    'Authorization'       => $signature,
                    'application_version' => $this->getVersion(),
                    'PLATFORM'            => $this->getPlatform(),
                    'X-Forwarded-For'     => $ip, // $this->getIP()
                    'authentication_Code' => 'TERNITUP'
                ],
                'form_params' => ['grant_type' => 'client_credentials']
            ]);

        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            Log::debug('Get Client Token Error IP: ' . $ip);
            if (!$response) {
                Log::warn(str_replace([$partnerSecret], '***', $exception->getMessage()));
                if (!$this->getClientTokenRetry) {
                  $this->getClientTokenRetry = true;
                  return $this->getClientToken(true);
                }
                $this->getClientTokenRetry = false;
                throw PortalException::create('Sorry that the operation failed due to an error (code: 01). We are investigating. Please reach out to our support if it failed constantly.');
            }

            $content = $response->getBody()->getContents() ?: '{}';
            $content = Util::s2j($content) ?? [];
            $message = $content['errorDescription'] ?? $content['error'] ?? $exception->getMessage();
            Log::warn(str_replace([$partnerSecret], '***', $message));
            if ($this->retry) {
              Data::del($key);
              Data::del($key . '_refresh_token');
              return ;
            }
            Log::debug('First time error IP: ' . $ip);
            if (!$this->getClientTokenRetry) {
              $this->getClientTokenRetry = true;
              return $this->getClientToken(false);
            }
            Log::debug('Second time error IP: ' . $ip);
            $this->getClientTokenRetry = false;
            throw PortalException::create('Sorry that the operation failed due to an error (code: 01). We are investigating. Please reach out to our support if it failed constantly.');
        }

        $content = $response->getBody()->getContents();
        $content = Util::s2j($content) ?? [];

        if (!Util::isLive()) {
            Log::debug('Uniteller Client token response', $content);
        }

        if (empty($content['access_token'])) {
            Log::error('Empty Uniteller token', $content);
            throw PortalException::create('Sorry that the operation failed due to an error (code: 01). We are investigating. Please reach out to our support if it failed constantly.');
        }
        $this->getClientTokenRetry = false;
        Data::set($key, $content['access_token'], false, ($content['expires_in'] ?? 60) - 5);
        return $content['access_token'];
    }

    public function getUserToken(User $user,$update = false)
    {
        $key = 'uniteller_user_token_' . $user->getId() . '_' . ($this->live ? 'live' : 'test');
        if (!$update) {
            $token = Data::get($key);
            if ($token) {
                return $token;
            }
        }
        $url = Util::getConfigKey('uniteller_base_url');
        $client = new Client();
        $ip = Security::getServerIp(); //isset($_SERVER["REMOTE_ADDR"]) ? $_SERVER["REMOTE_ADDR"] : Security::getClientIp();
        // Log::debug($url);
        // Log::debug($this->live);
        try {
            $partnerSecret = $this->getParameter('uniteller_partner_secret');
            $signature = 'Basic ' . $partnerSecret;
            $params = ['grant_type' => 'password', 'username' => UniTellerRemittanceService::getUniTellerId($user)];
            if ($update) {
              $params = [
                'grant_type' => 'refresh_token',
                'refresh_token' => Data::get($key . '_refresh_token'),
                'application_version' => $this->getVersion(),
                'PLATFORM'            => $this->getPlatform(),
                'X-Forwarded-For'     => $ip, // $this->getIP()
                'authentication_Code' => 'TERNITUP'
              ];
            }
            if (!Util::isLive()) {
              Log::debug('Uniteller User token params: ', $params);
            }
            $response = $client->post($url . 'oauth/token', [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Authorization' => $signature
                ],
                'form_params' => $params
            ]);

        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            Log::debug('Get User Token Error IP:' . $ip);
            if (!$response) {
                Log::warn(str_replace([$partnerSecret], '***', $exception->getMessage()));
                if (!$this->getUserTokenRetry) {
                  $this->getUserTokenRetry = true;
                  return $this->getUserToken($user, true);
                }
                $this->getUserTokenRetry = false;
                throw PortalException::create('Sorry that the operation failed due to an error (code: 02). We are investigating. Please reach out to our support if it failed constantly.');
            }

            $content = $response->getBody()->getContents() ?: '{}';
            $content = Util::s2j($content) ?? [];
            $message = $content['errorDescription'] ?? $content['error'] ?? $exception->getMessage();
            Log::warn(str_replace([$partnerSecret], '***', $message));
            if ($this->retry) {
              Data::del($key);
              Data::del($key . '_refresh_token');
              return ;
            }
            if (!$this->getUserTokenRetry) {
              $this->getUserTokenRetry = true;
              return $this->getUserToken($user, true);
            }
            $this->getUserTokenRetry = false;
            throw PortalException::create('Sorry that the operation failed due to an error (code: 02). We are investigating. Please reach out to our support if it failed constantly.');
        }

        $content = $response->getBody()->getContents();

        $content = Util::s2j($content) ?? [];

        if (!Util::isLive()) {
            Log::debug('Uniteller User token response', $content);
        }

        if (empty($content['access_token'])) {
            Log::error('Empty Uniteller token', $content);
            if (!$this->getUserTokenRetry) {
              $this->getUserTokenRetry = true;
              return $this->getUserToken($user, true);
            }
            $this->getUserTokenRetry = false;
            throw PortalException::create('Sorry that the operation failed due to an error (code: 02). We are investigating. Please reach out to our support if it failed constantly.');
        }

        $this->getUserTokenRetry = false;
        Data::set($key, $content['access_token'], false);
        Data::set($key . '_refresh_token', $content['refresh_token'], false);
        return $content['access_token'];
    }

    public function getAdminToken($update = false)
    {
        $key = 'uniteller_admin_token_' . ($this->live ? 'live' : 'test');
        if (!$update) {
            $token = Data::get($key);
            if ($token) {
                return $token;
            }
        }
        $url = Util::getConfigKey('uniteller_base_url');
        $client = new Client();
        $ip = Security::getServerIp(); // isset($_SERVER["REMOTE_ADDR"]) ? $_SERVER["REMOTE_ADDR"] : Security::getClientIp();
        // Log::debug($url);
        // Log::debug($this->live);
        try {
            $partnerAdminSecret = $this->getParameter('uniteller_partner_admin_secret');
            $signature = 'Basic ' . $partnerAdminSecret;
            $params = ['grant_type' => 'password', 'username' => Util::getConfigKey('uniteller_admin_email')];
            if ($update) {
              $params = [
                'grant_type' => 'refresh_token',
                'refresh_token' => Data::get($key . '_refresh_token')
              ];
            }
            $response = $client->post($url . 'oauth/token', [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Authorization' => $signature,
                    'application_version' => $this->getVersion(),
                    'PLATFORM'            => $this->getPlatform(),
                    'X-Forwarded-For'     => $ip, // $this->getIP()
                    'authentication_Code' => 'TERNITUP'
                ],
                'form_params' => $params
            ]);

        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            Log::debug('Get Admin Token Error IP' . $ip);
            if (!$response) {
                Log::warn(str_replace([$partnerAdminSecret], '***', $exception->getMessage()));
                if (!$this->getAdminTokenRetry) {
                  $this->getAdminTokenRetry = true;
                  return $this->getAdminToken( true);
                }
                $this->getAdminTokenRetry = false;
                throw PortalException::create('Sorry that the operation failed due to an error (code: 03). We are investigating. Please reach out to our support if it failed constantly.');
            }

            $content = $response->getBody()->getContents() ?: '{}';
            $content = Util::s2j($content) ?? [];
            $message = $content['errorDescription'] ?? $content['error'] ?? $exception->getMessage();
            Log::warn(str_replace([$partnerAdminSecret], '***', $message));
            if ($this->retry) {
              Data::del($key);
              Data::del($key . '_refresh_token');
              return ;
            }
            if (!$this->getAdminTokenRetry) {
              $this->getAdminTokenRetry = true;
              return $this->getAdminToken( true);
            }
            $this->getAdminTokenRetry = false;
            throw PortalException::create('Sorry that the operation failed due to an error (code: 03). We are investigating. Please reach out to our support if it failed constantly.');
        }

        $content = $response->getBody()->getContents();
        $content = Util::s2j($content) ?? [];

        if (!Util::isLive()) {
            Log::debug('Uniteller User token response', $content);
        }

        if (empty($content['access_token'])) {
            Log::error('Empty Uniteller token', $content);
            if (!$this->getAdminTokenRetry) {
              $this->getAdminTokenRetry = true;
              return $this->getAdminToken( true);
            }
            $this->getAdminTokenRetry = false;
            throw PortalException::create('Sorry that the operation failed due to an error (code: 03). We are investigating. Please reach out to our support if it failed constantly.');
        }

        $this->getAdminTokenRetry = false;
        Data::set($key, $content['access_token'], false, ($content['expires_in'] ?? 60) - 5);
        Data::set($key . '_refresh_token', $content['refresh_token'], false);
        return $content['access_token'];
    }

    protected function request($method, $endpoint, $params = [], $endpointSuffix = '', $saveEi = true, User $user = null, $isAdmin = false)
    {
        $method = strtoupper($method);
        $post = in_array($method, ['POST', 'PUT']);
        $url = Util::getConfigKey('uniteller_base_url');

        if ($isAdmin) {
          $token = $this->getAdminToken(false);
        } else {
          if ($user) {
            $token = $this->getUserToken($user, false);
          } else {
            $token = $this->getClientToken(false);
          }
        }

        // just for test: if retry is true, means we get a nw token, we should waite
        // 2 seconds to make sure the new token works in UniTeller to avoid 503 error
        if ($this->retry) {
          Log::debug('get new token and waite');
          sleep(2);
        }

        if (!Util::isLive()) {
          Log::debug($token);
          Log::debug($endpoint);
          Log::debug('data', $params);
        }

        $context = $params;
        $context['__url'] = $endpoint . $endpointSuffix;

        $ei = ExternalInvoke::create('uniteller_' . $method . '_' . $endpoint, $context, null, false);

        $ei->setRequestKey(Util::guid());
        if ($saveEi) {
            $ei->persist();
        }

        // if ($isAdmin) {
        //   Util::updateMeta($ei, [
        //     'token' => substr($token, -10)
        //   ]);
        // }

        if ($endpointSuffix) {
          $endpoint .= $endpointSuffix;
        }

        $client = new Client();
        try {

            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
                'application_version' => $this->getVersion(),
                'PLATFORM'            => $this->getPlatform(),
                'X-Forwarded-For'     => $this->getIP(),
                'authentication_Code' => 'TERNITUP'
            ];
            if (!Util::isLive()) {
              Log::debug('Request header: ', $headers);
            }
            $options = [
                'headers' => $headers,
            ];
            if ($params) {
                if ($post) {
                    $options['json'] = $params;
                } else {
                    $options['query'] = $params;
                }
            }

            $response = $client->request($method, $url . $endpoint, $options);
        } catch (\Throwable $exception) {
            $response = $exception instanceof RequestException ? $exception->getResponse() : null;
            if (!$response) {
                $msg = 'Empty response when call UniTeller API: ' . $exception->getMessage();
                $ei->fail(null, $msg)
                    ->persist();
                Log::exception($msg, $exception);
                $this->cacheErrorToPool($ei);
                Util::updateMeta($ei, [
                  'token' => substr($token, -10)
                ]);
                return [$ei, null];
            }

            $rawContent = $response->getBody()->getContents();
            $content = Util::s2j($rawContent ?: '{}') ?? [];
            $status = $content['status'] ?? [];
            $msg = (isset($status['error_code']) ? ($status['error_code'] . ': ') : '')
                   . ($status['message'] ?? $exception->getMessage());
            $msg = 'Error occurred when call the UniTeller API: ' . $msg;
            Log::debug($msg);
            $ei->fail($rawContent, $msg)
                ->persist();
            //if ($isAdmin) {
            Util::updateMeta($ei, [
              'token' => substr($token, -10)
            ]);
            //}
            // refresh token and request again
            if (isset($content['responseCode']) && ( in_array($content['responseCode'], ['50108004', '10104101']) || (isset($content['responseMessage']) && $content['responseMessage'] === 'invalid_token'))) {
                  $this->retryCount++;
                  // when we get token error second time, del the token and try
                  if ($this->retryCount == 2) {
                    if ($isAdmin) {
                      Data::del('uniteller_admin_token_' . ($this->live ? 'live' : 'test'));
                    } else {
                      if ($user) {
                        Data::del('uniteller_user_token_'. $user->getId() . '_' . ($this->live ? 'live' : 'test'));
                      } else {
                        Data::del('uniteller_client_token_' . ($this->live ? 'live' : 'test'));
                      }
                    }
                    return $this->request($method, $endpoint, $params, '', $saveEi, $user, $isAdmin);
                  }
                  // when we get token error first time, refresh the token and try
                  if (!$this->retry) {
                    $this->retry = true;
                    if ($isAdmin) {
                      $this->getAdminToken(true);
                    } else {
                      if ($user) {
                        $this->getUserToken($user, true);
                      } else {
                        $this->getClientToken(true);
                      }
                    }
                    return $this->request($method, $endpoint, $params, '', $saveEi, $user, $isAdmin);
                }
            }
            $this->ipLimit = false;
            if (stripos($msg, 'service is not available in') != false || (isset($content['responseCode']) && in_array($content['responseCode'], ['59906002']))) {
              $this->ipLimit = true;
              $this->retry = true;
              if (stripos($endpoint, 'Security/v2/Signup') != false) {
                $params['userIPAddress'] = $_SERVER["REMOTE_ADDR"];
              }
            }
            // when get 503 error or IP limit, the servie is not available, try again
            if ($this->ipLimit && !$this->retryAvailable) {
              $this->retryAvailable = true;
              return $this->request($method, $endpoint, $params, '', $saveEi, $user, $isAdmin);
            }

            $this->retry = false;
            $this->retryCount = 0;
            $this->cacheErrorToPool($ei);
            return [$ei, null];
        }
        if ($saveEi) {
          Util::updateMeta($ei, [
            'token' => substr($token, -10)
          ]);
        }

        $rawContent = $response->getBody()->getContents();
        if (!Util::isLive()) {
          Log::debug($rawContent);
        }
        $content = Util::s2j($rawContent) ?? [];
        $responseCode = $content['responseCode'] ?? [];
        //  refresh token and request again
        if (in_array($responseCode, ['50108004'])) {
              $this->retryCount++;
              // when we get token error second time, del the token and try
              if ($this->retryCount == 2) {
                if ($isAdmin) {
                  Data::del('uniteller_admin_token_' . ($this->live ? 'live' : 'test'));
                } else {
                  if ($user) {
                    Data::del('uniteller_user_token_'. $user->getId() . '_' . ($this->live ? 'live' : 'test'));
                  } else {
                    Data::del('uniteller_client_token_' . ($this->live ? 'live' : 'test'));
                  }
                }
                return $this->request($method, $endpoint, $params, '', $saveEi, $user, $isAdmin);
              }
              // when we get token error first time, refresh the token and try
              if (!$this->retry) {
                $this->retry = true;
                if ($isAdmin) {
                    $this->getAdminToken(true);
                  } else {
                    if ($user) {
                      $this->getUserToken($user, true);
                    } else {
                      $this->getClientToken(true);
                    }
                  }
                return $this->request($method, $endpoint, $params, '', $saveEi, $user, $isAdmin);
              }
        }
        if ($responseCode !== '00000000') {
            $msg = 'UniTeller API error: ' . (isset($content['errorCode']) ? ($content['errorCode']['errorCode'] . ': ') : '')
                   . isset($content['errorCode']) ? ($content['errorCode']['errorMessage'] ?  $content['errorCode']['errorMessage'] : 'Unknown error') : 'Unknown error';
            $ei->fail($rawContent, $msg)
                ->persist();
            $this->cacheErrorToPool($ei);
        } else {
            $ei->succeed($content);
            // $content = $content['data'] ?? [];
            if ($saveEi) {
                $ei->persist();
            }
        }
        $this->retry = false;
        $this->retryCount = 0;
        return [$ei, $content];

    }

    public function getSecurityQuestions ($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/SignupSecurityQuestions', $params);
    }

    public function getDestCountryWithCurrency ($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Common/DestCountryWithCurrency', $params);
    }

    public function checkDupllcateUser($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Security/CheckDuplicateUser', $params);
    }

    public function getUserProfileInfo($params, $user) {
      return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/UserProfileInfo',  $params, '', true, $user);
    }

    public function editUserProfile($params, $user) {
      return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/EditUserProfile', $params, '', true, $user);
    }

    public function registerUser($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Security/v2/Signup', $params);
    }

    public function getPayerWithReceptionMethodsList($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Common/v2/PayerWithReceptionMethodsList', $params);
    }

    public function getPayerAdditionalFields($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Common/v2/PayerAdditionalField', $params);
    }

    public function getPayerBranch($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Common/PayerBranches', $params);
    }

    public function getCountryState(Country $country) {
       $params = [
        'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
        'countryISOCode' => $country ? $country->getIsoCode() : 'MX',
        "locale" => "EN"
       ];
       return $this->request('post', 'UTLROnlineRemitAPI/Common/CountryStates', $params);
     }

     public function getCountryCurrencies(Country $country) {
      $params = [
       'partnerCode' => Util::getConfigKey('uniteller_partner_code'),
       'countryISOCode' => $country ? $country->getIsoCode() : 'MX',
       "locale" => "EN"
      ];
      return $this->request('post', 'UTLROnlineRemitAPI/Common/CountryCurrencies', $params);
    }

    public function getStateList() {
      $params = [
        'partnerCode' => Util::getConfigKey('uniteller_partner_code')];
      return $this->request('post', 'UTLROnlineRemitAPI/Common/LicensedStates', $params);
    }


    public function AddBeneficiary($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/AddBeneficiary', $params, '', true, $user);
    }

    public function editBeneficiary($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/EditBeneficiary', $params, '', true, $user);
    }


    public function deactivateBeneficiary($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/DeactivateBeneficiary', $params, '', true, $user);
    }

    public function sendMoneyPreview($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Remittance/SendMoneyPreview', $params, '', true, $user);
    }

    public function externalQuickQuotes($params) {
        return $this->request('post', 'UTLROnlineRemitAPI/Common/ExternalQuickQuotes', $params);
    }

    public function sendMoneyConfirm($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Remittance/SendMoneyConfirm', $params, '', true, $user);
    }

    public function cancelTransaction($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Remittance/v2/CancelTransaction', $params, '', true, $user);
    }

    public function getDailyRate($params) {
      return $this->request('post', 'UTLROnlineRemitAPI/Remittance/CurrentExchangeRate', $params);
    }

    public function getProfile($params, $user) {
      return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/UserProfileInfo', $params, '', true, $user);
    }

    public function getSendingMethodSummaryList($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/ProfileManagement/SendingMethodSummaryList', $params, '', true, $user);
    }

    public function getTransactionDetail($params, $user, $saveEi = true)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Remittance/TransactionDetail', $params, '', $saveEi, $user);
    }

    public function getTransactionSummaryList($params, $user, $saveEi = true)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Remittance/TransactionSummaryList', $params, '', $saveEi, $user);
    }

    public function getAllTransactions( $status = null, $page_number = null, $page_size = null, $start_date = null, $end_date = null, $saveEi = true) {
      $params = [
        'page.index' => $page_number,
        'page.size'  => $page_size,
        'startDate'  => $start_date,
        'endDate'    => $end_date,
        'status'     => $status,
        'wlpCode'    => 'TERNITUP'
      ];
      return $this->request('get', 'UTLROnlineRemitAPI/Remittance/transactions/search', $params, '', $saveEi, null, true );
    }

    public function getStateDisclaimer($params)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Common/StateDisclaimer', $params);
    }

    public function getPayerBrancahList($params)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Common/PayerBranches', $params);
    }


    public function getTransactionReceipt($params, $user)
    {
        return $this->request('post', 'UTLROnlineRemitAPI/Remittance/TransactionReceipt', $params, '', true, $user);
    }

    public function syncTxStatus($params) {
      return $this->request('post','UTLROnlineRemitAPI/Common/SyncTxStatus', $params);
    }

    public static function isOopsError($message)
    {
        return Util::containsSubString($message, [
            'Oops',
            'Currently Our Service is Not Available',
            'cURL error',
            'com.uniteller.general.system.error',
            'Please try again or Contact to Uniteller For Support.',
            'Internal Server Error',
            '<head><title>',
        ]);
    }

    private function cacheErrorToPool(ExternalInvoke $ei)
    {
        $error = $ei->getError();
        if (!$error) {
            return;
        }
        $key = 'uniteller_error_pool';
        $pool = Data::getArray($key);
        $pool[] = $ei->getId();
        Data::setArray($key, $pool);
    }

    public static function sendEmailToUniTeller($subject, $body) {
        if (Util::isLive()) {
            $to = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];
        } else {
            $to = '<EMAIL>';
            $cc = [
                '<EMAIL>',
                '<EMAIL>',
            ];
        }
        Email::sendWithTemplate($to, Email::TEMPLATE_SIMPLE_LAYOUT, [
            'name'    => 'UniTeller Team',
            'subject' => $subject,
            'body'    => $body,
            '_cc'     => $cc,
        ], null, CardProgram::transferMexUSD());
    }

    public static function sendEmailToUniTellerForSettle($subject, $body, $file, $debug = false) {

      if (Util::isLive() && !$debug) {
          $to = [
              '<EMAIL> ',
          ];
          $cc = [
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>'
          ];
      } else {
          $to = '<EMAIL>';
          $cc = [
               '<EMAIL>',
               '<EMAIL>',
          ];
      }
      Email::sendWithTemplate($to, Email::TEMPLATE_EMPTY_TEMPLATE, [
          'name'    => 'UniTeller Team',
          'subject' => $subject,
          'body'    => $body,
          '_cc'     => $cc,
          '_attachments' => [
            $file
          ]
      ], null, null);
  }
}
