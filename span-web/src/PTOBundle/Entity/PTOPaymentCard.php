<?php

namespace PTOBundle\Entity;

use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;

/**
 * PTOPaymentCard
 *
 * @ORM\Table(name="pto_payment_card")
 * @ORM\Entity(repositoryClass="PTOBundle\Repository\PTOPaymentCardRepository")
 */
class PTOPaymentCard
{

    public function persist()
    {
        Util::persist($this);
    }

    /**
     * @param $uuid
     *
     * @return PTOPaymentCard|null
     */
    public static function findCardByUuid($uuid)
    {
        $rs = Util::em()->getRepository(__CLASS__)
            ->createQueryBuilder('pc')
            ->where('pc.uuid = :uuid')
            ->setParameter('uuid', $uuid)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (!$rs) {
            return null;
        }
        return end($rs);
    }


    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="uuid", type="string", length=255)
     */
    private $uuid;

    /**
     * @var string
     *
     * @ORM\Column(name="user_id", type="string", length=255)
     */
    private $userId;

    /**
     * @var string
     *
     * @ORM\Column(name="card_name", type="string", length=255)
     */
    private $cardName;

    /**
     * @var int
     *
     * @ORM\Column(name="card_type", type="integer", nullable=true)
     */
    private $cardType;

    /**
     * @var string
     *
     * @ORM\Column(name="card_number", type="string", length=4000)
     */
    private $cardNumber;

    /**
     * @var string
     *
     * @ORM\Column(name="expiration_year", type="string", length=255)
     */
    private $expirationYear;

    /**
     * @var string
     *
     * @ORM\Column(name="expiration_month", type="string", length=255)
     */
    private $expirationMonth;

    /**
     * @var string
     *
     * @ORM\Column(name="card_number_last_4", type="string", length=255, nullable=true)
     */
    private $cardNumberLast4;

    /**
     * @var bool
     *
     * @ORM\Column(name="is_primary", type="boolean")
     */
    private $isPrimary;

    /**
     * @var string
     *
     * @ORM\Column(name="external_token", type="string", length=255, nullable=true)
     */
    private $externalToken;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set uuid
     *
     * @param string $uuid
     *
     * @return PTOPaymentCard
     */
    public function setUuid(string $uuid)
    {
        $this->uuid = $uuid;

        return $this;
    }

    /**
     * Get uuid
     *
     * @return string
     */
    public function getUuid()
    {
        return $this->uuid;
    }

    /**
     * Set userId
     *
     * @param string $userId
     *
     * @return PTOPaymentCard
     */
    public function setUserId($userId)
    {
        $this->userId = $userId;

        return $this;
    }

    /**
     * Get userId
     *
     * @return string
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * Set cardName
     *
     * @param string $cardName
     *
     * @return PTOPaymentCard
     */
    public function setCardName($cardName)
    {
        $this->cardName = $cardName;

        return $this;
    }

    /**
     * Get cardName
     *
     * @return string
     */
    public function getCardName()
    {
        return $this->cardName;
    }

    /**
     * Set cardType
     *
     * @param integer $cardType
     *
     * @return PTOPaymentCard
     */
    public function setCardType($cardType)
    {
        $this->cardType = $cardType;

        return $this;
    }

    /**
     * Get cardType
     *
     * @return int
     */
    public function getCardType()
    {
        return $this->cardType;
    }

    /**
     * Set cardNumber
     *
     * @param string $cardNumber
     *
     * @return PTOPaymentCard
     */
    public function setCardNumber($cardNumber)
    {
        $this->cardNumber = $cardNumber;

        return $this;
    }

    /**
     * Get cardNumber
     *
     * @return string
     */
    public function getCardNumber()
    {
        return $this->cardNumber;
    }

    /**
     * Set expirationYear
     *
     * @param string $expirationYear
     *
     * @return PTOPaymentCard
     */
    public function setExpirationYear($expirationYear)
    {
        $this->expirationYear = $expirationYear;

        return $this;
    }

    /**
     * Get expirationYear
     *
     * @return string
     */
    public function getExpirationYear()
    {
        return $this->expirationYear;
    }

    /**
     * Set expirationMonth
     *
     * @param string $expirationMonth
     *
     * @return PTOPaymentCard
     */
    public function setExpirationMonth($expirationMonth)
    {
        $this->expirationMonth = $expirationMonth;

        return $this;
    }

    /**
     * Get expirationMonth
     *
     * @return string
     */
    public function getExpirationMonth()
    {
        return $this->expirationMonth;
    }

    /**
     * Set cardNumberLast4
     *
     * @param string $cardNumberLast4
     *
     * @return PTOPaymentCard
     */
    public function setCardNumberLast4($cardNumberLast4)
    {
        $this->cardNumberLast4 = $cardNumberLast4;

        return $this;
    }

    /**
     * Get cardNumberLast4
     *
     * @return string
     */
    public function getCardNumberLast4()
    {
        return $this->cardNumberLast4;
    }

    /**
     * Set isPrimary
     *
     * @param boolean $isPrimary
     *
     * @return PTOPaymentCard
     */
    public function setIsPrimary($isPrimary)
    {
        $this->isPrimary = $isPrimary;

        return $this;
    }

    /**
     * Get isPrimary
     *
     * @return bool
     */
    public function getIsPrimary()
    {
        return $this->isPrimary;
    }

    /**
     * Set externalToken
     *
     * @param string $externalToken
     *
     * @return PTOPaymentCard
     */
    public function setExternalToken($externalToken)
    {
        $this->externalToken = $externalToken;

        return $this;
    }

    /**
     * Get externalToken
     *
     * @return string
     */
    public function getExternalToken()
    {
        return $this->externalToken;
    }
}

