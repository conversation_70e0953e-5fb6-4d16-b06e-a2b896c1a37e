<?php


namespace PTOBundle\Controller\WidgetController;


use ApiBundle\Services\RPNService;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Platform;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\SuccessResponse;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use PTOBundle\Entity\PTOExternalAccount;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use LeafLinkBundle\Services\YodleeService;
use CoreBundle\Services\SSLEncryptionService;
use UsUnlockedBundle\Entity\UserTrait;
use PTOBundle\Controller\PTOPaymentCardController;
use PTOBundle\Entity\PTOPaymentCard;
use PTOBundle\Entity\PTOUser;
use PTOBundle\Entity\PTOUserType;
use CoreBundle\Controller\BaseController;
use PTOBundle\Services\SlackService;


class PTOYodleeWidgetController extends BaseController
{
    public $protected = false;

    public static function checkRoutingNumber($routingNumber = 0)
    {
        $routingNumber = preg_replace('[\D]', '', $routingNumber);
        //only digits
        if(strlen($routingNumber) !== 9)
        {
            return false;
        }

        $checkSum = 0;
        for ($i = 0, $j = strlen($routingNumber); $i < $j; $i+= 3 )
        {
            //loop through routingNumber character by character
            $checkSum += ($routingNumber[$i] * 3);
            $checkSum += ($routingNumber[$i+1] * 7);
            $checkSum += ($routingNumber[$i+2]);
        }

        if($checkSum !== 0 and ($checkSum % 10) === 0)
        {
            return true;
        }

        return false;
    }

    /**
     * @Route("/p/submit-bank/alternate-bank/{widgetToken}")
     * @param Request $request
     * @return Response
     */
    public function alternateBank(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));

        if (!$user)
        {
            $response = new Response($this->renderView('@PTO/Widgets/widget-failed.html.twig', [
            ]), 200);
            $response->headers->set('X-Frame-Options', '');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
            return $response;
        }

        $response = new Response($this->renderView('@PTO/YodleeWidget/alternateBank.html.twig', [
            'user' => $user,
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/submit-verified-account-details")
     * @param Request $request
     * @return Response
     */
    public function submitBankVerifiedDetails(Request $request)
    {
        // Initialize user variables and update register step.
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));

        // Get data
        $token = YodleeService::getYodleeToken($request->get("widgetToken"));
        $data = YodleeService::getYodleeBankingDetails($token, $request->get("providerAcctId"), $request->get('accountId'));

        if (!array_key_exists("account", $data)) {
            SlackService::alert('PTO Bank Linking Widget Error: The bank account they were attempting to link did not have an account field or the JSON response from Yodlee was unexpected.', [
                'User UUID: ' => $user->getUuid()
            ]);
//            $message = $user->getUuid() . ' - PTO onboarding error: The bank account they were attempting to link did not have an account field or the JSON response from Yodlee was unexpected.';
//            $uc = NULL;
//            WebhookService::onboardingWebhook($config, $uc, $message, false, "error");
            return new FailedResponse($data);
        }

        if (!array_key_exists("fullAccountNumber", $data["account"][0])) {
            SlackService::alert('PTO Bank Linking Widget Error: The bank account they were attempting to link did not return a full account number.', [
                'User UUID: ' => $user->getUuid()
            ]);
//            $message = $config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not return a full account number.';
//            $uc = NULL;
//            WebhookService::onboardingWebhook($config, $uc, $message, false, "error");
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }

        $acctNum = $data["account"][0]["fullAccountNumber"];
        $routingNum = $data["account"][0]["bankTransferCode"][0]["id"];
        $bankName = $data["account"][0]["providerName"];

        $ea = new PTOExternalAccount();
        $ea->setUuid(Uuid::uuid4())
            ->setUser($user->getUuid())
            ->setName($user->getFirstName())
            ->setBankName($bankName)
            ->setAccountNumber(SSLEncryptionService::encrypt($acctNum))
            ->setRoutingNumber(SSLEncryptionService::encrypt($routingNum))
            ->setCurrency('USD')
            ->setType('checking')
            ->setAllowWithdrawals(true)
            ->setAllowDeposits(true)
            ->setIsPrimary(true);

        $eaNum = RPNService::createExternalAccount($user, $ea);
        if ($eaNum === 'Failure')
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        $ea->setExternalAccountNumber($eaNum)
            ->persist();

        //Slack notification:
        SlackService::tada($user->getUuid() . ' has completed the onboarding flow!', [
            'User' => $user->getUuid(),
            'Method' => 'Yodlee'
        ]);

        // Success Webhook to PTO
//        $message = $config->getCompanyName() . ' has completed the onboarding flow via Yodlee.';
//
//        WebhookService::onboardingWebhook($config, $uc, $message);

        return new SuccessResponse('noFullAccountNumber', ['error' => 'none'], 200);
    }

    /**
     * @Route("/p/submit-bank/submit-account-details/{widgetToken}")
     * @param Request $request
     * @return Response
     */
    public function submitBankDetails(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));

        if (Util::checkRoutingNumber($request->get('bankRouting')) === false)
        {
            $response = new Response($this->renderView('@PTO/YodleeWidget/alternateBank_routingFailure.html.twig', [
                'user' => $user,
            ]), 200);
            $response->headers->set('X-Frame-Options', '');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
            return $response;
        }

        $bank = (object)[];
        $bank->name = $request->get('bankName');
        $bank->aba = $request->get('bankAba');
        $bank->routing = $request->get('bankRouting');

        $ea = new PTOExternalAccount();
        $ea->setUuid(Uuid::uuid4())
            ->setUser($user->getUuid())
            ->setName($user->getFirstName())
            ->setBankName($bank->name)
            ->setAccountNumber(SSLEncryptionService::encrypt($bank->aba))
            ->setRoutingNumber(SSLEncryptionService::encrypt($bank->routing))
            ->setCurrency('USD')
            ->setType('checking')
            ->setAllowWithdrawals(true)
            ->setAllowDeposits(true)
            ->setIsPrimary(true);

        $eaNum = RPNService::createExternalAccount($user, $ea);
        if ($eaNum === 'Failure')
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        $ea->setExternalAccountNumber($eaNum)
            ->persist();

        //Slack notification:
        SlackService::tada($user->getUuid() . ' has completed the onboarding flow!', [
            'User' => $user->getUuid(),
            'Method' => 'Manual'
        ]);

        // Success Webhook to Leaflink
//        $message = $config->getCompanyName() . ' has completed the onboarding flow via Manual.';
//
//        WebhookService::onboardingWebhook($config, $uc, $message, true);

        $response = new Response($this->renderView('@PTO/YodleeWidget/bankDetails.html.twig', [
            'user' => $user,
            'bank' => $bank
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/noFullAccountNumber/{widgetToken}")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function tooManyAccounts(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));
        $response = new Response($this->renderView('@PTO/Widgets/YodleeWidget.html.twig', [
            'user' => $user
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/error")
     * @param Request $request
     *
     * @return Response
     * @throws PortalException
     */
    public function error(Request $request)
    {
        $response = new Response($this->renderView('@PTOBundle/Widgets/YodleeWidget/error.html.twig', [
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/success")
     * @param Request $request
     *
     * @return Response
     * @throws PortalException
     */
    public function success(Request $request)
    {
        $response = new Response($this->renderView('@PTOBundle/Widgets/YodleeWidget/thankYou.html.twig', [
            'completed' => true
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/link/{widgetToken}")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function index(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));

        if (!$user) {
            $response = new Response($this->renderView('@PTO/Widgets/widget-failed.html.twig', [
                'user' => $user
            ]), 200);
            $response->headers->set('X-Frame-Options', '');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
            return $response;
        }

        $token = YodleeService::getYodleeToken($user->getWidgetToken());

        if (Util::isLive())
        {
            $response = new Response($this->render('@PTO/YodleeWidget/index-prod.html.twig',
                ['user' => $user, 'token' => $token]), 200);
            $response->headers->set('X-Frame-Options', '');
            $response->headers->set('Content-Security-Policy', 'frame-src https://production.node.yodlee.com/');
            $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
            return $response;
        }

        $response = new Response($this->render('@PTO/YodleeWidget/index.html.twig',
            ['user' => $user, 'token' => $token]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://development.node.yodlee.com/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/terms/{widgetToken}")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function terms(Request $request)
    {
        $user = PTOUser::findUserByWidgetToken($request->get('widgetToken'));

        if (!$user) {
            $response = new Response($this->renderView('@PTOBundle\YodleeWidget\error.html.twig', [
            ]), 200);
            $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
            return $response;
        }

        $yodleeToken = YodleeService::getYodleeToken($user->getWidgetToken());

        $response = new Response($this->renderView('@PTO/YodleeWidget/terms.html.twig', [
            'user' => $user,
            'token' => $yodleeToken
        ]), 200);
        $response->headers->set('X-Frame-Options', '');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto.virtualcards.us/');
        $response->headers->set('Content-Security-Policy', 'frame-src https://pto-test.virtualcards.us/');
        return $response;
    }

    /**
     * @Route("/p/submit-bank/iframe-test")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function PTOIframeTest(Request $request)
    {
        return $this->render('@PTO/YodleeWidget/yodleeWidget-test.html.twig');
    }
}
