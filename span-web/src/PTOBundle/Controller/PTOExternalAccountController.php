<?php


namespace PTOBundle\Controller;


use ApiBundle\Services\RPNService;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Services\SSLEncryptionService;
use PTOBundle\Entity\PTOExternalAccount;
use PT<PERSON><PERSON><PERSON>le\Entity\PTOUser;
use PT<PERSON><PERSON><PERSON>le\Entity\PTOUserType;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use OpenApi\Annotations as SWG;
use Exception;

class PTOExternalAccountController extends \ApiBundle\Controller\BaseController
{
    /**
     * @Route("/api/pto/add-external-account", methods={"POST"}, name="api_pto_create_external_account")
     * @SWG\Post(
     *   tags={"External Accounts"},
     *   summary="Create An External Account for a User Profile",
     *   description="Add an external bank account for a User Profile. This account can be used to fund internal bank accounts.",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userUuid", "bankName", "accountNumber", "routingNumber", "name"}, properties={
     *     @SWG\Property(property="userUuid", description="Unique ID for the user. Generated by Tern using the Create User Profile API Call.", type="string"),
     *     @SWG\Property(property="bankName", description="Name of the institution that holds the account.", type="string"),
     *     @SWG\Property(property="accountNumber", description="Account number of the bank account being added.", type="string"),
     *     @SWG\Property(property="routingNumber", description="Routing number of the bank account being added.", type="string"),
     *     @SWG\Property(property="name", description="Name of the account. Only used as an identifier.", type="string"),
     *     @SWG\Property(property="currency", description="Currency the account will use. Will default to USD unless specified otherwise.", type="string"),
     *     @SWG\Property(property="type", description="Type of bank account being added. Type can be checking or savings. Will default to checking unless specified.", type="string"),
     *     @SWG\Property(property="allowWithdrawals", description="Allow withdrawals from this bank account by Fintech as a Service. Defaults to false if not specified.", type="boolean"),
     *     @SWG\Property(property="allowDeposits", description="Allow withdrawals from this bank account by Fintech as a Service. Defaults to false if not specified.", type="boolean"),
     *     @SWG\Property(property="isPrimary", description="Is this the default payment account for this user? This will overwrite any previous defaults. Will default to false unless specified otherwise.", type="boolean"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/responses/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function PTOCreateExternalAccount(): JsonResponse
    {
        $request = $this->validateParameters(__METHOD__);

        if (PTOUser::findUserByUuid($request->get('userUuid')) === NULL)
        {
            return new FailedResponse('This user ID does not exist.', [
                'User ID' => $request->get('userUuid')
            ], 409);
        }

        $user = PTOUser::findUserByUuid($request->get('userUuid'));
        $userType = PTOUserType::findUserTypeByUuid($user->getUserTypeId());

        if ($userType->getExternalBankAccount() === false)
        {
            return new FailedResponse('This user IDs type is not permissioned for external accounts.', [
                'User ID' => $request->get('userUuid'),
                'User Type' => $userType->getName(),
                'User Type ID' => $userType->getUuid()
            ], 409);
        }

        //TODO: Skip while in dev
//        if ($userType->getExternalBankValidation() === true)
//        {
//            return new FailedResponse('This user IDs type requires Yodlee validation to add an external bank account.', [
//                'User ID' => $request->get('userUuid'),
//                'User Type' => $userType->getName(),
//                'User Type ID' => $userType->getUuid()
//            ], 409);
//        }

        //TODO: Consider disabling for dev.
        if (Util::checkRoutingNumber($request->get('routingNumber')) === false)
        {
            return new FailedResponse('Routing number did not pass check.', [
                'Routing Number' => $request->get('routingNumber')
            ], 409);
        }

        $accountNumLength = strlen($request->get('accountNumber'));
        if ($accountNumLength < 5 || $accountNumLength > 17)
        {
            return new FailedResponse('Account number must be between 5 and 17 digits.', [
                'Account Number' => $request->get('accountNumber')
            ], 409);
        }

        if ($request->get('type') === 'savings')
        {
            $type = 'savings';
        } else {
            $type = 'checking';
        }

        if ($request->get('currency') === NULL)
        {
            $currency = 'USD';
        } else {
            $currency = $request->get('currency');
        }

        if ($request->get('allowDeposits') === NULL){
            $allowDeposits = false;
        } else {
            $allowDeposits = $request->get('allowDeposits');
        }

        if ($request->get('allowWithdrawals') === NULL){
            $allowWithdrawals = false;
        } else {
            $allowWithdrawals = $request->get('allowWithdrawals');
        }

        if ($request->get('isPrimary') === NULL){
            $isPrimary = false;
        } else {
            $isPrimary = $request->get('isPrimary');
        }

        $lastFourAcctNum = substr($request->get('accountNumber'), -4);

        $ea = new PTOExternalAccount();
        $ea->setUuid(Uuid::uuid4())
            ->setUser($request->get('userUuid'))
            ->setName($request->get('name'))
            ->setBankName($request->get('bankName'))
            ->setAccountNumber(SSLEncryptionService::encrypt($request->get('accountNumber')))
            ->setRoutingNumber(SSLEncryptionService::encrypt($request->get('routingNumber')))
            ->setCurrency($currency)
            ->setType($type)
            ->setAllowWithdrawals($allowWithdrawals)
            ->setAllowDeposits($allowDeposits)
            ->setIsPrimary($isPrimary);

        $eaNum = RPNService::createExternalAccount($user, $ea);
        if ($eaNum === 'Failure')
        {
            return new FailedResponse('Processor failure. Tern has been alerted.');
        }

        $ea->setExternalAccountNumber($eaNum)
            ->persist();

        return new SuccessResponse([
            'uuid' => $ea->getUuid(),
            'userUuid' => $ea->getUser(),
            'name' => $ea->getName(),
            'bankName' => $ea->getBankName(),
            'lastFourAcctNum' => $lastFourAcctNum,
            'routingNumber' => $request->get('routingNumber'),
            'currency' => $currency,
            'type' => $type,
            'allowWithdrawals' => $allowWithdrawals,
            'allowDeposits' => $allowDeposits,
            'isPrimary' => $isPrimary
        ]);
    }
}
