<?php


namespace LeafLinkBundle\Controller;


use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Platform;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\SuccessResponse;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use LeafLinkBundle\Services\YodleeService;
use CoreBundle\Services\SSLEncryptionService;
use LeafLinkBundle\Services\SlackService;
use LeafLinkBundle\Services\WebhookService;
use UsUnlockedBundle\Entity\UserTrait;

class OnboardController extends BaseController
{
    public $protected = false;

    private function getUserFromBankAccountId($request)
    {
        //Get user data based on query parameter (bankAccountId)
        $rs = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.config', 'c')
            ->where('c.bankAccountId = :bankAccountId')
            ->setParameter('bankAccountId', $request->get('bankAccountId'))
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (count($rs) < 1) {
            return false;
        }

        return end($rs);
    }

    public static function checkRoutingNumber($routingNumber = 0)
    {
        $routingNumber = preg_replace('[\D]', '', $routingNumber);
        //only digits
        if(strlen($routingNumber) !== 9)
        {
            return false;
        }

        $checkSum = 0;
        for ($i = 0, $j = strlen($routingNumber); $i < $j; $i+= 3 )
        {
            //loop through routingNumber character by character
            $checkSum += ($routingNumber[$i] * 3);
            $checkSum += ($routingNumber[$i+1] * 7);
            $checkSum += ($routingNumber[$i+2]);
        }

        if($checkSum !== 0 and ($checkSum % 10) === 0)
        {
            return true;
        }

        return false;
    }

    /**
     * @Route("/l/onboard/alternate-bank/{bankAccountId}/{referCode}")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function alternateBank(Request $request)
    {
        $user = $this->getUserFromBankAccountId($request);

        $config = $user->ensureConfig();
        if ($request->get('referCode') !== $config->getReferCode())
        {
            return $this->render('@LeafLink/Onboard/error.html.twig');

        }

        return $this->render('@LeafLink/Onboard/alternateBank.html.twig', ['user' => $user, 'config' => $config]);
    }

    /**
     * @Route("/l/onboard/submit-verified-account-details")
     * @param Request $request
     * @return Response
     */
    public function submitBankVerifiedDetails(Request $request)
    {
        // Initialize user variables and update register step.
        $user = User::findByBankAccountId($request->get('bankAccountId'));
        $config = $user->ensureConfig();

        // Get data
        $token = YodleeService::getYodleeToken($request->get("bankAccountId"));
        $data = YodleeService::getYodleeBankingDetails($token, $request->get("providerAcctId"), $request->get('accountId'));

        if (!array_key_exists("account", $data)) {
            SlackService::alert($config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not have an account field or the JSON response from Yodlee was unexpected.');
            $message = $config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not have an account field or the JSON response from Yodlee was unexpected.';
            $uc = NULL;
            WebhookService::onboardingWebhook($config, $uc, $message, false, "error");
            return new FailedResponse($data);
        }

        if (!array_key_exists("fullAccountNumber", $data["account"][0])) {
            SlackService::alert($config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not return a full account number.');
            $message = $config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not return a full account number.';
            $uc = NULL;
            WebhookService::onboardingWebhook($config, $uc, $message, false, "error");
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }

        if (!array_key_exists("id", $data["account"][0]["bankTransferCode"][0])) {
            SlackService::alert($config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not return a routing number.');
            $message = $config->getCompanyName() . ' onboarding error: The bank account they were attempting to link did not return a routing number.';
            $uc = NULL;
            WebhookService::onboardingWebhook($config, $uc, $message, false, "error");
            return new FailedResponse('noRoutingNumber', ['error' => 'noRoutingNumber'], 200);
        }

        $acctNum = $data["account"][0]["fullAccountNumber"];
        $routingNum = $data["account"][0]["bankTransferCode"][0]["id"];
        $bankName = $data["account"][0]["providerName"];

        //encrypt data
        $encryptedAcctNum = SSLEncryptionService::encrypt($acctNum);
        $encryptedRouting = SSLEncryptionService::encrypt($routingNum);

        //Set user register step to active
        $user->setRegisterStep(RegisterStep::ACTIVE)
            ->persist();

        //Store in new card
        $uc = new userCard();
        $uc->setUser($user)
            ->setCard(CardProgramCardType::getForCardProgram(CardProgram::leafLink()))
            ->setBankName($bankName)
            ->setAccountNumber($encryptedAcctNum)
            ->setRoutingNumber($encryptedRouting)
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setIssued(true)
            ->setInitializedAt(new \DateTime())
            ->setCurrency('USD')
            ->setAccountId($request->get('accountId'))
            ->setproviderAccountId($request->get('providerAcctId'))
            ->setType(UserCard::LL_TYPE_YODLEE)
            ->persist();

        //Slack notification:
        SlackService::tada($config->getCompanyName() . ' has completed the onboarding flow!', [
            'Bank Account ID' => $config->getBankAccountId(),
            'Method' => 'Yodlee'
        ]);

        // Success Webhook to Leaflink
        $message = $config->getCompanyName() . ' has completed the onboarding flow via Yodlee.';

        WebhookService::onboardingWebhook($config, $uc, $message);

        return new SuccessResponse('noFullAccountNumber', ['error' => 'none'], 200);
    }

    /**
     * @Route("/l/onboard/submit-account-details")
     * @param Request $request
     * @return Response
     */
    public function submitBankDetails(Request $request)
    {
        $user = User::findByBankAccountId($request->get('bankAccountId'));
        $user->setRegisterStep(RegisterStep::ACTIVE)
            ->persist();

        $config = $user->ensureConfig();

        if (Util::checkRoutingNumber($request->get('bankRouting')) === false)
        {
            return $this->render('@LeafLink/Onboard/alternateBank_routingFailure.html.twig', ['user' => $user, 'config' => $config]);
        }

        //encrypt data
        $encryptedAcctNum = SSLEncryptionService::encrypt($request->get('bankAba'));
        $encryptedRouting = SSLEncryptionService::encrypt($request->get('bankRouting'));

        $bank = (object)[];
        $bank->name = $request->get('bankName');
        $bank->aba = $request->get('bankAba');
        $bank->routing = $request->get('bankRouting');


        $uc = new userCard();
        $uc->setUser($user)
            ->setCard(CardProgramCardType::getForCardProgram(CardProgram::leafLink()))
            ->setBankName($request->get('bankName'))
            ->setAccountNumber($encryptedAcctNum)
            ->setRoutingNumber($encryptedRouting)
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setIssued(true)
            ->setInitializedAt(new \DateTime())
            ->setCurrency('USD')
            ->setType(UserCard::LL_TYPE_MANUAL)
            ->persist();

        //Slack notification:
        SlackService::tada($config->getCompanyName() . ' has completed the onboarding flow!', [
            'Bank Account Id' => $config->getBankAccountId(),
            'Method' => 'Manual',
            'Update Test: ' => 'Working',
            'env' => Util::isStage()
        ]);

        // Success Webhook to Leaflink
        $message = $config->getCompanyName() . ' has completed the onboarding flow via Manual.';

        WebhookService::onboardingWebhook($config, $uc, $message, true);

        return $this->render('@LeafLink/Onboard/bankDetails.html.twig', ['user' => $user, 'config' => $config, 'bank' => $bank]);
    }

    /**
     * @Route("/l/onboard/noFullAccountNumber/{bankAccountId}")
     * @param Request $request
     * @return Response
     */
    public function tooManyAccounts(Request $request)
    {
        $config = ['bankAccountId' => 0000000];
        return $this->render('@LeafLink/Onboard/noFullAccountNumber.html.twig', ["bankAccountId" => $request->get('bankAccountId'), "config" => $config]);
    }

    /**
     * @Route("/l/onboard/noRoutingNumber/{bankAccountId}")
     * @param Request $request
     * @param int $bankAccountId
     * @return Response
     * @throws PortalException
     */
    public function noRoutingNumber(Request $request)
    {
        $config = ['bankAccountId' => 0000000];
        return $this->render('@LeafLink/Onboard/noRoutingNumber.html.twig', ["bankAccountId" => $request->get('bankAccountId'), "config" => $config]);
    }

    /**
     * @Route("/l/onboard/error")
     * @param Request $request
     *
     * @return Response
     */
    public function error(Request $request)
    {
        return $this->render('@LeafLink/Onboard/error.html.twig');
    }

    /**
     * @Route("/l/onboard/success")
     * @param Request $request
     *
     * @return Response
     */
    public function success(Request $request)
    {
        $config = ['bankAccountId' => 0000000];
        return $this->render('@LeafLink/Onboard/thankYou.html.twig', ['config' => $config]);
    }

    /**
     * @Route("/l/onboard/{bankAccountId}/{referCode}")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function index(Request $request)
    {
        $user = $this->getUserFromBankAccountId($request);

        if (!$user) {
            return $this->render('@LeafLink/Onboard/error.html.twig');
        }

        $config = $user->ensureConfig();
        if ($request->get('referCode') !== $config->getReferCode())
        {
            return $this->render('@LeafLink/Onboard/error.html.twig');

        }

        $token = YodleeService::getYodleeToken($config->getBankAccountId());

        if (Util::isLive())
        {
            return $this->render('@LeafLink/Onboard/index-prod.html.twig', ['user' => $user, 'config' => $config, 'token' => $token]);
        }

        return $this->render('@LeafLink/Onboard/index.html.twig', ['user' => $user, 'config' => $config, 'token' => $token]);
    }

    /**
     * @Route("/l/onboard/terms/{bankAccountId}/{referCode}")
     * @param Request $request
     * @return Response
     * @throws PortalException
     */
    public function terms(Request $request)
    {
        $user = $this->getUserFromBankAccountId($request);

        if (!$user) {
            return $this->render('@LeafLink/Onboard/error.html.twig');
        }

        $config = $user->ensureConfig();

        if ($request->get('referCode') !== $config->getReferCode())
        {
            return $this->render('@LeafLink/Onboard/error.html.twig');

        }

        $token = YodleeService::getYodleeToken($config->getBankAccountId());

        return $this->render('@LeafLink/Onboard/terms.html.twig', ['user' => $user, 'config' => $config, 'token' => $token]);
    }

}
