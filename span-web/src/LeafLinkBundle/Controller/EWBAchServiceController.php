<?php


namespace Leaf<PERSON>inkBundle\Controller;


use Api<PERSON><PERSON>le\Controller\ImapController;
use CoreBundle\Controller\Cron\NakedProtectedController;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\NachaEntity;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use LeafLinkBundle\Entity\DailyCheck;
use LeafLinkBundle\Entity\LeafLinkLogEvent;
use LeafLinkBundle\Services\NachaReturnService;
use LeafLinkBundle\Services\PaymentService;
use LeafLinkBundle\Services\SlackService;
use LeafLinkBundle\Services\WebhookService;
use LeafLinkBundle\Services\YodleeService;
use Nacha\Batch;
use Nacha\Field\TransactionCode;
use Nacha\File;
use Nacha\Record\Addenda;
use Nacha\Record\CcdEntry;
use Nacha\Record\DebitEntry;
use phpseclib\Net\SFTP;
use SalexUserBundle\Entity\User;
use SecIT\ImapBundle\Service\Imap;
use Stringy\Stringy;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class EWBAchServiceController extends NakedProtectedController
{
    /** @var SFTP */
    public $sftp;

    protected $file;
    protected $batch;
    protected $creditEntry;
    protected $debitEntry;

    protected $allowFromApi = true;

    public function oldLogin()
    {
        $this->sftp = new SFTP(Util::getParameter('sl_ftp_host_2'), Util::getParameter('sl_ftp_port_2'), 90);
        if ($this->sftp === FALSE) {
            SlackService::alert('Leaflink FTP connection error!');
            return;
        }
        if (!$this->sftp->login(
            Util::getParameter('sl_ftp_username_2'),
            Util::getParameter('sl_ftp_password_2')
        )) {
            SlackService::alert('Failed to login the LeafLink ftp server (005)...', [
                'error' => $this->sftp->getLastSFTPError(),
            ]);
            return;
        }
    }

    // Login for sFTP server. Called in submitACHBatch().
    public function login($host = null)
    {
        $host = $host ?? Util::getParameter('ewb_ftp_host');
        $this->sftp = new SFTP($host, Util::getParameter('ewb_ftp_port'), 90);
        if (!$this->sftp->login(
            Util::getParameter('ewb_ftp_username'),
            Util::getParameter('ewb_ftp_password')
        )) {
            SlackService::alert('Failed to login the LeafLink ftp server (006)...', [
                'error' => $this->sftp->getLastSFTPError(),
            ]);
            return false;
        }
        SlackService::info('Successfully logged in EWB sFTP server (006)');
        return true;
    }

    /**
     * @Route("/t/cron/leaflink/ewb/sftp/test")
     * @param Request $request
     *
     * @return Response
     */
    public function sftpTest(Request $request)
    {
        $host = Util::getParameter('ewb_ftp_host');
        if ($request->get('host')) {
            $host = $request->get('host');
        }

        $connected = $this->login($host);
        $dir = "./";
        $files = $this->sftp->nlist($dir);
        Log::debug('EWB sFTP test', [
            'host' => $host,
            'connected' => $connected,
            'files' => $files,
        ]);
        return new SuccessResponse([
            'connected' => $connected,
        ]);
    }

    public function getQuery()
    {
        //Creates an array of all ACH transactions with the status LL_SENT. This should only be transactions created via leaflink payment controller.
        $achQuery = Util::em()->getRepository(AchTransactions::class)
            ->createQueryBuilder('ach')
            ->where('ach.tranStatus = :received')
            ->andWhere(Util::expr()->in('ach.bankId', ':bankIds'))
            ->setParameter('received', UserCardTransaction::STATUS_LL_RECEIVED)
            ->setParameter('bankIds', PaymentService::getDebitBankIds('ewb'))
            ->orderBy('ach.createdAt', 'ASC')
            ->getQuery()
            ->getResult();

        $achCrQuery = Util::em()->getRepository(AchTransactions::class)
            ->createQueryBuilder('ach')
            ->where('ach.tranStatus = :received')
            ->andWhere(Util::expr()->in('ach.bankId', ':bankIds'))
            ->setParameter('received', UserCardTransaction::STATUS_LL_RECEIVED)
            ->setParameter('bankIds', PaymentService::getCreditBankIds('ewb'))
            ->orderBy('ach.createdAt', 'ASC')
            ->getQuery()
            ->getResult();

        return array_merge($achQuery, $achCrQuery);
    }

    /**
     * @Route("/t/cron/leaflink/ewb-batch")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function achEfficiencyTest(Request $request, $retry = false)
    {
        Util::longRequest();

        if (!Util::isTodayBusinessDay())
        {
            SlackService::info('Today is not a business day, Leaflink East West Bank ACH Batch will be skipped.');
            return new SuccessResponse();
        }

        SlackService::info('Today is a business day, Leaflink East West Bank ACH Batch will not be skipped.');

        // Create ACH Batch and set variables.
        $achBatch = new AchBatch();
        $achBatch->persist();
        $batchId = $achBatch->getId();


        try
        {
            SlackService::clock('Leaflink East West Bank ACH Batch is initializing.');

            // Check if query is being passed as an argument. If yes, use that query. If no, build a query off of the existing que.

            if ($retry === false)
            {
                $achQuery = $this->getQuery();
            } else {
                $achQuery = $retry;
            }

            if (count($achQuery) > 0) //Do not run if que is empty.
            {
                $check = DailyCheck::ensureDailyCheck();
                $check->setDate(new \DateTime())
                    ->setEwbStart(true)
                    ->persist();

                // TODO: Update for EWB
                // Set origin and destination variables for nacha generation. These variables must be entered into the database via addNachaEntity API call.
                $origin = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'LEAFLINK']); //TODO: If in local, change to LEAFLINK_TEST!
                $destination = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'EAST WEST BANK']);
                $thirdPartyDebit = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'FUSION LLF I']);
                $thirdPartyCredit = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'FUSION LLF II']);

                SlackService::info('Ach Batch ID: ' . $batchId, [
                    'ACH Count' => count($achQuery)
                ]);

//                // Create ACH Batch and set variables.
//                $achBatch = new AchBatch();
//                $achBatch->persist();
//                $batchId = $achBatch->getId();
//                SlackService::info('Ach Batch ID: ' . $batchId, [
//                    'ACH Count' => count($achQuery)
//                ]);

                //trace number variables
                $traceNumberBase = $origin->getTraceNumberBase();
                $traceNumberCount = $origin->getTraceNumberCount();

                //limit count variables
                $dailyDbCount = $origin->getDailyDbCount();
                $weeklyDbCount = $origin->getWeeklyDbCount();
                $monthlyDbCount = $origin->getMonthlyDbCount();

                $dailyCrCount = $origin->getDailyCrCount();
                $weeklyCrCount = $origin->getWeeklyCrCount();
                $monthlyCrCount = $origin->getMonthlyCrCount();

                //offset variables
                $totalDebits = 0;
                $totalCredits = 0;

                //linecount
                $lineCount = 3;

                //params array for mail
                $params = [];
                $params['tranCount'] = 0;
                $params['debitCount'] = 0;
                $params['creditCount'] = 0;

                // Uses origin, destination, and ach transaction query variables to generate a nacha file string.
                $this->file = new File();
                $this->file->getHeader()->setPriorityCode(1)
                    ->setImmediateDestination($destination->getRoutingNumber())
                    ->setImmediateOrigin($origin->getRoutingNumber())
                    ->setFileCreationDate(date('ymd'))
                    ->setFileCreationTime(date('Hi'))
                    ->setFormatCode('1')
                    ->setFileIdModifier('Z')
                    ->setImmediateDestinationName($destination->getName())
                    ->setImmediateOriginName($origin->getName())
                    ->setReferenceCode('Tern');

                // Debit batch
                $this->batch = new Batch();

                $this->batch->getHeader()->setBatchNumber(1)
                    ->setServiceClassCode(Batch::DEBITS_ONLY)
//                    ->setCompanyName($thirdPartyDebit->getName())
                    ->setCompanyName('Fusion LLF I')
//                ->setCompanyDiscretionaryData('Leaflink ACH')
//                ->setCompanyId('S' . $thirdPartyDebit->getRoutingNumber())
                    ->setCompanyId('**********') //TODO: Update for EWB
                    ->setStandardEntryClassCode('CCD')
                    ->setCompanyEntryDescription('ACH')
                    ->setCompanyDescriptiveDate(date('ymd'))
//                    ->setEffectiveEntryDate(date("ymd", strtotime('today')))
                    ->setEffectiveEntryDate(Util::GetNextBusinessDayNacha())
                    ->setOriginatorStatusCode('1')
                    ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));

                $uctRepo = Util::em()->getRepository(UserCardTransaction::class);
                /** @var AchTransactions $ach */
                foreach ($achQuery as $ach) {
                    // Find all debits in que.
//                    $uct = $uctRepo->findOneBy(['tranId' => $ach->getTranId(), 'accountStatus' => $ach->getTranStatus()]);
                    $uct = $uctRepo->findOneBy(['tranId' => $ach->getTranId()]);

                    if ($uct->getTranCode() === 'debit') {

                        //Set up variables
                        $uc = $uct->getUserCard();
                        $user = User::findByBankAccountId($ach->getBankAccountId());
                        $config = $user->ensureConfig();
                        $txnAmount = $uct->getTxnAmount();

                        $newDailyCount = $dailyDbCount + $txnAmount;
                        $newWeeklyCount = $weeklyDbCount + $txnAmount;
                        $newMonthlyCount = $monthlyDbCount + $txnAmount;

                        $checkResult = AchServiceController::checkAchLimits($newDailyCount, $newWeeklyCount, $newMonthlyCount, $origin, AchServiceController::LIMIT_CHECK_DEBIT);

                        //Check if this transaction exceeds limits.
//                        if ($checkResult === AchServiceController::LIMIT_CHECK_SUCCESS)
//                        {

                            //Yodlee Balance Check:
                            //Check and see if account is verified via Yodlee
                            if ($uc->getAccountId() !== NULL && $uc->getProviderAccountId() !== NULL) {
                                //Get account balance and check if txn will clear
                                $token = YodleeService::getYodleeToken($config->getBankAccountId());
                                $data = YodleeService::getYodleeBankingDetails($token, $uc->getProviderAccountId(), $uc->getAccountId());
                                $balance = $data["account"][0]["balance"]["amount"];
                                //Check if balance data was retrieved.
                                if ($balance)
                                {
                                    //Cancel transaction if balance is lower than txnAmount.
                                    if (($balance * 100) < $txnAmount) {

                                        $newDailyCount = $dailyDbCount;
                                        $newWeeklyCount = $weeklyDbCount;
                                        $newMonthlyCount = $monthlyDbCount;

                                        SlackService::alert('EWB Transaction ID: ' . $ach->getTranId() . ' did not pass the balance check and will be canceled.');
                                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                            ->persist();
                                        $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                            ->persist();
                                        WebhookService::achErrorWebhook($ach, 'T03', AchServiceController::TERN_CODE_T03);
                                        continue;
                                    }
                                } else {
                                    //Unable to get data from Yodlee. Skip transaction.

                                    $newDailyCount = $dailyDbCount;
                                    $newWeeklyCount = $weeklyDbCount;
                                    $newMonthlyCount = $monthlyDbCount;

                                    SlackService::alert('EWB Transaction ID: ' . $ach->getTranId() . ' : Yodlee connection failed. Transaction will be canceled.');
                                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                        ->persist();
                                    $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                        ->persist();
                                    WebhookService::achErrorWebhook($ach, 'T05', AchServiceController::TERN_CODE_T05);
                                    continue;
                                }
                            }

                            // Check that number isn't too big
                            $num = $uct->getTxnAmount();
                            $numlength = strlen((string)abs($num));
                            if ($numlength >= 11)
                            {
                                $newDailyCount = $dailyDbCount;
                                $newWeeklyCount = $weeklyDbCount;
                                $newMonthlyCount = $monthlyDbCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is over the maximum processable transaction amount.');
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
//                                WebhookService::achErrorWebhook($ach, 'T02', self::TERN_CODE_T02);
                                continue;
                            }

                            //Check that number isn't 0.
                            if ($num === 0)
                            {
                                $newDailyCount = $dailyDbCount;
                                $newWeeklyCount = $weeklyDbCount;
                                $newMonthlyCount = $monthlyDbCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is 0. Transaction is being canceled.');
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();

//                                WebhookService::achErrorWebhook($ach, 'T01', self::TERN_CODE_T01);
                                continue;
                            }

                            //decrypt data
                            $decryptedDbaNo = SSLEncryptionService::decrypt($uc->getDbaNo());
//                        $decryptedDbaNo = $uc->getDbaNo();
                            $decryptedAccountNum = SSLEncryptionService::decrypt($uc->getAccountNumber());
//                        $decryptedAccountNum = $uc->getAccountNumber();

                        // Sanitize company name
                        $sanitizedObject = Stringy::create($config->getCompanyName())->toAscii();
                        $sanitizedCompanyName = $sanitizedObject->__toString();

                            $traceNumberCount = str_pad(++$traceNumberCount, 7, "0", STR_PAD_LEFT);
                            $this->debitEntry = (new DebitEntry)
                                ->setTransactionCode(TransactionCode::CHECKING_DEBIT)
                                ->setReceivingDfiId(substr($decryptedDbaNo, 0, 8))
                                ->setCheckDigit(substr($decryptedDbaNo, -1))
                                ->setDFiAccountNumber($decryptedAccountNum)
                                ->setAmount(number_format(($uct->getTxnAmount() / 100), 2, '.', ''))
                                ->setIndividualId(substr((string)$ach->getBankAccountId(), -15))
                                ->setIdividualName($sanitizedCompanyName)
//                            ->setDiscretionaryData('LeafLink Debit')
                                ->setAddendaRecordIndicator(0)
                                ->setTraceNumber($traceNumberBase, $traceNumberCount);

                            $this->batch->addEntry($this->debitEntry);

                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setBatchId($batchId)
                                ->setLinePosition($lineCount)
                                ->setTraceNumber($this->debitEntry->getTraceNumber())
                                ->persist();
                            $uct->setAccountStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setAchBatchId($batchId)
                                ->persist();

//                            SlackService::check('Transaction ID: ' . $ach->getTranId() . ' has been added to the batch.');
//                            WebhookService::achReturnWebhook($ach, self::ACH_BATCH_SUCCESS);


                            //Updates counts for next transaction
                            $dailyDbCount = $newDailyCount;
                            $weeklyDbCount = $newWeeklyCount;
                            $monthlyDbCount = $newMonthlyCount;
                            $totalDebits += $txnAmount;
                            ++$lineCount;
                            $params['tranCount']++;
                            $params['debitCount']++;

//                        } else {
//
//                            SlackService::info('Debit Failure');
//
//                            $newDailyCount = $dailyDbCount;
//                            $newWeeklyCount = $weeklyDbCount;
//                            $newMonthlyCount = $monthlyDbCount;
//
//                            // Limit check failed. Cancel transaction, send slack and webhook.
//
//                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
//                                ->persist();
//                            $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
//                                ->persist();
//
//                            //TODO: Possibly set up warning email.
//
////                            $failureMessage = self::getLimitErrorCode($checkResult);
//
////                            SlackService::alert('Transaction ' . $ach->getTranId() . ' has failed a limit check.', [
////                                'Transaction' => $ach->getTranId(),
////                                'Error Code' => $checkResult,
////                                'Error Description' => $failureMessage
////                            ]);
////
////                            WebhookService::achErrorWebhook($ach, $checkResult, $failureMessage);
//
//                        }
                    }
                }

                if ($totalDebits !== 0)
                {
                    $this->file->addBatch($this->batch);
                }

//                // Don't create offset if there are no debit entries.
//                if ($totalDebits !== 0)
//                {
//                    //Debit Offset
//                    $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
//                    $this->creditEntry = (new CcdEntry)
//                        ->setTransactionCode(22)
//                        ->setReceivingDfiId(substr($thirdPartyDebit->getRoutingNumber(), 0, 8))
//                        ->setCheckDigit(substr($thirdPartyDebit->getRoutingNumber(), 8, 1))
//                        ->setReceivingDFiAccountNumber(SSLEncryptionService::decrypt($thirdPartyDebit->getAccountNumber()))
//                        ->setAmount(number_format(($totalDebits / 100), 2, '.', ''))
////                        ->setReceivingCompanyId('**********')
////                        ->setReceivingCompanyName("Fusion LLF I")
//                        ->setReceivingCompanyId(substr((string)**************, -15))
//                        ->setReceivingCompanyName("Fusion LLF I")
////                ->setDiscretionaryData('Offset')
//                        ->setAddendaRecordIndicator(0)
//                        ->setTraceNumber($traceNumberBase, $traceNumberCount);
//
//                    $this->batch->addEntry($this->creditEntry);
//
//                    $this->file->addBatch($this->batch);
//                }

                // Credit batch
                $this->batch = new Batch();

                $this->batch->getHeader()->setBatchNumber(2)
                    ->setServiceClassCode(Batch::CREDITS_ONLY)
                    ->setCompanyName($thirdPartyCredit->getName())
//                ->setCompanyDiscretionaryData('Leaflink ACH')
//                ->setCompanyId('S' . $thirdPartyCredit->getRoutingNumber())
                    ->setCompanyId('**********')
                    ->setStandardEntryClassCode('CCD')
                    ->setCompanyEntryDescription('ACH')
                    ->setCompanyDescriptiveDate(date('ymd'))
//                    ->setEffectiveEntryDate(date("ymd", strtotime('+5 day')))
                    ->setEffectiveEntryDate(Util::GetNextBusinessDayNacha())
                    ->setOriginatorStatusCode('1')
                    ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));

                $uctRepo = Util::em()->getRepository(UserCardTransaction::class);
                // Increase line count by 4 to keep the transaction lines correct.
                $lineCount += 4;
                $companyName = null;
                foreach ($achQuery as $ach) {
                    //Find all credits in que.
                    $uct = $uctRepo->findOneBy(['tranId' => $ach->getTranId()]);
                    $txnDescriptor = Util::meta($uct, 'txnDescriptor');
                    if ($uct->getTranCode() === 'credit') {

                        //Initialize variables
                        $uc = $uct->getUserCard();
                        $user = User::findByBankAccountId($ach->getBankAccountId());
                        $config = $user->ensureConfig();
                        $txnAmount = $uct->getTxnAmount();

                        $newDailyCount = $dailyCrCount + $txnAmount;
                        $newWeeklyCount = $weeklyCrCount + $txnAmount;
                        $newMonthlyCount = $monthlyCrCount + $txnAmount;

                        $checkResult = AchServiceController::checkAchLimits($newDailyCount, $newWeeklyCount, $newMonthlyCount, $origin, AchServiceController::LIMIT_CHECK_CREDIT);

                        //Check and see if transaction hits platform limit
//                        if ($checkResult === AchServiceController::LIMIT_CHECK_SUCCESS)
//                        {

                            // Check that number isn't too big
                            $num = $uct->getTxnAmount();
                            $numlength = strlen((string)abs($num));
                            if ($numlength >= 11)
                            {
                                $newDailyCount = $dailyCrCount;
                                $newWeeklyCount = $weeklyCrCount;
                                $newMonthlyCount = $monthlyCrCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is over the maximum processable transaction amount.');
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
//                                WebhookService::achErrorWebhook($ach, 'T02', self::TERN_CODE_T02);
                                continue;
                            }

                            //Check that number isn't 0.
                            if ($num === 0)
                            {
                                $newDailyCount = $dailyCrCount;
                                $newWeeklyCount = $weeklyCrCount;
                                $newMonthlyCount = $monthlyCrCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is 0. Transaction is being canceled.');
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();

//                                WebhookService::achErrorWebhook($ach, 'T01', self::TERN_CODE_T01);
                                continue;
                            }

                            //Decrypt Data
                            $decryptedDbaNo = SSLEncryptionService::decrypt($uc->getDbaNo());
//                        $decryptedDbaNo = $uc->getDbaNo();
                            $decryptedAccountNum = SSLEncryptionService::decrypt($uc->getAccountNumber());
//                        $decryptedAccountNum = $uc->getAccountNumber();

                        // Sanitize company name
                        $sanitizedObject = Stringy::create($config->getCompanyName())->toAscii();
                        $sanitizedCompanyName = $sanitizedObject->__toString();

                            // Increases trace number count by 1 when line is added.
                            $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
                            $this->creditEntry = (new CcdEntry)
                                ->setTransactionCode(TransactionCode::CHECKING_DEPOSIT)
                                ->setReceivingDfiId(substr($decryptedDbaNo, 0, 8))
                                ->setCheckDigit(substr($decryptedDbaNo, -1))
                                ->setReceivingDFiAccountNumber($decryptedAccountNum)
                                ->setAmount(number_format(($uct->getTxnAmount()/100), 2, '.', ''))
                                ->setReceivingCompanyId(substr((string)$ach->getBankAccountId(), -15))
                                ->setReceivingCompanyName($sanitizedCompanyName)
//                            ->setDiscretionaryData('LeafLink Credit')
                                ->setAddendaRecordIndicator(0)
                                ->setTraceNumber($traceNumberBase, $traceNumberCount);
//                                ->setReceivingDFiId('**********');

                            if ($txnDescriptor) {
                                $this->creditEntry->addAddenda((new Addenda())->setPaymentRelatedInformation(
                                    $txnDescriptor
                                ));
                            }

                            $this->batch->addEntry($this->creditEntry);

                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setBatchId($batchId)
                                ->setLinePosition($lineCount)
                                ->setTraceNumber($this->creditEntry->getTraceNumber())
                                ->persist();
                            $uct->setAccountStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setAchBatchId($batchId)
                                ->persist();

//                            SlackService::check('Transaction ID: ' . $ach->getTranId() . ' has been added to the batch.');
//                            WebhookService::achReturnWebhook($ach, self::ACH_BATCH_SUCCESS);

                            //Updates counts for next transaction
                            $dailyCrCount = $newDailyCount;
                            $weeklyCrCount = $newWeeklyCount;
                            $monthlyCrCount = $newMonthlyCount;
                            $totalCredits += $txnAmount;
                            ++$lineCount;
                            $params['tranCount']++;
                            $params['creditCount']++;
//                        } else {
//
//                            $newDailyCount = $dailyCrCount;
//                            $newWeeklyCount = $weeklyCrCount;
//                            $newMonthlyCount = $monthlyCrCount;
//
//                            // Limit check failed. Cancel transaction, send slack and webhook.
//
//                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
//                                ->persist();
//                            $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
//                                ->persist();
//
//                            //TODO: Possibly set up warning email.
//
//                            $failureMessage = AchServiceController::getLimitErrorCode($checkResult);
//
////                            SlackService::alert('Transaction ' . $ach->getTranId() . ' has failed a limit check.', [
////                                'Transaction' => $ach->getTranId(),
////                                'Error Code' => $checkResult,
////                                'Error Description' => $failureMessage
////                            ]);
//
////                            WebhookService::achErrorWebhook($ach, $checkResult, $failureMessage);
//                        }
                    }
                }

                if ($totalCredits !== 0)
                {
                    $this->file->addBatch($this->batch);
                }

                // Don't create offset if there are no debit entries.
//                if ($totalDebits !== 0)
//                {
//                    $this->batch = new Batch();
//
//                    $this->batch->getHeader()->setBatchNumber(3)
//                        ->setServiceClassCode(Batch::CREDITS_ONLY)
//                        ->setCompanyName('Fusion LLF I')
//                        ->setCompanyId('**********') //TODO: Update for EWB
//                        ->setStandardEntryClassCode('CCD')
//                        ->setCompanyEntryDescription('ACH')
//                        ->setCompanyDescriptiveDate(date('ymd'))
////                        ->setEffectiveEntryDate(date("ymd", strtotime('today')))
//                        ->setEffectiveEntryDate(Util::GetNextBusinessDayNacha())
//                        ->setOriginatorStatusCode('1')
//                        ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));
//
//                    //Debit Offset
//                    $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
//                    $this->creditEntry = (new CcdEntry)
//                        ->setTransactionCode(22)
//                        ->setReceivingDfiId(substr($thirdPartyDebit->getRoutingNumber(), 0, 8))
//                        ->setCheckDigit(substr($thirdPartyDebit->getRoutingNumber(), 8, 1))
//                        ->setReceivingDFiAccountNumber(SSLEncryptionService::decrypt($thirdPartyDebit->getAccountNumber()))
//                        ->setAmount(number_format(($totalDebits / 100), 2, '.', ''))
//                        ->setReceivingCompanyId(substr((string)**************, -15))
//                        ->setReceivingCompanyName("Fusion LLF II")
////                ->setDiscretionaryData('Offset')
//                        ->setAddendaRecordIndicator(0)
//                        ->setTraceNumber($traceNumberBase, $traceNumberCount);
//
//                    $this->batch->addEntry($this->creditEntry);
//
//                    $this->file->addBatch($this->batch);
//                }
//
//                // Don't create offset if there are no credit entries.
//                if ($totalCredits !== 0)
//                {
//                    $this->batch = new Batch();
//
//                    $this->batch->getHeader()->setBatchNumber(4)
//                        ->setServiceClassCode(Batch::DEBITS_ONLY)
//                        ->setCompanyName($thirdPartyCredit->getName())
//                        ->setCompanyId('**********')
//                        ->setStandardEntryClassCode('CCD')
//                        ->setCompanyEntryDescription('ACH')
//                        ->setCompanyDescriptiveDate(date('ymd'))
////                        ->setEffectiveEntryDate(date("ymd", strtotime('today')))
//                        ->setEffectiveEntryDate(Util::GetNextBusinessDayNacha())
//                        ->setOriginatorStatusCode('1')
//                        ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));
//
//                    // Credit Offset
//                    $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
//                    $this->debitEntry = (new CcdEntry)
//                        ->setTransactionCode(27)
//                        ->setReceivingDfiId(substr($thirdPartyCredit->getRoutingNumber(), 0, 8))
//                        ->setCheckDigit(substr($thirdPartyCredit->getRoutingNumber(), 8, 1))
//                        ->setReceivingDFiAccountNumber(SSLEncryptionService::decrypt($thirdPartyCredit->getAccountNumber()))
//                        ->setAmount(number_format(($totalCredits / 100), 2, '.', ''))
//                        ->setReceivingCompanyId('**********')
//                        ->setReceivingCompanyName("Fusion LLF I")
////                        ->setReceivingCompanyId(substr((string)**************, -15))
////                        ->setReceivingCompanyName('Fusion LLF II')
////                ->setDiscretionaryData('Offset')
//                        ->setAddendaRecordIndicator(0)
//                        ->setTraceNumber($traceNumberBase, $traceNumberCount);
//
//                    $this->batch->addEntry($this->debitEntry);
//
//                    $this->file->addBatch($this->batch);
//                }

                // Updates database with new trace number count and limit counts.
                $origin->setTraceNumberCount($traceNumberCount)
                    ->setDailyDbCount($dailyDbCount)
                    ->setWeeklyDbCount($weeklyDbCount)
                    ->setMonthlyDbCount($monthlyDbCount)
                    ->setDailyCrCount($dailyCrCount)
                    ->setWeeklyCrCount($weeklyCrCount)
                    ->setMonthlyCrCount($monthlyCrCount)
                    ->persist();

                //Generates data string and file name for Nacha file.
                $batchString = $this->file->__toString();
                $fileName = date("Y-m-d-H-i") . ".txt";

                //Store file name and encrypted string.
                $encryptedBatchString = SSLEncryptionService::encrypt($batchString);

                $achBatch->setBatchFileName($fileName)
                    ->setBatchFileString($encryptedBatchString)
                    ->setTransactionCount($totalDebits + $totalCredits)
                    ->setBatchStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                    ->setSentTime(new \DateTime())
                    ->setWebhooksSent(AchBatch::ACH_BATCH_WEBHOOKS_LL_UNSENT)
                    ->setProgram(AchBatch::ACH_BATCH_PROGRAM_LEAFLINK)
                    ->persist();

//                $path = $fileName;
//
//                // For generating local files rather than pushing to sFTP.
//                file_put_contents($path, $batchString);

                $this->login();

                try
                {
                    $put = $this->sftp->put("/ACHPAYMENTSIMPORT/" . $fileName, $batchString);
                    Log::debug('Upload sFTP result for EWB ACH file', [
                        'put' => $put,
                    ]);

                    // Store previous file submission for later retrieval.
                    $this->oldLogin();
                    $origin->setPreviousFileName($fileName)
                        ->persist();
                    $this->sftp->put('./archive/' . $fileName, $batchString);

                } catch(\Throwable $e)
                {
                    SlackService::exception('Failed to run EWB batch internally', $e, [
                        'script' => Util::getCallstackSource(),
                    ]);

                    WebhookService::achExceptionWebhook('T06', AchServiceController::TERN_CODE_T06, null);
                }

                // Store previous file submission for later retrieval.
                $origin->setPreviousFileName($fileName)
                    ->persist();

                $this->sftp->put('/ACHPAYMENTSIMPORT/archive/' . $fileName, $batchString);

                SlackService::tada('EWB Nacha file ' . $fileName . ' has been created and sent successfully!', [
                    'ACH Batch ID: ' => $achBatch->getId(),
                    'ACH Batch File Name: ' => $achBatch->getBatchFileName(),
                    'Transaction Total: $' => $achBatch->getTransactionCount()/100,
                    'Status: ' => $achBatch->getBatchStatus(),
                    'Debit Count: ' => $params['debitCount'],
                    'Credit Count: ' => $params['creditCount'],
                    'Total Count: ' => $params['tranCount']
                ]);

                if (Util::isLive())
                {
                    Email::sendWithTemplate(['<EMAIL>', '<EMAIL>'], Email::BATCH_REPORT, $params);

                }
            } else {
                SlackService::check('Leaflink East West Bank ACH Batch has nothing to do.');
            }

            $this->sendBatchWebhooks($request);

            return new Response();
        } catch (\Throwable $t)
        {
            WebhookService::achExceptionWebhook('T06', AchServiceController::TERN_CODE_T06, $batchId);

            SlackService::exception('Failed to run EWB batch', $t, [
                'script' => '/leaflink/ewb-batch',
                'source' => Util::getCallstackSource(),
            ]);

            return new FailedResponse();
        }
    }

    /**
     * @Route("/t/cron/leaflink/send-batch-webhooks")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function sendBatchWebhooks(Request $request, $retry = false)
    {
        Util::longRequest();


        $batches = Util::em()->getRepository(AchBatch::class)
            ->createQueryBuilder('ach')
            ->where('ach.webhooksSent = :webhooksSent')
            ->setParameter('webhooksSent', AchBatch::ACH_BATCH_WEBHOOKS_LL_UNSENT)
            ->orderBy('ach.sentTime', 'desc')
            ->getQuery()
            ->getResult();

        if (!empty($batches))
        {
            foreach ($batches as $batch)
            {
                $batchId = $batch->getId();
                try {
                    $batch->setWebhooksSent(AchBatch::ACH_BATCH_WEBHOOKS_LL_PROCESSING)
                        ->persist();

                    $count = 0;

                    $transactionQuery = Util::em()->getRepository(AchTransactions::class)
                        ->createQueryBuilder('ach')
                        ->where('ach.batchId = :batchId')
                        ->setParameter('batchId', $batchId)
                        ->orderBy('ach.createdAt', 'desc')
                        ->getQuery()
                        ->getResult();

                    foreach ($transactionQuery as $transaction)
                    {
                        //TODO: Add logging here

                        WebhookService::achReturnWebhook($transaction, AchServiceController::ACH_BATCH_SUCCESS);
                        $count++;
                    }

                    $batch->setWebhooksSent(AchBatch::ACH_BATCH_WEBHOOKS_LL_SENT)
                        ->persist();

                    SlackService::check('Ach Batch ' . $batchId . ' webhooks have been sent successfully.', [
                        'Batch ID:' => $batch->getId(),
                        'Count:' => $count
                    ]);

                } catch (\Throwable $t)
                {
                    WebhookService::achExceptionWebhook('T07', AchServiceController::TERN_CODE_T07, $batchId);

                    SlackService::exception('Failed to send batch webhooks', $t, [
                        'script' => 'leaflink/send-batch-webhooks',
                        'source' => Util::getCallstackSource(),
                    ]);

                    return new FailedResponse();
                }
            }
        } else {
            SlackService::check('No batch webhooks need to be sent');
        }
        return new FailedResponse();
    }

    /**
     * @Route("/t/cron/leaflink/ewb-pre-fed")
     * @param Request $request
     * @param $retry
     * @throws \LogicException
     */
    public function ewbPreFed(Request $request, Imap $theImap, $retry = false)
    {
        if (Util::isLive())
        {
            Util::longRequest();

            try {
                $imap = new ImapController();
                $ewbConnection = $theImap->get('ewb_email');
                $prefedCheck = $imap->ewbPrefedEmail($request, $ewbConnection);

                if ($prefedCheck['Response'] === ImapController::EWB_PREFED_SUCCESS)
                {
                    $achBatch = Util::em()->getRepository(AchBatch::class)
                        ->findOneBy(['batchFileName' => $prefedCheck['Filename']]);
                    $batchId = $achBatch->getId();

                    // Get all ach transactions from file.
                    $achQuery = Util::em()->getRepository(AchTransactions::class)
                        ->createQueryBuilder('ach')
                        ->where('ach.batchId = :batchId')
                        ->andWhere('ach.tranStatus = :processing')
                        ->setParameter('batchId', $batchId)
                        ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                        ->getQuery()
                        ->getResult();

                    $uctRepo = Util::em()->getRepository(UserCardTransaction::class);

                    //Process all transactions in connected ACH Batch
                    foreach ($achQuery as $ach)
                    {
                        // Get UCT and set status of ACH Transaction
                        $tranId = $ach->getTranId();
                        $uct = $uctRepo->findOneBy(['tranId' => $tranId]);
                        $uc = $uct->getUserCard();
                        $user = $uc->getUser();
                        $config = $user->getConfig();

                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_SENT)
                            ->setSettleDate(new \DateTime('+3 day'))
                            ->persist();
                        $uct->setAccountStatus(UserCardTransaction::STATUS_LL_SENT)
                            ->persist();

                        //Send Slack notification
                        SlackService::tada('ACH Transaction ' . $ach->getTranId() . ' has been sent to the FED!',
                            [
                                'Ach Transaction Id' => $ach->getTranId(),
                                'Batch Id' => $batchId,
                                'Account' => $config->getBankAccountId(),
                                'Transaction Amount' => '$ ' . $uct->getTxnAmount()/100
                            ]);
                        WebhookService::achReturnWebhook($ach, AchServiceController::ACH_BATCH_PREFED_SUCCESS);
                    }

                    //Set batch return file string and status after all items have been processed
                    $achBatch->setBatchStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->persist();

                    //Send slack notification for finished batch settlement
                    SlackService::tada('ACH Batch ' . $achBatch->getId() . ' has passed Pre-FED Validation!', [
                        'ACH Batch ID' => $achBatch->getId(),
                        'ACH Batch File Name' => $achBatch->getBatchFileName(),
                        'ACH Transaction Count' => $achBatch->getTransactionCount(),
                        'Status' => $achBatch->getBatchStatus()
                    ]);

                    //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                    //TODO: Archive file in sFTP.

                    return new SuccessResponse([], 'success');

                }

                if ($prefedCheck['Response'] === ImapController::EWB_PREFED_FAILURE)
                {
                    $achBatch = Util::em()->getRepository(AchBatch::class)
                        ->findOneBy(['batchFileName' => $prefedCheck['Filename']]);
                    $batchId = $achBatch->getId();

                    SlackService::alert('Ach Batch ' . $batchId . ' has returned with an error.');

                    $achRepo = Util::em()->getRepository(AchTransactions::class);
                    $achQuery = $achRepo->createQueryBuilder('ach')
                        ->where('ach.batchId = :batchId')
                        ->andWhere('ach.tranStatus = :processing')
                        ->setParameter('batchId', $batchId)
                        ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                        ->getQuery()
                        ->getResult();

                    foreach ($achQuery as $ach) {
                        // Handle Entire Batch
                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                            ->persist();


                        // Remove webhooks as temporary stopgap for LL
//                        WebhookService::achReturnWebhook($ach, 'EWB Failed at Pre-FED');
                        SlackService::alert('Transaction ' . $ach->getTranId() . ' is part of a batch that contains a pre-fed error. Setting status to returned.', [
                            'error_message' => 'EWB Failed at Pre-FED.'
                        ]);
                    }

                    $achBatch->setBatchStatus(UserCardTransaction::STATUS_LL_CANCELED)
                        ->persist();

                    //TODO: Delete and archive file from sFTP.

                    return new FailedResponse();
                }

                return new SuccessResponse();

            } catch (\Throwable $t)
            {
                SlackService::exception('Failed to run EWB pre FED', $t, [
                    'script' => 'leaflink/ewb-pre-fed',
                    'source' => Util::getCallstackSource(),
                ]);

                return new FailedResponse();
            }
        } else {
            return new FailedResponse('Is not live.');
        }
    }

    /**
     * @Route("/t/cron/leaflink/ewb-return")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function ewbReturn(Request $request, $retry = false)
    {
        Util::longRequest();

        try {
            if (Util::isLive())
            {
                $this->login();
            } else {
                $this->oldLogin();
            }

            $dir = "./";
            $files = $this->sftp->nlist($dir);
            if (is_array($files)) {
                Log::debug('Found ' . count($files) . ' files on EWB sFTP when processing returns');
            } else {
                Log::debug('Nothing on EWB sFTP when processing returns', [
                    'files' => $files,
                ]);
            }

            // Process all files.
            foreach ($files as $file)
            {
                if (strpos($file, "ACH_Return_") === 0)
                {
                    $path = $dir . $file;
                    $dlFIle = $this->sftp->get($path);

//                    $returnArray = NachaReturnService::getTraceNumsFromFile($dlFIle);

                    $returnArray = NachaReturnService::parseReturnFileLL($dlFIle);

                    SlackService::info('Return Array', $returnArray);

                    foreach ($returnArray as $return)
                    {
                        $txn = AchTransactions::findTransactionByTraceId($return['traceNum']);

                        if ($txn !== NULL && strpos($return['returnCode'], 'R') === 0)
                        {
                            $errorDesc = AchServiceController::getFedErrorCode($return['returnCode']);

                            $txn->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                                ->setSettleDate(new \DateTime('+99 year'))
                                ->setDayCount(10)
                                ->persist();

                            SlackService::alert('EWB Transaction ' . $txn->getTranId() . ' has returned from the fed with an error.', [
                                'Transaction' => $txn->getTranId(),
                                'Error Code' => $return['returnCode'],
                                'Error Description' => $errorDesc
                            ]);

                            WebhookService::achErrorWebhook($txn, $return['returnCode'], $errorDesc);
                            LeafLinkLogEvent::logEvent($txn, $return['returnCode']);

                        } elseif ($txn !== NULL && strpos($return['returnCode'], 'C') === 0)
                        {
                            $errorDesc = AchServiceController::getFedNOCCode($return['returnCode']);

                            SlackService::alert('Transaction ' . $txn->getTranId() . ' has returned from the fed with an NOC. Transaction will still be processed.', [
                                'Transaction' => $txn->getTranId(),
                                'Error Code' => $return['returnCode'],
                                'Error Description' => $errorDesc
                            ]);

                            WebhookService::achErrorWebhook($txn, $return['returnCode'], $errorDesc);
                            LeafLinkLogEvent::logEvent($txn, $return['returnCode']);
                        } elseif ($txn !== NULL)
                        {
                            $txn->setTranStatus(UserCardTransaction::STATUS_LL_QUESTIONABLE)
                                ->setSettleDate(new \DateTime('+99 year'))
                                ->persist();

                            SlackService::alert('Transaction ' . $txn->getTranId() . ' has returned from the fed with an unknown return code.', [
                                'Transaction' => $txn->getTranId(),
                                'NOC Code' => $return['returnCode']
                            ]);

                            WebhookService::achReturnWebhook($txn, $return['returnCode']);
                            LeafLinkLogEvent::logEvent($txn, $return['returnCode']);
                        } else {
                            SlackService::alert('Transaction could not be found.', [
                                'Trace Number' => $return['traceNum'],
                                'Error Code' => $return['returnCode'],
                            ]);
                        }

                    }

                    //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                    $this->sftp->delete($path);
                    if (Util::isLive())
                    {
                        $this->oldLogin();
                        $this->sftp->put($dir."archive".'/'.$file, $dlFIle);
                    }
                }
            }

            return new SuccessResponse();

        } catch (\Throwable $t)
        {
            WebhookService::achExceptionWebhook('T09', AchServiceController::TERN_CODE_T09, null);

            SlackService::exception('Failed to run EWB return', $t, [
                'script' => 'leaflink/ewb-return',
                'source' => Util::getCallstackSource(),
            ]);

            return new FailedResponse();
        }
    }
}
