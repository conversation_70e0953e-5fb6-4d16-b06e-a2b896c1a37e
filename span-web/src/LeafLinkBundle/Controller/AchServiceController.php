<?php


namespace LeafLinkBundle\Controller;


use CoreBundle\Controller\Cron\NakedProtectedController;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\NachaEntity;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use LeafLinkBundle\Entity\AchB2BTransaction;
use LeafLinkBundle\Entity\DailyCheck;
use LeafLinkBundle\Entity\LeafLinkLogEvent;
use LeafLinkBundle\Services\NachaEntityService;
use LeafLinkBundle\Services\PaymentService;
use LeafLinkBundle\Services\SlackService;
use LeafLinkBundle\Services\WebhookService;
use LeafLinkBundle\Services\YodleeService;
use Nacha\Batch;
use Nacha\Field\TransactionCode;
use Nacha\File;
use Nacha\Record\Addenda;
use Nacha\Record\CcdEntry;
use Nacha\Record\DebitEntry;
use phpseclib\Net\SFTP;
use SalexUserBundle\Entity\User;
use Symfony\Component\Routing\Annotation\Route;
use Stringy\Stringy;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class AchServiceController extends NakedProtectedController
{
    // Slack Messages
    const ACH_BATCH_SUCCESS = "Success: Transaction added to batch";
    const ACH_BATCH_FAILURE_BALANCE = "Balance Check Failure: The account's checked balance was less than the transaction amount.";
    const ACH_BATCH_FAILURE_LIMITS = 'Limits Reached Failure: The set limits of this nacha entity have been reached.';
    const ACH_BATCH_PREFED_SUCCESS = 'Transaction has passed pre-fed validation.';
    const ACH_BATCH_YODLEE_FAILURE = 'Yodlee was unable to be reached when checking this transaction. It has been skipped.';

    // FED Return Codes
    const RETURN_CODE_R01 = 'Insufficient Funds';
    const RETURN_CODE_R02 = 'Account Closed';
    const RETURN_CODE_R03 = 'No account/Unable to locate account';
    const RETURN_CODE_R04 = 'Invalid account number';
    const RETURN_CODE_R05 = "Unauthorized debit to consumer account using corporate SEC code";
    const RETURN_CODE_R06 = "Returned per ODFI's Request";
    const RETURN_CODE_R07 = "Authorization revoked by customer";
    const RETURN_CODE_R08 = "Payment stopped";
    const RETURN_CODE_R09 = "Uncollected funds";
    const RETURN_CODE_R10 = "Customer advises unauthorized, improper,  ineligible, or part of an incomplete transaction";
    const RETURN_CODE_R13 = "Invalid ACH routing number";
    const RETURN_CODE_R16 = "Account frozen/Entry returned per OFAC instruction";
    const RETURN_CODE_R18 = "Improper effective entry date";
    const RETURN_CODE_R19 = "Amount field error";
    const RETURN_CODE_R20 = "Non-transaction account";
    const RETURN_CODE_R21 = "Invalid company identification";
    const RETURN_CODE_R22 = "Invalid individual ID number";
    const RETURN_CODE_R23 = "Credit entry refused by receiver";
    const RETURN_CODE_R24 = "Duplicate entry";
    const RETURN_CODE_R25 = "Addenda error";
    const RETURN_CODE_R26 = "Mandatory field error";
    const RETURN_CODE_R27 = "Trace number error";
    const RETURN_CODE_R28 = "Routing number check digit error";
    const RETURN_CODE_R29 = "Corporate customer advises not authorized";
    const RETURN_CODE_R35 = "Return of improper debit entry";
    const RETURN_CODE_R36 = "Return of improper credit entry";
    const RETURN_CODE_GENERIC = "Generic return code. Return code requires lookup.";

    // FED NOC Codes
    const NOC_CODE_C01 = 'Bank account number incorrect or formatted incorrectly';
    const NOC_CODE_C02 = 'Once valid transit/routing number must be changed';
    const NOC_CODE_C03 = 'Once valid transit/routing number must be changed and causes a change to bank account number structure';
    const NOC_CODE_C04 = 'Customer has changed name or ODFI submitted name incorrectly';
    const NOC_CODE_C05 = 'Entry posted to demand account should contain savings payment codes or vice versa';
    const NOC_CODE_C06 = 'Bank account number must be changed and payment code should indicate posting to another account type (demand/savings)';
    const NOC_CODE_C07 = 'Changes required in three fields indicated';
    const NOC_CODE_C09 = 'Individuals ID number is incorrect';
    const NOC_CODE_C10 = 'Company name is no longer valid and should be changed';
    const NOC_CODE_C11 = 'Company ID is no longer valid and should be changed';
    const NOC_CODE_C12 = 'Both the company name and company id are no longer valid and must be changed';
    const NOC_CODE_GENERIC = 'Generic NOC code. NOC code requires lookup.';

    // Tern Error Codes
    const TERN_CODE_T01 = 'Transaction amount is 0. Transaction has been canceled.';
    const TERN_CODE_T02 = 'Transaction amount exceeds the maximum amount allowed.';
    const TERN_CODE_T03 = "Balance Check Failure: The account's checked balance was less than the transaction amount.";
    const TERN_CODE_T05 = "Yodlee was unable to be reached when checking this transaction. It has been canceled.";
    const TERN_CODE_T06 = "Critical exception occurred during the EWB batch process.";
    const TERN_CODE_T07 = "Critical exception occurred during the EWB webhook process.";
    const TERN_CODE_T08 = "Critical exception occurred during the EWB pre-fed validation process.";
    const TERN_CODE_T09 = "Critical exception occurred during the EWB return process.";

    // Limit Error Codes
    const LIMIT_CODE_L01 = "Transaction exceeds daily debit banking limit and will be canceled.";
    const LIMIT_CODE_L02 = "Transaction exceeds weekly debit banking limit and will be canceled.";
    const LIMIT_CODE_L03 = "Transaction exceeds monthly debit banking limit and will be canceled.";
    const LIMIT_CODE_L04 = "Transaction exceeds daily credit banking limit and will be canceled.";
    const LIMIT_CODE_L05 = "Transaction exceeds weekly credit banking limit and will be canceled.";
    const LIMIT_CODE_L06 = "Transaction exceeds monthly credit banking limit and will be canceled.";
    const LIMIT_CODE_GENERIC = "Transaction exceeds unknown banking limit and will be canceled.";


    // Check Types
    const LIMIT_CHECK_DEBIT = "debit";
    const LIMIT_CHECK_CREDIT = "credit";
    const LIMIT_CHECK_SUCCESS = "success";

    // Check Failures
    const LIMIT_FAILURE_DEBIT_DAY = "L01";
    const LIMIT_FAILURE_DEBIT_WEEK = "L02";
    const LIMIT_FAILURE_DEBIT_MONTH = "L03";
    const LIMIT_FAILURE_CREDIT_DAY = "L04";
    const LIMIT_FAILURE_CREDIT_WEEK = "L05";
    const LIMIT_FAILURE_CREDIT_MONTH = "L06";


    /** @var SFTP */
    public $sftp;

    protected $file;
    protected $batch;
    protected $creditEntry;
    protected $debitEntry;

    protected $allowFromApi = true;

    // Login for sFTP server. Called in submitACHBatch().
    public function login()
    {
        $this->sftp = new SFTP(Util::getParameter('sl_ftp_host_2'), Util::getParameter('sl_ftp_port_2'), 90);
        if ($this->sftp === FALSE) {
            SlackService::alert('Leaflink FTP connection error!');
            return;
        }
        if (!$this->sftp->login(
            Util::getParameter('sl_ftp_username_2'),
            Util::getParameter('sl_ftp_password_2')
        )) {
            SlackService::alert('Failed to login the LeafLink ftp server (003)...', [
                'error' => $this->sftp->getLastSFTPError(),
            ]);
            return;
        }
    }

    public function getQuery()
    {
        //Creates an array of all ACH transactions with the status LL_SENT. This should only be transactions created via leaflink payment controller.
        $achQuery = Util::em()->getRepository(AchTransactions::class)
            ->createQueryBuilder('ach')
            ->where('ach.tranStatus = :received')
            ->andWhere(Util::expr()->in('ach.bankId', ':bankIds'))
            ->setParameter('received', UserCardTransaction::STATUS_LL_RECEIVED)
            ->setParameter('bankIds', array_merge(
                PaymentService::getCreditBankIds('salal'),
                PaymentService::getDebitBankIds('salal'),
            ))
            ->orderBy('ach.createdAt', 'ASC')
            ->getQuery()
            ->getResult();

        return $achQuery;
    }

    /**
     * @param NachaEntity $origin
     */
    public static function checkAchLimits($dayCount, $weekCount, $monthCount, $origin, $type)
    {
        if ($type === self::LIMIT_CHECK_DEBIT)
        {
            if ($monthCount <= $origin->getMonthlyDbLimit())
            {
                if ($weekCount <= $origin->getWeeklyDbLimit())
                {
                    if ($dayCount <= $origin->getDailyDbLimit())
                    {
                        return self::LIMIT_CHECK_SUCCESS;
                    }

                    return self::LIMIT_FAILURE_DEBIT_DAY;
                }

                return self::LIMIT_FAILURE_DEBIT_WEEK;
            }

            return self::LIMIT_FAILURE_DEBIT_MONTH;
        }

        if ($type === self::LIMIT_CHECK_CREDIT)
        {
            if ($monthCount <= $origin->getMonthlyCrLimit())
            {
                if ($weekCount <= $origin->getWeeklyCrLimit())
                {
                    if ($dayCount <= $origin->getDailyCrLimit())
                    {
                        return self::LIMIT_CHECK_SUCCESS;
                    }

                    return self::LIMIT_FAILURE_CREDIT_DAY;
                }

                return self::LIMIT_FAILURE_CREDIT_WEEK;
            }

            return self::LIMIT_FAILURE_CREDIT_MONTH;
        }

        SlackService::alert('Check Limits has encountered an incorrect check type. Please ensure this function is being used properly.');
        return new FailedResponse('Failure at checkAchLimits');
    }

    public static function getFedErrorCode($code)
    {
        switch ($code)
        {
            case "R01":
                return self::RETURN_CODE_R01;
                break;
            case "R02":
                return self::RETURN_CODE_R02;
                break;
            case "R03":
                return self::RETURN_CODE_R03;
                break;
            case "R04":
                return self::RETURN_CODE_R04;
                break;
            case "R05":
                return self::RETURN_CODE_R05;
                break;
            case "R06":
                return self::RETURN_CODE_R06;
                break;
            case "R07":
                return self::RETURN_CODE_R07;
                break;
            case "R08":
                return self::RETURN_CODE_R08;
                break;
            case "R09":
                return self::RETURN_CODE_R09;
                break;
            case "R10":
                return self::RETURN_CODE_R10;
                break;
            case "R13":
                return self::RETURN_CODE_R13;
                break;
            case "R16":
                return self::RETURN_CODE_R16;
                break;
            case "R18":
                return self::RETURN_CODE_R18;
                break;
            case "R19":
                return self::RETURN_CODE_R19;
                break;
            case "R20":
                return self::RETURN_CODE_R20;
                break;
            case "R21":
                return self::RETURN_CODE_R21;
                break;
            case "R22":
                return self::RETURN_CODE_R22;
                break;
            case "R23":
                return self::RETURN_CODE_R23;
                break;
            case "R24":
                return self::RETURN_CODE_R24;
                break;
            case "R25":
                return self::RETURN_CODE_R25;
                break;
            case "R26":
                return self::RETURN_CODE_R26;
                break;
            case "R27":
                return self::RETURN_CODE_R27;
                break;
            case "R28":
                return self::RETURN_CODE_R28;
                break;
            case "R29":
                return self::RETURN_CODE_R29;
                break;
            case "R35":
                return self::RETURN_CODE_R35;
                break;
            case "R36":
                return self::RETURN_CODE_R36;
                break;
            default:
                return self::RETURN_CODE_GENERIC;
        }
    }

    public static function getFedNOCCode($code)
    {
        switch ($code)
        {
            case "C01":
                return self::NOC_CODE_C01;
                break;
            case "C02":
                return self::NOC_CODE_C02;
                break;
            case "C03":
                return self::NOC_CODE_C03;
                break;
            case "C04":
                return self::NOC_CODE_C04;
                break;
            case "C05":
                return self::NOC_CODE_C05;
                break;
            case "C06":
                return self::NOC_CODE_C06;
                break;
            case "C07":
                return self::NOC_CODE_C07;
                break;
            case "C09":
                return self::NOC_CODE_C09;
                break;
            case "C10":
                return self::NOC_CODE_C10;
                break;
            case "C11":
                return self::NOC_CODE_C11;
                break;
            case "C12":
                return self::NOC_CODE_C12;
                break;
            default:
                return self::NOC_CODE_GENERIC;
        }
    }

    public static function getLimitErrorCode($code)
    {
        switch ($code)
        {
            case "L01":
                return self::LIMIT_CODE_L01;
                break;
            case "L02":
                return self::LIMIT_CODE_L02;
                break;
            case "L03":
                return self::LIMIT_CODE_L03;
                break;
            case "L04":
                return self::LIMIT_CODE_L04;
                break;
            case "L05":
                return self::LIMIT_CODE_L05;
                break;
            case "L06":
                return self::LIMIT_CODE_L06;
                break;
            default:
                return self::NOC_CODE_GENERIC;
        }
    }

    /**
     * @Route("/t/cron/leaflink/ach/daily-counter-reset")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function achDailyReset(Request $request)
    {
        $nachaEntity = Util::em()->getRepository(NachaEntity::class)
            ->findOneBy(['name' => 'SALAL CREDIT UNION']);

        $oldDbAmount = $nachaEntity->getDailyDbCount();
        $oldCrAmount = $nachaEntity->getDailyCrCount();

        if ($oldDbAmount !== 0)
        {
            $nachaEntity->setDailyDbCount(0)
                ->persist();
        }

        if ($oldCrAmount !== 0)
        {
            $nachaEntity->setDailyCrCount(0)
                ->persist();
        }

        SlackService::check('Salal daily amount counters have been reset.', [
            'Previous Debit Amount' => $oldDbAmount,
            'Previous Credit Amount' => $oldCrAmount,
            'New Debit Amount' => $nachaEntity->getDailyDbCount(),
            'New Credit Amount' => $nachaEntity->getDailyCrCount(),
        ]);

        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/weekly-counter-reset")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function achWeeklyReset(Request $request)
    {
        $nachaEntity = Util::em()->getRepository(NachaEntity::class)
            ->findOneBy(['name' => 'SALAL CREDIT UNION']);

        $oldDbAmount = $nachaEntity->getWeeklyDbCount();
        $oldCrAmount = $nachaEntity->getWeeklyCrCount();

        if ($oldDbAmount !== 0)
        {
            $nachaEntity->setWeeklyDbCount(0)
                ->persist();
        }

        if ($oldCrAmount !== 0)
        {
            $nachaEntity->setWeeklyCrCount(0)
                ->persist();
        }

        SlackService::check('Salal weekly amount counters have been reset.', [
            'Previous Debit Amount' => $oldDbAmount,
            'Previous Credit Amount' => $oldCrAmount,
            'New Debit Amount' => $nachaEntity->getWeeklyDbCount(),
            'New Credit Amount' => $nachaEntity->getWeeklyCrCount(),
        ]);

        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/monthly-counter-reset")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     */
    public function achMonthlyReset(Request $request)
    {
        $nachaEntity = Util::em()->getRepository(NachaEntity::class)
            ->findOneBy(['name' => 'SALAL CREDIT UNION']);

        $oldDbAmount = $nachaEntity->getMonthlyDbCount();
        $oldCrAmount = $nachaEntity->getMonthlyCrCount();

        if ($oldDbAmount !== 0)
        {
            $nachaEntity->setMonthlyDbCount(0)
                ->persist();
        }

        if ($oldCrAmount !== 0)
        {
            $nachaEntity->setMonthlyCrCount(0)
                ->persist();
        }

        SlackService::check('Salal monthly amount counters have been reset.', [
            'Previous Debit Amount' => $oldDbAmount,
            'Previous Credit Amount' => $oldCrAmount,
            'New Debit Amount' => $nachaEntity->getMonthlyDbCount(),
            'New Credit Amount' => $nachaEntity->getMonthlyCrCount(),
        ]);

        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/return")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function return(Request $request)
    {

        Util::longRequest();

        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);

        // Process all files.
        foreach ($files as $file)
        {
            //Only process files that start with Salal (which are the salal return files)
            if (strpos($file, "Salal") === 0 && strpos($file, "Salal_b2b") !== 0)
            {
                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);
                $returnArr = json_decode($dlFIle, true, 512, JSON_THROW_ON_ERROR);

                $fileName = $returnArr['file_name'];

                $achBatch = Util::em()->getRepository(AchBatch::class)
                    ->findOneBy(['batchFileName' => $fileName]);
            $batchId = $achBatch->getId();
                //testing only
//                $batchId = 1;

                //Check if error exists
                if ($returnArr['error_message'] !== "NO ERRORS")
                {
                    SlackService::alert('Ach Batch ' . $batchId . ' has returned with an error', $returnArr);

                    //TODO: Test and set up emails.

                    // If error message is expected, find line where error occurred.

                    if (strpos($returnArr['error_message'], 'FILE REJECTED LINE') !== false)
                    {
                        $start = strpos($returnArr['error_message'], 'FILE REJECTED LINE') + 20;
                        $end = strpos($returnArr['error_message'], '-') - 1;
                        $numString = substr($returnArr['error_message'], $start, $end);
                        $errorLine = intval($numString);

                        // Get array of transactions from the failed file

                        $achRepo = Util::em()->getRepository(AchTransactions::class);
                        $achQuery = $achRepo->createQueryBuilder('ach')
                            ->where('ach.batchId = :batchId')
                            ->andWhere('ach.tranStatus = :processing')
                            ->setParameter('batchId', $batchId)
                            ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                            ->getQuery()
                            ->getResult();

                        foreach ($achQuery as $ach)
                        {
                            // Handle Entire Batch
                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                                ->persist();
                            //Report on transaction
                            if ($ach->getLinePosition() === $errorLine)
                            {
//                                WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
                                LeafLinkLogEvent::logEvent($ach, $returnArr['error_message']);
                                SlackService::alert('Transaction ' . $ach->getTranId() . ' has been identified as containing a pre-fed error. Setting status to returned.', [
                                    'error_message' => $returnArr['error_message'],
                                ]);
                            } else {
//                                WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
                                LeafLinkLogEvent::logEvent($ach, $returnArr['error_message']);
                                SlackService::alert('Transaction ' . $ach->getTranId() . ' is part of a batch that contains a pre-fed error. Setting status to returned.', [
                                    'error_message' => $returnArr['error_message'],
                                ]);
                            }


                            //Select Specific Transaction (Depreciated)
//                            if ($ach->getLinePosition() === $errorLine)
//                            {
//                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
//                                    ->persist();
//                                //Report on transaction
//                                WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
//                                SlackService::alert($ach->getTranId() . ' has been identified as containing an error. Setting status to returned.', [
//                                    'error_message' => $returnArr['error_message']
//                                ]);
//
//                            }
                        }

//                        SlackService::alert('ACH batch ' . $batchId . 'will be retried without errored transactions.');
//
//                        $newQuery = Util::em()->getRepository(AchTransactions::class)
//                            ->createQueryBuilder('ach')
//                            ->where('ach.batchId = :batchId')
//                            ->andWhere('ach.tranStatus = :processing')
//                            ->setParameter('batchId', $batchId)
//                            ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
//                            ->getQuery()
//                            ->getResult();
//
//                        return $this->submitACHBatch($request, $newQuery);

                    }

                    $achBatch->setBatchReturnString($dlFIle)
                        ->setBatchStatus(UserCardTransaction::STATUS_LL_CANCELED)
                        ->persist();

                    $this->sftp->put($dir."archive".'/'.$file, $this->sftp->get($dir.'/'.$file));
                    $this->sftp->delete($path);
                    $this->sftp->delete($dir . $fileName);

                    return new FailedResponse('Ach Batch ' . $batchId . ' has failed for an unknown reason.', $returnArr);
                }

                // Get all ach transactions from file.
                $achQuery = Util::em()->getRepository(AchTransactions::class)
                    ->createQueryBuilder('ach')
                    ->where('ach.batchId = :batchId')
                    ->andWhere('ach.tranStatus = :processing')
                    ->setParameter('batchId', $batchId)
                    ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                    ->getQuery()
                    ->getResult();

                $uctRepo = Util::em()->getRepository(UserCardTransaction::class);

                //Process all transactions in connected ACH Batch
                foreach ($achQuery as $ach)
                {
                    // Get UCT and set status of ACH Transaction
                    $tranId = $ach->getTranId();
                    $uct = $uctRepo->findOneBy(['tranId' => $tranId, 'accountStatus' => $ach->getTranStatus()]);
                    $uc = $uct->getUserCard();
                    $user = $uc->getUser();
                    $config = $user->getConfig();

                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->setSettleDate(new \DateTime('+3 day'))
                        ->setDayCount(0)
                        ->persist();
                    $uct->setAccountStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->persist();

                    //Send Slack notification
//                    SlackService::tada('ACH Transaction ' . $ach->getTranId() . ' has been sent to the FED!',
//                        [
//                            'Ach Transaction Id' => $ach->getTranId(),
//                            'Batch Id' => $batchId,
//                            'Account' => $config->getBankAccountId(),
//                            'Transaction Amount' => '$ ' . $uct->getTxnAmount()/100
//                        ]);
                    WebhookService::achReturnWebhook($ach, self::ACH_BATCH_PREFED_SUCCESS);
                    LeafLinkLogEvent::logEvent($ach, LeafLinkLogEvent::LL_LOG_EVENT_PREFED_CHECK);
                }

                //Set batch return file string and status after all items have been processed
                $achBatch->setBatchReturnString($dlFIle)
                    ->setBatchStatus(UserCardTransaction::STATUS_LL_SENT)
                    ->persist();

                //Send slack notification for finished batch settlement
                SlackService::tada('ACH Batch ' . $achBatch->getId() . ' has passed Pre-FED Validation!', [
                    'ACH Batch ID' => $achBatch->getId(),
                    'ACH Batch File Name' => $achBatch->getBatchFileName(),
                    'ACH Transaction Count' => $achBatch->getTransactionCount(),
                    'Status' => $achBatch->getBatchStatus(),
                ]);

                //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                $this->sftp->put($dir."archive".'/'.$file, $this->sftp->get($dir.'/'.$file));
                $this->sftp->delete($path);
                $this->sftp->delete($dir . $fileName);

                return new SuccessResponse($returnArr, 'success');
            }
        }

        return new JsonResponse(true, $files);
//        return new Response();
    }

    /**
     * @Route("/t/cron/leaflink/ach/salal-prefed")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function returnUpdated(Request $request)
    {

        Util::longRequest();

        try
        {
            // Get list of all files in sFTP
            $this->login();
            $dir = "./";
            $files = $this->sftp->nlist($dir);

            // Process all files.
            foreach ($files as $file)
            {
                //Only process files that start with Salal (which are the salal return files)
                if (strpos($file, "Salal") === 0 && strpos($file, "Salal_b2b") !== 0) {
                    $path = $dir . $file;
                    $dlFIle = $this->sftp->get($path);
                    $returnArr = json_decode($dlFIle, true, 512, JSON_THROW_ON_ERROR);

                    $fileName = $returnArr['file_name'];

                    $achBatch = Util::em()->getRepository(AchBatch::class)
                        ->findOneBy(['batchFileName' => $fileName]);
                    $batchId = $achBatch->getId();
                    //testing only
//                $batchId = 1;

                    //Check if error exists
                    if ($returnArr['error_message'] !== "NO ERRORS")
                    {
                        SlackService::alert('Ach Batch ' . $batchId . ' has returned with an error', $returnArr);

                        //TODO: Test and set up emails.

                        // If error message is expected, find line where error occurred.

                        if (strpos($returnArr['error_message'], 'FILE REJECTED LINE') !== false)
                        {
                            $start = strpos($returnArr['error_message'], 'FILE REJECTED LINE') + 20;
                            $end = strpos($returnArr['error_message'], '-') - 1;
                            $numString = substr($returnArr['error_message'], $start, $end);
                            $errorLine = intval($numString);

                            // Get array of transactions from the failed file

                            $achRepo = Util::em()->getRepository(AchTransactions::class);
                            $achQuery = $achRepo->createQueryBuilder('ach')
                                ->where('ach.batchId = :batchId')
                                ->andWhere('ach.tranStatus = :processing')
                                ->setParameter('batchId', $batchId)
                                ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                                ->getQuery()
                                ->getResult();

                            foreach ($achQuery as $ach)
                            {
                                // Handle Entire Batch
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                                    ->persist();
                                //Report on transaction
                                if ($ach->getLinePosition() === $errorLine)
                                {
//                                    // Temporarily remove webhooks
//                                    WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
                                    SlackService::alert('Transaction ' . $ach->getTranId() . ' has been identified as containing a pre-fed error. Setting status to returned.', [
                                        'error_message' => $returnArr['error_message'],
                                    ]);
                                } else {
//                                    WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
                                    SlackService::alert('Transaction ' . $ach->getTranId() . ' is part of a batch that contains a pre-fed error. Setting status to returned.', [
                                        'error_message' => $returnArr['error_message'],
                                    ]);
                                }


                                //Select Specific Transaction (Depreciated)
//                            if ($ach->getLinePosition() === $errorLine)
//                            {
//                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
//                                    ->persist();
//                                //Report on transaction
//                                WebhookService::achReturnWebhook($ach, $returnArr['error_message']);
//                                SlackService::alert($ach->getTranId() . ' has been identified as containing an error. Setting status to returned.', [
//                                    'error_message' => $returnArr['error_message']
//                                ]);
//
//                            }
                            }

//                        SlackService::alert('ACH batch ' . $batchId . 'will be retried without errored transactions.');
//
//                        $newQuery = Util::em()->getRepository(AchTransactions::class)
//                            ->createQueryBuilder('ach')
//                            ->where('ach.batchId = :batchId')
//                            ->andWhere('ach.tranStatus = :processing')
//                            ->setParameter('batchId', $batchId)
//                            ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
//                            ->getQuery()
//                            ->getResult();
//
//                        return $this->submitACHBatch($request, $newQuery);

                        }

                        $achBatch->setBatchReturnString($dlFIle)
                            ->setBatchStatus(UserCardTransaction::STATUS_LL_CANCELED)
                            ->persist();

                        $this->sftp->put($dir."archive".'/'.$file, $this->sftp->get($dir.'/'.$file));
                        $this->sftp->delete($path);
                        $this->sftp->delete($dir . $fileName);

                        return new FailedResponse('Ach Batch ' . $batchId . ' has failed for an unknown reason.', $returnArr);
                    }

                    // Get all ach transactions from file.
                    $achQuery = Util::em()->getRepository(AchTransactions::class)
                        ->createQueryBuilder('ach')
                        ->where('ach.batchId = :batchId')
                        ->andWhere('ach.tranStatus = :processing')
                        ->setParameter('batchId', $batchId)
                        ->setParameter('processing', UserCardTransaction::STATUS_LL_PROCESSING)
                        ->getQuery()
                        ->getResult();

                    $uctRepo = Util::em()->getRepository(UserCardTransaction::class);

                    //Process all transactions in connected ACH Batch
                    foreach ($achQuery as $ach)
                    {
                        // Get UCT and set status of ACH Transaction
                        $tranId = $ach->getTranId();
                        $uct = $uctRepo->findOneBy(['tranId' => $tranId, 'accountStatus' => $ach->getTranStatus()]);
                        $uc = $uct->getUserCard();
                        $user = $uc->getUser();
                        $config = $user->getConfig();

                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_SENT)
                            ->setSettleDate(new \DateTime('+3 day'))
                            ->persist();
                        $uct->setAccountStatus(UserCardTransaction::STATUS_LL_SENT)
                            ->persist();

                        //Send Slack notification
                        SlackService::tada('ACH Transaction ' . $ach->getTranId() . ' has been sent to the FED!',
                            [
                                'Ach Transaction Id' => $ach->getTranId(),
                                'Batch Id' => $batchId,
                                'Account' => $config->getBankAccountId(),
                                'Transaction Amount' => '$ ' . $uct->getTxnAmount()/100,
                            ]);
                        WebhookService::achReturnWebhook($ach, self::ACH_BATCH_PREFED_SUCCESS);
                    }

                    //Set batch return file string and status after all items have been processed
                    $achBatch->setBatchReturnString($dlFIle)
                        ->setBatchStatus(UserCardTransaction::STATUS_LL_SENT)
                        ->persist();

                    //Send slack notification for finished batch settlement
                    SlackService::tada('ACH Batch ' . $achBatch->getId() . ' has passed Pre-FED Validation!', [
                        'ACH Batch ID' => $achBatch->getId(),
                        'ACH Batch File Name' => $achBatch->getBatchFileName(),
                        'ACH Transaction Count' => $achBatch->getTransactionCount(),
                        'Status' => $achBatch->getBatchStatus(),
                    ]);

                    $check = DailyCheck::ensureDailyCheck();
                    $check->setDate(new \DateTime())
                        ->setSalalCompleted(true)
                        ->persist();

                    //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                    $this->sftp->put($dir."archive".'/'.$file, $this->sftp->get($dir.'/'.$file));
                    $this->sftp->delete($path);
                    $this->sftp->delete($dir . $fileName);

                    return new SuccessResponse($returnArr, 'success');
                }

                if (strpos($file, "2021") === 0) {
                    $achBatch = Util::em()->getRepository(AchBatch::class)
                        ->findOneBy(['batchFileName' => $file]);

                    $checkCount = $achBatch->getCheckCount();

                    if ($checkCount > 3)
                    {
                        //TODO: Add Warning Email

                        SlackService::alert('Warning: Salal prefed response has not yet been receieved.');
                    }

                    $achBatch->setCheckCount($checkCount++)
                        ->persist();
                }
            }

            return new JsonResponse(true, $files);
//        return new Response();
        } catch (\Throwable $t)
            {
                //TODO: Add email alerts

                SlackService::exception('Error when process LL salal-prefed', $t, [
                    'source' => '/leaflink/ach/salal-prefed',
                ]);

                return new FailedResponse();
            }
        }


    /**
     * @Route("/t/cron/leaflink/ach/post-fed")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function postFed(Request $request)
    {
        Util::longRequest();

        // Get list of all files in sFTP
        $this->login();
        $dir = "./";
        $files = $this->sftp->nlist($dir);
        // Process all files.
        foreach ($files as $file)
        {
            //Only process files that start with fed (which are the fed return files)
            if (strpos($file, "fed") === 0) // fed_return file
            {
                $path = $dir . $file;
                $dlFIle = $this->sftp->get($path);
                $returnArr = json_decode($dlFIle, true, 512, JSON_THROW_ON_ERROR);

                // Run through list of each error in file.
                foreach ($returnArr['ach_excp_items'] as $error)
                {
                    $txn = null;
                    $suffix = $error['discretionary_description_data'] ?? null;
                    if ( ! empty($suffix)) {
                        $trans = AchB2BTransaction::findByTranIdSuffix($suffix);
                        if (count($trans) === 1) {
                            B2BAchServiceController::processReturnTransaction($error, $trans[0]);
                            continue;
                        }
                        if (count($trans) > 1) {
                            SlackService::alert(
                                'Found more than one B2B returned transactions with the tran ID suffix',
                                $error,
                                [
                                    SlackService::MENTION_HANS,
                                ]
                            );
                            continue;
                        }
                        $trans = AchTransactions::findByTranIdSuffix($suffix);
                        if (count($trans) > 1) {
                            SlackService::alert(
                                'Found more than one returned transactions with the tran ID suffix',
                                $error,
                                [
                                    SlackService::MENTION_HANS,
                                ]
                            );
                        } else if (count($trans) < 1) {
                            Log::warn('No transaction found by the suffix: ' . $suffix, $error);
                        } else {
                            $txn = $trans[0];
                            Log::debug('Found the return transaction by the suffix: ' . $txn->getTranId());
                        }
                    }

                    // Find transaction by trace number.
                    $txn = $txn ?? AchTransactions::findTransactionByTraceId($error['trace_number']);

                    // If transaction is found and error code starts with R (return error)
                    if ($txn !== NULL && strpos($error['return_code'], 'R') === 0)
                    {
                        $txn->setTranStatus(UserCardTransaction::STATUS_LL_RETURNED)
                            ->setSettleDate(new \DateTime('+99 year'))
                            ->setDayCount(10)
                            ->persist();

                        $errorDesc = self::getFedErrorCode($error['return_code']);

                        SlackService::alert('Transaction ' . $txn->getTranId() . ' has returned from the fed with an error.', [
                            'Transaction' => $txn->getTranId(),
                            'Error Code' => $error['return_code'],
                            'Error Description' => $errorDesc,
                        ]);

                        WebhookService::achErrorWebhook($txn, $error['return_code'], $errorDesc);
                        LeafLinkLogEvent::logEvent($txn, $error['return_code']);
                        // If transaction is found and error code starts with C (NOC Code)
                    } elseif ($txn !== NULL && strpos($error['return_code'], 'C') === 0) {

                        $codeDesc = self::getFedNOCCode($error['return_code']);

                        SlackService::info('Transaction ' . $txn->getTranId() . ' has returned from the fed with an NOC. Transaction will still be processed.', [
                            'Transaction' => $txn->getTranId(),
                            'NOC Code' => $error['return_code'],
                            'NOC Description' => $codeDesc,
                        ]);

                        WebhookService::achErrorWebhook($txn, $error['return_code'], $codeDesc);
                        LeafLinkLogEvent::logEvent($txn, $error['return_code']);
                        // Return code is not identified.
                    } elseif ($txn !== NULL) {

                        $txn->setTranStatus(UserCardTransaction::STATUS_LL_QUESTIONABLE)
                            ->setSettleDate(new \DateTime('+99 year'))
                            ->persist();

                        SlackService::alert('Transaction ' . $txn->getTranId() . ' has returned from the fed with an unknown return code.', [
                            'Transaction' => $txn->getTranId(),
                            'NOC Code' => $error['return_code'],
                        ]);

                        WebhookService::achReturnWebhook($txn, $error['return_code']);
                        LeafLinkLogEvent::logEvent($txn, $error['return_code']);
                    } else {
                        SlackService::alert('Transaction could not be found.', [
                            'Trace Number' => $error['trace_number'],
                            'Error Code' => $error['return_code'],
                        ]);
                        LeafLinkLogEvent::logEvent($txn, $error['return_code']);
                    }
                }

                //Delete unnecessary files from the sFTP server. (Files are stored in archive folder and database)
                $this->sftp->put($dir."archive".'/'.$file, $this->sftp->get($dir.'/'.$file));
                $this->sftp->delete($path);

//                return new SuccessResponse($returnArr, 'success');
            }
//            return new FailedResponse('No files need to be processed.', [
//                'drive' => $files
//            ]);
        }

        return new JsonResponse(true, $files);
    }

    /**
     * @Route("/t/cron/leaflink/ach/set-settled")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function setTransactionsToSettled(Request $request)
    {
        Util::longRequest();

        SlackService::clock('Initializing LeafLink Transaction Settlement.');

        $achQuery = Util::em()->getRepository(AchTransactions::class)
            ->createQueryBuilder('ach')
            ->where('ach.settleDate <= :currentDate')
            ->setParameter('currentDate', new \DateTime())
            ->andWhere('ach.bankId <> :spendrBankId')
            ->setParameter('spendrBankId', Util::getParameter('spendr_ffb_id'))
            ->orderBy('ach.createdAt', 'ASC')
            ->getQuery()
            ->getResult();

        if (is_array($achQuery))
        {
            SlackService::wave('Setting LeafLink transactions to settled.', [
                'Transaction Count' => count($achQuery),
            ]);
        }

        $bankIds = PaymentService::getBankIds();
        /** @var AchTransactions $ach */
        foreach ($achQuery as $ach)
        {
            $ach->setTranStatus(UserCardTransaction::STATUS_LL_SETTLED)
                ->setSettleDate(new \DateTime('+99 year'))
                ->persist();

            if (in_array($ach->getBankId(), $bankIds, true)) {
                SlackService::check('Transaction ' . $ach->getTranId() . ' has been set to settled.', [
                    'Transaction' => $ach->getTranId(),
                    'Status' => $ach->getTranStatus(),
                ]);

                WebhookService::achReturnWebhook($ach, 'ACH Transaction settled.');
                LeafLinkLogEvent::logEvent($ach, LeafLinkLogEvent::LL_LOG_EVENT_SET_SETTLED);
            }
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/t/cron/leaflink/ach/set-settled-incremental")
     * @param Request $request
     * @return Response
     * @throws \LogicException
     * @throws \JsonException
     */
    public function setTransactionsToSettledIncremental(Request $request)
    {
        Util::longRequest();

        if (Util::isTodayBusinessDay())
        {
            SlackService::clock('Initializing LeafLink Incremental Transaction Settlement.');

            $achQuery = Util::em()->getRepository(AchTransactions::class)
                ->createQueryBuilder('ach')
                ->where('ach.dayCount <= :dayCount')
                ->setParameter('dayCount', 3)
                ->andWhere('ach.bankId <> :spendrBankId')
                ->setParameter('spendrBankId', Util::getParameter('spendr_ffb_id'))
                ->orderBy('ach.dayCount', 'ASC')
                ->getQuery()
                ->getResult();

            if (is_array($achQuery))
            {
                SlackService::wave('Setting LeafLink transactions to settled.', [
                    'Transaction Count' => count($achQuery),
                ]);
            }

            foreach ($achQuery as $ach)
            {
                $count = $ach->getDayCount();
                $ach->setDayCount($count++)
                    ->persist();

                if ($ach->getDayCount() === 4)
                {
                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_SETTLED)
                        ->setSettleDate(new \DateTime('+99 year'))
                        ->persist();

                    SlackService::check('Transaction ' . $ach->getTranId() . ' has been set to settled.', [
                        'Transaction' => $ach->getTranId(),
                        'Status' => $ach->getTranStatus(),
                    ]);

                    WebhookService::achReturnWebhook($ach, 'ACH Transaction settled.');
                    LeafLinkLogEvent::logEvent($ach, LeafLinkLogEvent::LL_LOG_EVENT_SET_SETTLED);
                }
            }
        } else {
            SlackService::info('Today is not a business day so LeafLink Transaction Settlement has been skipped.');
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/t/cron/leaflink/ach/updated-batch")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function achEfficiencyTest(Request $request, $retry = false)
    {
        Util::longRequest();

        $testMode = $request->get('testMode');
        $slackContext = $testMode ? [
            'Test mode' => true,
        ] : [];

        $force = $request->get('force');
        if (!$force && !Util::isTodayBusinessDay())
        {
            SlackService::info('Today is not a business day, Leaflink Salal ACH Batch will be skipped.',
                $slackContext);
            return new SuccessResponse();
        }

        SlackService::info('Today is a business day, Leaflink Salal ACH Batch will not be skipped.',
            $slackContext);

        // Create ACH Batch and set variables.
        $achBatch = new AchBatch();
        $achBatch->persist();
        $batchId = $achBatch->getId();


        try
        {
            $live = Util::isLive() && !$testMode;
            SlackService::clock('Leaflink Salal ACH Batch is initializing.', $slackContext);

            // Check if query is being passed as an argument. If yes, use that query. If no, build a query off of the existing que.

            if ($retry === false)
            {
                $achQuery = $this->getQuery();
            } else {
                $achQuery = $retry;
            }

            if (count($achQuery) > 0) //Do not run if que is empty.
            {
                if (!$testMode) {
                    $check = DailyCheck::ensureDailyCheck();
                    $check->setDate(new \DateTime())
                        ->setEwbStart(true)
                        ->persist();
                }

                // Set origin and destination variables for nacha generation. These variables must be entered into the database via addNachaEntity API call.
                $origin = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'SALAL CREDIT UNION']);
                $destination = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'FRB SF']);
                $thirdPartyDebit = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'FUSION LLF DB']);
                $thirdPartyCredit = Util::em()->getRepository(NachaEntity::class)
                    ->findOneBy(['name' => 'FUSION LLF CR']);

                SlackService::info('Ach Batch ID: ' . $batchId, [
                    'ACH Count' => count($achQuery),
                ] + $slackContext);

                //trace number variables
                $traceNumberBase = $origin->getTraceNumberBase();
                $traceNumberCount = $origin->getTraceNumberCount();

                //limit count variables
                $dailyDbCount = $origin->getDailyDbCount();
                $weeklyDbCount = $origin->getWeeklyDbCount();
                $monthlyDbCount = $origin->getMonthlyDbCount();

                $dailyCrCount = $origin->getDailyCrCount();
                $weeklyCrCount = $origin->getWeeklyCrCount();
                $monthlyCrCount = $origin->getMonthlyCrCount();

                $totalCreditsDebits = 0;

                //linecount
                $lineCount = 3;

                //params array for mail
                $params = [];
                $params['tranCount'] = 0;
                $params['debitCount'] = 0;
                $params['creditCount'] = 0;

                // Uses origin, destination, and ach transaction query variables to generate a nacha file string.
                $this->file = new File();
                $this->file->getHeader()->setPriorityCode(1)
                    ->setImmediateDestination($destination->getRoutingNumber())
                    ->setImmediateOrigin($origin->getRoutingNumber())
                    ->setFileCreationDate(date('ymd'))
                    ->setFileCreationTime(date('Hi'))
                    ->setFormatCode('1')
                    ->setFileIdModifier('Z')
                    ->setImmediateDestinationName($destination->getName())
                    ->setImmediateOriginName($origin->getName())
                    ->setReferenceCode('Tern');

                $uctRepo = Util::em()->getRepository(UserCardTransaction::class);

                /** @var AchTransactions $ach */
                foreach ($achQuery as $ach) {
                    //offset variables
                    $totalDebits = 0;
                    $totalCredits = 0;

                    $debitEntity = NachaEntityService::getDummyForBankId($ach->getBankId(),
                        NachaEntityService::TYPE_DEBIT, $thirdPartyDebit);
                    $creditEntity = NachaEntityService::getDummyForBankId($ach->getBankId(),
                        NachaEntityService::TYPE_CREDIT, $thirdPartyCredit);

                    // Find all debits in que.
//                    $uct = $uctRepo->findOneBy(['tranId' => $ach->getTranId(), 'accountStatus' => $ach->getTranStatus()]);
                    $uct = $uctRepo->findOneBy(['tranId' => $ach->getTranId()]);
                    $sanitizedCompanyName = 'LeafLink';
                    $txnDescriptor = Util::meta($uct, 'txnDescriptor');

                    if ($uct->getTranCode() === 'debit') {
                        if ( ! $debitEntity) {
                            Log::warn('Debit entity not found for bank ID in ACH txn: ' . $ach->getTranId());
                            continue;
                        }

                        // Debit batch
                        $this->batch = new Batch();

                        $this->batch->getHeader()->setBatchNumber(1)
                            ->setServiceClassCode('225')
                            ->setCompanyName('Flex Pay')
//                ->setCompanyDiscretionaryData('Leaflink ACH')
//                ->setCompanyId('S' . $thirdPartyDebit->getRoutingNumber())
                            ->setCompanyId('S843524524') // 'S851294764')
                            ->setStandardEntryClassCode('CCD')
                            ->setCompanyEntryDescription('ACH')
                            ->setCompanyDiscretionaryData(substr($ach->getTranId(), -19)) // Use this to identify each transaction
                            ->setCompanyDescriptiveDate(date('ymd'))
                            ->setEffectiveEntryDate(date("ymd", strtotime('tomorrow')))
                            ->setOriginatorStatusCode('1')
                            ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));

                        //Set up variables
                        $uc = $uct->getUserCard();
                        $user = User::findByBankAccountId($ach->getBankAccountId());
                        $config = $user->ensureConfig();
                        $txnAmount = $uct->getTxnAmount();

                        $newDailyCount = $dailyDbCount + $txnAmount;
                        $newWeeklyCount = $weeklyDbCount + $txnAmount;
                        $newMonthlyCount = $monthlyDbCount + $txnAmount;

                        $checkResult = AchServiceController::checkAchLimits($newDailyCount, $newWeeklyCount, $newMonthlyCount, $origin, AchServiceController::LIMIT_CHECK_DEBIT);

                        //Check if this transaction exceeds limits.
//                        if ($checkResult === AchServiceController::LIMIT_CHECK_SUCCESS)
//                        {

                        //Yodlee Balance Check:
                        //Check and see if account is verified via Yodlee
                        if ($uc->getAccountId() !== NULL && $uc->getProviderAccountId() !== NULL) {
                            //Get account balance and check if txn will clear
                            $token = YodleeService::getYodleeToken($config->getBankAccountId());
                            $data = YodleeService::getYodleeBankingDetails($token, $uc->getProviderAccountId(), $uc->getAccountId());
                            $balance = $data["account"][0]["balance"]["amount"];
                            //Check if balance data was retrieved.
                            if ($balance)
                            {
                                //Cancel transaction if balance is lower than txnAmount.
                                if (($balance * 100) < $txnAmount) {

                                    $newDailyCount = $dailyDbCount;
                                    $newWeeklyCount = $weeklyDbCount;
                                    $newMonthlyCount = $monthlyDbCount;

                                    SlackService::alert('Updated Transaction ID: ' . $ach->getTranId() . ' did not pass the balance check and will be canceled.',
                                        $slackContext);
                                    if (!$testMode) {
                                        $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                            ->persist();
                                        $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                            ->persist();
                                        WebhookService::achErrorWebhook($ach, 'T03', self::TERN_CODE_T03);
                                    }
                                    continue;
                                }
                            } else {
                                //Unable to get data from Yodlee. Skip transaction.

                                $newDailyCount = $dailyDbCount;
                                $newWeeklyCount = $weeklyDbCount;
                                $newMonthlyCount = $monthlyDbCount;

                                SlackService::alert('Updated Transaction ID: ' . $ach->getTranId() . ' : Yodlee connection failed. Transaction will be canceled.',
                                    $slackContext);
                                if (!$testMode) {
                                    $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                        ->persist();
                                    $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                        ->persist();
                                    WebhookService::achErrorWebhook($ach, 'T05', self::TERN_CODE_T05);
                                }
                                continue;
                            }
                        }

                        // Check that number isn't too big
                        $num = $uct->getTxnAmount();
                        $numlength = strlen((string)abs($num));
                        if ($numlength >= 11)
                        {
                            $newDailyCount = $dailyDbCount;
                            $newWeeklyCount = $weeklyDbCount;
                            $newMonthlyCount = $monthlyDbCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is over the maximum processable transaction amount.');
                            if (!$testMode) {
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                            }
//                                WebhookService::achErrorWebhook($ach, 'T02', self::TERN_CODE_T02);
                            continue;
                        }

                        //Check that number isn't 0.
                        if ($num === 0)
                        {
                            $newDailyCount = $dailyDbCount;
                            $newWeeklyCount = $weeklyDbCount;
                            $newMonthlyCount = $monthlyDbCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is 0. Transaction is being canceled.');
                            if (!$testMode) {
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                            }

//                                WebhookService::achErrorWebhook($ach, 'T01', self::TERN_CODE_T01);
                            continue;
                        }

                        //decrypt data
                        $decryptedDbaNo = SSLEncryptionService::cachedDecryptShort($uc->getDbaNo());
//                        $decryptedDbaNo = $uc->getDbaNo();
                        $decryptedAccountNum = SSLEncryptionService::cachedDecryptShort($uc->getAccountNumber());
//                        $decryptedAccountNum = $uc->getAccountNumber();

                        // Sanitize company name
                        $sanitizedObject = Stringy::create($config->getCompanyName())->toAscii();
                        $sanitizedCompanyName = $sanitizedObject->__toString();

                        $traceNumberCount = str_pad(++$traceNumberCount, 7, "0", STR_PAD_LEFT);
                        $this->debitEntry = (new DebitEntry)
                            ->setTransactionCode(TransactionCode::CHECKING_DEBIT)
                            ->setReceivingDfiId(substr($decryptedDbaNo, 0, 8))
                            ->setCheckDigit(substr($decryptedDbaNo, -1))
                            ->setDFiAccountNumber($decryptedAccountNum)
                            ->setAmount(number_format(($uct->getTxnAmount() / 100), 2, '.', ''))
                            ->setIndividualId(substr((string)$ach->getBankAccountId(), -15))
                            ->setIdividualName($sanitizedCompanyName)
//                            ->setDiscretionaryData('LeafLink Debit')
                            ->setAddendaRecordIndicator(0)
                            ->setTraceNumber($traceNumberBase, $traceNumberCount);

                        if ($txnDescriptor) {
                            $this->debitEntry->addAddenda((new Addenda())->setPaymentRelatedInformation(
                                $txnDescriptor
                            ));
                        }

                        $this->batch->addEntry($this->debitEntry);

                        if (!$testMode) {
                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setBatchId($batchId)
                                ->setLinePosition($lineCount)
                                ->setTraceNumber($this->debitEntry->getTraceNumber())
                                ->persist();
                            $uct->setAccountStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setAchBatchId($batchId)
                                ->persist();
                        }

                        //Updates counts for next transaction
                        $dailyDbCount = $newDailyCount;
                        $weeklyDbCount = $newWeeklyCount;
                        $monthlyDbCount = $newMonthlyCount;
                        $totalDebits += $txnAmount;
                        ++$lineCount;
                        $params['tranCount']++;
                        $params['debitCount']++;
                    } else if ($uct->getTranCode() === 'credit') {
                        if ( ! $creditEntity) {
                            Log::warn('Credit entity not found for bank ID in ACH txn: ' . $ach->getTranId());
                            continue;
                        }

                        // Credit batch
                        $this->batch = new Batch();

                        $this->batch->getHeader()->setBatchNumber(1)
                            ->setServiceClassCode('225')
                            ->setCompanyName('Flex Pay')
//                ->setCompanyDiscretionaryData('Leaflink ACH')
//                ->setCompanyId('S' . $thirdPartyCredit->getRoutingNumber())
                            ->setCompanyId('S843524524') // 'S851294764')
                            ->setStandardEntryClassCode('CCD')
                            ->setCompanyEntryDescription('ACH')
                            ->setCompanyDiscretionaryData(substr($ach->getTranId(), -19)) // Use this to identify each transaction
                            ->setCompanyDescriptiveDate(date('ymd'))
                            ->setEffectiveEntryDate(date("ymd", strtotime('tomorrow')))
                            ->setOriginatorStatusCode('1')
                            ->setOriginatingDFiId(substr($origin->getRoutingNumber(), 0, 9));

                        //Initialize variables
                        $uc = $uct->getUserCard();
                        $user = User::findByBankAccountId($ach->getBankAccountId());
                        $config = $user->ensureConfig();
                        $txnAmount = $uct->getTxnAmount();

                        $newDailyCount = $dailyCrCount + $txnAmount;
                        $newWeeklyCount = $weeklyCrCount + $txnAmount;
                        $newMonthlyCount = $monthlyCrCount + $txnAmount;

                        $checkResult = AchServiceController::checkAchLimits($newDailyCount, $newWeeklyCount, $newMonthlyCount, $origin, AchServiceController::LIMIT_CHECK_CREDIT);

                        //Check and see if transaction hits platform limit
//                        if ($checkResult === AchServiceController::LIMIT_CHECK_SUCCESS)
//                        {

                        // Check that number isn't too big
                        $num = $uct->getTxnAmount();
                        $numlength = strlen((string)abs($num));
                        if ($numlength >= 11)
                        {
                            $newDailyCount = $dailyCrCount;
                            $newWeeklyCount = $weeklyCrCount;
                            $newMonthlyCount = $monthlyCrCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is over the maximum processable transaction amount.');
                            if (!$testMode) {
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                            }
//                                WebhookService::achErrorWebhook($ach, 'T02', self::TERN_CODE_T02);
                            continue;
                        }

                        //Check that number isn't 0.
                        if ($num === 0)
                        {
                            $newDailyCount = $dailyCrCount;
                            $newWeeklyCount = $weeklyCrCount;
                            $newMonthlyCount = $monthlyCrCount;

//                                SlackService::alert('B2B Transaction ID: ' . $ach->getTranId() . ' is 0. Transaction is being canceled.');
                            if (!$testMode) {
                                $ach->setTranStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                                $uct->setAccountStatus(UserCardTransaction::STATUS_LL_CANCELED)
                                    ->persist();
                            }

//                                WebhookService::achErrorWebhook($ach, 'T01', self::TERN_CODE_T01);
                            continue;
                        }

                        //Decrypt Data
                        $decryptedDbaNo = SSLEncryptionService::cachedDecryptShort($uc->getDbaNo());
//                        $decryptedDbaNo = $uc->getDbaNo();
                        $decryptedAccountNum = SSLEncryptionService::cachedDecryptShort($uc->getAccountNumber());
//                        $decryptedAccountNum = $uc->getAccountNumber();

                        // Sanitize company name
                        $sanitizedObject = Stringy::create($config->getCompanyName())->toAscii();
                        $sanitizedCompanyName = $sanitizedObject->__toString();

                        // Increases trace number count by 1 when line is added.
                        $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
                        $this->creditEntry = (new CcdEntry)
                            ->setTransactionCode(TransactionCode::CHECKING_DEPOSIT)
                            ->setReceivingDfiId(substr($decryptedDbaNo, 0, 8))
                            ->setCheckDigit(substr($decryptedDbaNo, -1))
                            ->setReceivingDFiAccountNumber($decryptedAccountNum)
                            ->setAmount(number_format(($uct->getTxnAmount()/100), 2, '.', ''))
                            ->setReceivingCompanyId(substr((string)$ach->getBankAccountId(), -15))
                            ->setReceivingCompanyName($sanitizedCompanyName)
//                            ->setDiscretionaryData('LeafLink Credit')
                            ->setAddendaRecordIndicator(0)
                            ->setTraceNumber($traceNumberBase, $traceNumberCount);

                        $this->batch->addEntry($this->creditEntry);

                        if (!$testMode) {
                            $ach->setTranStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setBatchId($batchId)
                                ->setLinePosition($lineCount)
                                ->setTraceNumber($this->creditEntry->getTraceNumber())
                                ->persist();
                            $uct->setAccountStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                                ->setAchBatchId($batchId)
                                ->persist();
                        }

//                            SlackService::check('Transaction ID: ' . $ach->getTranId() . ' has been added to the batch.');
//                            WebhookService::achReturnWebhook($ach, self::ACH_BATCH_SUCCESS);

                        //Updates counts for next transaction
                        $dailyCrCount = $newDailyCount;
                        $weeklyCrCount = $newWeeklyCount;
                        $monthlyCrCount = $newMonthlyCount;
                        $totalCredits += $txnAmount;
                        ++$lineCount;
                        $params['tranCount']++;
                        $params['creditCount']++;
                    }

                    // Don't create offset if there are no debit entries.
                    if ($totalDebits !== 0)
                    {
                        //Debit Offset
                        $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
                        $this->creditEntry = (new CcdEntry)
                            ->setTransactionCode(22)
                            ->setReceivingDfiId(substr($debitEntity->getRoutingNumber(), 0, 8))
                            ->setCheckDigit(substr($debitEntity->getRoutingNumber(), 8, 1))
                            ->setReceivingDFiAccountNumber(SSLEncryptionService::cachedDecryptShort($debitEntity->getAccountNumber()))
                            ->setAmount(number_format(($totalDebits / 100), 2, '.', ''))
                            ->setReceivingCompanyId(substr((string)**************, -15))
                            ->setReceivingCompanyName($sanitizedCompanyName)
//                ->setDiscretionaryData('Offset')
                            ->setAddendaRecordIndicator(0)
                            ->setTraceNumber($traceNumberBase, $traceNumberCount);

                        $this->batch->addEntry($this->creditEntry);

                        $this->file->addBatch($this->batch);
                    }

                    // Don't create offset if there are no credit entries.
                    if ($totalCredits !== 0)
                    {
                        // Credit Offset
                        $traceNumberCount = str_pad(++$traceNumberCount,7,"0",STR_PAD_LEFT);
                        $this->debitEntry = (new CcdEntry)
                            ->setTransactionCode(27)
                            ->setReceivingDfiId(substr($creditEntity->getRoutingNumber(), 0, 8))
                            ->setCheckDigit(substr($creditEntity->getRoutingNumber(), 8, 1))
                            ->setReceivingDFiAccountNumber(SSLEncryptionService::cachedDecryptShort($creditEntity->getAccountNumber()))
                            ->setAmount(number_format(($totalCredits / 100), 2, '.', ''))
                            ->setReceivingCompanyId(substr((string)**************, -15))
                            ->setReceivingCompanyName($sanitizedCompanyName)
//                ->setDiscretionaryData('Offset')
                            ->setAddendaRecordIndicator(0)
                            ->setTraceNumber($traceNumberBase, $traceNumberCount);

                        $this->batch->addEntry($this->debitEntry);

                        $this->file->addBatch($this->batch);
                    }

                    // Increase line count by 4 to keep the transaction lines correct.
                    $lineCount += 4;
                    $totalCreditsDebits += $totalCredits + $totalDebits;
                }

                if (!$testMode) {
                    // Updates database with new trace number count and limit counts.
                    $origin->setTraceNumberCount($traceNumberCount)
                        ->setDailyDbCount($dailyDbCount)
                        ->setWeeklyDbCount($weeklyDbCount)
                        ->setMonthlyDbCount($monthlyDbCount)
                        ->setDailyCrCount($dailyCrCount)
                        ->setWeeklyCrCount($weeklyCrCount)
                        ->setMonthlyCrCount($monthlyCrCount)
                        ->persist();
                }

                //Generates data string and file name for Nacha file.
                $batchString = $this->file->__toString();
                $fileName = date("Y-m-d-H-i") . ".txt";

                //Store file name and encrypted string.
                $encryptedBatchString = SSLEncryptionService::encrypt($batchString);

                $achBatch->setBatchFileName($fileName)
                    ->setBatchFileString($encryptedBatchString)
                    ->setTransactionCount($totalCreditsDebits)
                    ->setBatchStatus(UserCardTransaction::STATUS_LL_PROCESSING)
                    ->setSentTime(new \DateTime())
                    ->setWebhooksSent(AchBatch::ACH_BATCH_WEBHOOKS_LL_UNSENT)
                    ->setProgram(AchBatch::ACH_BATCH_PROGRAM_LEAFLINK)
                    ->persist();

//                $path = $fileName;
//
//                // For generating local files rather than pushing to sFTP.
//                file_put_contents($path, $batchString);

//                $this->login();

                try
                {
                    // For generating local files rather than pushing to sFTP.
//            file_put_contents($path, $batchString);

                    if ($live)
                    {
                        $this->login();
                        $this->sftp->put($fileName, $batchString);
                    }

                    if (!$testMode)
                    {
                        // Store previous file submission for later retrieval.
                        $origin->setPreviousFileName($fileName)
                            ->persist();
                    }

                    if ($live)
                    {
                        $this->sftp->put('./archive/' . $fileName, $batchString);
                    }

                    SlackService::tada('Salal Nacha file ' . $fileName . ' has been created and sent successfully!', [
                        'ACH Batch ID: ' => $achBatch->getId(),
                        'ACH Batch File Name: ' => $achBatch->getBatchFileName(),
                        'Transaction Total: $' => $achBatch->getTransactionCount()/100,
                        'Status: ' => $achBatch->getBatchStatus(),
                        'Debit Count: ' => $params['debitCount'],
                        'Credit Count: ' => $params['creditCount'],
                        'Total Count: ' => $params['tranCount'],
                    ] + $slackContext);

                    if ($live)
                    {
                        Email::sendWithTemplate(['<EMAIL>', '<EMAIL>'], Email::BATCH_REPORT, $params);

                    }

                } catch(\Throwable $e)
                {
                    SlackService::exception('Failed to updating ACH batch', $e, [
                        'script' => Util::getCallstackSource(),
                    ] + $slackContext);

                    if (!$testMode) {
                        WebhookService::achExceptionWebhook('T06', AchServiceController::TERN_CODE_T06, null);
                    }
                }
            }

            if ($live) {
                //TODO
                $this->sendSalalBatchWebhooks($request);
            }

            return new Response();
        } catch (\Throwable $t)
        {
            if (!$testMode) {
                WebhookService::achExceptionWebhook('T06', AchServiceController::TERN_CODE_T06, $batchId);
            }

            SlackService::exception('Error when updating ACH batch', $t, [
                'source' => '/leaflink/ach/updated-batch',
            ] + $slackContext);

            return new FailedResponse();
        }
    }

    /**
     * @Route("/t/cron/leaflink/send-batch-webhooks")
     * @param Request $request
     * @param $retry
     * @return Response
     * @throws \LogicException
     */
    public function sendSalalBatchWebhooks(Request $request, $retry = false)
    {
        Util::longRequest();


        $batches = Util::em()->getRepository(AchBatch::class)
            ->createQueryBuilder('ach')
            ->where('ach.webhooksSent = :webhooksSent')
            ->setParameter('webhooksSent', AchBatch::ACH_BATCH_WEBHOOKS_LL_UNSENT)
            ->orderBy('ach.sentTime', 'desc')
            ->getQuery()
            ->getResult();

        if (!empty($batches))
        {
            foreach ($batches as $batch)
            {
                $batchId = $batch->getId();
                try {
                    $batch->setWebhooksSent(AchBatch::ACH_BATCH_WEBHOOKS_LL_PROCESSING)
                        ->persist();

                    $count = 0;

                    $transactionQuery = Util::em()->getRepository(AchTransactions::class)
                        ->createQueryBuilder('ach')
                        ->where('ach.batchId = :batchId')
                        ->setParameter('batchId', $batchId)
                        ->orderBy('ach.createdAt', 'desc')
                        ->getQuery()
                        ->getResult();

                    foreach ($transactionQuery as $transaction)
                    {
                        //TODO: Add logging here

                        WebhookService::achReturnWebhook($transaction, AchServiceController::ACH_BATCH_SUCCESS);
                        $count++;
                    }

                    $batch->setWebhooksSent(AchBatch::ACH_BATCH_WEBHOOKS_LL_SENT)
                        ->persist();

                    SlackService::check('Ach Batch ' . $batchId . ' webhooks have been sent successfully.', [
                        'Batch ID:' => $batch->getId(),
                        'Count:' => $count,
                    ]);

                } catch (\Throwable $t)
                {
                    WebhookService::achExceptionWebhook('T07', AchServiceController::TERN_CODE_T07, $batchId);

                    SlackService::exception('Error when sending batch webhooks', $t, [
                        'script' => '/leaflink/send-batch-webhooks',
                        'source' => Util::getCallstackSource(),
                    ]);

                    return new FailedResponse();
                }
            }
        }
        return new FailedResponse();
    }
}

