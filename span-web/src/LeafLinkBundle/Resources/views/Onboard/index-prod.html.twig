{% extends '@LeafLink/Layouts/onboard.html.twig' %}

{% block body_box %}
    <div class="onboard-container">

    </div>
    <div id="container-fastlink">
        <div class="wait">
            <h2 class="special-text m-16">Please Wait</h2>
            <div class="wait-container">
                <div class="ispinner gray animating">
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                    <div class="ispinner-blade"></div>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="text-center ll_link">
        <p class="ll-link-p">Don't see your bank?</p>
        <a class="ll-link-text" href="/l/onboard/alternate-bank/{{ config.bankAccountId }}/{{ config.referCode }}">Link Manually</a>
    </div>
    <script>
        (function (window) {
            //Open FastLink
            window.addEventListener('load', function() {
                    $(document).ready($(".wait").toggle());
                    window.fastlink.open({
                            fastLinkURL: 'https://production.node.yodlee.com/authenticate/USDevexProd2-290/?channelAppName=usdevexprod2',
                            accessToken: 'Bearer {{ token }}',
                            params: {
                                userExperienceFlow : 'Verification',
                            },
                            forceIframe: true,
                            onSuccess: function (data) {
                            },
                            onError: function (data) {
                                // Will use built in error display for now
                                // window.location.href = 'https://leaflink.virtualcards.us/l/onboard/error'
                            },
                            onExit: function (data) {
                                $(".wait").toggle()
                                $(".ll_link").toggle()
                                console.log(data)
                                let providerAcctId = data.sites[0].providerAccountId
                                let accountId = data.sites[0].accountId
                                let form = new FormData();
                                form.append("bankAccountId", "{{ config.bankAccountId }}");
                                form.append("providerAcctId", providerAcctId);
                                form.append("accountId", accountId);

                                let settings = {
                                    "url": "/l/onboard/submit-verified-account-details",
                                    "method": "POST",
                                    "timeout": 0,
                                    "processData": false,
                                    "mimeType": "multipart/form-data",
                                    "contentType": false,
                                    "data": form
                                };
                                $.ajax(settings).done(function (response) {
                                    let decodedResponse = JSON.parse(response)
                                    if (decodedResponse.data.error === 'noFullAccountNumber') {
                                        window.location.href = '/l/onboard/noFullAccountNumber/{{ config.bankAccountId }}'
                                    } else if (decodedResponse.data.error === 'noRoutingNumber') {
                                        window.location.href = '/l/onboard/noRoutingNumber/{{ config.bankAccountId }}'
                                    } else {
                                        window.location.href = '/l/onboard/success'
                                    }
                                });
                            },
                            onEvent: function (data) {
                            }
                        },
                        'container-fastlink');
                },
                false);
        }(window));
    </script>
{% endblock %}
