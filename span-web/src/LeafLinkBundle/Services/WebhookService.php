<?php


namespace LeafLinkBundle\Services;


use Core<PERSON>undle\Entity\AchTransactions;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Util;
use Leaf<PERSON>inkBundle\Entity\AchB2BTransaction;
use Leaf<PERSON>inkBundle\Services\SlackService;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\UserConfig;
use CoreBundle\Entity\UserCard;
use SalexUserBundle\Entity\User;
use LeafLinkBundle\Entity\WebhookEvent;

class WebhookService
{
    const LL_EVENT_TYPE_ONBOARDING = "bank-onboarding";
    const LL_EVENT_TYPE_PAYMENT = "payment-event";
    const LL_EVENT_TYPE_EXCEPTION = "failure-notice";

    public static function onboardingWebhook($config, $uc, $message, $manual = false, $status = "linked"): void
    {

        SlackService::info('Update', [
            'host' => Util::host(),
            'isStage' => Util::isStage(),
            'Check:' => 1
        ]);
        // Check if a User Card was passed. If not (most likely due to an error) give dummy data to prevent a server error.
        if ($uc === NULL)
        {
            $institutionName = "none";
            $lastFour = "0000";
        } else {
            $institutionName = $uc->getBankName();
            $lastFour = substr(SSLEncryptionService::decrypt($uc->getAccountNumber()), -4);
        }

        $lastEvent = Util::em()->getRepository(WebhookEvent::class)
            ->createQueryBuilder('event')
            ->orderBy('event.eventDate', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // In case of null result, get new auth token. Otherwise get auth token from most recent event.

        if ($lastEvent === NULL || Util::isStage())
        {
            $authKey = self::getAuthToken();
            SlackService::info('New authKey has been retrieved.', [
                'Reason' => 'Null search response.'
            ]);
        } else {
            $authKey = $lastEvent->getAuthKey();
        }

        if (Util::isStage())
        {
            SlackService::info('Stage Authkey', [
                'Authkey' => $authKey
            ]);
        }

        // Create a new event for logging purposes and for saving the authkey for re-use.
        $webhookEvent = new WebhookEvent();
        $webhookEvent->setEventId(self::genUuid())
            ->setEventDate(new \DateTime('now'))
            ->setBankAccountId($config->getBankAccountId())
            ->setInstitutionName($institutionName)
            ->setAccountLast4($lastFour)
            ->setOnboardingStatus($status)
            ->setIsManuallyLinked($manual)
            ->setOnboardingStatusMessage($message)
            ->persist();

        if (Util::isLive())
        {
            $webhookEvent->setIsProd(true)
                ->persist();
        }

        // Create event data for webhook post body.
        $eventData = [
            'bank_account_id' => $webhookEvent->getBankAccountId(),
            'institution_name' => $webhookEvent->getInstitutionName(),
            'account_last_4' => $webhookEvent->getAccountLast4(),
            'onboarding_status' => $webhookEvent->getOnboardingStatus(),
            'is_manually_linked' => $webhookEvent->getIsManuallyLinked(),
            'onboarding_status_message' => $webhookEvent->getOnboardingStatusMessage(),
        ];

        // Create final webhook call with event details and event data.
        $postData = [
            'event_id' => $webhookEvent->getEventId(),
            'event_type' => self::LL_EVENT_TYPE_ONBOARDING,
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_data' => $eventData
        ];

        // Send webhook. If response is unauthorized, get new authkey and try again.
        $response = self::sendWebhook($postData, $authKey);
        if ($response === null)
        {
            $response = [
                'message' => "Unauthorized"
            ];
        }

        if (array_key_exists('message', $response))
        {
            if ($response['message'] === "Unauthorized" || $response['message'] === "Forbidden") {
                $authKey = self::getAuthToken();
                SlackService::info('New authKey has been retrieved.', [
                    'Reason' => 'Last key was unauthorized.',
                ]);

                $response = self::sendWebhook($postData, $authKey);
            }
        }

        $webhookEvent->setAuthKey($authKey)
            ->persist();

        // Send slack message detailing webhook and response.
        SlackService::check('Onboarding webhook sent to Leaflink:', [
            'event_id' => $webhookEvent->getEventId(),
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'bank_account_id' => $webhookEvent->getBankAccountId(),
            'institution_name' => $webhookEvent->getInstitutionName(),
            'account_last_4' => $webhookEvent->getAccountLast4(),
            'onboarding_status' => $webhookEvent->getOnboardingStatus(),
            'onboarding_status_message' => $webhookEvent->getIsManuallyLinked(),
            'is_production' => $webhookEvent->getIsProd(),
            'Sent' => $postData,
            'Response' => $response
        ]);
    }

    public static function achReturnWebhook(AchTransactions $ach, $message, $webhookEvent = null): void
    {
        // Get last used authkey. This will be tried before getting a new key.
        $lastEvent = Util::em()->getRepository(WebhookEvent::class)
            ->createQueryBuilder('event')
            ->orderBy('event.eventDate', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // In case of null result, get new auth token. Otherwise get auth token from most recent event.
        if ($lastEvent === NULL)
        {
            $authKey = self::getAuthToken();
            SlackService::info('New authKey has been retrieved.', [
                'Reason' => 'Null search response.'
            ]);
        } else {
            $authKey = $lastEvent->getAuthKey();
        }

        if ($webhookEvent === null)
        {
            // Create a new event for logging purposes and for saving the authkey for re-use.
            $webhookEvent = new WebhookEvent();
            $webhookEvent->setEventId(self::genUuid())
                ->setEventDate(new \DateTime('now'))
                ->setAchTransaction($ach->getTranId())
                ->setBankAccountId($ach->getBankAccountId())
                ->setOnboardingStatusMessage($message)
                ->persist();
        }

        // Add isProd flag to prod webhooks.
        if (Util::isLive())
        {
            $webhookEvent->setIsProd(true)
                ->persist();
        }

        // Create event data for webhook post body.
        $eventData = [
            'id' => $ach->getTranId(),
            'status' => $ach->getTranStatus(),
            'context' => [
                'message' => $message
            ],
        ];

        // Create final webhook call with event details and event data.
        $postData = [
            'event_id' => $webhookEvent->getEventId(),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_data' => $eventData
        ];

        // Send webhook. If response is unauthorized, get new authkey and try again.
        $response = self::sendWebhook($postData, $authKey);

        if (array_key_exists('message', $response))
        {
            if ($response['message'] === "Unauthorized" || $response['message'] === "Forbidden") {
                $authKey = self::getAuthToken();
                SlackService::info('New authKey has been retrieved.', [
                    'Reason' => 'Last key was unauthorized.',
                ]);

                $response = self::sendWebhook($postData, $authKey);
            }
        }

        $webhookEvent->setAuthKey($authKey)
            ->persist();

        // Send slack message detailing webhook and response.
        SlackService::check('Payment Update webhook sent to Leaflink (1)', [
            'method' => __METHOD__,
            'event_id' => $webhookEvent->getEventId(),
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'transaction_id' => $webhookEvent->getAchTransaction(),
            'bank_account_id' => $webhookEvent->getBankAccountId(),
            'is_production' => $webhookEvent->getIsProd(),
            'data' => $postData,
            'response' => $response
        ]);
    }

    public static function achB2BReturnWebhook(AchB2BTransaction $ach, $message, $webhookEvent = null): void
    {
        // Get last used authkey. This will be tried before getting a new key.
        $lastEvent = Util::em()->getRepository(WebhookEvent::class)
            ->createQueryBuilder('event')
            ->orderBy('event.eventDate', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // In case of null result, get new auth token. Otherwise get auth token from most recent event.
        if ($lastEvent === NULL)
        {
            $authKey = self::getAuthToken();
            SlackService::info('New authKey has been retrieved.', [
                'Reason' => 'Null search response.'
            ]);
        } else {
            $authKey = $lastEvent->getAuthKey();
        }

        if ($webhookEvent === null)
        {
            // Create a new event for logging purposes and for saving the authkey for re-use.
            $webhookEvent = new WebhookEvent();
            $webhookEvent->setEventId(self::genUuid())
                ->setEventDate(new \DateTime('now'))
                ->setAchTransaction($ach->getTranId())
                ->setBankAccountId($ach->getFromBankAccountId())
                ->setOnboardingStatusMessage($message)
                ->persist();
        }

        // Add isProd flag to prod webhooks.
        if (Util::isLive())
        {
            $webhookEvent->setIsProd(true)
                ->persist();
        }

        // Create event data for webhook post body.
        $eventData = [
            'id' => $ach->getTranId(),
            'status' => $ach->getTranStatus(),
            'context' => [
                'message' => $message
            ],
        ];

        // Create final webhook call with event details and event data.
        $postData = [
            'event_id' => $webhookEvent->getEventId(),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_data' => $eventData
        ];

        // Send webhook. If response is unauthorized, get new authkey and try again.
        $response = self::sendWebhook($postData, $authKey);

        if (array_key_exists('message', $response))
        {
            if ($response['message'] === "Unauthorized" || $response['message'] === "Forbidden") {
                $authKey = self::getAuthToken();
                SlackService::info('New authKey has been retrieved.', [
                    'Reason' => 'Last key was unauthorized.',
                ]);

                $response = self::sendWebhook($postData, $authKey);
            }
        }

        $webhookEvent->setAuthKey($authKey)
            ->persist();

        // Send slack message detailing webhook and response.
        SlackService::check('Payment Update webhook sent to Leaflink (2)', [
            'method' => __METHOD__,
            'event_id' => $webhookEvent->getEventId(),
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'transaction_id' => $webhookEvent->getAchTransaction(),
            'bank_account_id' => $webhookEvent->getBankAccountId(),
            'is_production' => $webhookEvent->getIsProd(),
            'data' => $postData,
            'response' => $response
        ]);
    }

    public static function achErrorWebhook(AchTransactions $ach, $fedCode, $message): void
    {
        // Get last used authkey. This will be tried before getting a new key.
        $lastEvent = Util::em()->getRepository(WebhookEvent::class)
            ->createQueryBuilder('event')
            ->orderBy('event.eventDate', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // In case of null result, get new auth token. Otherwise get auth token from most recent event.
        if ($lastEvent === NULL)
        {
            $authKey = self::getAuthToken();
            SlackService::info('New authKey has been retrieved.', [
                'Reason' => 'Null search response.'
            ]);
        } else {
            $authKey = $lastEvent->getAuthKey();
        }

        // Create a new event for logging purposes and for saving the authkey for re-use.
        $webhookEvent = new WebhookEvent();
        $webhookEvent->setEventId(self::genUuid())
            ->setEventDate(new \DateTime('now'))
            ->setAchTransaction($ach->getTranId())
            ->setBankAccountId($ach->getBankAccountId())
            ->setOnboardingStatusMessage($message)
            ->persist();

        if (Util::isLive())
        {
            $webhookEvent->setIsProd(true)
                ->persist();
        }

        // Create event data for webhook post body.
        $eventData = [
            'id' => $ach->getTranId(),
            'status' => $ach->getTranStatus(),
            'context' => [
                'error_code' => $fedCode,
                'error_message' => $message
            ],
        ];

        // Create final webhook call with event details and event data.
        $postData = [
            'event_id' => $webhookEvent->getEventId(),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_data' => $eventData
        ];

        // Send webhook. If response is unauthorized, get new authkey and try again.
        $response = self::sendWebhook($postData, $authKey);

        if (array_key_exists('message', $response))
        {
            if ($response['message'] === "Unauthorized" || $response['message'] === "Forbidden") {
                $authKey = self::getAuthToken();
                SlackService::info('New authKey has been retrieved.', [
                    'Reason' => 'Last key was unauthorized.',
                ]);

                $response = self::sendWebhook($postData, $authKey);
            }
        }

        $webhookEvent->setAuthKey($authKey)
            ->persist();

        // Send slack message detailing webhook and response.
        SlackService::check('Payment Update webhook sent to Leaflink (3)', [
            'method' => __METHOD__,
            'event_id' => $webhookEvent->getEventId(),
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'transaction_id' => $webhookEvent->getAchTransaction(),
            'bank_account_id' => $webhookEvent->getBankAccountId(),
            'is_production' => $webhookEvent->getIsProd(),
            'data' => $postData,
            'response' => $response
        ]);
    }

    public static function achExceptionWebhook($errorCode, $message, $batchId): void
    {
        // Get last used authkey. This will be tried before getting a new key.
        $lastEvent = Util::em()->getRepository(WebhookEvent::class)
            ->createQueryBuilder('event')
            ->orderBy('event.eventDate', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // In case of null result, get new auth token. Otherwise get auth token from most recent event.
        if ($lastEvent === NULL)
        {
            $authKey = self::getAuthToken();
            SlackService::info('New authKey has been retrieved.', [
                'Reason' => 'Null search response.'
            ]);
        } else {
            $authKey = $lastEvent->getAuthKey();
        }

        // Create a new event for logging purposes and for saving the authkey for re-use.
        $webhookEvent = new WebhookEvent();
        $webhookEvent->setEventId(self::genUuid())
            ->setEventDate(new \DateTime('now'))
            ->setOnboardingStatusMessage($message)
            ->persist();

        if (Util::isLive())
        {
            $webhookEvent->setIsProd(true)
                ->persist();
        }

        // Create event data for webhook post body.
        $eventData = [
            'context' => [
                'error_code' => $errorCode,
                'error_message' => $message,
                'batch_id' => $batchId
            ],
        ];

        // Create final webhook call with event details and event data.
        $postData = [
            'event_id' => $webhookEvent->getEventId(),
            'event_type' => self::LL_EVENT_TYPE_EXCEPTION,
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_data' => $eventData
        ];

        // Send webhook. If response is unauthorized, get new authkey and try again.
        $response = self::sendWebhook($postData, $authKey);

        if (array_key_exists('message', $response))
        {
            if ($response['message'] === "Unauthorized" || $response['message'] === "Forbidden") {
                $authKey = self::getAuthToken();
                SlackService::info('New authKey has been retrieved.', [
                    'Reason' => 'Last key was unauthorized.',
                ]);

                $response = self::sendWebhook($postData, $authKey);
            }
        }

        $webhookEvent->setAuthKey($authKey)
            ->persist();

        // Send slack message detailing webhook and response.
        SlackService::alert('An exception has terminated a scheduled process. LeafLink has been alerted.', [
            'event_id' => $webhookEvent->getEventId(),
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_type' => self::LL_EVENT_TYPE_EXCEPTION,
            'is_production' => $webhookEvent->getIsProd(),
            'data' => $postData,
            'response' => $response
        ]);
    }

    public static function achB2BErrorWebhook(AchB2BTransaction $ach, $fedCode, $message, $account = null): void
    {
        // Get last used authkey. This will be tried before getting a new key.
        $lastEvent = Util::em()->getRepository(WebhookEvent::class)
            ->createQueryBuilder('event')
            ->orderBy('event.eventDate', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // In case of null result, get new auth token. Otherwise get auth token from most recent event.
        if ($lastEvent === NULL)
        {
            $authKey = self::getAuthToken();
            SlackService::info('New authKey has been retrieved.', [
                'Reason' => 'Null search response.'
            ]);
        } else {
            $authKey = $lastEvent->getAuthKey();
        }

        // Create a new event for logging purposes and for saving the authkey for re-use.
        $webhookEvent = new WebhookEvent();
        $webhookEvent->setEventId(self::genUuid())
            ->setEventDate(new \DateTime('now'))
            ->setAchTransaction($ach->getTranId())
            ->setBankAccountId($ach->getFromBankAccountId())
            ->setOnboardingStatusMessage($message)
            ->persist();

        if (Util::isLive())
        {
            $webhookEvent->setIsProd(true)
                ->persist();
        }

        if ($account)
        {
            // create event data that specifies specific account
            $eventData = [
                'id' => $ach->getTranId(),
                'status' => $ach->getTranStatus(),
                'context' => [
                    'error_code' => $fedCode,
                    'error_message' => $message,
                    'error_account' => $account
                ],
            ];
        } else {
            // Create event data for webhook post body.
            $eventData = [
                'id' => $ach->getTranId(),
                'status' => $ach->getTranStatus(),
                'context' => [
                    'error_code' => $fedCode,
                    'error_message' => $message
                ],
            ];
        }

        // Create final webhook call with event details and event data.
        $postData = [
            'event_id' => $webhookEvent->getEventId(),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_data' => $eventData
        ];

        // Send webhook. If response is unauthorized, get new authkey and try again.
        $response = self::sendWebhook($postData, $authKey);

        if (array_key_exists('message', $response))
        {
            if ($response['message'] === "Unauthorized" || $response['message'] === "Forbidden") {
                $authKey = self::getAuthToken();
                SlackService::info('New authKey has been retrieved.', [
                    'Reason' => 'Last key was unauthorized.',
                ]);

                $response = self::sendWebhook($postData, $authKey);
            }
        }

        $webhookEvent->setAuthKey($authKey)
            ->persist();

        // Send slack message detailing webhook and response.
        SlackService::check('Payment Update webhook sent to Leaflink (4)', [
            'method' => __METHOD__,
            'event_id' => $webhookEvent->getEventId(),
            'event_date' => ($webhookEvent->getEventDate())->format('Y-m-d\TH:i:s\.\0\Z'),
            'event_type' => self::LL_EVENT_TYPE_PAYMENT,
            'transaction_id' => $webhookEvent->getAchTransaction(),
            'bank_account_id' => $webhookEvent->getBankAccountId(),
            'is_production' => $webhookEvent->getIsProd(),
            'data' => $postData,
            'response' => $response
        ]);
    }

    public static function sendWebhook($postData, $authKey)
    {
        // Send webhook with provided postdata and authkey.

        if (Util::isLive())
        {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://llf-payment-hub-prod.leaflink.io/api/v1/events/hook",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($postData),
                CURLOPT_HTTPHEADER => array(
                    "Authorization: Bearer " . $authKey,
                    "Content-Type: application/json"
                ),
            ));
        } elseif (Util::isStage()) {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://llf-payment-hub-staging-payments.leaflink.io/api/v1/events/hook",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($postData),
                CURLOPT_HTTPHEADER => array(
                    "Authorization: Bearer " . $authKey,
                    "Content-Type: application/json"
                ),
            ));
        } else {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://llf-payment-hub-dev.leaflink.io/api/v1/events/hook",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($postData),
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $authKey,
                "Content-Type: application/json"
            ),
        ));
    }

        $json = curl_exec($curl);
        $response = json_decode($json, true);
        curl_close($curl);

        if ($response === null)
        {
            $response = [
                'message' => "Unauthorized"
            ];
        }

        return $response;
    }

    public static function getAuthToken()
    {
        if (Util::isLive())
        {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://leaflink.auth0.com/oauth/token',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => 'client_id=y1GGB4i0sgGJiEJk2Ot5zmHRetCBwNXM&client_secret=UBsXe4atSugIARZ-NkedqfxdIkasICp6EgMGyrc6TQ61ec2BVza3C3_GBLw2p5yD&audience=https%3A%2F%2Fllf-payment-hub-prod.leaflink.io&grant_type=client_credentials',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/x-www-form-urlencoded',
                    'Cookie: did=s%3Av0%3A2b8b0170-43d4-11eb-81e5-7d0ba5ef5ab1.nzsQomuWqRpsngbnB5HW01HZ9ARtablh8VDPgmFHfn4; did_compat=s%3Av0%3A2b8b0170-43d4-11eb-81e5-7d0ba5ef5ab1.nzsQomuWqRpsngbnB5HW01HZ9ARtablh8VDPgmFHfn4; __cfduid=db63898e2aceda3a0fb7e269c12629cc81619452292'
                ),
            ));
        } elseif (Util::isStage()) {
            // Ping auth server for auth token.
            SlackService::info('Sending to stage endpoint.');
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://leaflink-non-production.us.auth0.com/oauth/token",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => "client_id=aQGjUt9VSga5DcSZElHLZD59oCC1HfD8&client_secret=bezcNEipXydv9g1EEnqg_ZFw6UKMl67U1D0nud2cH20JP6nje0VTcNJprCUV8Jrr&audience=https%3A//llf-payment-hub-staging-payments.leaflink.io&grant_type=client_credentials",
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/x-www-form-urlencoded",
                    "Cookie: __cfduid=d42025e165f2b0c34a3f2f3199a9abdf01608586410; did=s%3Av0%3A2b8b0170-43d4-11eb-81e5-7d0ba5ef5ab1.nzsQomuWqRpsngbnB5HW01HZ9ARtablh8VDPgmFHfn4; did_compat=s%3Av0%3A2b8b0170-43d4-11eb-81e5-7d0ba5ef5ab1.nzsQomuWqRpsngbnB5HW01HZ9ARtablh8VDPgmFHfn4"
                ),
            ));
        } else {
        // Ping auth server for auth token.
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://leaflink-non-production.us.auth0.com/oauth/token",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "client_id=LvPT7Cuco0d8c2pfccTl5s56QZmuAHq0&client_secret=lIqgx8jjh9fGM0BLg6CqpIGDbQAivQaMv8PzYRDRzHNKV-oJsx3AuCplaioGNVdk&audience=https%3A//llf-payment-hub-dev.leaflink.io&grant_type=client_credentials",
            CURLOPT_HTTPHEADER => array(
                "Content-Type: application/x-www-form-urlencoded",
                "Cookie: __cfduid=d42025e165f2b0c34a3f2f3199a9abdf01608586410; did=s%3Av0%3A2b8b0170-43d4-11eb-81e5-7d0ba5ef5ab1.nzsQomuWqRpsngbnB5HW01HZ9ARtablh8VDPgmFHfn4; did_compat=s%3Av0%3A2b8b0170-43d4-11eb-81e5-7d0ba5ef5ab1.nzsQomuWqRpsngbnB5HW01HZ9ARtablh8VDPgmFHfn4"
            ),
        ));
    }

        $json = curl_exec($curl);

        $response = json_decode($json, true);

        curl_close($curl);

        return $response["access_token"];
    }

    public static function genUuid()
    {
        // Create a unique UUID.
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }
}
