<?php

namespace Spendr<PERSON><PERSON>le\Controller\API\Plaid;

use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use OpenApi\Annotations as SWG;
use SpendrBundle\Controller\API\BaseController;
use SpendrBundle\Services\PlaidService;
use SpendrBundle\Services\UserCardService;
use SpendrBundle\Services\UserService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PlaidController extends BaseController
{
    /**
     * @Route("/api/spendr/plaid/check-balance", methods={"POST"}, name="api_spendr_plaid_check_balance")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="check balance",
     *   description="check balance",
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"bankCardHash"},
     *          properties={
     *              @SWG\Property(property="bankCardHash", description="bank card hash", type="string"),
     *              @SWG\Property(property="amount", description="load(deposit) amount", type="string"),
     *              @SWG\Property(property="clientTxnId", description="Unique ID to identify client transactions", type="string"),
     *              @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function checkBalance(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash();

        $meta = Util::meta($uc) ?? [];
        if (empty($meta['account_id']) || !$uc->getPlaidAccessToken()) {
            return new FailedResponse('Bank card not enrolled yet!');
        }
        // $needOptions = $meta && isset($meta['institution_id']) && $meta['institution_id'] === 'ins_128026';
        // $info = PlaidService::getBalanceInfo($uc->getPlaidAccessToken(), $meta['account_id'], $needOptions);
        $amount = $request->get('amount', 1);
        $info = PlaidService::getBalancePlusInfo(
            $uc->getPlaidAccessToken(),
            $meta['account_id'],
            Money::formatAmountToNumber($amount),
            // clientTxnId: $request->get('clientTxnId'),
            clientTxnId: null,
            env: $request->get('plaidEnv')
        );
        if (!$info) {
            return new FailedResponse('Empty balance info!');
        }
        if (isset($info->error_code)) {
            $msg = 'Failed to get balance info: ' . $info->error_code;
            if (isset($info->error_message)) {
                $msg .= ' (' . $info->error_message . ')';
            }
            return new FailedResponse($msg, (array)$info);
        }
        if (isset($info->reason_code)) {
            return new FailedResponse('Receive risk reason: ' . $info->reason_code, (array)$info);
        }
        if (!isset($info->balances)) {
            return new FailedResponse('Invalid balance info', (array)$info);
        }
        $available = $info->balances->available ?? $info->balances->current ?? 0;
        $currency = $info->balances->iso_currency_code ?? 'USD';
        $balance = Money::normalizeAmount($available, $currency);

        $info = (array)$info;
        unset($info['account_id'], $info['persistent_account_id']);

        return new SuccessResponse([
            'balance' => $balance,
            'currency' => $currency,
            'info' => $info,
        ]);
    }

    /**
     * @Route("/api/spendr/plaid/create-link-token", methods={"POST"}, name="api_spendr_plaid_create_link_token")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Create link token",
     *   description="Create link token",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(
     *          properties={
     *     @SWG\Property(property="bankCardHash", description="Card reference", type="string"),
     *     @SWG\Property(property="bankAccountId", description="Specify the bank account ID", type="string"),
     *     @SWG\Property(property="sameDay", description="Enable the `same_day_microdeposits_enabled` option or not", type="boolean", default=true),
     *     @SWG\Property(property="manual", description="Explicit manual link to omit the `balance_plus` product", type="boolean", default=false),
     *     @SWG\Property(property="webhook", description="The webhook URL to notify when it is verified", type="string"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function createLinkToken(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash(false);

        $sameDay = Util::isTrue($request->get('sameDay'));
        $env = $request->get('plaidEnv');

        $accountId = $request->get('bankAccountId', UserService::ensureBankAccountId($uc));
        $webhook = $request->get('webhook', '');
        $accessToken = $uc?->getPlaidAccessToken();
        $token = PlaidService::curlLinkToken(
            $accountId,
            $request->get('platform'),
            $accessToken,
            $webhook,
            $sameDay,
            $env,
            $request->get('redirect_uri'),
            $request->get('android_package_name'),
        );
        if (!$token || !isset($token->link_token)) {
            $message = 'Failed to create link token.';
            if (isset($token->error_code) && $token->error_code === PlaidService::ERROR_ITEM_NOT_FOUND) {
                $message = 'The bank account may have been access removed. Please remove it and re-link again.';
            }
            return new FailedResponse($message, (array)$token);
        }
        $token->env = PlaidService::getEnv($env);
        $token->product = PlaidService::getProduct($accessToken !== null, $sameDay, $env);
        $token->webhook = $webhook;

        return new SuccessResponse($token);
    }

    /**
     * @Route("/api/spendr/plaid/exchange-access-token", methods={"POST"}, name="api_spendr_plaid_exchange_access_token")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Exchange access token",
     *   description="Exchange access token",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"publicToken"},
     *          properties={
     *     @SWG\Property(property="bankCardHash", description="Card reference", type="string"),
     *     @SWG\Property(property="publicToken", description="public token", type="string"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function exchangeAccessToken(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash(false);

        $publicToken = $request->get('publicToken');
        $token = PlaidService::exchangeAccessToken($publicToken, $request->get('plaidEnv'));
        if (!$token) {
            return new FailedResponse('Failed to exchange access token.');
        }

        if ($uc) {
            $uc->setPlaidAccessToken($token->access_token);
            if ($request->get('bankAccountId')) {
                Util::updateMeta($uc, [
                    'plaidBankAccountId' => $request->get('bankAccountId'),
                ], false);
            }
            $uc->persist();
        }

        $result = Util::o2j($token);
        unset($result['access_token']);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/api/spendr/plaid/add-card", methods={"POST"}, name="api_spendr_plaid_add_card")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Add Plaid cards",
     *   description="Add Plaid cards",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"fromBankCardHash", "newBankCardHash", "accountId"},
     *          properties={
     *     @SWG\Property(property="fromBankCardHash", description="Card reference of the old card with the same access token", type="string"),
     *     @SWG\Property(property="newBankCardHash", description="Card reference", type="string"),
     *     @SWG\Property(property="accountId", description="The new account ID", type="string"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function addCard(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $fromUc = $this->validateUserCardByHash(field: 'fromBankCardHash');
        if ( ! $fromUc->getPlaidAccessToken()) {
            return new FailedResponse('The from card was not linked yet.');
        }
        $accountId = $request->get('accountId');
        if (Util::meta($fromUc, 'account_id') === $accountId) {
            return new FailedResponse('The new account ID was already linked to the from card.');
        }
        $newUc = $this->validateUserCardByHash(false, field: 'newBankCardHash');
        if ($newUc->getPlaidAccessToken()) {
            return new FailedResponse('The new card was already linked with another token.');
        }
        $newUc->setPlaidAccessToken($fromUc->getPlaidAccessToken());
        Util::updateMeta($newUc, [
            'plaidBankAccountId' => Util::meta($fromUc, 'plaidBankAccountId'),
            'account_id' => $accountId,
        ]);
        Log::info('Update user card meta', [
            'card' => $newUc->getHash(),
            'meta' => Util::meta($newUc)
        ]);
        $data = PlaidService::syncAccountInfo($newUc, $accountId, false, $request->get('plaidEnv'));
        if ($data instanceof FailedResponse) {
            return $data;
        }
        return new SuccessResponse($data);
    }

    /**
     * @Route("/api/spendr/plaid/account", methods={"POST"}, name="api_spendr_plaid_account")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Get account info",
     *   description="Sync and get account info",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"bankCardHash"},
     *          properties={
     *     @SWG\Property(property="bankCardHash", description="Card reference", type="string"),
     *     @SWG\Property(property="accountId", description="The account ID", type="string"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function getAccountInfo(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash();

        if (Util::isStaging() && $this->isInternalDebugMode()) {
            $data = PlaidService::getAccountInfoArray($uc, $request->get('plaidEnv'));
        } else {
            $accountId = $request->get('accountId', Util::meta($uc, 'account_id'));
            $data = PlaidService::syncAccountInfo(
                $uc,
                $accountId,
                false,
                $request->get('plaidEnv')
            );
            if ($data instanceof FailedResponse) {
                return $data;
            }
        }

        return new SuccessResponse($data);
    }

    /**
     * @Route("/api/spendr/plaid/meta", methods={"POST"}, name="api_spendr_plaid_meta")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Get account meta info",
     *   description="Get account meta info",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"bankCardHash"},
     *          properties={
     *     @SWG\Property(property="bankCardHash", description="Card reference", type="string"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function getMetaInfo(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash();

        $routing = $uc->getRoutingNumber();
        if ($routing) {
            $routing = SSLEncryptionService::decrypt($routing);
        }
        $data = [
            'non_deposited_routing' => UserCardService::isNonDepositedRouting($routing ?? ''),
        ];
        return new SuccessResponse($data);
    }

    /**
     * @Route("/api/spendr/plaid/send-decision-report", methods={"POST"}, name="api_spendr_plaid_send_decision_report")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Send decision report",
     *   description="Send decision report",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"clientTxnId", "initiated"},
     *          properties={
     *     @SWG\Property(property="clientTxnId", description="Client transaction Id", type="string"),
     *     @SWG\Property(property="initiated", description="Initiated or not", type="boolean"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function sendDecisionReport(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $data = PlaidService::sendDecisionReport(
            $request->get('clientTxnId'),
            Util::isTrue($request->get('initiated')),
            $request->get('plaidEnv')
        );
        return new SuccessResponse($data);
    }

    /**
     * @Route("/api/spendr/plaid/send-return-report", methods={"POST"}, name="api_spendr_plaid_send_return_report")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Send return report",
     *   description="Send return report",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"clientTxnId", "returnCode"},
     *          properties={
     *     @SWG\Property(property="clientTxnId", description="Client transaction Id", type="string"),
     *     @SWG\Property(property="returnCode", description="Return code", type="string"),
     *     @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function sendReturnReport(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $data = PlaidService::sendReturnReport(
            $request->get('clientTxnId'),
            $request->get('returnCode'),
            $request->get('plaidEnv')
        );
        return new SuccessResponse($data);
    }

    /**
     * @Route("/api/spendr/plaid/identity", methods={"POST"}, name="api_spendr_plaid_identity")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Identity",
     *   description="Get account identity",
     *   @SWG\RequestBody(required=true,
     *      @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *           schema=@SWG\Schema(required={"bankCardHash"},
     *           properties={
     *      @SWG\Property(property="bankCardHash", description="bank card hash", type="string"),
     *      @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function identity(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash();

        $data = PlaidService::getIdentityInformation($uc, null, $request->get('plaidEnv'));

        return new SuccessResponse($data);
    }

    /**
     * @Route("/api/spendr/plaid/institution", methods={"POST"}, name="api_spendr_plaid_institution")
     * @SWG\Post(
     *   tags={"Plaid"},
     *   summary="Institution",
     *   description="Get bankcard institution",
     *   @SWG\RequestBody(required=true,
     *      @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *           schema=@SWG\Schema(required={"bankCardHash"},
     *           properties={
     *      @SWG\Property(property="bankCardHash", description="bank card hash", type="string"),
     *      @SWG\Property(property="plaidEnv", description="Environment of plaid", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function institution(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $uc = $this->validateUserCardByHash();

        $data = PlaidService::getInstitution($uc, $request->get('plaidEnv'));

        return new SuccessResponse($data);
    }
}
