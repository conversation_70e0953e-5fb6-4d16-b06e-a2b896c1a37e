<?php

namespace SpendrBundle\Controller\API\ACH;

use App<PERSON><PERSON>le\Command\InstantBackgroundCommand;
use CoreBundle\Controller\Cron\Spendr\FFBAchServiceController;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use OpenApi\Annotations as SWG;
use SpendrBundle\Controller\API\BaseController;
use SpendrBundle\Services\ACH\BatchService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class BatchFileController extends BaseController
{
    public function validateBatchReference()
    {
        $reference = $this->request->get('reference');
        $achBatch = AchBatch::findBySpendrMerchantReference($reference);
        if (!$achBatch) {
            throw new FailedException('Batch record not found.');
        }
        return $achBatch;
    }

    /**
     * @Route("/api/spendr/ach/batch-all", methods={"POST"}, name="api_spendr_ach_batch_all")
     * @SWG\Post(
     *   tags={"ACH"},
     *   summary="Batch files",
     *   description="Batch files with different types",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"types", "webhook"},
     *          properties={
     *     @SWG\Property(property="types", description="the transaction JSON string to include in the batches", type="array"),
     *     @SWG\Property(property="webhook", description="the webhook URL to update the batch status", type="string"),
     *     @SWG\Property(property="mode", description="batch mode, such as precheck etc.", type="string"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function batchAllFiles(Request $request)
    {
        $this->validateParameters(__METHOD__);
        
        if (FFBAchServiceController::isAchBatchLocked()) {
            return new FailedResponse('The ACH batch is being executed. Please wait.');
        }

        $webhook = $request->get('webhook');
        if ( ! Util::isUrl($webhook)) {
            return new FailedResponse('Invalid webhook.');
        }

        FFBAchServiceController::addAchBatchLock();

        $types = $request->get('types');
        $mode = $request->get('mode');
        $isPrecheck = $mode === 'precheck';
        $errorMsg = '';
        foreach ($types as $type => $s) {
            if (!$isPrecheck) {
                $found = AchBatch::findBySpendrMerchantReference($s['reference']);
                if ($found) {
                    $errorMsg = 'Duplicated merchant reference!';
                    break;
                }
            }

            $types[$type]['reference'] = $s['reference'];
            $types[$type]['items'] = Util::s2j($s['items']);
        }
        if ($errorMsg) {
            FFBAchServiceController::clearAchBatchLock();
            return new FailedResponse($errorMsg);
        }
        $dataKey = 'ach_batch_parameters_' . Util::randTimeNumber();
        Data::setArray($dataKey, compact(
            'types', 'webhook', 'isPrecheck'
        ), 3600 * 6);

        InstantBackgroundCommand::add('span:spendr:api:ach:batch', [
            '--dataKey' => $dataKey,
        ], 1);

        return new SuccessResponse(null, 'The background job will start soon. Please wait and check the status later.');
    }

    /**
     * @Route("/api/spendr/ach/download-batch-file", methods={"POST"}, name="api_spendr_ach_download_batch_file")
     * @SWG\Post(
     *   tags={"ACH"},
     *   summary="download batch file",
     *   description="download batch file",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"reference"},
     *          properties={
     *     @SWG\Property(property="reference", description="The merchant batch reference", type="integer"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function downloadBatchFile(Request $request)
    {
        $this->validateParameters(__METHOD__);

        $reference = $request->get('reference');
        $achBatch = $this->validateBatchReference();

        if (!in_array(
            $achBatch->getBatchStatus(),
            haystack: [
                AchBatch::STATUS_SENT,
                AchBatch::STATUS_PROCESSING
            ]
        )) {
            return new FailedResponse('Invalid status!');
        }

        if (!$achBatch->getBatchFileString()) {
            return new FailedResponse('Batch file was not generated yet.');
        }

        if ($achBatch->getBatchStatus() === AchBatch::STATUS_PROCESSING) {
            $achBatch->setBatchStatus(AchBatch::STATUS_SENT)
                ->persist();
        }

        $guid = Util::guid();
        $key = 'spendr_ach_batch_download_' . $guid;
        Data::set($key, $reference, 120);
        $url = Util::host() . '/spendr/download/batch/' . $reference . '/' . $guid;

        return new SuccessResponse($url);
    }

    /**
     * @Route("/api/spendr/ach/sync-batch", methods={"POST"}, name="api_spendr_ach_sync_batch")
     * @SWG\Post(
     *   tags={"ACH"},
     *   summary="sync batch",
     *   description="sync batch",
     *   @SWG\RequestBody(required=true,
     *     @SWG\MediaType(mediaType="application/x-www-form-urlencoded",
     *          schema=@SWG\Schema(required={"reference"},
     *          properties={
     *      @SWG\Property(property="reference", description="The merchant batch reference", type="integer"),
     *   }))),
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function syncBatch(Request $request)
    {
        $this->validateParameters(__METHOD__);
        $batch = $this->validateBatchReference();

        return new SuccessResponse($batch->getSyncData());
    }

    /**
     * @Route("/api/spendr/ach/reset-failed-batch-file", methods={"POST"}, name="api_spendr_ach_reset_failed_batch_file")
     * @SWG\Post(
     *   tags={"ACH"},
     *   summary="Reset failed batch file",
     *   description="Reset the latest failed batch file",
     *   security={{"x-api-key":{}, "x-timezone":{}, "x-i18n":{}, "x-token":{}}},
     *   @SWG\Response(response=200, ref="#/components/schemas/Response"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     *
     * @return JsonResponse
     */
    public function resetFailedBatchFile(Request $request)
    {
        $this->validateParameters(__METHOD__);

        FFBAchServiceController::clearAchBatchLock();

        return new SuccessResponse();
    }
}
