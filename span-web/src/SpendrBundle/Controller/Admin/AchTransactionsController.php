<?php


namespace SpendrBundle\Controller\Admin;

use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use LeafLinkBundle\Services\NachaReturnService;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Services\ACH\FFBAchService;
use SpendrBundle\Services\ACH\SpendrACHService;
use SpendrBundle\Services\Bank\BankLedgerService;
use SpendrBundle\Services\SlackService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\UserCardTransaction;
use Doctrine\ORM\Query\Expr\Join;
use CoreBundle\Entity\Attachment;
use SpendrBundle\Services\ACH\ReturnService;

class AchTransactionsController extends BaseController
{
    use ExcelTrait;
    use DbTrait;

    private $timezone;
    private $pending = false;

    public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutGroup();
	}

	/**
	 * @Route("/admin/spendr/ach-transactions/list/{page}/{limit}", defaults={"page" = 1,"limit" = 10}, name="spendr_ach_payments")
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}

        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();

        $expr = Util::expr();
        $allQuery = $this->query($request)
            ->leftJoin('CoreBundle:UserCardTransaction', 'uct', Join::WITH, 'ach.tranId = uct.tranId')
            ->andWhere('ach.tranStatus = :statue')
            ->setParameter('statue', UserCardTransaction::STATUS_LL_SETTLED);
        $result['quick']['total'] =  $allQuery->select('count(uct)')
                                     ->getQuery()
                                     ->getSingleScalarResult();
        $result['quick']['debit'] =  $allQuery->andWhere($expr->eq('uct.tranCode',':type'))
                                     ->setParameter('type', 'debit')
                                     ->select('sum(uct.txnAmount)')
                                     ->getQuery()
                                     ->getSingleScalarResult();
        $result['quick']['credit'] = $allQuery->andWhere($expr->eq('uct.tranCode',':type'))
                                     ->setParameter('type', 'credit')
                                     ->select('sum(uct.txnAmount)')
                                     ->getQuery()
                                     ->getSingleScalarResult();

        $result['quick']['revenue'] = -$result['quick']['credit'] + $result['quick']['debit'];
        return new SuccessResponse($result);
    }

	/**
	 * @Route("/admin/spendr/ach-returns/list/{page}/{limit}", defaults={"page" = 1,"limit" = 10}, name="spendr_ach_returns_payments")
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function searchReturns(Request $request, $page = 1, $limit = 10)
    {
    	if ($this->user->inTeams([Role::ROLE_SPENDR_CUSTOMER_SUPPORT])) {
    		return new DeniedResponse();
		}
        $request->query->set('tranStatus', UserCardTransaction::STATUS_LL_RETURNED);
        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();

        $result['quick']['total'] = $this->query($request)->select('count(ach)')
            ->getQuery()
            ->getSingleScalarResult();
        $result['quick']['totalAmount'] = $this->query($request)->select('sum(ucl.initialAmount)')
            ->getQuery()
            ->getSingleScalarResult();

        return new SuccessResponse($result);
    }

    public function query(Request $request)
    {
		$expr = Util::expr();
        $query = Util::em()->getRepository(AchTransactions::class)
            ->createQueryBuilder('ach')
            ->andWhere($expr->eq('ach.bankId', ':bankId'))
            ->setParameter('bankId', Util::getParameter('spendr_ffb_id'));

        if ($this->pending) {
          	$query->andWhere($expr->in('ach.tranStatus', ':status'))
                ->setParameter('status', [
                	UserCardTransaction::STATUS_LL_PROCESSING,
					UserCardTransaction::STATUS_LL_RECEIVED,
					UserCardTransaction::STATUS_LL_QUESTIONABLE,
					UserCardTransaction::STATUS_LL_RETURNED
				]);
        }
        $notFlag = false;
        $tranStatus = null;
        $not = $request->get('__not');
        if ($not) {
          	if (is_string($not)) {
            	$not = (array)json_decode($not, TRUE);
          	}
          	if (isset($not['tranStatus'])) {
            	$notFlag = true;
            	$tranStatus = $not['tranStatus'];
          	}
        } else {
          	$tranStatus = $request->get('tranStatus');
        }

        if ($tranStatus) {
			if ($tranStatus === UserCardTransaction::STATUS_LL_PROCESSING) {
				$option = $notFlag ? 'notIn' : 'in';
				$query->andWhere($expr->$option('ach.tranStatus', ':filterStatus'))
					->setParameter('filterStatus', [UserCardTransaction::STATUS_LL_PROCESSING, UserCardTransaction::STATUS_LL_RECEIVED ]);
			} else if ($tranStatus === UserCardTransaction::STATUS_LL_RETURNED) {
				$option = $notFlag ? 'neq' : 'eq';
                $statusOption = $notFlag ? 'isNull' : 'isNotNull';
				$query->leftJoin(\CoreBundle\Entity\UserCardLoad::class, 'ucl', Join::WITH, 'ach.tranId = ucl.transactionNo')
					->andWhere('ucl.loadStatus = :loadStatus')
					->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_ERROR)
					->andWhere($expr->like('ucl.meta', ':returnedByBank'))
					->setParameter('returnedByBank', '%"returnedByBank":true%')
					->andWhere($expr->like('ucl.meta', ':returnedCode'))
					->setParameter('returnedCode', '%"returnedCode":"R%')
                    ->andWhere($expr->orX(
                        $expr->$option('ach.tranStatus', ':filterStatus'),
                        $expr->$statusOption('ach.returnDate')
                    ))
					->setParameter('filterStatus', $tranStatus);
			} else {
			  	$option = $notFlag ? 'neq' : 'eq';
			  	$query->andWhere($expr->$option('ach.tranStatus', ':filterStatus'))
				  	->setParameter('filterStatus', $tranStatus);
			}
        }

        if (
            Util::isLive() && 
            !$this->user->isMasterAdmin()
        ) {
            $query->leftJoin('CoreBundle:AchBatch', 'ab', Join::WITH, 'ach.batchId = ab.id')
                ->andWhere('ab.merchantReference is null');
        }

        return $this->queryByDateRange($query, 'ach.createdAt', $request);
    }


    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'ach', 'count(distinct ach)');
        $params->distinct = true;
        $params->orderBy = [
            'ach.createdAt' => 'desc',
        ];
        $params->searchFields = [
          // 'c.companyName',
          'ach.bankAccountId',
          'ach.batchId'
        ];
        return $params;
    }

    /**
     * @param AchTransactions $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $load = UserCardLoad::findByTransactionNo($entity->getTranId());
        $user = User::findByBankAccountId($entity->getBankAccountId());
        return [
			'Bank Account ID' => $entity->getBankAccountId(),
			'Bank ID' => $entity->getBankAccountId(),
			'Txn ID' => $entity->getTranId(),
			'Company Name'  =>  $user ? ($user->getConfig() ? $user->getConfig()->getCompanyName() : '') : '',
			'ACH Batch ID' => $entity->getBatchId(),
			'Created Date' => Util::formatDateTime(
				$entity->getCreatedAt(),
				Util::DATE_TIME_FORMAT,
				$this->tz
			),
			'Send Date' => Util::formatDateTime(
				$entity->getCreatedAt(),
			   Util::DATE_TIME_FORMAT,
			   	$this->tz
			),
			'Trx Type' => $entity->getTranCode(),
			'Payment Amount' => Money::format($entity->getAmount() ? $entity->getAmount() : 0, 'USD', false),
			'Return Date' => $entity->getReturnDate() ? Util::formatDateTime(
			   	$entity->getReturnDate(),
			   	Util::DATE_TIME_FORMAT,
			   	$this->tz
			) : '',
           	'ID' => $entity->getId(),
           	'Status' => $entity->getTranStatus(),
			'Load ID' => $load ? $load->getId() : null
        ];
    }

    protected function validateUser(User $user = null)
    {
        if (!$user) {
            throw PortalException::temp('Unknown user!');
        }
    }



    /**
     * @Route("/admin/spendr/ach-transactions/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        return $this->commonExport($request, 'Spendr ACH Transactions', [
          'Bank ID' => 20,
          'Txn ID' => 20,
          'Company Name'  => 20,
          'ACH Batch ID' => 20,
          'Created Date' => 20,
          'Send Date' => 20,
          'Trx Type' => 20,
          'Voice Total' => 20,
          'Return Date' => 20,
          'Invoice ID' => 20,
          'Status' => 20
        ]);
    }

    /**
     * @Route("/admin/spendr/ach-returns/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function exportReturn(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}

        $request->query->set('tranStatus', UserCardTransaction::STATUS_LL_RETURNED);

        return $this->commonExport($request, 'Spendr ACH Transactions', [
            'Bank ID' => 20,
            'Txn ID' => 20,
            'Company Name'  => 20,
            'ACH Batch ID' => 20,
            'Created Date' => 20,
            'Send Date' => 20,
            'Trx Type' => 20,
            'Voice Total' => 20,
            'Return Date' => 20,
            'Invoice ID' => 20,
            'Status' => 20
        ]);
    }

    /**
     * @Route("/admin/spendr/ach-returns/upload", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function importAction(Request $request)
    {
		$this->authSuperOrSpendrEmployeeCompliance();

        $aid = $request->get('attachment');
        return ReturnService::import($aid);
    }
}
