<?php

namespace SpendrBundle\Controller\Admin;

use CoreBundle\Controller\ListControllerTrait;
use CoreBundle\Entity\Role;
use SpendrBundle\SpendrBundle;

class BaseController extends \SpendrBundle\Controller\BaseController
{
	use ListControllerTrait;

	/**
	 * BaseController constructor.
	 */
	public function __construct()
	{
		parent::__construct();

        $this->authSuperOrOtherAdmins();
	}

	public function accessibleRoles()
	{
		return array_merge(
			[
				Role::ROLE_SPENDR_BANK
				// Role::ROLE_SPENDR_GROUP_ADMIN
			],
			SpendrBundle::getSpendrAdminRoles()
		);
	}

	public function authSuperOrOtherAdmins()
	{
		return $this->authSuperAdminOrAdmins($this->accessibleRoles());
	}

	public function authAdminsWithoutBank()
	{
		return $this->authSuperAdminOrAdmins(
			// array_merge(
			// 	[
			// 		Role::ROLE_SPENDR_GROUP_ADMIN
			// 	],
				SpendrBundle::getSpendrAdminRoles()
			// )
		);
	}

	public function authAdminsWithoutGroup()
	{
		return $this->authSuperAdminOrAdmins(
			array_merge(
				[
					Role::ROLE_SPENDR_BANK
				],
				SpendrBundle::getSpendrAdminRoles()
			)
		);
	}

	public function authAdminsWithoutBankAndGroup()
	{
		return $this->authSuperAdminOrAdmins(
			SpendrBundle::getSpendrAdminRoles()
		);
	}

	public function authAdminsWithout($withoutRoles)
	{
		$roles = $this->accessibleRoles();
		foreach($withoutRoles as $r) {
			if (in_array($r, $roles)) {
				$keys = array_keys($roles, $r);
				if ($keys && $keys[0]) {
					unset($roles[$keys[0]]);
				}
			}
		}
		return $this->authSuperAdminOrAdmins($roles);
	}

	public function authSuperOrSpendrEmployeeCompliance()
	{
		return $this->authSuperAdminOrAdmins(
			array_merge(
				[
					Role::ROLE_SPENDR_PROGRAM_OWNER,
				],
				SpendrBundle::getSpendrEmployeeComplianceRoles()
			)
		);
	}

	public function isViewOnlyRole()
	{
		return $this->user->inTeams(SpendrBundle::getViewOnlyRoles()) ? true : false;
	}
}
