<?php


namespace SpendrBundle\Controller\Admin;


use Core<PERSON><PERSON>le\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\ConfigLog;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SpendrBundle\Entity\AppVersion;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\PlaidService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class SettingController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authSuperAdminOrAdmins(
            [
                Role::ROLE_SPENDR_PROGRAM_OWNER,
                Role::ROLE_SPENDR_COMPLIANCE,
                Role::ROLE_SPENDR_ACCOUNTANT
            ]
        );
    }

    /**
     * @Route("/admin/spendr/setting", methods={"GET"})
     * @return SuccessResponse
     */
    public function getSettings(Request $request)
    {
        return new SuccessResponse($this->getSettingData($request->get('type')));
    }

    protected function getSettingData($type = null)
    {
        $data = [];
        if (!$type || $type === 'Plaid') {
            $data['Plaid Env'] = PlaidService::getEnv();
//            $data['Plaid Ips'] = Util::em()->getRepository(Config::class)
//                ->getArray(Config::CONFIG_PLAID_SPENDR_IP_WHITELIST);
            $data['Manual Instant'] = Money::formatAmountToNumber(Config::get(Config::CONFIG_SPENDR_MANUAL_CARD_INSTANT_AMOUNT, UserCard::CONSUMER_MANUAL_LINK_INSTANT_LIMIT));
            $data['No Deposit Load Status'] = Util::em()->getRepository(Config::class)
                ->getValue(Config::CONFIG_SPENDR_NO_DEPOSIT_LOAD_STATUS, ConsumerService::LOAD_STATUS_INSTANT);
            $data['No Stale Return Load Status'] = Util::em()->getRepository(Config::class)
                ->getValue(Config::CONFIG_SPENDR_NO_STALE_RETURN_LOAD_STATUS, ConsumerService::LOAD_STATUS_INSTANT);
            $data['Auth Instant'] = Money::formatAmountToNumber(Config::get(Config::CONFIG_SPENDR_AUTH_CARD_INSTANT_AMOUNT, UserCard::CONSUMER_AUTH_INSTANT_LIMIT));
        }
        if (!$type || $type === 'App Versions') {
            $data['App Versions'] = [];
            $versions = Util::em()->getRepository(AppVersion::class)
                ->findBy([
                    'cardProgram' => CardProgram::spendr()
                ]);
            /** @var AppVersion $version */
            foreach ($versions as $version) {
                $data['App Versions'][] = $version->toApiArray();
            }
        }
        if (!$type || $type === 'Fuzzy Match') {
            $data['Fuzzy Match'] = Util::em()->getRepository(Config::class)
                ->getArray(Config::CONFIG_SPENDR_FUZZY_MATCH, [
                    'score' => 0.5,
                    'lastNameMinTreshold' => 0.6
                ]);
        }
        if (!$type || $type === 'Username Blacklist') {
            $data['Username Blacklist'] = Util::em()->getRepository(Config::class)
                ->getArray(Config::CONFIG_SPENDR_USERNAME_BLACKLIST);
        }

        return $data;
    }

    /**
     * @Route("/admin/spendr/update-setting", methods={"POST"})
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function changeSettings(Request $request)
    {
        if ($this->user->inTeams([Role::ROLE_SPENDR_ACCOUNTANT])) {
            return new DeniedResponse();
        }
        $setting = $request->get('type');
        if (Util::isStaging() && $setting === 'Plaid Env' && $request->get('plaid_env')) {
            Data::set(
                PlaidService::CACHE_KEY,
                $request->get('plaid_env')
            );
            $setting = 'Plaid';
        } elseif ($setting === 'Manual Instant') {
            if ($request->get('Manual Instant') < 0 || $request->get('Manual Instant') > 1000) {
                return new FailedResponse('The manual card instant amount should in $0 ~ $1000');
            }
            /** @var Config $config */
            $config = Util::em()->getRepository(Config::class)
                ->findOneBy([
                    'name' => Config::CONFIG_SPENDR_MANUAL_CARD_INSTANT_AMOUNT
                ]);
            $val = UserCard::CONSUMER_MANUAL_LINK_INSTANT_LIMIT;
            if (!$config) {
                $config = new Config();
                $config->setName(Config::CONFIG_SPENDR_MANUAL_CARD_INSTANT_AMOUNT);
            } else {
                $val = $config->getValue();
            }
            $newVal = Money::normalizeAmount($request->get('Manual Instant'), 'USD');
            $config->setValue($newVal)
                ->persist();
            ConfigLog::store($config, $val, $newVal);
        } elseif (in_array($setting, [
            'No Deposit Load Status',
            'No Stale Return Load Status'
            ]) && $request->get('load_status')) {
            if (!in_array($request->get('load_status'), [
                ConsumerService::LOAD_STATUS_INSTANT,
                ConsumerService::LOAD_STATUS_PREFUND
            ])) {
                return new FailedResponse('Invalid load status');
            }
            $name = Config::CONFIG_SPENDR_NO_DEPOSIT_LOAD_STATUS;
            $config = null;
            if ($setting === 'No Stale Return Load Status') {
                /** @var Config $config */
                $config = Util::em()->getRepository(Config::class)
                    ->findOneBy([
                        'name' => $name
                    ]);
            }
            $val = null;
            if (!$config) {
                $config = new Config();
                $config->setName($name);
            } else {
                $val = $config->getValue();
            }
            $newVal = $request->get('load_status');
            $config->setValue($newVal)
                ->persist();
            ConfigLog::store($config, $val, $newVal);
        }  elseif ($setting === 'Auth Instant') {
            if ($request->get('Auth Instant') < 0 || $request->get('Auth Instant') > 1000) {
                return new FailedResponse('The manual card instant amount should in $0 ~ $1000');
            }
            /** @var Config $config */
            $config = Util::em()->getRepository(Config::class)
                ->findOneBy([
                    'name' => Config::CONFIG_SPENDR_AUTH_CARD_INSTANT_AMOUNT
                ]);
            $val = UserCard::CONSUMER_AUTH_INSTANT_LIMIT;
            if (!$config) {
                $config = new Config();
                $config->setName(Config::CONFIG_SPENDR_AUTH_CARD_INSTANT_AMOUNT);
            } else {
                $val = $config->getValue();
            }
            $newVal = Money::normalizeAmount($request->get('Auth Instant'), 'USD');
            $config->setValue($newVal)
                ->persist();
            ConfigLog::store($config, $val, $newVal);
        } elseif ($setting === 'App Versions' && $request->get('versions')) {
            foreach ($request->get('versions') as $item) {
                /** @var AppVersion $config */
                $version = Util::em()->getRepository(AppVersion::class)
                    ->find($item['id']);
                if (!$version || $version->getCardProgram() !== CardProgram::spendr()) {
                    return new FailedResponse('Invalid setting parameters.');
                }
                if (count(explode('.', $item['version'])) < 3) {
                    return new FailedResponse('Invalid version format(#.#.#, # should be a number).');
                }
                if ($item['build'] < $version->getBuild()) {
                    return new FailedResponse('Invalid build, the build should be higher than the current build.');
                }
                $version->setVersion($item['version'])
                    ->setBuild($item['build'])
                    ->setDescription($item['description'])
                    ->setUrl($item['url'])
                    ->persist();
            }
        } elseif ($setting === 'Fuzzy Match') {
            if (
                !isset($request->get('config')['score'])
                || $request->get('config')['score'] < 0.5
                || $request->get('config')['score'] > 1
            ) {
                return new FailedResponse('Invalid fuzzy match score');
            }
            if (
                !isset($request->get('config')['lastNameMinTreshold'])
                || $request->get('config')['lastNameMinTreshold'] < 0.5
                || $request->get('config')['lastNameMinTreshold'] > 1
            ) {
                return new FailedResponse('Invalid last name min treshold');
            }
            /** @var Config $config */
            $config = Util::em()->getRepository(Config::class)
                ->findOneBy([
                    'name' => Config::CONFIG_SPENDR_FUZZY_MATCH
                ]);
            if (!$config) {
                return new FailedResponse('Invalid setting parameters.');
            }
            $oldData = $config->getValue();
            $config->setValue(Util::j2s($request->get('config')))
                ->persist();
            ConfigLog::store($config, $oldData, Util::j2s($request->get('config')));
        } elseif ($setting === 'Username Blacklist') {
            /** @var Config $config */
            $config = Util::em()->getRepository(Config::class)
                ->findOneBy([
                    'name' => Config::CONFIG_SPENDR_USERNAME_BLACKLIST
                ]);
            if (!$config) {
                $config = new Config();
                $config->setName(Config::CONFIG_SPENDR_USERNAME_BLACKLIST);
            }
            $oldData = $config->getValue();
            $config->setValue(Util::j2s($request->get('config')))
                ->persist();
            ConfigLog::store($config, $oldData, Util::j2s($request->get('config')));
        } else {
            return new FailedResponse('Invalid setting type.');
        }

        return new SuccessResponse($this->getSettingData($setting));
    }
}
