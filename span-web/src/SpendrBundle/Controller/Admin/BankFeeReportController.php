<?php

namespace SpendrBundle\Controller\Admin;

use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SpendrBundle\Services\FeeService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class BankFeeReportController extends BaseController
{
	public function __construct()
	{
		parent::__construct();
		$this->authAdminsWithoutBankAndGroup();
	}

	/**
	 * @Route("/admin/spendr/fee-report/bank/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function search(Request $request, $page = 1, $limit = 10)
	{
		$resp = $this->traitSearch($request, [], $page, $limit);
		$result = $resp->getResult();
		$query = $this->query($request);
		$total = (clone $query)->select('count(distinct ufh)')
			->getQuery()
			->getSingleScalarResult();
		$totalAmount = (clone $query)->select('sum(ufh.amount)')
			->getQuery()
			->getSingleScalarResult();

		$start = $request->get('start') ? Carbon::parse($request->get('start')) : null;
		$end = $request->get('end') ? Carbon::parse($request->get('end')) : null;

		$result['quick'] = [
			'total' => (int)$total,
			'totalAmount' => $totalAmount,
			'totalMonthlyAccountFee' => FeeService::getBankMonthlyAccountFee($start, $end),
			'totalAchReturnFee' => FeeService::getBankReturnedLoadFee($start, $end),
			'totalAchLoadFee' => FeeService::getBankLoadFee($start, $end),
			'totalWireFee' => FeeService::getBankWireFee($start, $end),
			'totalAchSetupFee' => FeeService::getBankAchSetupFee($start, $end),
		];

		return new SuccessResponse($result);
	}

	protected function query(Request $request)
	{
		$query = FeeService::getBankOrTernFeesFromUFHQuery(FeeService::bankFeeNames());
		return $this->queryByDateRange($query, 'ufh.time', $request);
	}

	protected function queryListParams(QueryBuilder $query, Request $request)
	{
		$params = new QueryListParams($query, $request, 'ufh', 'count(distinct ufh)');
		$params->distinct = true;
		$params->orderBy = [
			'ufh.id' => 'desc',
		];
		$params->searchFields = [
			'ufh.id',
			'ufh.feeName'
		];
		return $params;
	}

	/**
	 * @param UserFeeHistory $entity
	 *
	 * @return array
	 */
	protected function getRowData($entity)
	{
		return [
			'Transaction' => $entity->getFeeName(),
			'Date & Time' => Util::formatDateTime($entity->getTime(), Util::DATE_TIME_FORMAT, $this->tz),
			'Amount' => Money::format($entity->getAmount(), 'USD', false),
			'Transaction ID' => $entity->getEntityId(),
		];
	}
}
