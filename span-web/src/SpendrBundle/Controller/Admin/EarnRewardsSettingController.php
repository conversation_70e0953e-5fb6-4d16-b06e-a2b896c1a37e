<?php


namespace SpendrBundle\Controller\Admin;


use Carbon\Carbon;
use CoreBundle\Entity\Attachment;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Excel;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\RewardLocation;
use SpendrBundle\Entity\RewardTarget;
use SpendrBundle\Entity\SpendrReward;
use SpendrBundle\Services\LocationService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use function Functional\pluck;

class EarnRewardsSettingController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authSuperAdminOrAdmins(
            [
                Role::ROLE_SPENDR_PROGRAM_OWNER,
                Role::ROLE_SPENDR_COMPLIANCE,
                Role::ROLE_SPENDR_ACCOUNTANT
            ]
        );
    }

    /**
     * @Route("/admin/spendr/earn-rewards/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int $page
     * @param int $limit
     *
     * @return SuccessResponse
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct r)')
            ->getQuery()
            ->getSingleScalarResult();

        $result['quick'] = [
            'total'  => (int)$total
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        return $this->em->getRepository(SpendrReward::class)
            ->createQueryBuilder('r')
            ->where('r.status = :status')
            ->setParameter('status', SpendrReward::STATUS_ACTIVE)
            ->orderBy('r.type')
            ->addOrderBy('r.dateType')
            ->addOrderBy('r.spendAmount');
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'r', 'count(distinct r)');
        $params->distinct = true;
        $params->searchFields = [
            'r.id',
            'r.title',
        ];
        return $params;
    }

    /**
     * @param SpendrReward $entity
     * @return array
     */
    protected function getRowData($entity)
    {
        return $entity->toApiArray();
    }

    /**
     * @Route("/admin/spendr/earn-rewards/form-options")
     * @return SuccessResponse
     */
    public function formOptions()
    {
        $locations = LocationService::getAllLocations($this->user);
        $data = [];
        foreach ($locations as $location) {
            $data[] = [
                'label' => $location->getName(),
                'value' => $location->getId(),
            ];
        }
        return new SuccessResponse([
            'types' => [
                [ 'label' => 'Bank Link Reward', 'value' => 'Bank Link' ],
                [ 'label' => 'Milestone Reward', 'value' => 'First $100 Spend' ],
                [ 'label' => 'Spend Reward', 'value' => 'Spend Reward' ]
            ],
            'reward_formats' => [
                [ 'label' => 'Dollar', 'value' => 'dollar' ],
                [ 'label' => 'Percent', 'value' => 'percent' ]
            ],
            'spend_types' => [
                [ 'label' => 'Total Spend Over', 'value' => 'total_spend' ],
                [ 'label' => '# Of Spend with Min Amount', 'value' => 'spend_count_with_min_amount' ]
            ],
            'date_types' => [
                [ 'label' => 'Per Month', 'value' => 'per_month' ],
                [ 'label' => 'Date Range', 'value' => 'date_range' ]
            ],
            'locations' => $data
        ]);
    }

    /**
     * @Route("/admin/spendr/earn-rewards/{reward}/detail")
     * @param SpendrReward $reward
     *
     * @return SuccessResponse
     * @throws PortalException
     */
    public function detailAction(SpendrReward $reward)
    {
        if (!$reward) {
            throw PortalException::temp('Unknown Earn Reward ID!');
        }
        $data = $reward->toApiArray();
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/spendr/earn-rewards/save", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|FailedResponse
     */
    public function saveAction(Request $request)
    {
        if ($this->user->inTeams([Role::ROLE_SPENDR_ACCOUNTANT])) {
            return new DeniedResponse();
        }
        if (!in_array($request->get('Type'), SpendrReward::TYPES)) {
            return new FailedResponse('Invalid earn reward type');
        }
        // reward format validate
        if (!in_array($request->get('Reward Format'), SpendrReward::AMAOUNT_TYPES)) {
            return new FailedResponse('Invalid reward format');
        } elseif (($request->get('Amount') <= 0 || $request->get('Amount') > 1000)
            && $request->get('Reward Format') === SpendrReward::AMOUNT_TYPE_AMOUNT
        ) {
            return new FailedResponse('Invalid reward amount');
        } elseif ((
            $request->get('Percentage') <= 0 ||
            $request->get('Percentage') > 100 ||
            $request->get('Type') !== UserCardLoad::EARN_TYPE_SPEND_REWARD
            ) && $request->get('Reward Format') === SpendrReward::AMOUNT_TYPE_PERCENT) {
            return new FailedResponse('Invalid reward percentage(only available for spend reward)');
        }
        // spend type validate
        if ($request->get('Type') !== UserCardLoad::EARN_TYPE_BANK_LINK) {
            if (!in_array($request->get('Spend Type'), SpendrReward::SPEND_TYPES)) {
                return new FailedResponse('Invalid spend type');
            } elseif (
                $request->get('Spend Amount') <= 0 ||
                (
                    $request->get('Spend Count') <= 0 &&
                    $request->get('Spend Type') === SpendrReward::SPEND_TYPE_NUM_WITH_MIN_AMOUNT
                )
            ) {
                return new FailedResponse('Invalid spend amount or count');
            }
        }
        // count limit validate
        if (
            $request->get('Count Limit') <= 0 &&
            $request->get('Type') === UserCardLoad::EARN_TYPE_BANK_LINK
        ) {
            return new FailedResponse('Invalid count limit');
        }
        $dateType = $request->get('Date Type');
        $begin = $request->get('Start Date') ? Util::toUTC(Util::timeLocal($request->get('Start Date'))) : null;
        $end = $request->get('End Date') ? Util::toUTC(Util::timeLocal($request->get('End Date'))) : null;
        $locationIds = [];
        if ($request->get('Type') === UserCardLoad::EARN_TYPE_SPEND_REWARD && $request->get('Location IDs')) {
            $locationIds = $request->get('Location IDs');
        }
        if ($request->get('Type') === UserCardLoad::EARN_TYPE_SPEND_REWARD && $dateType) {
            if (!in_array($dateType, SpendrReward::DATE_TYPES)) {
                return new FailedResponse('Invalid date type');
            }
            if ($dateType === SpendrReward::DATE_TYPE_DATE_RANGE) {
                if ($begin || $end) {
                    if (!$begin->isBefore($end)) {
                        return new FailedResponse('Start Date Should be before End Date');
                    }
                    if (Carbon::now()->isAfter($end)) {
                        return new FailedResponse('End Date should be later than now');
                    }
                }
                if (!$begin || !$end) {
                    return new FailedResponse('Invalid date range');
                }
            }
        }
        if ($request->get('Type') === UserCardLoad::EARN_TYPE_BANK_LINK) {
            $existed = SpendrReward::findOneByType($request->get('Type'));
            if ($existed && $existed->getId() !== $request->get('ID')) {
                return new FailedResponse('This type of earn reward is single and it has exists already');
            }
        } else {
            $q = Util::em()->getRepository(SpendrReward::class)
                ->createQueryBuilder('r')
                ->leftJoin('r.locations', 'l')
                ->where('r.status = :status')
                ->setParameter('status', SpendrReward::STATUS_ACTIVE)
                ->andWhere('r.type = :type')
                ->setParameter('type', $request->get('Type'))
                ->andWhere('r.spendAmount = :spendAmount')
                ->setParameter('spendAmount', Money::normalizeAmount($request->get('Spend Amount'), 'USD'));
            if ($request->get('Spend Type') === SpendrReward::SPEND_TYPE_NUM_WITH_MIN_AMOUNT) {
                $q->andWhere('r.spendCount = :spendCount')
                    ->setParameter('spendCount', $request->get('Spend Count'));
            }
            if ($request->get('Type') === UserCardLoad::EARN_TYPE_SPEND_REWARD) {
                $q->andWhere('r.dateType = :dateType')
                    ->setParameter('dateType', $dateType);
                if ($dateType === SpendrReward::DATE_TYPE_DATE_RANGE) {
                    $q->andWhere(Util::expr()->orX(
                        Util::expr()->between('r.startAt', ':start', ':end'),
                        Util::expr()->between('r.endAt', ':start', ':end'),
                        Util::expr()->andX(
                            Util::expr()->lte('r.startAt', ':start'),
                            Util::expr()->gte('r.endAt', ':end')
                        )
                    ))
                    ->setParameter('start', $begin)
                    ->setParameter('end', $end);
                }
            }
            if ($request->get('ID')) {
                $q->andWhere('r.id <> :id')
                    ->setParameter('id', $request->get('ID'));
            }
            if ($locationIds) {
                $q->andWhere('l.id is not null');
            } else {
                $q->andWhere('l.id is null');
            }
            $q = $q->groupBy('r.id')
                ->getQuery()
                ->getResult();
            if (count($q)) {
                return new FailedResponse('This type of earn reward has exists with the same spent amount');
            }
        }
        $attachmentPath = null;
        if ($request->get('ID')) {
            /** @var SpendrReward $existed */
            $existed = Util::find(SpendrReward::class, $request->get('ID'));
            if ($existed) {
                $attachmentPath = $existed->getAttachment();
                $existed->setStatus(SpendrReward::STATUS_INACTIVE)
                    ->persist();
            }
        }
        $realMemberIDs = [];
        if ($request->get('attachment') && $request->get('Type') === UserCardLoad::EARN_TYPE_SPEND_REWARD) {
            $members = [];
            $attachment = Attachment::find($request->get('attachment'));
            $attachmentPath = $attachment->getHash();
            $path = $attachment->prepareForLocalRead();
            $excel = Excel::openExcel($path);
            $sheet = $excel->getActiveSheet();
            $rows = $sheet->toArray();
            foreach ($rows as $i => $row) {
                if (intval($row[0]) > 0) {
                    $members[] = intval($row[0]);
                }
            }
            $members = array_values(array_unique($members));
            $realMembers = Util::em()->getRepository(User::class)
                ->createQueryBuilder('u')
                ->join('u.teams', 't')
                ->join('u.cards', 'uc')
                ->join('uc.card', 'c')
                ->where(Util::expr()->in('t.name', ':roles'))
                ->andWhere('c.cardProgram = :cardProgram')
                ->andWhere(Util::expr()->in('u.id', ':ids'))
                ->setParameter('cardProgram', CardProgram::spendr())
                ->setParameter('roles', SpendrBundle::getConsumerRoles())
                ->setParameter('ids', $members)
                ->distinct()
                ->select('u.id')
                ->getQuery()
                ->getArrayResult();
            $realMemberIDs = pluck($realMembers, 'id');
        }

        $em = Util::em();
        $reward = new SpendrReward();
        $reward->setType($request->get('Type'))
            ->setTitle($request->get('Title'))
            ->setFormat($request->get('Reward Format'))
            ->setStatus(SpendrReward::STATUS_ACTIVE)
            ->setAttachment($attachmentPath);
        if (
            $request->get('Type') === UserCardLoad::EARN_TYPE_SPEND_REWARD &&
            $request->get('Reward Format') === SpendrReward::AMOUNT_TYPE_PERCENT
        ) {
            $reward->setPercentage($request->get('Percentage'));
        } else {
            $reward->setAmount(Money::normalizeAmount($request->get('Amount'), 'USD'));
        }
        // Bank Link reward can not set spend type/amount/count
        if ($request->get('Type') !== UserCardLoad::EARN_TYPE_BANK_LINK) {
            $reward->setSpendType($request->get('Spend Type'))
                ->setSpendAmount(Money::normalizeAmount($request->get('Spend Amount'), 'USD'));
            if ($request->get('Spend Type') === SpendrReward::SPEND_TYPE_NUM_WITH_MIN_AMOUNT) {
                $reward->setSpendCount($request->get('Spend Count'));
            }
        } else {
            $reward->setCountLimit($request->get('Count Limit'));
        }
        // Only Spend Reward can set date info
        if ($request->get('Type') === UserCardLoad::EARN_TYPE_SPEND_REWARD) {
            $reward->setDateType($dateType);
            if ($dateType === SpendrReward::DATE_TYPE_DATE_RANGE && ($begin || $end)) {
                $reward->setStartAt($begin)
                    ->setEndAt($end);
            }
        }
        $em->persist($reward);
        if (count($realMemberIDs)) {
            foreach ($realMemberIDs as $realMemberID) {
                $target = new RewardTarget();
                $target->setReward($reward);
                $target->setConsumerID($realMemberID);
                $em->persist($target);
            }
        }
        if ($locationIds) {
            $locations = LocationService::findActiveLocationsByIds($locationIds);
            /** @var Location $item */
            foreach ($locations as $item) {
                $location = new RewardLocation();
                $location->setReward($reward)
                    ->setLocation($item);
                $em->persist($location);
            }
        }
        $em->flush();

        return new SuccessResponse($reward->toApiArray());
    }

    /**
     * @Route("/admin/spendr/earn-rewards/{reward}/toggle-status", methods={"POST"})
     * @param Request $request
     * @param SpendrReward $reward
     * @return DeniedResponse|SuccessResponse|FailedResponse
     */
    public function toggleStatusAction(Request $request, SpendrReward $reward)
    {
        if ($this->user->inTeam(Role::ROLE_SPENDR_ACCOUNTANT)) {
            return new DeniedResponse();
        }
        $status = $request->get('status', 'inactive') === 'active'
            ? SpendrReward::STATUS_ACTIVE
            : SpendrReward::STATUS_INACTIVE;
        $reward->setStatus($status)
            ->persist();
        return new SuccessResponse($reward->toApiArray());
    }

    /**
     * @Route("/admin/spendr/earn-rewards/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        $fields = [
            'ID' => 10,
            'Title' => 20,
            'Reward Format' => 20,
            'Amount' => 30,
            'Percentage' => 30,
            'Spend Type Text' => 30,
            'Spend Amount' => 20,
            'Spend Count' => 20,
            'Count Limit' => 20,
            'Date Type' => 20,
            'Date Count' => 20,
            'Start Date' => 30,
            'End Date' => 30,
            'Created At' => 30
        ];
        return $this->commonExport($request, 'Spendr Earn Rewards', $fields);
    }
}
