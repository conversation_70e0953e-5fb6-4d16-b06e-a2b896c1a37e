<?php


namespace S<PERSON>dr<PERSON><PERSON>le\Controller\Admin\Recon;


use Carbon\Carbon;
use ClfBundle\Entity\TransactionStatus;
use ClfBundle\Entity\TransactionType;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use SpendrBundle\Controller\Admin\BaseController;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\PromotionService;
use SpendrBundle\Services\UserCardService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use SpendrBundle\Traits\TransactionTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class ActivityReportController extends BaseController
{
	use TransactionTrait;

	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBankAndGroup();
	}

	/**
	 * @Route("/admin/spendr/recon/activity-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @return SuccessResponse
	 */
	public function index(Request $request)
	{
		$result = [
//			'merchantRevenue' => null,
			'spendrRevenue' => null,
			'bankFees' => null,
			'estimatedBankFees' => null,
			'totalNegativeMembers' => null,
			'totalNegativeMemberBalance' => null,
			'totalPendingBalance' =>null,

			'totalLoadUnload' => null,
			'totalLoads' => null,
			'totalLoadAmount' => null,
			'totalConsumerLoads' => null,
			'totalConsumerLoadAmount' => null,
			'totalConsumerNormalLoads' => null,
			'totalConsumerNormalLoadAmount' => null,
			'totalManuallyLoads' => null,
			'totalManuallyLoadAmount' => null,
			'totalEarnLoads' => null,
			'totalEarnLoadAmount' => null,
			'totalPromoCodeLoads' => null,
			'totalPromoCodeLoadAmount' => null,
			'totalPartnerLoads' => null,
			'totalPartnerLoadAmount' => null,
			'totalUnloads' => null,
			'totalUnloadAmount' => null,
			'totalConsumerUnloads' => null,
			'totalConsumerUnloadAmount' => null,
			'totalPartnerUnloads' => null,
			'totalPartnerUnloadAmount' => null,
			'totalMerchantUnloads' => null,
			'totalMerchantUnloadAmount' => null,
//			'totalLoadSpendrFee' => null,
			'totalLoadAchReturnFee' => null,

			'totalTransactions' => null,
			'totalInStorePayment' => null,
			'totalInStorePaymentAmount' => null,
			'totalInStoreRefund' => null,
			'totalInStoreRefundAmount' => null,
//			'totalOnlinePayment' => null,
//			'totalOnlinePaymentAmount' => null,
//			'totalOnlineRefund' => null,
//			'totalOnlineRefundAmount' => null,
			'totalTransactionSpendrFee' => null,
			'totalTransactionRefundFee' => null,
            'totalTips' => null,
            'totalTipsAmount' => null,
            'totalTipsSpendrFee' => null,
		];

		$start = $request->get('start') ? Carbon::parse($request->get('start')) : null;
		$end = $request->get('end') ? Carbon::parse($request->get('end')) : null;

		$result['spendrRevenue'] = FeeService::calculateSpendrRevenue($start, $end);
		$result['bankFees'] = FeeService::getCurrentBankFees($start, $end);
		$result['estimatedBankFees'] = FeeService::estimatedBankFees($start, $end);

		$negativeMemberData = UserService::getNegativeMemberData();
		[$result['totalNegativeMembers'], $result['totalNegativeMemberBalance']]
			= $negativeMemberData ? [$negativeMemberData['total'], $negativeMemberData['totalAmount']] : [null, null];

		$result['totalPendingBalance'] = UserCardService::getPendingBalanceData('all', 'amount');

		$result['totalLoadAchReturnFee'] = FeeService::calculateSpendrAchReturnFee($start, $end);

		[$result['totalLoadUnload']]
			= $this->getLoadData('all', 'all', $start, $end);

		[$result['totalLoads'], $result['totalLoadAmount']]
			= $this->getLoadData('all', 'load', $start, $end);

		[$result['totalConsumerLoads'], $result['totalConsumerLoadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_CONSUMER, 'load', $start, $end);

		[$result['totalConsumerNormalLoads'], $result['totalConsumerNormalLoadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_CONSUMER, 'deposit', $start, $end);

		[$result['totalManuallyLoads'], $result['totalManuallyLoadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_CONSUMER, 'manually', $start, $end);

		[$result['totalEarnLoads'], $result['totalEarnLoadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_CONSUMER, 'earn', $start, $end);

		[$result['totalPromoCodeLoads'], $result['totalPromoCodeLoadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_CONSUMER, 'promoCode', $start, $end);

		[$result['totalPartnerLoads'], $result['totalPartnerLoadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_PROGRAM_OWNER, 'load', $start, $end);

		[$result['totalUnloads'], $result['totalUnloadAmount']]
			= $this->getLoadData('all', 'unload',$start, $end);

		[$result['totalConsumerUnloads'], $result['totalConsumerUnloadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_CONSUMER, 'unload', $start, $end);

		[$result['totalPartnerUnloads'], $result['totalPartnerUnloadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_PROGRAM_OWNER, 'unload', $start, $end);

		[$result['totalMerchantUnloads'], $result['totalMerchantUnloadAmount']]
			= $this->getLoadData(Role::ROLE_SPENDR_MERCHANT_ADMIN, 'unload', $start, $end);

//		$result['merchantRevenue'] = $this->traitGetMerchantTotalRevenue($this->user, $request);

		[
			$result['totalTransactions'],
			$result['totalTransactionAmount'],
			$result['totalTransactionSpendrFee'],
			$result['totalTransactionRefundFee'],
            $result['totalTips'],
            $result['totalTipsAmount'],
            $result['totalTipsSpendrFee']
		] = $this->getTransactionData($request, $this->user);

		$result['totalTransactionSpendrFee'] += $result['totalTipsSpendrFee'];
		$result['totalTransactionSpendrFee'] -= $result['totalTransactionRefundFee'];

		[$result['totalInStorePayment'], $result['totalInStorePaymentAmount']]
			= $this->getTransactionData($request, $this->user, TransactionType::NAME_PURCHASE);

		[$result['totalInStoreRefund'], $result['totalInStoreRefundAmount']]
			= $this->getTransactionData($request, $this->user, TransactionType::NAME_REFUND);

//		[$result['totalOnlinePayment'], $result['totalOnlinePaymentAmount']]
//			= $this->getTransactionData($request, $this->user, TransactionType::NAME_REFUND);
//
//		[$result['totalOnlineRefund'], $result['totalOnlineRefundAmount']]
//			= $this->getTransactionData($request, $this->user, TransactionType::NAME_PURCHASE);


		$data['quick'] = $result;
		return new SuccessResponse($data);
	}

	protected function getLoadData($role = 'all', $type = 'all', Carbon $start = null, Carbon $end = null)
	{
		$expr = Util::expr();

		$query = $this->em->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'cpct')
			->where($expr->eq('cpct.cardProgram',':cardProgram'))
			->setParameter('cardProgram', $this->cardProgram)
			->andWhere('uc.type = :cardType')
			->setParameter('cardType', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('ucl.loadStatus = :loadStatus')
			->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
			->andWhere($expr->notLike('ucl.meta', ':metaRollbackLoad'))
			->setParameter('metaRollbackLoad', '%' . LoadService::LOAD_TYPE_ROLLBACK . '":true%')
			->andWhere($expr->notLike('ucl.meta', ':metaUnloadFailed'))
			->setParameter('metaUnloadFailed', '%"Spendr unload failed"%')
			->andWhere($expr->notLike('ucl.meta', ':metaLoadFailed'))
			->setParameter('metaLoadFailed', '%"Spendr load failed"%');

		if ($role !== 'all') {
			$query->andWhere('t.name = :role')
				->setParameter('role', $role);
		}

		if (in_array($type, ['load', 'deposit', 'manually', 'earn', 'promoCode'])) {
			$query->andWhere('ucl.type = :type')
				->setParameter('type', UserCardLoad::TYPE_LOAD_CARD);

			if ($type === 'deposit') {
				$query->andWhere($expr->in('ucl.loadType', ':loadTypes'))
					->setParameter('loadTypes', [
                        LoadService::LOAD_TYPE_INSTANT,
                        LoadService::LOAD_TYPE_PREFUND
                    ]);
			} else if (in_array($type, ['manually', 'earn', 'promoCode'])) {
				$query->andWhere('ucl.loadType = :promotion')
					->setParameter('promotion', LoadService::LOAD_TYPE_PROMOTION);
				if ($type === 'manually') {
					$query->andWhere($expr->isNull('ucl.promoType'));
				} else if ($type === 'earn') {
					$query->andWhere('ucl.promoType = :promoType')
						->setParameter('promoType', PromotionService::PROMOTION_TYPE_EARN);
				} else if ($type === 'promoCode') {
					$query->andWhere('ucl.promoType = :promoType')
						->setParameter('promoType', PromotionService::PROMOTION_TYPE_PROMO_CODE);
				}
			}
		} else if ($type === 'unload') {
			$query->andWhere('ucl.type = :type')
				->setParameter('type', UserCardLoad::TYPE_UNLOAD);
		}

		if ($start) {
			$query->andWhere('ucl.initializedAt >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('ucl.initializedAt <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		$result = $query->select('count(distinct ucl) as total, sum(ucl.initialAmount) as amount')
			->getQuery()
			->getSingleResult();

		return $result ? [$result['total'], $result['amount']] : [null, null];
	}

	/**
	 * @Route("/admin/spendr/recon/activity-report/export", methods={"GET"})
	 * @param Request $request
	 */
	public function export(Request $request)
	{

	}
}
