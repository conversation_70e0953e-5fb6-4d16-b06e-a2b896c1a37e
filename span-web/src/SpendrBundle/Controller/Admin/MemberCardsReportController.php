<?php


namespace SpendrBundle\Controller\Admin;

use CoreBundle\Entity\UserCard;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use Doctrine\ORM\QueryBuilder;
use SalexUserBundle\Entity\User;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Utils\Util;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class MemberCardsReportController extends BaseController
{
	public $member = null;

	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBankAndGroup();
	}

	/**
	 * @Route("/admin/spendr/member-cards-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param User $member
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
		$memberId = $request->get('memberID');
    	if (!$memberId) {
    		return new DeniedResponse();
		}
    	$member = User::find($memberId);
    	if (!$member || !$member->inTeams(SpendrBundle::getConsumerRoles())) {
			return new DeniedResponse();
		}
    	$this->member = $member;

        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();
        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct uc)')
                ->getQuery()
                ->getSingleScalarResult();
        $result['quick'] = [
                  'total' => (int)$total,
              ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(UserCard::class)
			->createQueryBuilder('uc')
			->join('uc.card', 'cpct')
			->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
			->setParameter('cardProgram', $this->cardProgram)
			->andWhere('uc.user = :user')
			->setParameter('user', $this->member)
			->andWhere('uc.type != :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
			->andWhere('uc.status = :status')
			->setParameter('status', UserCard::STATUS_ACTIVE);

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'uc', 'count(distinct uc)');
        $params->distinct = true;
        $params->orderBy = [
            'uc.id' => 'desc',
        ];
        $params->searchFields = [
            'uc.id',
        ];
        return $params;
    }

    /**
     * @param UserCard $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $member = $entity->getUser();
        $data = [
            'Card ID' => $entity->getId(),
            'User ID' => $member->getId(),
            'Bank Name' => $entity->getBankName(),
            'Last 4 Digits' => Util::meta($entity, 'mask'),
			'Type' => $entity->getType(),
			'Date & Time' => Util::formatDateTime($entity->getCreatedAt(), Util::DATE_TIME_FORMAT, $this->tz)
        ];

		return $data;
    }
}
