<?php

namespace SpendrBundle\Controller\Admin;

use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use SpendrBundle\Services\FeeService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class BalanceReportController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBankAndGroup();
	}

	/**
	 * @Route("/admin/spendr/balance-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1, "limit" = 10})
	 *
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function search(Request $request, $page = 1, $limit = 10)
	{
		$resp = $this->traitSearch($request, [], $page, $limit);
		$result = $resp->getResult();
		$query = $this->query($request);
		$total = (int)(
			(clone $query)->select('count(distinct ucb)')
			->getQuery()
			->getSingleScalarResult()
		);
		$result['quick'] = [
			'total' => $total,
		];
		return new SuccessResponse($result);
	}

	protected function query(Request $request)
	{
		$type = $request->get('type', 'All');
		$expr = Util::expr();
		$query = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'c')
			->join('c.user', 'u')
			->join('u.teams', 't')
			->join('c.card', 'cpct')
			->where($expr->eq('cpct.cardProgram',':cardProgram'))
			->setParameter('cardProgram', $this->cardProgram);

		$partnerQuery = '';
		$merchantQuery = '';
		$consumerQuery = '';
		$ternQuery = '';

		if ($type === 'All' || $type === 'Partner') {
			$partner = UserService::getSpendrBalanceAdminAccount('spendr');
			$partnerQuery = $expr->eq('c.user', ':partner');
			$query->setParameter('partner', $partner);
		}

		if ($type === 'All' || $type === 'Merchant') {
			$merchantQuery = $expr->in('t.name', ':merchantRole');
			$query->setParameter('merchantRole', SpendrBundle::getMerchantBalanceAdminRoles());
		}

		if ($type === 'All' || $type === 'Consumer') {
			$merchantQuery = $expr->in('t.name', ':consumerRole');
			$query->setParameter('consumerRole', SpendrBundle::getConsumerRoles());
		}

		if ($type === 'All' || $type === 'Tern') {
			$tern = UserService::getSpendrBalanceAdminAccount('tern');
			$ternQuery = $expr->eq('c.user', ':tern');
			$query->setParameter('tern', $tern);
		}

		if ($partnerQuery || $merchantQuery || $consumerQuery || $ternQuery) {
			$query->andWhere($expr->orX(

			));
		}

		return $this->queryByDateRange($query, 'ucb.createdAt', $request);
	}

	protected function consumerQuery(Request $request, $type = 'begin')
	{
		$ucbIds = $this->consumerSubQuery($request, $type);
		$expr = Util::expr();
		$query = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->where($expr->in('ucb.id', $ucbIds));

		if ($type === 'begin') {
			$query->select('sum(ucb.preBalance)');
		} else if ($type === 'end') {
			$query->select('sum(ucb.curBalance)');
		}

		return $query->getQuery()
			->getSingleScalarResult();
	}

	protected function consumerSubQuery(Request $request, $type = 'begin')
	{
		$start = $request->get('startDate', '2022-03-08');
		$end = $request->get('endDate', '2022-03-08');
		if (!$start && !$end) {
			$startDate = Carbon::now()->startOfDay();
			$endDate = Carbon::now()->endOfDay();
		} else {
			$startDate = Carbon::parse($start)->startOfDay();
			$endDate = Carbon::parse($end)->endOfDay();
		}

		$expr = Util::expr();
		$query = $this->em->getRepository(UserCardBalance::class)
			->createQueryBuilder('ucb')
			->join('ucb.userCard', 'c')
			->join('c.user', 'u')
			->join('u.teams', 't')
			->where($expr->in('t.name', ':consumerRoles'))
			->setParameter('consumerRoles', [Role::ROLE_SPENDR_CONSUMER])
			->andWhere('ucb.createdAt >= :startDate')
			->setParameter('startDate', $startDate)
			->andWhere('ucb.createdAt <= :endDate')
			->setParameter('endDate', $endDate)
			->groupBy('u.id');

		if ($type === 'begin') {
			$query->orderBy('ucb.id', 'asc')
				->select('min(ucb.id)');
		} else if ($type === 'end') {
			$query->orderBy('ucb.id', 'desc')
				->select('max(ucb.id)');
		}

		$ucbs = $query->getQuery()
			->getResult();

		if (!$ucbs) {
			return null;
		}

		$ids = [];
		/** @var UserCardBalance $ucb */
		foreach($ucbs as $ucb) {
			$ids[] = $ucb->getId();
		}

		return $ids;
	}


















}
