<?php


namespace SpendrBundle\Controller\Admin;


use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SpendrBundle\Services\PromotionService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PromotionReportController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->authAdminsWithoutBankAndGroup();
    }

    /**
     * @Route("/admin/spendr/promotion-report/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int $page
     * @param int $limit
     *
     * @return SuccessResponse
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [
            'createdAt',
        ], $page, $limit);
        $result = $resp->getResult();
        $query = $this->query($request);
        $quick = (clone $query)->andWhere('ucl.type = :type')
        ->andWhere('ucl.loadStatus != :status')
        ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
        ->setParameter('status', UserCardLoad::LOAD_STATUS_ERROR)
        ->select('count(distinct ucl) total, sum(ucl.initialAmount) totalAmount')
        ->distinct()
        ->getQuery()
        ->getSingleResult();
        $result['quick'] = [
            'total'  => (int)$quick['total'],
            'redeem_total' => (int)$quick['totalAmount'],
            'revoke_total' => (int)((clone $query)->andWhere('ucl.type = :type')
                ->andWhere('ucl.loadStatus = :status')
                ->setParameter('type', UserCardLoad::TYPE_UNLOAD)
                ->setParameter('status', UserCardLoad::LOAD_STATUS_LOADED)
                ->select('sum(ucl.initialAmount)')
                ->getQuery()
                ->getSingleScalarResult()),
            'avg_redeem' => $quick['total'] ? round($quick['totalAmount'] / $quick['total']) : 0
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'c')
            ->join('c.user', 'u')
            ->join('c.card', 'cpct')
            ->where(Util::expr()->isNotNull('ucl.promoCode'))
            ->andWhere(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', $this->cardProgram);
        $loadType = $request->get('loadType', 'All');
        if (in_array($loadType, [
            UserCardLoad::TYPE_LOAD_CARD,
            UserCardLoad::TYPE_UNLOAD
        ])) {
            $query->andWhere('ucl.type = :type')
                ->setParameter('type', $loadType);
        }

        return $this->queryByDateRange($query, 'ucl.createdAt', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'ucl', 'count(distinct ucl)');
        $params->distinct = true;
        $params->orderBy = [
            'ucl.id' => 'desc',
        ];
        $params->searchFields = [
            'ucl.id',
            'ucl.loadStatus'
        ];
        return $params;
    }

    /**
     * @param UserCardLoad $entity
     * @return array
     */
    protected function getRowData($entity)
    {
        $amount = $entity->getLoadAmountUSD() ? $entity->getLoadAmountUSD() : $entity->getInitialAmount();
        $amountText = Money::format($amount, 'USD', false);
        return [
            'ID' => $entity->getId(),
            'User ID' => $entity->getUserCard()->getUser()->getId(),
            'First Name' => $entity->getUserCard()->getUser()->getFirstName(),
            'Last Name' => $entity->getUserCard()->getUser()->getLastName(),
            'Email' => $entity->getUserCard()->getUser()->getEmail(),
            'Reward Type' => $entity->getType() === UserCardLoad::TYPE_LOAD_CARD
				? PromotionService::PROMOTION_TYPE_PROMO_CODE : 'Revoke',
            'Rewards Earned' => $amountText,
            'Total Spend' => $amountText,
            'Redemption Date' => Util::formatDate($entity->getCompletedAt(), $this->tz, Util::DATE_TIME_FORMAT),
            'Reward Status' => $entity->getLoadStatus() ? $entity->getLoadStatus() : 'Pending'
        ];
    }

    /**
     * @Route("/admin/spendr/promotion-report/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        $fields = [
            'User ID' => 10,
            'First Name' => 20,
            'Last Name' => 20,
            'Email' => 30,
            'Reward Type' => 20,
            'Rewards Earned' => 20,
            'Total Spend' => 20,
            'Redemption Date' => 20,
            'Reward Status' => 20
        ];
        return $this->commonExport($request, 'Spendr Promotion Report', $fields);
    }
}
