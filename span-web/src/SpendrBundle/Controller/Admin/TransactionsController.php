<?php


namespace Spendr<PERSON><PERSON>le\Controller\Admin;


use CoreB<PERSON>le\Entity\Attachment;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\QueryBuilder;
use SpendrBundle\Entity\SpendrTransaction;
use SpendrBundle\Services\Bank\BankLedgerService;
use SpendrBundle\Services\Bank\ImportTransactionsService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class TransactionsController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBankAndGroup();
	}

	/**
	 * @Route("/admin/spendr/transactions/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $row = $this->query($request)
            ->select('max(t.importAt) last, sum(t.credit) credit, sum(t.debit) debit')
            ->getQuery()
            ->getSingleResult(AbstractQuery::HYDRATE_ARRAY);

        $result['quick'] = [
            'last' => Util::formatApiDateTime($row['last']),
            'credit' => (int)$row['credit'],
            'debit' => (int)$row['debit'],
        ];
        return new SuccessResponse($result);
    }

    protected function query(Request $request)
    {
    	$expr = Util::expr();
        $query = $this->em->getRepository(SpendrTransaction::class)
            ->createQueryBuilder('t')
			->where($expr->notIn('t.description', ':descs'))
			->setParameter('descs', ImportTransactionsService::DESC_FILTER_OUTER)
			->andWhere(
				$expr->orX(
					$expr->isNull('t.meta'),
					$expr->notLike('t.meta', ':meta')
				)
			)
			->setParameter('meta', '%"combined"%');

        return $this->queryByDateRange($query, 't.date', $request);
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 't', 'count(distinct t)');
        $params->distinct = true;
        $params->orderBy = [
            't.id' => 'desc',
        ];
        $params->searchFields = [
            't.account',
            't.chkRef',
            't.description',
        ];
        return $params;
    }

    /**
     * @param SpendrTransaction $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        return [
            'ID' => $entity->getId(),
            'Account' => $entity->getAccount(),
            'Chk Ref' => $entity->getChkRef(),
            'Debit' => Money::formatWhen($entity->getDebit()),
            'Credit' => Money::formatWhen($entity->getCredit()),
            'Balance' => Money::formatWhen($entity->getBalance()),
            'Date' => Util::formatDate($entity->getDate(), $this->tz, Util::DATE_TIME_FORMAT),
            'Import Date' => Util::formatDate($entity->getImportAt(), $this->tz, Util::DATE_TIME_FORMAT),
            'Description' => $entity->getDescription(),
            'Status' => $entity->getStatus() ?? 'Invalid',
        ];
    }

    /**
     * @Route("/admin/spendr/transactions/import", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function import(Request $request)
    {
		$this->authSuperOrSpendrEmployeeCompliance();

        $aid = $request->get('attachment');

        return BankLedgerService::importAction($aid);
    }

    /**
     * @Route("/admin/spendr/transactions/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
		$fields = [
            'ID' => 10,
            'Account' => 15,
            'Chk Ref' => 15,
            'Debit' => 12,
            'Credit' => 12,
            'Balance' => 16,
            'Date' => 16,
            'Description' => 50,
        ];
        return $this->commonExport($request, 'Spendr Transactions', $fields);
    }

//	/**
//	 * @Route("/admin/spendr/transactions/process-balance")
//	 * @param Request $request
//	 *
//	 * @return SuccessResponse
//	 */
//    public function manualProcessBalance(Request $request)
//	{
//		ImportTransactionsService::creditToPartnerBalance();
//		ImportTransactionsService::debitFromPartnerBalance();
//		ImportTransactionsService::debitFromBankBalance();
//		return new SuccessResponse();
//	}

//	/**
//	 * @Route("/admin/spendr/transactions/create", methods={"POST"})
//	 * @param Request $request
//	 * @return FailedResponse|SuccessResponse
//	 */
//	public function create(Request $request)
//	{
//		$data = $request->request->all();
//		if (!$data['Description'] || (!$data['Debit'] && !$data['Credit'])) {
//			return new FailedResponse('Invalid parameters.');
//		}
//
//		$signatureArr = [
//			$data['Account'],
//			$data['Chk Ref'],
//			$data['Debit'],
//			$data['Credit'],
//			$data['Balance'],
//			$data['Date'],
//			$data['Description'],
//		];
//
//		$signature = sha1('spendr_' . implode('_', $signatureArr));
//		Util::disableSoftDeletable();
//		$old = $this->em->getRepository(SpendrTransaction::class)
//			->findOneBy([
//				'signature' => $signature
//			]);
//		Util::enableSoftDeletable();
//		if ($old) {
//			return new FailedResponse('The same data cannot be created repeatedly!');
//		}
//
//		$debit = Money::normalizeAmountOrNull($data['Debit']);
//		$credit = Money::normalizeAmountOrNull($data['Credit']);
//		$balance = Money::normalizeAmountOrNull($data['Balance']);
//
//		$description = trim($data['Description']);
//
//		if (ImportTransactionsService::checkIfNeedFilterOut($description)) {
//			return new FailedResponse('The description "' . $description . '" does not allow creation!');
//		}
//
//		$tz = Util::tzUTC();
//		$st = new SpendrTransaction();
//		$st->setAccount($data['Account'])
//			->setChkRef($data['Chk Ref'])
//			->setDebit($debit)
//			->setCredit($credit)
//			->setBalance($balance)
//			->setDate(empty($data['Date']) ? null : Carbon::parse($data['Date'], $tz))
//			->setDescription($description)
//			->setSignature($signature)
//			->setImportAt(new \DateTime())
//			->setImportBy($this->user)
//		;
//
//		$status = null;
//		$type = null;
//		if ($credit && ImportTransactionsService::checkIfNeedCreditToPartner($description)) {
//			$status = SpendrTransaction::STATUS_PENDING;
//			$type = 'credit_to_partner';
//		}
//
//		if ($debit && ImportTransactionsService::checkIfNeedDebitFromPartner($description)) {
//			$status = SpendrTransaction::STATUS_PENDING;
//			$type = 'debit_from_partner';
//		}
//
//		if ($debit && ImportTransactionsService::checkIfNeedDebitFromBank($description)) {
//			$status = SpendrTransaction::STATUS_PENDING;
//			$type = 'debit_from_bank';
//		}
//
//		$st->setStatus($status);
//		Util::persist($st);
//
//		if ($status && $type) {
//			if ($type === 'credit_to_partner') {
//				ImportTransactionsService::creditToPartnerBalance();
//			} else if ($type === 'debit_from_partner') {
//				ImportTransactionsService::debitFromPartnerBalance();
//			} else if ($type === 'debit_from_bank') {
//				ImportTransactionsService::debitFromBankBalance();
//			}
//		}
//
//		return new SuccessResponse($this->getRowData($st));
//	}

//	/**
//	 * @Route("/admin/spendr/transactions/rollback/{id}")
//	 * @param Request $request
//	 * @param $id
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \Throwable
//	 */
//	public function rollback(Request $request, $id)
//	{
//		if (!$id) {
//			return new FailedResponse('Invalid parameters.');
//		}
//		/** @var SpendrTransaction $st */
//		$st = $this->em->getRepository(SpendrTransaction::class)
//			->find($id);
//		if (!$st) {
//			return new FailedResponse('Unknown transaction!');
//		}
//
//		ImportTransactionsService::rollbackBalance($st);
//
//		$st->setStatus(SpendrTransaction::STATUS_ROLLBACK);
//		Util::persist($st);
//
//		return new SuccessResponse($this->getRowData($st));
//	}

//	/**
//	 * @Route("/admin/spendr/transactions/delete/{id}")
//	 * @param Request $request
//	 * @param $id
//	 * @return FailedResponse|SuccessResponse
//	 * @throws \Exception
//	 */
//	public function delete(Request $request, $id)
//	{
//		if (!$id) {
//			return new FailedResponse('Invalid parameters.');
//		}
//		/** @var SpendrTransaction $st */
//		$st = $this->em->getRepository(SpendrTransaction::class)
//			->find($id);
//		if (!$st) {
//			return new FailedResponse('Unknown transaction!');
//		}
//
//		$status = $st->getStatus();
//		if (in_array($status, [
//			SpendrTransaction::STATUS_COMPLETED,
//			SpendrTransaction::STATUS_ROLLBACK
//		])) {
//			return new FailedResponse('The transaction cannot be deleted.');
//		}
//
//		$st->setDeletedAt(new \DateTime());
//		Util::persist($st);
//
//		return new SuccessResponse($this->getRowData($st));
//	}
}
