<?php

namespace Spendr<PERSON><PERSON>le\Controller\Admin;

use <PERSON><PERSON><PERSON><PERSON>le\Entity\Transaction;
use CoreBundle\Entity\Role;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use SpendrBundle\Entity\SpendrMerchant;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class MerchantsTransactionsController extends MerchantsController
{
    public function __construct()
	{
		parent::__construct();

		$this->authAdminsWithoutBankAndGroup();
	}
    
	/**
     * @Route("/admin/spendr/merchants/transactions/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
     * @param Request $request
     * @param int     $page
     * @param int     $limit
     *
     * @return SuccessResponse
     */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        return $this->traitSearch($request, [
            'dateTime',
        ], $page, $limit);
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(Transaction::class)
            ->createQueryBuilder('t')
            ->join('t.consumer', 'c');

        $merchant = $this->em->getRepository(SpendrMerchant::class)->find($request->get('member'));
        if ($merchant) {
            $query->where('t.merchant = :merchant')
                ->setParameter('merchant', $merchant);
        } else {
            $query->where('t.id < 0');
        }

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 't', 'count(distinct t)');
        $params->distinct = true;
        $params->orderBy = [
            't.dateTime' => 'desc',
            't.id' => 'desc',
        ];
        $params->searchFields = [
            't.id',
            'c.firstName',
            'c.lastName',
            'c.email',
        ];
        return $params;
    }

    /**
     * @param Transaction $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        $consumer = $entity->getConsumer();
        $spendrFee = null;
        if (!$this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
        	$spendrFee = Money::formatUSD($entity->getSpendrFee());
		}

        return [
            'Transaction ID' => $entity->getId(),
            'Member ID' => $consumer->getId(),
            'First Name' => $consumer->getFirstName(),
            'Last Name' => $consumer->getLastName(),
            'Email' => $consumer->getEmail(),
            'Location' => Util::field($entity->getLocation()),
            'Location(old)' => Util::field($entity->getLocation()),
            'Clerk ID' => Util::field($entity->getLocationEmployee(), 'id'),
            'Amount' => Money::formatUSD($entity->getAmount()),
            'Spendr Fee' => $spendrFee,
            'Tern Fee' => $this->user->isMasterAdmin() ? Money::formatUSD($entity->getTernFee()) : null,
            'Merchant Balance Before' => Money::formatUSD($entity->getProducerBalanceBefore()),
            'Merchant Balance After' => Money::formatUSD($entity->getProducerBalanceAfter()),
            'Location Balance Before' => Money::formatUSD($entity->getProducerBalanceBefore()),
            'Location Balance After' => Money::formatUSD($entity->getProducerBalanceAfter()),
			'Type' => Util::field($entity->getType()),
            'Status' => Util::field($entity->getStatus()),
        ];
    }
}
