<?php


namespace SpendrB<PERSON>le\Controller\Admin;


use CoreBundle\Entity\Role;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserService;
use CoreB<PERSON>le\Utils\QueryListParams;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\IDologyService;

class AdminsController extends BaseController
{
	public function __construct()
	{
		parent::__construct();

		$this->authSuperAdminOrAdmins(
			[
				Role::ROLE_SPENDR_PROGRAM_OWNER,
				Role::ROLE_SPENDR_BANK
			]
		);
	}

	/**
	 * @Route("/admin/spendr/admins/list/{page}/{limit}", methods={"GET"}, defaults={"page" = 1,"limit" = 10})
	 * @param Request $request
	 * @param int $page
	 * @param int $limit
	 *
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
    public function search(Request $request, $page = 1, $limit = 10)
    {
        $resp = $this->traitSearch($request, [], $page, $limit);
        $result = $resp->getResult();

        $query = $this->query($request);
        $total = (clone $query)->select('count(distinct u)')
            ->getQuery()
            ->getSingleScalarResult();

        $result['quick'] = [
            'total' => (int)$total,
        ];

        return new SuccessResponse($result);
    }

    protected function getInvolvedRoles()
    {
        return array_merge([
            Role::ROLE_SPENDR_PROGRAM_OWNER,
            Role::ROLE_SPENDR_BANK,
        ], SpendrBundle::getSpendrEmployeeRoles());
    }

    protected function query(Request $request)
    {
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
			->andWhere(Util::expr()->notIn('u.email', ':emails'))
			->setParameter('emails', \SpendrBundle\Services\UserService::SPENDR_BALANCE_ADMIN_EMAILS);

        if ($this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
        	$query->setParameter('roles', [Role::ROLE_SPENDR_BANK]);
		} else {
			$query->setParameter('roles', $this->getInvolvedRoles());
		}

        return $query;
    }

    protected function queryListParams(QueryBuilder $query, Request $request)
    {
        $params = new QueryListParams($query, $request, 'u', 'count(distinct u)');
        $params->distinct = true;
        $params->orderBy = [
            'u.id' => 'desc',
        ];
        $params->searchFields = [
            'u.id',
            'u.name',
            'u.email',
            't.name',
        ];
        return $params;
    }

    /**
     * @param User $entity
     *
     * @return array
     */
    protected function getRowData($entity)
    {
        return [
            'User ID' => $entity->getId(),
            'First Name' => $entity->getFirstName(),
            'Last Name' => $entity->getLastName(),
            'Email' => $entity->getEmail(),
            'Phone' => $entity->getMobilephone(),
            'User Type' => $entity->getTeamName(true),
            'Last Login' => Util::formatDateTime($entity->getLastLogin(), Util::DATE_TIME_FORMAT, $this->tz),
            'Status' => ucfirst($entity->getStatus()),
            'Locked' => $entity->isLocked()
        ];
    }

    protected function validateUser(User $user = null)
    {
        if (!$user || !$user->inTeams($this->getInvolvedRoles())) {
            throw PortalException::temp('Unknown user!');
        }
    }

	/**
	 * @Route("/admin/spendr/admins/{user}/details")
	 * @param User $user
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws PortalException
	 */
    public function detailAction(User $user)
    {
		if ($this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
			return new DeniedResponse();
		}

        $this->validateUser($user);
        $data = $this->getRowData($user);
        return new SuccessResponse($data);
    }

	/**
	 * @Route("/admin/spendr/admins/save", methods={"POST"})
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws PortalException
	 */
    public function saveAction(Request $request)
    {
    	if ($this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
    		return new DeniedResponse();
		}

        $role = Role::find('Spendr ' . $request->get('User Type'));
        if (!$role) {
            return new FailedResponse('Unknown role');
        }

        $uid = $request->get('User ID');
        $email = $request->get('Email');

        $newUser = false;
        if ($uid) {
            $user = User::find($uid);
            $this->validateUser($user);
        } else {
            $user = User::findPlatformUserByEmail($email, $this->getInvolvedRoles());
            if ($user) {
                throw PortalException::temp('Duplicated accounts with the same email address!');
            }
            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setSource('spendr_admin');
            $newUser = true;
        }

        $user->setFirstName($request->get('First Name'))
            ->setLastName($request->get('Last Name'))
            ->changeMobilePhone(Util::inputPhone($request->get('Phone')), $this->getInvolvedRoles())
            ->changeEmail($email, $this->getInvolvedRoles())
            ->clearRoles($this->getInvolvedRoles())
            ->ensureRole($role->getName())
            ->persist();

        $user->ensureAccessiblePlatformProgram()
            ->persist();

        if (Util::isServer()) {
            IDologyService::ofac($user);
        }

        if ($newUser) {
            UserService::sendResetPasswordEmail($user);
        }
        BrazeService::userTrack(null, null, null, [
            'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'email' => $user->getEmail(),
            'phone' => $user->getMobilephone(),
            'email_subscribe' => BrazeService::TYPE_OPTEDIN
        ]);

        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/spendr/admins/{user}/toggle-status", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function toggleStatusAction(Request $request, User $user)
    {
		if ($this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
			return new DeniedResponse();
		}

        $this->validateUser($user);
        $status = $request->get('Status', 'Active') === 'Active'
            ? User::STATUS_ACTIVE
            : User::STATUS_INACTIVE;
        $user->setStatus($status, false)
            ->persist();
        // Update admin subscribe status
        BrazeService::userTrack(null, null, null, [
            'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
            'email_subscribe' => $status === User::STATUS_ACTIVE ? BrazeService::TYPE_OPTEDIN : BrazeService::TYPE_UNSUBSCRIBED
        ]);

        return new SuccessResponse($this->getRowData($user));
    }

    /**
     * @Route("/admin/spendr/admins/{user}/unlock", methods={"POST"})
     * @param Request $request
     * @param User    $user
     *
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
    public function unlockAction(Request $request, User $user)
    {
        $this->authSuperAdminOrAdmins([Role::ROLE_SPENDR_PROGRAM_OWNER]);

        $this->validateUser($user);
        $user->setLockedStatus(User::LOCK_STATUS_UNLOCK)
            ->persist();

        return new SuccessResponse('Unlock success');
    }

	/**
	 * @Route("/admin/spendr/admins/{user}/resend-invitation", methods={"POST"})
	 * @param Request $request
	 * @param User $user
	 *
	 * @return SuccessResponse|DeniedResponse
	 * @throws PortalException
	 */
    public function resendInvitation(Request $request, User $user)
    {
		if ($this->user->inTeams([Role::ROLE_SPENDR_BANK])) {
			return new DeniedResponse();
		}

        $this->validateUser($user);

        UserService::sendResetPasswordEmail($user);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/spendr/admins/export", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse|DeniedResponse
     */
    public function export(Request $request)
    {
		if (SpendrBundle::notAllowedExport()) {
			return new DeniedResponse();
		}
        $fields = [
            'User ID' => 10,
            'First Name' => 20,
            'Last Name' => 20,
            'Email' => 30,
            'Phone' => 20,
            'User Type' => 20,
            'Last Login' => 20,
            'Status' => 20,
        ];
        return $this->commonExport($request, 'Spendr Admins', $fields);
    }
}
