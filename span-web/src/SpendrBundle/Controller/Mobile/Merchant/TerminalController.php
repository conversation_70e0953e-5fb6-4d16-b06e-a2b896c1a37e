<?php

namespace SpendrB<PERSON>le\Controller\Mobile\Merchant;


use CoreB<PERSON>le\Entity\Role;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreB<PERSON>le\Utils\Util;
use SpendrB<PERSON>le\Entity\Location;
use SpendrB<PERSON>le\Entity\SpendrMerchant;
use SpendrBundle\Entity\Terminal;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\Pay\PayService;
use SpendrBundle\Services\TerminalService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class TerminalController extends BaseController
{
	public function __construct()
	{
		parent::__construct();
		$this->authRoles($this->getAccessibleRoles());
	}

	protected function getAccessibleRoles()
	{
		return SpendrBundle::getMerchantEmployeeRoles();
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/list")
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws FailedException
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function indexAction(Request $request)
	{
		$view = $request->get('view', 'all'); // all/active/inactive
		$keyword = $request->get('keyword');
		$page = $request->get('page', 1);
		$pageSize = $request->get('pageSize', 6);

		$result = [
			'current' => null,
			'totalActive' => null,
			'totalBound' => null,
			'totalInactive' => null,
			'active' => [],
			'bound' => [],
			'inactive' => [],
		];

		$current = null;
		$location = LocationService::getCurrentLocation();
		if ($request->headers->has('x-terminal') && $request->headers->get('x-terminal')) {
            try {
				$current = TerminalService::getCurrentTerminal($location);
            } catch (FailedException $exception) {
                Log::warn($exception->getMessage(), [
                    'location' => $request->headers->get('x-location'),
                    'terminal' => $request->headers->get('x-terminal'),
                ]);
				$current = null;
            }
		}

		if ($current) {
			$result['current'] = $current->toApiArray(true);
		}

		$expr = Util::expr();
		$query = $this->em->getRepository(Terminal::class)
			->createQueryBuilder('t')
			->where($expr->eq('t.location', ':location'))
            ->andWhere($expr->orX(
                $expr->isNull('t.deviceId'),
                $expr->notLike('t.deviceId', ':device_id')
            ))
			->setParameter('location', $location)
            ->setParameter('device_id', PayService::DUMMY_TERMINAL_PREFIX . '%')
        ;

		if ($keyword) {
			$query->andWhere('t.name like :desc')
				->setParameter('desc', '%' . $keyword . '%');
		}

		if ($view === 'all' || $view === 'bound') {
			$boundQuery = (clone $query)->andWhere('t.deviceId is not null')
				->andWhere($expr->eq('t.status', ':status'))
				->setParameter('status', Terminal::STATUS_ACTIVE);

			$result['totalBound'] = (int)(
				(clone $boundQuery)->select('count(distinct t)')
					->getQuery()
					->getSingleScalarResult()
			);

			$bound = (clone $boundQuery)->setFirstResult(($page - 1) * $pageSize)
				->setMaxResults($pageSize)
				->getQuery()
				->getResult();

			/** @var Terminal $item */
			foreach($bound as $item) {
				$result['bound'][] = $item->toApiArray(true);
			}
		}

		if ($view === 'all' || $view === 'active') {
			$activeQuery = (clone $query)->andWhere('t.deviceId is null')
				->andWhere($expr->eq('t.status', ':status'))
				->setParameter('status', Terminal::STATUS_ACTIVE);

			$result['totalActive'] = (int)(
				(clone $activeQuery)->select('count(distinct t)')
				->getQuery()
				->getSingleScalarResult()
			);

			$active = (clone $activeQuery)->setFirstResult(($page - 1) * $pageSize)
				->setMaxResults($pageSize)
				->getQuery()
				->getResult();

			/** @var Terminal $item */
			foreach($active as $item) {
				$result['active'][] = $item->toApiArray(true);
			}
		}

		if ($view === 'all' || $view === 'inactive') {
			$inactiveQuery = (clone $query)
				->andWhere($expr->eq('t.status', ':status'))
				->setParameter('status', Terminal::STATUS_INACTIVE);

			$result['totalInactive'] = (int)(
				(clone $inactiveQuery)->select('count(distinct t)')
					->getQuery()
					->getSingleScalarResult()
			);

			$inactive = (clone $inactiveQuery)->setFirstResult(($page - 1) * $pageSize)
				->setMaxResults($pageSize)
				->getQuery()
				->getResult();

			/** @var Terminal $item */
			foreach($inactive as $item) {
				$result['inactive'][] = $item->toApiArray(true);
			}
		}

		return new SuccessResponse($result);
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/current-old")
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function currentTerminalOld(Request $request)
	{
		$location = LocationService::getCurrentLocation();

		/** @var Terminal $terminal */
		$terminal = TerminalService::getCurrentTerminal($location);
		return new SuccessResponse($terminal->toApiArray(true));
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/current")
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function currentTerminal(Request $request)
	{
		$deviceId = $request->get('deviceId');
		if (!trim($deviceId)) {
			return new FailedResponse('Invalid parameters.');
		}

		$location = LocationService::getCurrentLocation();

		/** @var Terminal $terminal */
		$terminal = $this->em->getRepository(Terminal::class)
			->findOneBy([
				'deviceId' => $deviceId,
				'location' => $location
			]);

		if (!$terminal) {
			return new FailedResponse('The device is not bound to terminal.');
		}

		if ($terminal->getStatus() !== Terminal::STATUS_ACTIVE) {
			throw new FailedException('The terminal is not activated!');
		}

		return new SuccessResponse($terminal->toApiArray(true));
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/create", methods={"POST"})
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 * @throws \CoreBundle\Exception\FailedException
	 */
	public function createAction(Request $request)
	{
		$this->authRoles([
			Role::ROLE_SPENDR_MERCHANT_MANAGER,
		]);

		$name = $request->get('name');

		if (!$name) {
			return new FailedResponse("Invalid parameters.");
		}

		$location = LocationService::getCurrentLocation();

		if (!TerminalService::checkNameUnique($location, $name)) {
			return new FailedResponse('Duplicated terminal name!');
		}

		$terminal = new Terminal();
		$terminal->setName($name)
			->setLocation($location)
			->setMerchant($location->getMerchant())
			->persist();

		return new SuccessResponse($terminal->toApiArray(true));
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/details/{terminalId}")
	 *
	 * @param Request $request
	 * @param $terminalId
	 * @return FailedResponse|SuccessResponse
	 */
	public function detailsAction(Request $request, $terminalId)
	{
		if (!$terminalId) {
			return new FailedResponse('Invalid parameters.');
		}

		$terminal = Terminal::find($terminalId);
		return new SuccessResponse($terminal->toApiArray());
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/assign-new", methods={"POST"})
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 */
	public function assignNewTerminal(Request $request)
	{
		$terminalId = $request->get('terminalId');
		$deviceId = $request->get('deviceId');

		if (!$terminalId || !$deviceId) {
			return new FailedResponse('Invalid parameters.');
		}

		$newTerminal = Terminal::find($terminalId);
		if ($newTerminal->getDeviceId()) {
			if ($newTerminal->getDeviceId() !== $deviceId) {
				return new FailedResponse(
					'Oops! It looks like you are trying to set up one terminal on multiple devices! ' .
					'Please create a new terminal or delete the old one using the Terminal Setup menu in the Manager App or Merchant Dashboard!'
				);
			} else {
				return new SuccessResponse($newTerminal->toApiArray(true));
			}
		}

		/** @var Terminal $oldTerminal */
		$oldTerminal = $this->em->getRepository(Terminal::class)
			->findOneBy([
				'deviceId' => $deviceId,
			]);
		if ($oldTerminal) {
			$oldTerminal->setDeviceId(null)
				->persist();
		}

		$newTerminal->setDeviceId($deviceId)
			->persist();

		return new SuccessResponse($newTerminal->toApiArray(true));
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/update-status", methods={"POST"})
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 */
	public function updateTerminalStatus(Request $request)
	{
		$terminalId = $request->get('terminalId');
		$status = $request->get('status', Terminal::STATUS_ACTIVE);
		if (!$terminalId) {
			return new FailedResponse('Invalid parameters.');
		}
		$terminal = Terminal::find($terminalId);
		if (!$terminal) {
			return new FailedResponse('Unknown terminal.');
		}

		$terminal->setStatus($status);
		if ($status === Terminal::STATUS_INACTIVE) {
			$terminal->setDeviceId(null);
		}
		$terminal->persist();

		return new SuccessResponse($terminal->toApiArray());
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/unbound")
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 */
	public function unboundTerminal(Request $request)
	{
		$terminalId = $request->get('terminalId');
		if (!$terminalId) {
			return new FailedResponse('Invalid parameters.');
		}
		$terminal = Terminal::find($terminalId);
		if (!$terminal) {
			return new FailedResponse('Unknown terminal.');
		}

		$terminal->setDeviceId(null)
			->persist();

		return new SuccessResponse($terminal->toApiArray());
	}

	/**
	 * @Route("/spendr/m/merchant/terminal/update", methods={"POST"})
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\DeniedException
	 */
	public function update(Request $request)
	{
		$this->authRoles([
			Role::ROLE_SPENDR_MERCHANT_MANAGER,
		]);

		$name = $request->get('name');
		$terminalId = $request->get('terminalId');

		if (!$terminalId || !$name) {
			return new FailedResponse('Invalid parameters!');
		}

		$terminal = Terminal::find($terminalId);
		if (!$terminal) {
			return new FailedResponse('Unknown terminal!');
		}

		$location = $terminal->getLocation();
		if (!$location || ($location && $location->getStatus() !== Location::STATUS_ACTIVE)) {
			return new FailedResponse('Unknown location!');
		}

		/** @var SpendrMerchant $merchant */
		$merchant = $terminal->getMerchant();
		if (!$merchant) {
			return new FailedResponse('Unknown merchant!');
		}

		if (!TerminalService::checkNameUnique($location, $name, $terminal)) {
			return new FailedResponse('Duplicated terminal name!');
		}

		$terminal->setName($name)
			->persist();

		return new SuccessResponse($terminal->toApiArray());
	}
}
