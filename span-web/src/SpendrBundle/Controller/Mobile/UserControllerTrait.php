<?php

namespace SpendrB<PERSON>le\Controller\Mobile;

use Carbon\Carbon;
use CoreBundle\Constant\IdentityType;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\LocationEmployee;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\MQTT\PublishService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;

trait UserControllerTrait
{
	public function getUserProfile(User $user)
	{
		$platform = Util::platform();
		$platformMeta = Util::meta($platform);

		$data = [
			'id'          => $user->getId(),
			'email'       => $user->getEmail(),
			'firstName'   => $user->getFirstName(),
			'lastName'    => $user->getLastName(),
			'fullName'    => $user->getFullName(),
			'mobile'      => $user->getMobilephone(),
			'address'     => $user->getSimpleFullAddress(),
            'birthday'    => $user->getBirthday() ? $user->getBirthday()->format(Util::DATE_FORMAT_ISO_FULL_DATE) : null,
			'status'      => $user->getStatus(),
			'avatar'      => $user->getAvatarUrl(),
			'teams'       => $user->getTeams()->map(function (Role $role) {
				return $role->getName();
			})->toArray(),
			'currentRole' => $user->getCurrentRoleName(),
			'platform' => [
				'id'   => $platform->getId(),
				'name' => $platform->getName(),
			],
			'supportPhone' => $platformMeta['customerSupportPhone'] ?? null,
			'supportEmail' => $platformMeta['customerSupportEmail'] ?? null,
		];

		if ($user->inTeams(SpendrBundle::getConsumerRoles())) {
		    $meta = Util::s2j($user->getMeta(), []);
		    $pushScribe = isset($meta['pushSubscribe']) ? $meta['pushSubscribe'] : true;
		    $data['brazeExternalId'] = BrazeService::getExternalPrefix() . $user->getId();
			$data['isBiometricLoginEnabled'] = $user->isBiometricLoginEnabled();
			$data['isViewIdButtonEnabled'] = $user->isViewIdButtonEnabled();
			$data['hasPinCode'] = $user->getConsumerPinCode() ? true : false;
			$data['isPinEnabled'] = $user->isPinEnabled();
			$data['mobilePhoneVerified'] = !!Util::meta($user, 'mobilePhoneVerified');
            $data['bankLinked'] = count(ConsumerService::getUserBankList($user, 'plaid')) > 0;
			$data['pushSubscribe'] = $pushScribe;
			$dummy = UserService::getDummyCard($user);
			$data['balance'] = $dummy ? $dummy->getBalance() : 0;
            $data['createdAt'] = Carbon::instance($user->getCreatedAt())->format('Y-m-d\TH:i:s\Z');
            $data['onboardStatus'] = ConsumerService::text($user->getRegisterStep());
            $data['fundingStatus'] = ConsumerService::getConsumerLoadStatus($user);

			$verifyDocs = $this->getDocuments($user);
			$data['DLImages'] = $verifyDocs ? $verifyDocs[IdentityType::DL] : null;
			$data['DLImagesIds'] = $verifyDocs ? $verifyDocs[IdentityType::DL . 'Ids'] : null;
			$data['PSPImages'] = $verifyDocs ? $verifyDocs[IdentityType::PSP] : null;
			$data['PSPImagesIds'] = $verifyDocs ? $verifyDocs[IdentityType::PSP . 'Ids'] : null;
			$MCinfo = $user->getIdCardImages();
			$data['medicalCardImages'] = $MCinfo['images'];

			$data['subscribePushPaymentTopicUrl'] = PublishService::generateTopicUrl(PublishService::TOPIC_SEND_PAYMENT_TO_CONSUMER);
		}

        if ($user->inTeams(SpendrBundle::getMerchantEmployeeRoles())) {
            $merchant = LocationEmployee::getMerchantForUser($user);
            $data['merchant'] = $merchant ? $merchant->toApiArray() : null;
        }

		return $data;
	}

	public function getDocuments(User $user)
	{
		if (!Util::isLive()) {
			$uivs = $user->getIdVerifies(true);
		} else {
			$uivs = $user->getIdVerifies();
		}
		$default = [
			'frontImage' => null,
			'backImage' => null
		];
		$result = [
			IdentityType::DL => null,
			IdentityType::PSP => null,
			IdentityType::DL . 'Ids' => null,
			IdentityType::PSP . 'Ids' => null,
		];

		$defaultIds = [
			'frontImageId' => null,
			'backImageId' => null
		];

		if (count($uivs) > 0) {
			/** @var UserIdVerify $uiv */
			foreach ($uivs as $uiv) {
				if (!$result[$uiv->getType()]) {
					$result[$uiv->getType()] = [
						'frontImage' => $uiv->frontImage() ? $uiv->frontImage()->getAssetUrl(true) : null,
						'backImage' => $uiv->backImage() ? $uiv->backImage()->getAssetUrl(true) : null,
					];
					$result[$uiv->getType() . 'Ids'] = [
						'frontImageId' => $uiv->frontImage() ? $uiv->frontImage()->getId() : null,
						'backImageId' => $uiv->backImage() ? $uiv->backImage()->getId() : null,
					];
				}
			}
		}

		if (!$result[IdentityType::DL]) {
			$result[IdentityType::DL] = $default;
		}

		if (!$result[IdentityType::PSP]) {
			$result[IdentityType::PSP] = $default;
		}

		if (!$result[IdentityType::DL . 'Ids']) {
			$result[IdentityType::DL . 'Ids'] = $defaultIds;
		}

		if (!$result[IdentityType::PSP . 'Ids']) {
			$result[IdentityType::PSP . 'Ids'] = $defaultIds;
		}

		return $result;
	}
}
