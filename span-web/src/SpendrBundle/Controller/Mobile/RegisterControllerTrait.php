<?php

namespace Spendr<PERSON><PERSON>le\Controller\Mobile;

use Carbon\Carbon;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Entity\CardProgram;
use Core<PERSON><PERSON>le\Entity\Email;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserPin;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserPinService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrPayAuthorization;
use SpendrBundle\Services\TransactionService;
use SpendrBundle\Services\UserService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;

trait RegisterControllerTrait
{
	/**
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 */
	public function traitResendRegisterEmail(Request $request)
	{
		$userId = $request->get('userId');
		if (!$userId) {
			return new FailedResponse('Invalid user!');
		}

		$user = User::find($userId);
		if (!$user) {
			return new FailedResponse('Invalid user!');
		}

		if (!$user->getEmailVerified()) {
			$this->traitSendRegisterEmail($user);
		}

		$debug = true;
		if ($debug) {
			$data['sessionCode'] = Data::get('register_email_verify_code_' . $user->getId());
			return new SuccessResponse($data);
		}
		return new SuccessResponse();
	}

	public function traitSendRegisterEmail(User $user)
	{
		$verifyCode = Util::randNumber(6);

		Email::sendWithTemplateToUser($user,
			Email::SPENDR_MOBILE_TEMPLATE_EMAIL_ADDRESS_VERIFICATION,
			array(
				'name' => $user->getName(),
				'verify_code' => $verifyCode,
				'username' => $user->getUsername(),
                'email' => $user->getEmail(),
			),
			CardProgram::spendr()
		);

		Data::set('register_email_verify_code_' . $user->getId(), $verifyCode, false, 600);
	}

    protected function verifyUSPhone($phone)
    {
        if (
            (strlen($phone) === 10 && !Util::startsWith($phone, '+'))
            || (strlen($phone) === 11 && Util::startsWith($phone, '1'))
            || (strlen($phone) === 12 && Util::startsWith($phone, '+1'))
        ) {
            return true;
        }
        return false;
    }

    protected function getEmailUser(Request $request, $roleParamName = 'loginRole', $isMobile = false)
    {
        $roles = SpendrBundle::getMobileLoginRoles();
        $role = $request->get($roleParamName);
        $message = $isMobile ? 'phone number' : 'email address';
        if ($role) {
            if (!in_array($role, $roles)) {
                return new FailedResponse("Your {$message} is currently not associated to {$role}");
            }
            $roles = [$role];
        }
        $query = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', $roles);
        if ($isMobile) {
            $mobile = $request->get('mobile');
            $mobile = Util::formatManualInputPhoneNumber($mobile);
            $mobile = Util::formatFullPhone($mobile);
            $query->andWhere('u.mobilephone = :phone')
                ->setParameter('phone', $mobile);
        } else {
            $email = $request->get('email');
            if (!$email) {
                return new FailedResponse('Invalid email address');
            }
            $query->andWhere('u.email = :email')
                ->setParameter('email', $email);
        }
        $us = $query->setMaxResults(2)
            ->getQuery()
            ->getResult();
        if (!$us || !count($us)) {
            return new FailedResponse("Your {$message} is currently not associated to a Spendr account.");
        }
        if (count($us) > 1) {
            $us = array_values(array_filter($us, function (User $u) {
                return $u->inTeams(SpendrBundle::getConsumerRoles()) || (
                    $u->inTeams(SpendrBundle::getMerchantEmployeeMobileLoginRoles())
                    && strtolower($u->getStatus()) === User::STATUS_ACTIVE
                );
            }));
            if (!$us) {
                return new FailedResponse("Your {$message} is currently not associated to a Spendr account.");
            }
            if (count($us) > 1) {
                $teams = [];
                /** @var User $u */
                foreach ($us as $u) {
                    $spendrRoles = array_intersect($roles, $u->getTeams()->map(function (Role $role) {
                        return $role->getName();
                    })->toArray());
                    $teams = array_merge($teams, $spendrRoles);
                }
                return new FailedResponse('Multiple members found! Please contact support.', [
                    'type' => 'multiple_user',
                    'teams' => array_unique($teams)
                ]);
            }
        }

        return $us[0];
    }

    protected function sendAuthSMS($user, $transaction)
    {
        if ($transaction->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
            return new FailedResponse('Transaction has been completed or canceled!', [
                'tokenError' => true
            ]);
        }
        if ($user->isLocked()) {
            return new FailedResponse('Your account has been locked. Please try again later or contact support.');
        }
        $error = null;
        $status = $user->getStatus();
        if ($status === User::STATUS_CLOSED) {
            $error = 'This account is '.$status.'! '.$user->getCloseReason();
        } else {
            if ($status === User::STATUS_BANNED) {
                $error = 'This account is '.$status."! Banned Reason:\n".$user->getBannedReason();
            } else {
                if ($status === User::STATUS_INACTIVE) {
                    $error = 'Your account has been deactivated, please contact support.';
                }
            }
        }
        if ($error) {
            return new FailedResponse($error);
        }
        if (Util::em()->getRepository(SpendrPayAuthorization::class)->findOneBy([
            'user' => $user,
            'merchant' => $transaction->getMerchant(),
            'expiredAt' => null
        ])) {
            return new FailedResponse('You have authorized Spendr Pay already.');
        }

        UserPinService::create($user, UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);

        $data = $user->toFinalApiArray();
        $userCard = UserService::getDummyCard($user);
        $data['cards'] = $userCard ? [$userCard->toApiArray()] : [];
        $data['cardInfo'] = $userCard ? [
            $userCard->getCardProgram()->getName(),
            $userCard->getCardProgram()->getPlatform()->getName(),
        ] : [];

        return new SuccessResponse($data);
    }

    protected function checkTransaction(Request $request)
    {
        $txnToken = $request->get('txnToken');
        if (!$txnToken) {
            return new FailedResponse('Invalid online checkout token.', [
                'tokenError' => true
            ]);
        }
        $ts = Util::em()->getRepository(Transaction::class)
            ->createQueryBuilder('t')
            ->where('t.token = :token')
            ->andWhere('t.type = :type')
            ->andWhere('t.dateTime <= :now')
            ->andWhere('t.dateTime > :endTime')
            ->setParameter('token', $txnToken)
            ->setParameter('type', TransactionType::get(TransactionType::NAME_CHECKOUT_ONLINE))
            ->setParameter('now', Carbon::now())
            ->setParameter('endTime', Carbon::now()->subMinutes(TransactionService::onlineCheckoutExpiredTime))
            ->getQuery()
            ->getResult();
        if (!$ts || !count($ts)) {
            return new FailedResponse('Unknown transaction or outdated!', [
                'tokenError' => true
            ]);
        }
        /** @var Transaction $transaction */
        $transaction = $ts[0];
        if ($transaction->getStatus()->getName() !== TransactionStatus::NAME_PENDING) {
            return new FailedResponse('Transaction has been completed or canceled!', [
                'tokenError' => true
            ]);
        }
        return $transaction;
    }
}
