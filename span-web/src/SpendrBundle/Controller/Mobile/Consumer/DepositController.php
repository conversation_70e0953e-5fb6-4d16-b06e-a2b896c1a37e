<?php

namespace SpendrB<PERSON>le\Controller\Mobile\Consumer;

use Carbon\Carbon;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SpendrBundle\Services\LoadService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use SpendrBundle\Services\UserService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\PlaidService;

class DepositController extends BaseController
{
	use DepositControllerTrait;
    /**
     * @Route("/spendr/m/consumer/bankCard/list")
     *
     * @param Request $request
     * @return SuccessResponse
     */
    public function list(Request $request)
    {
        return new SuccessResponse(ConsumerService::getUserBankList(
            $this->user,
            $request->get('cardType'),
            $request->get('pending', true)
        ));
    }

	/**
	 * @Route("/spendr/m/consumer/bankCard/del/{userCard}")
	 * @param UserCard $userCard
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \Doctrine\ORM\OptimisticLockException
	 */
    public function del(UserCard $userCard) {
        if (SpendrBundle::isSpendrAdminLoggedInAs()) {
            return new DeniedResponse();
        }
        return PlaidService::deleteCard($userCard);
    }

	/**
	 * @Route("/spendr/m/consumer/deposit/init")
	 * @param Request $request
	 *
	 * @return FailedResponse|SuccessResponse
	 * @throws \PortalBundle\Exception\PortalException
	 */
    public function init(Request $request)
    {
        if (SpendrBundle::isSpendrAdminLoggedInAs()) {
            return new DeniedResponse();
        }
        if ($this->cardProgram && !$this->cardProgram->isSpendr()) {
            return new FailedResponse('Loading is not available now. Please wait a moment or contact support.');
        }
        $dummy = UserService::getDummyCard($this->user);
        $card = $dummy->getCard();
        $min = $card->getMinLoad() ?? 10;
        $max = $card->getMaxLoad() ?? 1000;
        $default = 0;

        $isPrefund = false;
        $showAmountRange = true;

        $currentLoadStatus = ConsumerService::getConsumerLoadStatus($this->user);
        if ($currentLoadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
            $isPrefund = true;
        }

        // todo: If a user moves from an unrestricted area to a restricted area and his balance is negative, do we need to provide him with a "make whole" entrance?
        $needMakeWholeAmount = ConsumerService::needMakeWhole($this->user, $dummy, 'amount');
        if ($needMakeWholeAmount) {
            $default = abs($needMakeWholeAmount) / 100;
            // $min = 0.01;
            // $max = $default;
            $showAmountRange = false;

            $isPrefund = true;
        }

        $canDeposit = ConsumerService::canDeposit($this->user, $dummy);

        return new SuccessResponse([
            'init' => $default,
            'min' => $min,
            'max' => $max,
            'isPrefund' => $isPrefund,
            'needMakeWhole' => $needMakeWholeAmount > 0 ? true : false,
            'canDeposit' => $canDeposit,
            'showAmountRange' => $showAmountRange
        ]);
    }

	/**
	 * @Route("/spendr/m/consumer/deposit/confirm")
	 *
	 * @param Request $request
	 * @return SuccessResponse|FailedResponse
	 * @throws \PortalBundle\Exception\PortalException
	 * @throws \Throwable
	 */
    public function confirm(Request $request)
    {   
        $uc = UserCard::find($request->get('bankId'));
        $loadAmount = Money::normalizeAmount($request->get('loadAmount'), 'USD');
        return self::traitLoad($uc, $loadAmount, $this->user);
    }

	/**
	 * @Route("/spendr/m/consumer/deposit/status/{loadId}", methods={"GET"})
	 * @param Request $request
	 * @param $loadId
	 * @return FailedResponse|SuccessResponse
	 */
	public function statusAction (Request $request, $loadId) {
		$load = UserCardLoad::find($loadId);

		if (!$load) {
			return new FailedResponse('Invalid load.');
		}

		$user = $load->getUserCard()->getUser();
		if (!$user || $user->getId() !== $this->user->getId()) {
			return new FailedResponse('No permission.');
		}

		$data = $load->toAppApiArray(false, true);
		if (LoadService::isInstantLoad($load) && $load->getStatus() === UserCardLoad::LOAD_STATUS_RECEIVED) {
		    $data['status'] = 'initiated';
		    $data['reason'] = 'Deposit Complete';
        }

		return new SuccessResponse($data);
	}

	/**
	 * @Route("/spendr/m/consumer/deposit/list")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 * @throws \PortalBundle\Exception\PortalException
	 */
    public function indexAction(Request $request)
    {
        $dummy = UserService::getDummyCard($this->user);
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 6);
        $date = $request->get('date');
        $endDate = $request->get('endDate') ?? $date;

        $expr = Util::expr();
        $query = $this->em->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->where(Util::expr()->eq('uc.id', ':ucId'))
            ->andWhere($expr->in('ucl.loadStatus', ':status'))
            ->andWhere($expr->isNotNull('ucl.transactionNo'))
			->andWhere($expr->notLike('ucl.meta', ':rollbackLoadMeta'))
			->setParameter('rollbackLoadMeta', '%' . LoadService::LOAD_TYPE_ROLLBACK . '":true%')
			->andWhere($expr->notLike('ucl.meta', ':rollbackMeta'))
			->setParameter('rollbackMeta', '%Spendr rollback":true%')
            ->setParameter('ucId', $dummy->getId())
            ->setParameter('status', [
                UserCardLoad::LOAD_STATUS_LOADED,
                UserCardLoad::LOAD_STATUS_RECEIVED,
                UserCardLoad::LOAD_STATUS_INITIATED,
                UserCardLoad::LOAD_STATUS_ERROR
            ]);

        if ($date) {
            $start = Util::timeUTC($date);
            $end = Util::timeUTC($endDate)->addDay();
            $query->andWhere('ucl.initializedAt >= :__startTime')
                ->setParameter('__startTime', $start);
            $query->andWhere('ucl.initializedAt <= :__endTime')
                ->setParameter('__endTime', $end);
        }

        $total = (int)(
        (clone $query)->select('count(distinct ucl)')
            ->getQuery()
            ->getSingleScalarResult()
        );

        $result = [
            'total' => $total,
            'currentBalance' => null,
            'data' => [],
        ];

        $all = $query->orderBy('ucl.id', 'desc')
            ->setFirstResult(($page - 1) * $pageSize)
            ->setMaxResults($pageSize)
            ->getQuery()
            ->getResult();

        /** @var UserCardLoad $item */
        foreach ($all as $item) {
			$newItem = $item->toAppApiArray(true);
			$createdAt = Util::formatDateTime(
				$item->getCreatedAt(),
				Util::DATE_FORMAT_ISO_DATE_TIME,
				$this->tz
			);
			$loadAt = Util::formatDateTime(
				$item->getLoadAt(),
				Util::DATE_FORMAT_ISO_DATE_TIME,
				$this->tz
			);

			$date = Util::formatDateTime(
				$item->getCreatedAt(),
				Util::DATE_TIME_FORMAT,
				$this->tz
			);

        	$newItem['date'] = $date;
        	$newItem['createdAt'] = $createdAt;
        	$newItem['loadAt'] = $loadAt;
            $result['data'][] = $newItem;
        }

        $balance = $dummy ? $dummy->getBalance() : 0;
        $result['currentBalance'] = [
            'balance' => Money::format($balance, 'USD', false),
            'balanceAmount' => $balance,
            'name' => $this->user->getName(),
        ];

        return new SuccessResponse($result);
    }
}
