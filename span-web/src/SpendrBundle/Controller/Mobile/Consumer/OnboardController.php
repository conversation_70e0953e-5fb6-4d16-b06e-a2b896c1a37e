<?php


namespace SpendrBundle\Controller\Mobile\Consumer;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use SalexUserBundle\Entity\UserConfig;
use SpendrBundle\Entity\BankCardBlacklist;
use SpendrBundle\Entity\SpendrReward;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\LoadService;
use SpendrBundle\Services\PlaidService;
use SpendrBundle\Services\UserCardService;
use SpendrBundle\Services\YodleeErrorRecordService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Services\SpendrYodleeService;
use CoreBundle\Services\SSLEncryptionService;
use TomorrowIdeas\Plaid\PlaidRequestException;
use SpendrBundle\Services\SlackService;
use Ramsey\Uuid\Uuid;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use Symfony\Component\Routing\Annotation\Route;

class OnboardController extends BaseController
{
    public $protected = false;
    private function getUserFromBankAccountId($request)
    {
        //Get user data based on query parameter (bankAccountId)
        $rs = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.config', 'c')
            ->where('c.bankAccountId = :bankAccountId')
            ->setParameter('bankAccountId', $request->get('bankAccountId'))
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if (count($rs) < 1) {
            return false;
        }

        return end($rs);
    }

	/**
	 * @Route("/spendr/m/consumer/onboard/alternate-bank/{bankAccountId}")
	 * @param Request $request
	 * @return Response
	 */
    public function alternateBank(Request $request)
    {
        $user = $this->getUserFromBankAccountId($request);
        if (!$user) {
            return $this->render('@Spendr/Onboard/error.html.twig');
        }

        $config = $user->ensureConfig();

        return $this->render('@Spendr/Onboard/alternateBank.html.twig', ['user' => $user, 'config' => $config]);
    }

	/**
	 * @Route("/spendr/m/consumer/onboard/submit-account-details")
	 * @param Request $request
	 * @return Response
	 */
    public function submitBankDetails(Request $request)
    {
        $user = User::findByBankAccountId($request->get('bankAccountId'));

        $config = $user->ensureConfig();

        if (Util::checkRoutingNumber($request->get('bankRouting')) === false)
        {
            return $this->render('@Spendr/Onboard/alternateBank_routingFailure.html.twig', ['user' => $user, 'config' => $config]);
        }

        $bank = (object)[];
        $bank->name = $request->get('bankName');
        $bank->aba = $request->get('bankAba');
        $bank->routing = $request->get('bankRouting');

        return $this->render('@Spendr/Onboard/bankDetails.html.twig', ['user' => $user, 'config' => $config, 'bank' => $bank]);
    }


	/**
	 * @Route("/spendr/m/consumer/onboard/submit-store-account-details")
	 * @param Request $request
	 * @return Response
	 * @throws NoResultException
	 * @throws NonUniqueResultException
	 */
    public function storeBankDetails(Request $request)
    {
        $user = User::findByBankAccountId($request->get('bankAccountId', '2af9dd79-d0c2-44ce-ae67-f769369a2124'));

        $config = $user->ensureConfig();

        //encrypt data
        $encryptedAcctNum = SSLEncryptionService::encrypt($request->get('bankAba'));
        $encryptedRouting = SSLEncryptionService::encrypt($request->get('bankRouting'));

        if ($this->em->getRepository(UserCard::class)->createQueryBuilder('uc')
            ->join('uc.user', 'u')
            ->where('uc.accountNumber = :accountNumber')
            ->andWhere('u.id = :userId')
            ->andWhere('uc.dbaNo= :dbaNo')
            ->setParameter('userId', $user->getId())
            ->setParameter('accountNumber', $encryptedAcctNum)
            ->setParameter('dbaNo',$encryptedRouting)
            ->select('count(distinct uc)')
            ->getQuery()
            ->getSingleScalarResult()) {
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }

        $uc = new userCard();
        $uc->setUser($user)
            ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
            ->setBankName($request->get('bankName'))
            ->setAccountNumber($encryptedAcctNum)
            ->setRoutingNumber($encryptedRouting)
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setIssued(true)
            ->setInitializedAt(new \DateTime())
            ->setCurrency('USD')
            ->setType(UserCard::LL_TYPE_MANUAL)
            ->persist();

        //Slack notification:
        SlackService::tada($user->getSignature() . ' has completed the add bank flow!', [
            'Bank Account Id' => $config->getBankAccountId(),
            'Method' => 'Manual'
        ]);
        return new SuccessResponse('noFullAccountNumber', ['error' => 'none'], 200);
    }

//	/**
//	 * @Route("/spendr/m/consumer/onboard/submit-verified-account-details")
//	 * @param Request $request
//	 * @return Response
//	 * @throws NoResultException
//	 * @throws NonUniqueResultException
//	 */
    public function submitBankVerifiedDetails(Request $request)
    {
        // Here is Plaid logic.
        if ($request->get('cardType') === 'plaid') {
            $bankAccountId = $request->get('bankAccountId');
            $publicToken = $request->get('publicToken');
            $user = User::findByBankAccountId($bankAccountId);
            $config = $user->ensureConfig();
            $token = PlaidService::exchangeAccessToken($publicToken);
            if (!$token) {
                return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
            }
            $info = PlaidService::getAccountInformation($token->access_token);
            if (!isset($info->accounts) || !isset($info->numbers)) {
                SlackService::warning($user->getSignature() . ' add bank error: The bank account they were attempting to link did not have an account field or the JSON response from Plaid was unexpected.');
                return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
            }
            $institutionId = $info->item->institution_id;
            $itemId = $info->item->item_id;
            $banks = [];
            // Handle all the selected balance accounts data.
            foreach ($info->accounts as $item) {
                $bank = [];
                $bank['account_id'] = $item->account_id;
                $bank['bank_name'] = $item->name;
                $bank['mask'] = $item->mask;
                $account = array_first($info->numbers->ach, function ($num) use ($item) {
                    return $num->account_id == $item->account_id;
                });
                if ($account) {
                    $bank['account_num'] = $account->account;
                    $bank['routing_num'] = $account->routing;
                    $bank['encoded_account_num'] = SSLEncryptionService::encrypt($account->account);
                    $bank['encoded_routing_num'] = SSLEncryptionService::encrypt($account->routing);
                }
                if ($bank['account_id'] && $bank['bank_name'] && $bank['account_num'] && $bank['routing_num']) {
                    array_push($banks, $bank);
                }
            }
            if (!count($banks)) {
                SlackService::warning($user->getSignature() . ' add bank error: The bank account they were attempting to link did not return a full account number.');
                return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
            }

            $existed = 0;
            $ucs = $this->em->getRepository(UserCard::class)
                ->createQueryBuilder('uc')
                ->join('uc.card', 'cpct')
                ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
                ->setParameter('cardProgram', CardProgram::spendr())
                ->join('uc.user', 'u')
                ->andWhere('uc.status = :status')
                ->andWhere('u.id = :userId')
                ->andWhere('uc.type != :type')
                ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
                ->setParameter('userId', $user->getId())
                ->setParameter('status', UserCard::STATUS_ACTIVE)
                ->getQuery()
                ->getResult();
            foreach ($banks as $bank) {
                $has = false;
                foreach ($ucs as $uc) {
                    if ($uc->getAccountNumber() && $bank['account_num'] === SSLEncryptionService::tryToDecrypt($uc->getAccountNumber())) {
                        $has = true;
                        break;
                    }
                }
                if ($has) {
                    $existed += 1;
                    continue;
                }
                //Store in new card
                $uc = new userCard();
                $uc->setUser($user)
                    ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
                    ->setBankName($bank['bank_name'])
                    ->setAccountNumber($bank['encoded_account_num'])
                    ->setRoutingNumber($bank['encoded_routing_num'])
                    ->setStatus(UserCard::STATUS_ACTIVE)
                    ->setIssued(true)
                    ->setInitializedAt(new \DateTime())
                    ->setCurrency('USD')
                    ->setType(UserCard::LL_TYPE_PLAID)
                    ->setPlaidAccessToken($token->access_token)
                    ->setMeta(Util::j2s([
                        'account_id' => $bank['account_id'],
                        'mask' => $bank['mask'],
                        'institution_id' => $institutionId,
                        'item_id' => $itemId
                    ]))
                    ->persist();

                //Slack notification:
                SlackService::tada($user->getSignature() . ' has completed the add bank flow!', [
                    'Bank Account ID' => $config->getBankAccountId(),
                    'Method' => 'Plaid'
                ]);
            }
            if (count($banks) == $existed) {
                SlackService::warning($user->getSignature() . ' add bank error: The bank accounts they were attempting to link are all exists now.');
                return new FailedResponse('noFullAccountNumber', [
                    'error' => 'noFullAccountNumber',
                    'item_id' => $itemId
                ], 200);
            }
            return new SuccessResponse('noFullAccountNumber', ['error' => 'none', 'info' => $info, 'banks' => $banks], 200);
        }

        // Here is Yodlee logic.
		$bankAccountId = $request->get('bankAccountId');
		$providerAcctId = $request->get('providerAcctId');
		$accountId = $request->get('accountId');
        // Initialize user variables and update register step.
        $user = User::findByBankAccountId($bankAccountId);
        $config = $user->ensureConfig();
        // $user->setRegisterStep(RegisterStep::ACTIVE)
        //     ->persist();

        // Get data
        $token = SpendrYodleeService::getYodleeToken($bankAccountId);
        $data = SpendrYodleeService::getYodleeBankingDetails($token, $providerAcctId, $accountId);
        if (!array_key_exists("account", $data)) {
            SlackService::warning($user->getSignature() . ' add bank error: The bank account they were attempting to link did not have an account field or the JSON response from Yodlee was unexpected.');
            YodleeErrorRecordService::saveRecord(
            	$user,
				$providerAcctId,
				$accountId,
				YodleeErrorRecordService::REASON_MISSING_ACCOUNT_FIELD,
			);
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }

        if (!array_key_exists("fullAccountNumberList", $data["account"][0])) {
            SlackService::warning($user->getSignature() . ' add bank error: The bank account they were attempting to link did not return a full account number.');
			YodleeErrorRecordService::saveRecord(
				$user,
				$providerAcctId,
				$accountId,
				YodleeErrorRecordService::REASON_MISSING_FULL_ACCOUNT_NUMBER_LIST_FIELD,
			);
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }

        if (!array_key_exists("bankTransferCode", $data["account"][0])) {
            SlackService::warning($user->getSignature() . ' add bank error: The bank account they were attempting to link did not return a bank transfer code.');
			YodleeErrorRecordService::saveRecord(
				$user,
				$providerAcctId,
				$accountId,
				YodleeErrorRecordService::REASON_MISSING_BANK_TRANSFER_CODE_FIELD,
			);
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }

        $acctNum = $data["account"][0]['fullAccountNumberList']['paymentAccountNumber'];
        $routingNum = $data["account"][0]["bankTransferCode"][0]["id"];
        $bankName = $data["account"][0]["providerName"];

        //encrypt data
        $encryptedAcctNum = SSLEncryptionService::encrypt($acctNum);
        $encryptedRouting = SSLEncryptionService::encrypt($routingNum);

        // Is this to confirm whether the bank already exists? If yes, this judgment is invalid
        if ($this->em->getRepository(UserCard::class)->createQueryBuilder('uc')
              ->join('uc.user', 'u')
              ->where('uc.accountNumber = :accountNumber')
              ->andWhere('u.id = :userId')
              ->andWhere('uc.dbaNo= :dbaNo')
              ->setParameter('userId', $user->getId())
              ->setParameter('accountNumber', $encryptedAcctNum)
              ->setParameter('dbaNo',$encryptedRouting)
              ->select('count(distinct uc)')
              ->getQuery()
              ->getSingleScalarResult()) {
            return new FailedResponse('noFullAccountNumber', ['error' => 'noFullAccountNumber'], 200);
        }
        //Store in new card
        $uc = new userCard();
        $uc->setUser($user)
            ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
            ->setBankName($bankName)
            ->setAccountNumber($encryptedAcctNum)
            ->setRoutingNumber($encryptedRouting)
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setIssued(true)
            ->setInitializedAt(new \DateTime())
            ->setCurrency('USD')
            ->setAccountId($request->get('accountId'))
            ->setproviderAccountId($request->get('providerAcctId'))
            ->setType(UserCard::LL_TYPE_YODLEE)
            ->persist();

        //Slack notification:
        SlackService::tada($user->getSignature() . ' has completed the add bank flow!', [
            'Bank Account ID' => $config->getBankAccountId(),
            'Method' => 'Yodlee'
        ]);

        return new SuccessResponse('noFullAccountNumber', ['error' => 'none'], 200);
    }

	/**
	 * @Route("/spendr/m/consumer/onboard/noFullAccountNumber/{bankAccountId}")
	 * @param Request $request
	 * @return Response
	 */
    public function tooManyAccounts(Request $request)
    {
        $config = $request->query->all();
        $config['bankAccountId'] =  $request->get('bankAccountId');
        $redirect = $request->get('redirect') ? $request->get('redirect') : '/spendr/consumer/deposit';
        $config['redirect'] = SpendrBundle::getAppDomain() . '/static/spendr/web/#' . $redirect;
        return $this->render('@Spendr/Onboard/result.html.twig', ["isSuccess" => false, "config" => $config]);
    }

    /**
     * @Route("/spendr/m/consumer/onboard/error")
     * @param Request $request
     *
     * @return Response
	 */
    public function error(Request $request)
    {
        return $this->render('@Spendr/Onboard/error.html.twig');
    }

    /**
     * @Route("/spendr/m/consumer/onboard/success")
     * @param Request $request
     *
     * @return Response
	 */
    public function success(Request $request)
    {
        $config = $request->query->all();
        $redirect = $request->get('redirect') ? $request->get('redirect') : '/spendr/consumer/deposit';
        $config['redirect'] = SpendrBundle::getAppDomain() . '/static/spendr/web/#' . $redirect;
        return $this->render('@Spendr/Onboard/result.html.twig', ['config' => $config, 'isSuccess' => true]);
    }

	/**
	 * @Route("/spendr/m/consumer/onboard/{bankAccountId}")
	 * @param Request $request
	 * @return Response
	 */
    public function index(Request $request)
    {
         $user = $this->getUserFromBankAccountId($request);

         if (!$user) {
             return $this->render('@Spendr/Onboard/error.html.twig');
         }

         $config = $user->ensureConfig();

        // Here will jump to plaid page.
        if ($request->get('cardType') === 'plaid') {
            $accessToken = null;
            if ($request->get('bankId')) {
                $uc = UserCard::find($request->get('bankId'));
                if ($uc) {
                    $accessToken = $uc->getPlaidAccessToken();
                }
            }
            $webhook = PlaidService::getConsumerWebhook() . $user->getId();
            $sameDay = !!Data::get('spendr_link_sameday_' . $user->getId());
            $token = PlaidService::curlLinkToken(
                $request->get('bankAccountId'),
                null,
                $accessToken,
                $webhook,
                $sameDay
            );
            $token->bankAccountId = $request->get('bankAccountId');
            $token->refresh = $request->get('refresh', false);
            if (!$token || !isset($token->link_token)) {
                return $this->render('@Spendr/Onboard/error.html.twig');
            }
            return $this->render('@Spendr/Onboard/plaid.html.twig', ['token' => $token]);
        }

        // Here will jump to Yodlee page.
        $token = SpendrYodleeService::getYodleeToken($config->getBankAccountId());

         if (Util::isLive())
         {
             return $this->render('@Spendr/Onboard/index-prod.html.twig', ['user' => $user, 'config' => $config, 'token' => $token]);
         }

         return $this->render('@Spendr/Onboard/index.html.twig', ['user' => $user, 'config' => $config, 'token' => $token]);
    }

	/**
	 * @Route("/spendr/m/consumer/addBank")
	 * @param Request $request
	 * @return Response
	 * @throws \Exception
	 */
    public function terms(Request $request)
    {
        $ut = $this->getApiUserToken();
        if (!$ut) {
            return $this->render('@Spendr/Onboard/error.html.twig');
        }
        $userId = $ut->decodeUserId();
        if (!$userId) {
            return $this->render('@Spendr/Onboard/error.html.twig');
        }
        $user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);
        if (!$user) {
            return $this->render('@Spendr/Onboard/error.html.twig');
        }

        $config = $user->ensureConfig();

        if ($config && $config->getBankAccountId()) {
          $accountId = $config->getBankAccountId();
        } else {
          $accountId = Uuid::uuid4();
          $config->setBankAccountId($accountId)
                  ->setPlatform(UserConfig::PLATFORM_SPENDR)
                  ->setReferCode(Uuid::uuid4())
                  ->persist();
        }

        // Here will jump to plaid page.
        if ($request->get('cardType') === 'plaid') {
            $accessToken = null;
            if ($request->get('bankId')) {
                $uc = UserCard::find($request->get('bankId'));
                if ($uc) {
                    $accessToken = $uc->getPlaidAccessToken();
                }
            }
            $webhook = PlaidService::getConsumerWebhook() . $user->getId();
            $sameDay = !!Data::get('spendr_link_sameday_' . $user->getId());
            $token = PlaidService::curlLinkToken(
                $accountId,
                null,
                $accessToken,
                $webhook,
                $sameDay
            );
            $token->bankAccountId = $accountId;
            $token->refresh = $request->get('refresh', false);
            if (!$token || !isset($token->link_token)) {
                return $this->render('@Spendr/Onboard/error.html.twig');
            }
            return $this->render('@Spendr/Onboard/plaid.html.twig', ['token' => $token, 'redirect' => $request->get('redirect')]);
        }

        // Here will jump to Yodlee page.
        $token = SpendrYodleeService::getYodleeToken($accountId);

        return $this->render('@Spendr/Onboard/terms.html.twig', ['user' => $user, 'config' => $config, 'token' => $token]);
    }

    /**
     * @Route("/spendr/m/consumer/plaid/create-link-token")
     * @param Request $request
     * @return FailedResponse|SuccessResponse|Response|null
     * @throws \Exception
     */
    public function createLinkToken(Request $request)
    {
        $ut = $this->getApiUserToken();
        if (!$ut) {
            return new FailedResponse('Unknown user!');
        }
        $userId = $ut->decodeUserId();
        $user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);
        if (!$user) {
            return new FailedResponse('Unknown user!');
        }

        $config = $user->ensureConfig();

        if ($config && $config->getBankAccountId()) {
            $accountId = $config->getBankAccountId();
        } else {
            $accountId = Uuid::uuid4();
            $config->setBankAccountId($accountId)
                ->setPlatform(UserConfig::PLATFORM_SPENDR)
                ->setReferCode(Uuid::uuid4())
                ->persist();
        }
        $platform = $request->get('platform');
        $accessToken = null;
        if ($request->get('bankId')) {
            $uc = UserCard::find($request->get('bankId'));
            if ($uc) {
                if (Util::meta($uc, 'verification_status') === PlaidService::VERIFY_STATUS_PENDING) {
                    return new FailedResponse('Your bank account is pending automated micro-deposits verification. It will be active automatically when verified.');
                }
                $accessToken = $uc->getPlaidAccessToken();
            }
        }
        $webhook = PlaidService::getConsumerWebhook() . $userId;
        $sameDay = !!Data::get('spendr_link_sameday_' . $userId);
        $token = PlaidService::curlLinkToken(
            $accountId,
            $platform,
            $accessToken,
            $webhook,
            $sameDay
        );
        if (!$token || !isset($token->link_token)) {
            return new FailedResponse('Failed to create link token.', [
                'account_id' => $accountId,
                'token_info' => $token,
                'platform' => $platform,
                'reason' => 'Invalid credentials'
            ]);
        }
        $token->bankAccountId = $accountId;

        return new SuccessResponse((array)$token, 'Create link token success.');
    }

    /**
     * @Route("/spendr/m/consumer/plaid/exchange-access-token")
     * @param Request $request
     * @return FailedResponse|SuccessResponse|Response
     * @throws \Exception
     */
    public function exchangeAccessToken(Request $request)
    {
        $bankAccountId = $request->get('bankAccountId');
        $publicToken = $request->get('publicToken');
        $user = User::findByBankAccountId($bankAccountId);
        if (!$user) {
            return new FailedResponse('Unknown account!');
        }
        $token = PlaidService::exchangeAccessToken($publicToken);
        if (!$token) {
            return new FailedResponse('Plaid can not exchange the token. Please try again.', [
                'reason' => 'Invalid credentials',
            ]);
        }
        // If the bank account is manually linked, we should save the account_id and access_token
        $account = $request->get('account');
        if ($account && is_string($account)) $account = (array)json_decode($account);
        if ($account && $account['verification_status']) {
            return $this->handlePlaidManualLink($user, $token, $account, $request->get('bankId'));
        }

        // Save authorized bank account
        $info = PlaidService::getAccountInformation($token->access_token);
        if (!$info || !isset($info->accounts) || !isset($info->numbers)) {
            return new FailedResponse('Plaid can not get bank account info. Please try again.', [
                'reason' => 'No account',
            ]);
        }
        $institutionId = $info->item->institution_id;
        $itemId = $info->item->item_id;
        $banks = [];
        $disabledRouting = 0;
        // Handle all the selected balance accounts data.
        foreach ($info->accounts as $item) {
            if ($item->type === 'credit') continue;
            $bank = [];
            $bank['account_id'] = $item->account_id;
            $bank['bank_name'] = $item->name;
            $bank['mask'] = $item->mask;
            $bank['official_name'] = $item->official_name;
            $bank['subtype'] = $item->subtype;
            $bank['type'] = $item->type;
            if (strlen($bank['bank_name']) > 64) {
                $bank['bank_name'] = $bank['subtype'] . ' ' . $bank['mask'];
            }
            $account = array_first($info->numbers->ach, function ($num) use ($item) {
                return $num->account_id == $item->account_id;
            });
            if ($account) {
                $bank['account_num'] = $account->account;
                $bank['routing_num'] = $account->routing;
                $bank['encoded_account_num'] = SSLEncryptionService::encrypt($account->account);
                $bank['encoded_routing_num'] = SSLEncryptionService::encrypt($account->routing);
                $bank['wire_routing'] = $account->wire_routing;
            }
            if ($bank['account_id'] && $bank['bank_name'] && $bank['account_num'] && $bank['routing_num']) {
                if (UserCardService::isNonDepositedRouting($account->routing)) {
                    $disabledRouting += 1;
                } else {
                    array_push($banks, $bank);
                }
            }
        }
        if ($disabledRouting === count($info->accounts)) {
            $platformMeta = Util::meta(Platform::spendr());
            $phone = $platformMeta['customerSupportPhone'] ?? null;
            $email = $platformMeta['customerSupportEmail'] ?? null;
            return new FailedResponse("The bank you're trying to link is no longer supported with Spendr for reasons outside of our control. Please try linking a different bank account and reach out to Spendr Support at {$phone} or {$email} with any questions or concerns.", [
                'reason' => 'Not supported routing',
            ]);
        }
        if (!count($banks)) {
            return new FailedResponse('Add bank error: The bank account you\'re attempting to link did not return a full account number.', [
                'error' => 'noFullAccountNumber',
                'info' => $info,
                'banks' => $banks,
                'reason' => 'No account',
            ], 200);
        }
        $existed = 0;
        $properties = [];
        $ucs = $this->em->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->join('uc.user', 'u')
            ->join('u.teams', 't')
            ->andWhere('uc.status = :status')
            ->andWhere(Util::expr()->in('t.name', ':roles'))
            ->andWhere('u.id = :userId')
            ->andWhere('uc.type != :type')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('userId', $user->getId())
            ->setParameter('roles', SpendrBundle::getConsumerRoles())
            ->setParameter('status', UserCard::STATUS_ACTIVE)
            ->getQuery()
            ->getResult();
        // First link bank account and bank primary phone is not exists
        /** @var SpendrReward $config */
        $config = SpendrReward::findOneByType(UserCardLoad::EARN_TYPE_BANK_LINK);
        $canEarn = $config && ConsumerService::canEarnLinkedAmount($user, $config);

        $notMatch = 0;
        $blocked = 0;
        foreach ($banks as $bank) {
            $has = false;
            foreach ($ucs as $uc) {
                if (($uc->getAccountNumber()
                        && $uc->getRoutingNumber()
                        && $bank['account_num'] === SSLEncryptionService::tryToDecrypt($uc->getAccountNumber())
                        && $bank['routing_num'] === SSLEncryptionService::tryToDecrypt($uc->getRoutingNumber()))
                    || (Util::meta($uc, 'mask') === $bank['mask']
                        && $uc->getBankName() === $bank['bank_name']
                        && Util::meta($uc, 'institution_id') === $institutionId)
                ) {
                    $has = true;
                    break;
                }
            }
            if ($has) {
                $existed += 1;
                continue;
            }
            //Store in new card
            $uc = new userCard();
            $uc->setUser($user)
                ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
                ->setBankName($bank['bank_name'])
                ->setAccountNumber($bank['encoded_account_num'])
                ->setRoutingNumber($bank['encoded_routing_num'])
                ->setStatus(UserCard::STATUS_ACTIVE)
                ->setIssued(true)
                ->setInitializedAt(new \DateTime())
                ->setCurrency('USD')
                ->setType(UserCard::LL_TYPE_PLAID)
                ->setPlaidAccessToken($token->access_token)
                ->setMeta(Util::j2s([
                    'account_id' => $bank['account_id'],
                    'mask' => $bank['mask'],
                    'routing_mask' => substr($bank['routing_num'], -4),
                    'institution_id' => $institutionId,
                    'item_id' => $itemId,
                    'subtype' => $bank['subtype']
                ]))
                ->setHash(Uuid::uuid4())
                ->persist();

            if (BankCardBlacklist::exists($bank['account_num'], $bank['routing_num'])) {
                $uc->setStatus(UserCard::STATUS_INACTIVE)->persist();
                Util::updateMeta($uc, [
                    'Issue Type' => ConsumerService::INACTIVE_TYPE_BANKCARD_BLACKLIST
                ]);
                $blocked += 1;
                $existed += 1;
                continue;
            }

            // Save identity data
            if (!Util::meta($user, 'disableIdentityMatch')) {
                $res = ConsumerService::savePlaidCardIdentityData($uc);
                if (is_bool($res)) {
                    $user->addNote('No identity was run on this user.', true, $user->getId());
                } elseif (is_string($res)) {
                    // identity not match
                    $uc->setStatus(UserCard::STATUS_INACTIVE)->persist();
                    Util::updateMeta($uc, [
                        'Issue Type' => ConsumerService::INACTIVE_TYPE_IDENTITY_NOT_MATCH
                    ]);
                    $notMatch += 1;
                    $existed += 1;
                    BrazeService::userTrack(null, [
                        'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                        'name' => 'Identity Failed',
                        'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z')
                    ]);
                    continue;
                }
            }

            //Slack notification:
            SlackService::tada($user->getSignature() . ' has completed the add bank flow!', [
                'mask' => $bank['mask'],
                'subtype' => $bank['subtype'],
                'type' => $bank['type']
            ]);
            array_push($properties, [
                'bank name' => $bank['bank_name'],
                'tail number' => $bank['mask'],
            ]);
        }
        if (count($banks) == $existed) {
            if ($blocked) {
                return new FailedResponse(
                    "Action cannot be completed. Please contact Spendr Support at: {$this->supportMsgInfo()}.",
                    [
                        'reason' => 'Bank account in blacklist',
                    ]
                );
            }
            if ($notMatch) {
                return new FailedResponse(
                    'Unable to link bank account. Spendr information does not match bank information. Please check your Spendr account details and ensure they match the bank you\'re trying to link. Please reach out to Spendr Support for assistance.',
                    [
                        'error' => 'Identity Not Match',
                        'reason' => 'Identity failed',
                    ]
                );
            }
            return new FailedResponse('The bank accounts you attempting to link have existed already.', [
                'reason' => 'Account exists',
            ]);
        }
        $bankLink = null;
        if ($canEarn && $config->getAmount()) {
            $bankLink = Money::formatAmountToNumber($config->getAmount());
            LoadService::earnLoad($user, $config);
        }

        return new SuccessResponse([
            'banks' => $properties,
            'bankLink' => $bankLink
        ]);
    }

    /**
     * @Route("/spendr/m/consumer/plaid/relink")
     * @param Request $request
     * @return Response
     */
    public function handleRelink(Request $request)
    {
        $ut = $this->getApiUserToken();
        if (!$ut) {
            return new FailedResponse('Unknown user!');
        }
        $userId = $ut->decodeUserId();
        $user = $this->em->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);
        if (!$user) {
            return new FailedResponse('Unknown user!');
        }
        if (!$request->get('bankId')) {
            return new FailedResponse('Invalid params!');
        }
        $card = UserCard::find($request->get('bankId'));
        $ucs = Util::em()->getRepository(UserCard::class)
            ->findBy([
                'plaidAccessToken' => $card->getPlaidAccessToken(),
                'type' => UserCard::LL_TYPE_PLAID
            ]);
        $accounts = null;
        try {
            $plaid = PlaidService::initPlaid();
            $accounts = $plaid->auth->get($card->getPlaidAccessToken());
            if (!$accounts || !isset($accounts->accounts) || !isset($accounts->numbers)) {
                return new FailedResponse('Plaid can not get bank account info. Please try again.', [
                    'reason' => 'No account',
                ]);
            }
        } catch (PlaidRequestException $e) {
            SlackService::warning(
                'Plaid get account info error: ' . $e->getMessage(),
                [ 'resp' => $e->getResponse() ],
            );
            return new FailedResponse('Plaid can not get bank account info. Please try again.', [
                'reason' => 'No account',
            ]);
        }
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $meta = Util::meta($uc);
            $item = array_first($accounts->accounts, function ($a) use ($meta) {
                return $a->account_id == $meta['account_id'];
            });
            $account = array_first($accounts->numbers->ach, function ($num) use ($meta) {
                return $num->account_id == $meta['account_id'];
            });
            if ($item) {
                $bankName = $item->name;
                if (strlen($bankName) > 64) {
                    $bankName = $item->subtype . ' ' . $item->mask;
                }
                $uc->setBankName($bankName);
                $meta['mask'] = $item->mask;
                if (!UserCardService::isIncorrectSubtype($uc->getId())) $meta['subtype'] = $item->subtype;
            }
            if ($account) {
                if ($account->routing !== SSLEncryptionService::tryToDecrypt($uc->getRoutingNumber())) {
                    $uc->setRoutingNumber(SSLEncryptionService::encrypt($account->routing));
                    $meta['routing_mask'] = substr($account->routing, -4);
                }
                if ($account->account !== SSLEncryptionService::tryToDecrypt($uc->getAccountNumber())) {
                    $uc->setAccountNumber(SSLEncryptionService::encrypt($account->account));
                }
            }
            if (isset($meta['error'])) unset($meta['error']);
            $uc->setMeta(Util::j2s($meta))
                ->persist();
        }

        return new SuccessResponse(null, 'Relink Successfully.');
    }

    protected function handlePlaidManualLink(User $user, $token, $account, $bankId) {
        Data::del('spendr_link_sameday_' . $user->getId(), true);
        $cards = ConsumerService::getManualCards($user);
        if ($cards && count($cards)) {
            $manual = $cards[0];
            $mask = UserCardService::getAccountNumMask(Util::meta($manual));
            return new FailedResponse("You have manually linked already. Please delete the current account({$mask}) first if you want to manually link another bank account!");
        }
        if ($account['verification_status'] == PlaidService::VERIFY_STATUS_PENDING ||
            $account['verification_status'] == PlaidService::VERIFY_STATUS_MANUAL_PENDING) {
            $uc = new userCard();
            $uc->setUser($user)
                ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
                ->setStatus(UserCard::STATUS_PENDING)
                ->setIssued(true)
                ->setCurrency('USD')
                ->setType(UserCard::LL_TYPE_PLAID)
                ->setPlaidAccessToken($token->access_token)
                ->setMeta(Util::j2s([
                    'account_id' => $account['id'],
                    'item_id' => $token->item_id,
                    'name' => $account['name'],
                    'mask' => $account['mask'],
                    'verification_status' => $account['verification_status']
                ]))
                ->setHash(Uuid::uuid4())
                ->persist();

            return new SuccessResponse([
                'manualLink' => true,
                'waiting' => true
            ]);
        } elseif ($account['verification_status'] == PlaidService::VERIFY_STATUS_MANUAL_VERIFIED) {
            if (!$bankId) {
                return new FailedResponse('Your bank account is not existed.');
            }
            $uc = UserCard::find($bankId);
            if (!$uc) {
                return new FailedResponse('Your bank account is not existed.');
            }
            $res = ConsumerService::updatePendingCard($token->access_token, $uc, $user, true);
            if (!is_bool($res)) {
                return $res;
            }
            /** @var SpendrReward $config */
            $config = SpendrReward::findOneByType(UserCardLoad::EARN_TYPE_BANK_LINK);
            $bankLink = null;
            if ($res && $config && $config->getAmount()) {
                $bankLink = Money::formatAmountToNumber($config->getAmount());
            }

            return new SuccessResponse([
                'manualLink' => true,
                'waiting' => false,
                'bankLink' => $bankLink
            ]);
        }

        return new FailedResponse('Unhandled verification status: ' . $account['verification_status']);
    }
}
