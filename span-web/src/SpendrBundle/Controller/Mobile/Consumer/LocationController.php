<?php

namespace SpendrB<PERSON>le\Controller\Mobile\Consumer;

use Clf<PERSON><PERSON>le\Entity\Common\GeoLocation;
use CoreBundle\Entity\BaseState;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use GuzzleHttp\Client;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Entity\SpendrRestrict;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\MerchantService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LocationController extends BaseController
{
	/**
	 * @Route("/spendr/m/consumer/location/list")
	 *
	 * @param Request $request
	 * @return SuccessResponse
	 */
	public function list(Request $request)
	{
		$page = (int)$request->get('page', 1);
		$pageSize = (int)$request->get('pageSize', 10);
		$latitude = (float)$request->get('latitude', '40.730610'); // New York City
		$longitude = (float)$request->get('longitude', '-73.935242');
		$radius = (float)$request->get('radius', 10);
        // change the default distance to 500 miles
        if ($radius < 500) $radius = 500;
		$keyword = $request->get('keyword');

		$expr = Util::expr();
		$query = $this->em->getRepository(Location::class)
			->createQueryBuilder('l')
			->join('l.merchant', 'm')
			->join('l.address', 'a')
			->join('a.state', 's')
			->select('l as loc, s.name as state, a as address, m.name as merchant_name,
				SQRT(
                    POW(69.1 * (a.latitude - :latitude), 2) +
                    POW(69.1 * (:longitude - a.longitude) * COS(a.latitude / 57.3), 2)
                ) AS distance
			')
			->where('l.status = :status')
            ->andWhere('m.onboardingStatus = :onboardStatus')
			->having('distance < :distance')
			->orderBy('distance')
			->setParameter('latitude', $latitude)
			->setParameter('longitude', $longitude)
			->setParameter('distance', $radius)
			->setParameter('status', Location::STATUS_ACTIVE)
            ->setParameter('onboardStatus', SpendrMerchant::ONBOARDING_STATUS_APPROVED);

		$merchantIdsForTest = MerchantService::merchantIdsForTestArray(true);
		if ($merchantIdsForTest) {
			$query->andWhere($expr->notIn('m.id', ':merchantIdsForTest'))
				->setParameter('merchantIdsForTest', $merchantIdsForTest);
		}

		if ($keyword) {
			$query->andWhere($expr->orX(
			    $expr->like('m.name', ':desc'),
			    $expr->like('l.name', ':desc'),
			    $expr->like('a.address1', ':desc'),
            ))
                ->setParameter('desc', '%' . $keyword . '%');
		}

		$rs = $query->setFirstResult(($page - 1) * $pageSize)
			->setMaxResults($pageSize)
			->getQuery()
			->getResult();

		$data = [];
		$num = 0.62; // 1km = 0.6213712m
		foreach ($rs as $r) {
			/** @var Location $loc */
			$loc = $r['loc'];
			$address = $loc->getAddress();
			$distance = round((float)$r['distance'], 2);
			$mileDistance = round((float)$r['distance'] * $num, 2);
			$data[] = [
				'id' => $loc->getId(),
				'locationName' => $loc->getName(),
				'merchantName' => $r['merchant_name'],
				'status' => $loc->getStatus(),
				'address' => $address->getShortDescription(),
				'latitude' => $address->getLatitude(),
				'longitude' => $address->getLongitude(),
				'phone' => $address->getPhone(),
				'distance' => $distance,
				'mileDistance' => $mileDistance . 'm',
                'hours' => $loc->getHours(),
                'hoursWeekend' => $loc->getHoursWeekend(),
			];
		}

		return new SuccessResponse($data);
	}

    /**
     * @Route("/spendr/m/consumer/location/restrict")
     *
     * @param Request $request
     * @return SuccessResponse|FailedResponse
     */
    public function restricted(Request $request)
    {
        if ($request->get('lat') && $request->get('lng')) {
            $state = $this->geoLocation($request->get('lat'), $request->get('lng'));
        } elseif (!$request->get('state') || !$request->get('isoCode')) {
            $state = $this->user->getState();
        } else {
            $state = BaseState::findByAbbrOrName($request->get('state'), $request->get('isoCode'));
        }
        if (!$state) {
            return new FailedResponse('Invalid parameters');
        }
        $restrict = SpendrRestrict::getByStateOrStateID($state);

		// todo: If a user moves from an unrestricted area to a restricted area and his balance is negative, do we need to provide him with a "make whole" entrance?
		$needMakeWhole = ConsumerService::needMakeWhole($this->user);
		$canDeposit = ConsumerService::canDeposit($this->user);

		$depositBtnText = 'Deposit';

        if (
            Util::meta($this->user, 'currentStateId') !== $state->getId()
        ) {
            Util::updateMeta($this->user, [
                'currentStateId' => $state->getId()
            ]);
            $this->user->addNote("Change location state to " . $state->getId() . ' which is ' . ($restrict ? 'restricted' : 'not restricted'));
        }
        if ($restrict) {
            $data = Util::meta($restrict);
            $data['deposit_alert'] = "Your State prevents depositing funds to Spendr in advance. When you pay, the exact funds are drawn directly from your linked bank. The 'Deposit' you'll see records the amount to be deducted from your bank.\n\nAny questions? Contact Support:{$this->supportMsgInfo()}.";
            $data['canDeposit'] = $canDeposit;
			$data['depositBtnText'] = $needMakeWhole ? 'Make your account whole' : $depositBtnText;
			return new SuccessResponse($data);
        }

        return new SuccessResponse([
			'canDeposit' => true,
			'depositBtnText' => $depositBtnText
		]);
    }

    private function geoLocation($lat, $lng)
    {
        try {
            $client = new Client();
            $query = [
                'latlng' => $lat . ',' . $lng,
                'key'    => Util::getKmsParameter('spendr_google_api_key')
            ];
            $response = $client->get('https://maps.googleapis.com/maps/api/geocode/json', [
                'query'   => $query,
                'headers' => [
                    'Accept' => 'application/json',
                    'Referer' => Util::host(),
                ],
            ]);
            $content = json_decode($response->getBody()->getContents(), true);
            if ($content && $content['results'] && isset($content['results'][0]['address_components'])) {
                $as = $content['results'][0]['address_components'];
                $isoCode = null;
                $state = null;
                foreach ($as as $a) {
                    if (in_array('country', $a['types'])) {
                        $isoCode = $a['short_name'];
                    } elseif (in_array('administrative_area_level_1', $a['types'])) {
                        $state = $a['short_name'];
                    }
                }
                if ($isoCode && $state) {
                    return BaseState::findByAbbrOrName($state, $isoCode);
                }
            }
            Log::warn('Spendr geo location', [
                'content' => $content,
            ]);
        } catch (\Exception $exception) {
            Log::warn('Spendr geo location', [
                'exception' => $exception->getMessage(),
            ]);
        }
        return null;
    }
}
