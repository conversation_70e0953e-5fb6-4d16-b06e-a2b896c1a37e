<?php

namespace SpendrB<PERSON>le\Controller\Mobile;

use Carbon\Carbon;
use SpendrBundle\Services\SlackService;
use Clf<PERSON><PERSON>le\Entity\Account;
use Clf<PERSON><PERSON>le\Entity\AccountStatus;
use Clf<PERSON><PERSON>le\Entity\AccountType;
use Clf<PERSON><PERSON>le\Entity\Patient;
use CoreBundle\Entity\Address;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Currency;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\IpUsage;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserPin;
use CoreBundle\Entity\UserToken;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserPinService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\OptimisticLockException;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use SpendrBundle\Entity\IdentityLog;
use SpendrBundle\Entity\PlaidIdentity;
use SpendrBundle\Entity\SpendrReferConfig;
use SpendrBundle\Entity\SpendrReferral;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ConsumerService;
use SpendrBundle\Services\SegmentService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\IDologyService;

class RegisterController extends BaseController
{
	use RegisterControllerTrait;

	public $protected = false;

    /**
     * @Route("/spendr/m/referral")
     * @param $inviterId
     * @param Request $request
     * @return Response
     */
	public function referralPage(Request $request)
    {
        $inviterId = $request->get('inviterId') ?? '';
        Data::set('spendr_user_refer:' . (Security::getClientIp(false) ?? Security::getClientIp()), $inviterId, false, 86400);
        $ua = $request->headers->get('User-Agent');
        $openUrl = SpendrBundle::getAppDomain() . '/static/spendr/web/#/spendr/consumer/sign_up?inviterId=' . $inviterId;
        if (strpos($ua, 'Android') !== false) {
            $downloadUrl = 'https://play.google.com/store/apps/details?id=com.ternitup.spendrapp&pli=1';
        } elseif (strpos($ua, 'iPhone') !== false || strpos($ua, 'iPad') !== false) {
            $downloadUrl = 'https://apps.apple.com/us/app/spendr-app/id1585769858';
        } else {
            $downloadUrl = $openUrl;
        }
        return new RedirectResponse($downloadUrl);
//        return $this->render('@Spendr/Login/referral.html.twig', compact('openUrl', 'downloadUrl'));
    }

	/**
	 * @Route("/spendr/m/country-list")
	 *
	 * @return SuccessResponse
	 */
	public function countryList()
	{
		$cardProgramId = CardProgram::spendr()->getId();
		$countryList = $this->em->getRepository(Country::class)
			->getCountriesWithCardPrograms([$cardProgramId]);

		$countries = null;
		foreach($countryList as $country) {
			$countries[] = $country->toApiArray();
		}

		return new SuccessResponse($countries);
	}

	/**
	 * @Route("/spendr/m/country/{countryId}/states", methods={"GET"})
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 */
	public function states(Request $request)
	{
		$countryId = $request->get('countryId');
		if (!$countryId) {
			return new FailedResponse('Invalid country!');
		}

		$country = $this->em->getRepository(Country::class)->find($countryId);
		if (!$country) {
			return new FailedResponse('Invalid country!');
		}

		$states = $country->getSortedStates();
		return new SuccessResponse(Util::toApiArray($states));
	}

    /**
     * @Route("/spendr/m/register")
     *
     * @param Request $request
     * @return SuccessResponse|FailedResponse
     * @throws PortalException
     * @throws OptimisticLockException
     */
	public function registerAction(Request $request)
	{
	    $transaction = null;
        // If sign up from checkout online plugin payment link
        if ($request->get('txnToken')) {
            $transaction = $this->checkTransaction($request);
            if (ClassUtils::getClass($transaction) != 'ClfBundle\Entity\Transaction') {
                return $transaction;
            }
        }
		$emailAddress = trim($request->get('email'));
		$firstName = trim($request->request->get('firstName'));
		$lastName = trim($request->request->get('lastName'));
		$password = trim($request->request->get('password'));
		$dateOfBirth = trim($request->get('dateOfBirth'));
		$mobilePhone = trim($request->get('phoneNumber'));
		$countryId = trim($request->get('countryId'));
		$stateId = trim($request->get('stateId'));
		$city = trim($request->get('city'));
		$postalCode = trim($request->get('postalCode'));
		$address = trim($request->get('address'));
		$country = Country::find($countryId);
		$state = BaseState::find($stateId);

		$cardType = CardProgramCardType::getForCardProgram(CardProgram::spendr());
		if (!$cardType) {
			return new FailedResponse('Unknown card type!');
		}

		// valid name
		if (!$firstName) {
			return new FailedResponse('Invalid first name!', $this->errorMessage('firstName', 'Invalid first name'));
		}
		if (!Util::testLatinName($firstName)) {
			$message = 'First name can only include latin characters!';
			return new FailedResponse($message, $this->errorMessage('firstName', $message));
		}
		if (!$lastName) {
			return new FailedResponse('Invalid last name!', $this->errorMessage('lastName', 'Invalid last name'));
		}
		if (!Util::testLatinName($lastName)) {
			$message = 'Last name can only include latin characters!';
			return new FailedResponse($message, $this->errorMessage('lastName', $message));
		}

		// valid email
		if (!$emailAddress) {
			return new FailedResponse('Invalid email address!', $this->errorMessage('email', 'Invalid email address!'));
		}
		if (Config::isEmailInactive($emailAddress)) {
			$message = 'There is a problem with delivering to this email address,
                please use an alternate email address.';
			return new FailedResponse($message, $this->errorMessage('email', $message));
		}
		$filtered = filter_var($emailAddress, FILTER_VALIDATE_EMAIL);
		if (!$filtered || $filtered !== $emailAddress) {
			$message = 'Invalid email address!';
			return new FailedResponse($message, $this->errorMessage('email', $message));
		}

		$supportMessage = $this->supportMsgInfo();

		// valid user
		$user = $this->getUserByEmail($emailAddress);
		if ($user) {
			$message = 'The email address has been registered before.'
				. ' Please try to login or use another email address to register,'.
				' and you can also ask support for help.' . $supportMessage;
			return new FailedResponse($message, $this->errorMessage('email', $message));
		}

		// init user
		$newUser = new User();
		$newUser->setEnabled(true);

		// valid phone number
		$rawMobilePhone = $mobilePhone;
        $usMobile = str_replace([' ', '-', '(', ')', '.'], '', $mobilePhone);
        if (Util::isLive() &&
            (!$usMobile
            || $usMobile === '+'
            || !$this->verifyUSPhone($usMobile))
        ) {
            $message = 'Invalid phone number!';
            return new FailedResponse($message, $this->errorMessage('phoneNumber', $message));
        }
        $mobilePhone = Util::formatManualInputPhoneNumber($mobilePhone);
        $mobilePhone = Util::formatFullPhone($mobilePhone);
		$users = $this->validPhoneNumber($rawMobilePhone, $mobilePhone, $mobilePhone);
		if ($users && count($users)) {
			foreach ($users as $u) {
				if (Util::neq($newUser, $u)) {
					$message = 'The phone number has been registered before. ' .
					 'Please login to your account or ask support for help.' . $supportMessage;
					return new FailedResponse($message, $this->errorMessage('phoneNumber', $message));
				}
			}
		}

		// valid password
		if (!$password) {
			return new FailedResponse('Invalid password!', $this->errorMessage('password', 'Invalid password'));
		}
		$checked = UserService::checkNewPasswordSecurity($password);
		if (gettype($checked) === 'string') {
			return new FailedResponse('Password too weak! ' . $checked, $this->errorMessage('password', 'Password too weak! ' . $checked));
		}

        // valid country
        if (!$country) {
            return new FailedResponse('Invalid country!', $this->errorMessage('countryId', 'Invalid country!'));
        }
		// valid address
		if (!$state || $state->getCountry() !== $country) {
			return new FailedResponse('Invalid state!', $this->errorMessage('stateId', 'Invalid state!'));
		}
		if (!$city) {
			return new FailedResponse('Invalid city!', $this->errorMessage('city', 'Invalid city!'));
		}
		if (!$address || strlen($address) < 3) {
			return new FailedResponse('Invalid address!', $this->errorMessage('address', 'Invalid address!'));
		}
		if (!$postalCode || (Util::isLive() && strlen($postalCode) > 5)) {
			return new FailedResponse('Invalid postal code!', $this->errorMessage('postalCode', 'Invalid postal code!'));
		}

		// valid birthday
		$bithAt = null;
		if ($dateOfBirth) {
		    $bithAt = null;
            try {
                $bithAt = Util::timeLocal($dateOfBirth);
            } catch (\Exception $e) {
                return new FailedResponse('Invalid birthday!');
            }
			$atLeast = (new Carbon())->subYearsWithoutOverflow(18)->startOfMonth();
			if ($bithAt->gte($atLeast)) {
				$message = 'You must be older than 18 years old!';
				return new FailedResponse($message, $this->errorMessage('dateOfBirth', $message));
			}
		} else {
			return new FailedResponse('Invalid birthday!', $this->errorMessage('dateOfBirth', 'Invalid birthday!'));
		}

		// check banned users
		$newUser = $this->checkBannedStatus($newUser);

		// set user data
		$userName = $emailAddress;
		$newUser->setFirstName($firstName);
		$newUser->setLastName($lastName);
		$newUser->setEmail($emailAddress);
		$newUser->setEmailVerified(false);
		$newUser->setUsername($userName . '_spendr');
		$newUser->setBirthday(Util::toUTC($bithAt));
		$newUser->setMobilephone($mobilePhone);

		$newUser->setAddress($address);
		$newUser->setCountry($country);
		$newUser->setState($state);
		$newUser->setCity($city);
		$newUser->setZip($postalCode);

		$newUser->setRegisterStep(RegisterStep::EMAIL);
		$encodedPassword = Util::encodePassword($newUser, $password);
		$newUser->setPassword($encodedPassword);
		$newUser->setEnabled(true);
		$newUser->setLockedStatus(User::LOCK_STATUS_UNLOCK);
		$newUser->setStatus(User::STATUS_ACTIVE);
		$newUser->setSource('spendr_register');

		$this->em->persist($newUser);
		$this->em->flush();

        Util::updateMeta($newUser, [
            'registerIp' => Security::getClientIp()
        ]);

        $newUser->ensureRole(Role::ROLE_SPENDR_CONSUMER);
        $newUser->resetPasswordDone();

        $userCard = \SpendrBundle\Services\UserService::getDummyCard($newUser);

//		ConsumerService::updateOnboardStatus($newUser);

        $this->createAccount($newUser);

        $deviceId = $request->get('deviceId');
        /** @var UserConfig $config */
        $config = $newUser->ensureConfig();
        $config->setDeviceId($deviceId)
            ->persist();

        $attrs = BrazeService::getUserAttributes($newUser);
        BrazeService::userTrack(null, [
            'external_id' => BrazeService::getExternalPrefix() . $newUser->getId(),
            'name' => 'Profile Initiated',
            'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z')
        ], null, $attrs);
        SegmentService::track(null, [
            'userId' => BrazeService::getExternalPrefix() . $newUser->getId(),
            'traits' => $attrs
        ]);

        // If user name is in blacklist, block registration
        if (ConsumerService::nameFuzzyMatch($firstName, $lastName, ConsumerService::nameBlacklist())) {
            $newUser->setStatus(User::STATUS_INACTIVE, false)->persist();
            $newUser->addNote('Change status to inactive because the user name is in blacklist.', true, $newUser->getId());
            SlackService::warning('User name is in blacklist!', [
                'ID' => $newUser->getId(),
                'Issue Type' => IdentityLog::TYPE_IDENTITY_LIMIT
            ]);
            return new FailedResponse("Account cannot be created. Please contact Spendr Support at: {$this->supportMsgInfo()}.");
        }

//        $ofacResponse = $this->verifyOfac($newUser);
//        if($ofacResponse) {
//            return new FailedResponse($ofacResponse, $this->errorMessage('OFAC', $ofacResponse));
//        }

        // Identity check, if consumer count >= 2, the new account need change to inactive
        $idRes = $this->checkIdentity($newUser);
        if ($idRes) {
            return $idRes;
        }

        // Device check, allow 2 consumers per device, so if the result count > 2, the new account need change to inactive
		if ($deviceId && Util::isLive()) {
            $res = $this->checkDeviceId($newUser, $deviceId, $firstName, $lastName, $city, $address);
            if ($res) {
                return $res;
            }
        }

		// If sign up from checkout online plugin payment link
        if ($request->get('txnToken')) {
            return $this->sendAuthSMS($newUser, $transaction);
        }
        $verifyMobile = $request->get('verifyMobile', false);
		if ($verifyMobile) {
            UserPinService::create($newUser, UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);
        } else {
            // send email with verify code
            $this->traitSendRegisterEmail($newUser);
        }

		$data = $newUser->toFinalApiArray();
		$data['cards'] = $userCard ? [$userCard->toApiArray()] : [];
		$data['cardInfo'] = $userCard ? [
			$userCard->getCardProgram()->getName(),
			$userCard->getCardProgram()->getPlatform()->getName(),
		] : [];

		$debug = false;
		if ($debug && !$verifyMobile) {
			$data['sessionCode'] = Data::get('register_email_verify_code_' . $newUser->getId());
		}
        $key = 'spendr_user_refer:' . (Security::getClientIp(false) ?? Security::getClientIp());
        if ($request->get('inviterId') || Data::has($key)) {
            if ($request->get('inviterId')) {
                $inviterId = $request->get('inviterId');
            } else {
                $inviterId = Data::get($key);
            }
            $referConfig = SpendrReferConfig::findOne();
            $inviter = User::find($inviterId);
            if ($referConfig && $inviter && $inviter->isSpendrConsumer()) {
                $referral = new SpendrReferral();
                $referral->setInviter($inviter)
                    ->setInvitee($newUser)
                    ->setStatus(SpendrReferral::STATUS_REFERRED)
                    ->persist();

                BrazeService::userTrack(null, [
                    'external_id' => BrazeService::getExternalPrefix() . $inviter->getId(),
                    'name' => 'Referred New User',
                    'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                    'properties' => [
                        'total_referred' => SpendrReferral::totalReferred($inviter)
                    ]
                ]);
                SegmentService::track([
                    'userId' => BrazeService::getExternalPrefix() . $newUser->getId(),
                    'event' => 'Referred User Start',
                    'properties' => [
                        'userId' => BrazeService::getExternalPrefix() . $newUser->getId(),
                        'date' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                        'relatedUserId' => BrazeService::getExternalPrefix() . $inviterId
                    ]
                ]);
                SegmentService::track([
                    'userId' => BrazeService::getExternalPrefix() . $inviterId,
                    'event' => 'User Referred Start',
                    'properties' => [
                        'userId' => BrazeService::getExternalPrefix() . $inviterId,
                        'date' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
                        'relatedUserId' => BrazeService::getExternalPrefix() . $newUser->getId()
                    ]
                ]);
                Data::del($key);
            }
        }

		return new SuccessResponse($data, 'Registration success');
	}

//	/**
//	 * @Route("/spendr/m/register/{userId}/id-ofac", methods={"POST"})
//	 * @param Request $request
//	 *
//	 * @return FailedResponse|SuccessResponse
//	 */
	public function idOfacAction(Request $request)
	{
		$userId = $request->get('userId');
		if (!$userId) {
			return new FailedResponse('Invalid user!');
		}

		$user = $this->em->getRepository(User::class)->find($userId);
		if (!$user) {
			return new FailedResponse('Invalid user!');
		}

		/** @var ExternalInvoke $ei */
		[$ei] = IDologyService::ofac($user);
		if ($ei && $ei->isFailed()) {
			$error = $ei->getError();
			Util::updateMeta($user, [
				'ofacFailed' => $ei->getError(),
			]);
			return new FailedResponse($error, $this->errorMessage('OFAC', $error));
		} else {
			Util::updateMeta($user, [
				'ofacSuccess' => true,
			]);
			ConsumerService::updateOnboardStatus($user);
//			if (!$user->getEmailVerified()) {
//				$this->sendRegisterEmail($request, $user);
//			}
			$data['userId'] = $user->getId();
//			$debug = true;
//			if ($debug) {
//				$data['sessionCode'] = Data::get('register_email_verify_code_' . $user->getId());
//			}
			return new SuccessResponse($data);
		}
	}

	protected function verifyOfac(User $user)
	{
		/** @var ExternalInvoke $ei */
		[$ei] = IDologyService::ofac($user);
		if ($ei && $ei->isFailed()) {
			Util::updateMeta($user, [
				'ofacFailed' => $ei->getError(),
			]);
            $user->setStatus(User::STATUS_INACTIVE, false)->persist();
            $log = new IdentityLog();
            $log->setUser($user)
                ->setType(IdentityLog::TYPE_IDOLOGY_FAILED)
                ->setDescription($ei->getError())
                ->persist();
            $user->addNote('Change status to inactive because of the idolog OFAC is failed.', true, $user->getId());
			return $ei->getError();
		} else {
			Util::updateMeta($user, [
				'ofacSuccess' => true,
			]);
			return null;
		}
	}

	protected function getUserByEmail($email)
	{
		$users = $this->em->getRepository(User::class)
			->createQueryBuilder('u')
			->join('u.teams', 't')
			->where(Util::expr()->in('t.name', ':roles'))
			->andWhere('u.email = :email')
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->setParameter('email', $email)
			->setMaxResults(2)
			->getQuery()
			->getResult();
		return $users && count($users) ? $users[0] : null;
	}

	protected function getUserByNameAndZip(User $user)
    {
        $users = $this->em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->andWhere('u.firstName = :firstName')
            ->andWhere('u.lastName = :lastName')
            ->andWhere('u.zip = :zip')
            ->setParameter('roles', SpendrBundle::getConsumerRoles())
            ->setParameter('firstName', $user->getFirstName())
            ->setParameter('lastName', $user->getLastName())
            ->setParameter('zip', $user->getZip())
            ->setMaxResults(2)
            ->getQuery()
            ->getResult();
        return $users && count($users) > 1;
    }

	protected function validPhoneNumber($rawMobilePhone, $mobilePhone, $formattedPhone)
	{
		$users = null;
		$phones = Config::array(Config::CONFIG_WILD_PHONE_NUMBERS);
		if (!in_array($mobilePhone, $phones)) {
			$users = $this->em->getRepository(User::class)
				->createQueryBuilder('u')
				->join('u.teams', 't')
				->where(Util::expr()->in('t.name', ':roles'))
				->Andwhere(Util::expr()->orX(
					Util::expr()->eq('u.phone',':phone'),
					Util::expr()->eq('u.mobilephone', ':mobilephone'),
					Util::expr()->eq('u.workphone', ':workphone'),
					Util::expr()->eq('u.phone', ':raw_phone'),
					Util::expr()->eq('u.mobilephone', ':raw_mobilephone'),
					Util::expr()->eq('u.workphone', ':raw_workphone'),
					Util::expr()->eq('u.phone', ':format_phone'),
					Util::expr()->eq('u.mobilephone', ':format_mobilephone'),
					Util::expr()->eq('u.workphone', ':format_workphone')
				))
				->setParameter('roles', SpendrBundle::getConsumerRoles())
				->setParameter('phone', $mobilePhone)
				->setParameter('mobilephone', $mobilePhone)
				->setParameter('workphone', $mobilePhone)
				->setParameter('raw_phone', $rawMobilePhone)
				->setParameter('raw_mobilephone', $rawMobilePhone)
				->setParameter('raw_workphone', $rawMobilePhone)
				->setParameter('format_phone', $formattedPhone)
				->setParameter('format_mobilephone', $formattedPhone)
				->setParameter('format_workphone', $formattedPhone)
				->getQuery()
				->getResult();
		}
		return $users;
	}

	protected function checkBannedStatus(User $newUser)
	{
		$users = $this->em->getRepository(User::class)
			->findBy(['status' => User::STATUS_BANNED]);

		$currentIp = Security::getClientIp();

		foreach ($users as $bannedUser){
			$bannedIps = Util::em()->getRepository(\CoreBundle\Entity\IpUsage::class)->findBy(['users' => $bannedUser]);
			$ipsArr = [];
			/** @var IpUsage $bannedIp */
			foreach ($bannedIps as $bannedIp){
				if ($bannedIp->getLoginIp()){
					$ipsArr[] = $bannedIp->getLoginIp();
				}
			}
			if (in_array($currentIp,$ipsArr)){
				$newUser->addFlag(User::FLAG_BAD_AP_ASSOCIATION);
				break;
			}
		}
		return $newUser;
	}

	protected function errorMessage($type, $error)
	{
		return [
			'type' => $type,
			'error' => $error,
		];
	}

    /**
     * @Route("/spendr/m/register/change-mobile", methods={"POST"})
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
	public function changeMobile(Request $request)
    {
        $userId = $request->get('userId');
        $mobile = $request->get('mobile');
        $newMobile = trim($request->get('newMobile'));
        $password = trim($request->get('password'));
        if (!$userId || !$mobile || !$password) {
            return new FailedResponse('Invalid user!');
        }

        $user = User::find($userId);
        if (!$user || $user->getStatus() === User::STATUS_INACTIVE) {
            return new FailedResponse('Invalid user!');
        }
        $mobile = Util::formatManualInputPhoneNumber($mobile);
        $mobile = Util::formatFullPhone($mobile);
        if ($mobile !== $user->getMobilephone()) {
            return new FailedResponse('Invalid user!');
        }
        $encoder = Util::getPasswordEncoder();
        if (!$encoder->isPasswordValid($user, $password)) {
            return new FailedResponse('Invalid user!');
        }
        $rawMobilePhone = $newMobile;
        $usMobile = str_replace([' ', '-', '(', ')', '.'], '', $newMobile);
        if (Util::isLive() &&
            (!$usMobile
                || $usMobile === '+'
                || !$this->verifyUSPhone($usMobile))
        ) {
            $message = 'Invalid phone number!';
            return new FailedResponse($message, $this->errorMessage('phoneNumber', $message));
        }
        $mobilePhone = Util::formatManualInputPhoneNumber($newMobile);
        $mobilePhone = Util::formatFullPhone($mobilePhone);
        if ($mobile === $mobilePhone) {
            return new FailedResponse('Your new mobile is same to the old mobile.');
        }
        $users = $this->validPhoneNumber($rawMobilePhone, $mobilePhone, $mobilePhone);
        if ($users && count($users)) {
            $message = 'The phone number has been registered before. ' .
                'Please login to your account or ask support for help.' . $this->supportMsgInfo();
            return new FailedResponse($message, $this->errorMessage('phoneNumber', $message));
        }
        $user->setMobilephone($mobilePhone)
            ->persist();
//        UserPinService::create($user, UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);

        return new SuccessResponse([
            'New Phone' => $mobilePhone
        ]);
    }

    /**
     * @Route("/spendr/m/register/resend-email", methods={"POST"})
     *
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     * @throws PortalException
     */
	public function resendRegisterEmail(Request $request)
	{
	    if ($request->get('verifyMobile', false)) {
            $userId = $request->get('userId');
            if (!$userId) {
                return new FailedResponse('Invalid user!');
            }

            $user = User::find($userId);
            if (!$user || $user->getStatus() === User::STATUS_INACTIVE) {
                return new FailedResponse('Invalid user!');
            }
            if (!Util::meta($user, 'mobilePhoneVerified') || $request->get('isLogin', false)) {
                UserPinService::create($user, UserPin::PHONE_TYPE_MOBILE, UserPin::MSG_TYPE_SMS);
            }

            return new SuccessResponse();
        }
//		return $this->traitResendRegisterEmail($request);
        return new FailedResponse('The email verification has been disabled. If you cannot receive the verification code, please ask support for help.' . $this->supportMsgInfo());
	}

	/**
	 * @Route("/spendr/m/register/verify-code")
	 *
	 * @param Request $request
	 * @return FailedResponse|SuccessResponse
	 * @throws OptimisticLockException
	 */
	public function verifyCodeAction(Request $request)
	{
		$verifyCode = $request->get('verifyCode');
		if (!$verifyCode) {
			return new FailedResponse('Invalid code!');
		}

		$userId = $request->get('userId');
		if (!$userId) {
			return new FailedResponse('Invalid user!');
		}

		/** @var User $user */
		$user = $this->em->getRepository(User::class)->find($userId);
		if (!$user) {
			return new FailedResponse('Invalid user!');
		}

        $verifyMobile = $request->get('verifyMobile', false);
		if ($verifyMobile) {
            $verified = UserPinService::verify($user, $verifyCode, 10);
        } else {
            $sessionCode = Data::get('register_email_verify_code_' . $user->getId());
            $verified = $sessionCode && !strcmp($verifyCode, $sessionCode);
        }
        if (!$verified) {
            return new FailedResponse('Invalid code or outdated!');
        }

        if ($verifyMobile) {
            Util::updateMeta($user, [
                'mobilePhoneVerified' => true
            ]);
        } else {
            $user->setEmailVerified(true);
        }
        $user->setRegisterStep(ConsumerService::STEP_ACTIVE)
            ->persist();

		$user->logIP($verifyMobile ? 'verify_mobilePhone' : 'verify_email');

		$ut = UserToken::instance($user);
        UserToken::updateUseTime($ut);
		$user->logLogin();
        // Create braze user
        $attrs = BrazeService::getUserAttributes($user);
        BrazeService::userTrack(null, null, null, $attrs);
        SegmentService::track(null, [
            'userId' => BrazeService::getExternalPrefix() . $user->getId(),
            'traits' => $attrs
        ]);

		return new SuccessResponse([
			'userId'     => $user->getId(),
			'token'      => $ut->getToken(),
		]);
	}

	protected function createAccount(User $user) {
		if (!$user) {
			return null;
		}
		$account = new Account();
		$max = 999999;
		$max2 = 99999;
		$maxPerLoad = 100000;
		$maxMonthlyLoad = 500000;

		$type = AccountType::get(AccountType::NAME_PATIENT);
	  	$account->setName($user->getFullName())
			->setType($type)
		  	->setStatus(AccountStatus::active())
		  	->setAdminUser($user)
		  	->setAllowMultipleUsers(0)
		  	->setAllowSubAccounts(0)
		  	->setCurrency(Currency::find('USD'))
		  	->setBalance(100000)
		  	->setMaxBalance($max)
		  	->setMaxDailySpend($max2)
		  	->setMaxWeeklySpend($max)
		  	->setMaxMonthlySpend($max)
		  	->setMaxDailyLoad($max2)
		  	->setMaxPerLoad($maxPerLoad)
		  	->setMaxMonthlyLoad($maxMonthlyLoad)
		  	->persist();

	  	$this->createPatient($user, $account);

	  	$this->em->flush();
	}

	protected function createPatient(User $user, Account $account) {
		if (!$user) {
			return null;
		}
		$patient = new Patient();
		$address = Address::createFromUser($user, TRUE);
		$patient->setUser($user)
			->setAccount($account)
			->setAddress($address);
		$this->em->persist($patient);
	}

    /**
     * @param User $newUser
     * @param $deviceId
     * @param $firstName
     * @param $lastName
     * @param $city
     * @param $address
     * @return FailedResponse|null
     */
	protected function checkDeviceId(User $newUser, $deviceId, $firstName, $lastName, $city, $address)
    {
        if (in_array($deviceId, Config::array(Config::CONFIG_SPENDR_DEVICE_ID_WHITELIST))) {
            return null;
        }
        $confs = Util::em()->getRepository(UserConfig::class)
            ->createQueryBuilder('uc')
            ->join('uc.user', 'u')
            ->join('u.teams', 't')
            ->where(Util::expr()->in('t.name', ':roles'))
            ->setParameter('roles', SpendrBundle::getMemberRoles())
            ->andWhere('uc.deviceId = :deviceId')
            ->setParameter('deviceId', $deviceId)
            ->setMaxResults(3)
            ->getQuery()
            ->getResult();
        if (count($confs) > 2) {
            $newUser->setStatus(User::STATUS_INACTIVE, false)->persist();
            $log = new IdentityLog();
            $log->setUser($newUser)
                ->setType(IdentityLog::TYPE_DEVICE_ID_LIMIT)
                ->setDescription("A Spendr account is already associated with this device. Device ID: $deviceId")
                ->persist();
            $newUser->addNote('Change status to inactive because of the device id limit when sign up.', true, $newUser->getId());
            SlackService::warning('Duplicate user registration!', [
                'ID' => $newUser->getId(),
                'Issue Type' => IdentityLog::TYPE_DEVICE_ID_LIMIT
            ]);
            return new FailedResponse("A Spendr account is already associated with this device. Please use a different device or reach out to Spendr Support: {$this->supportMsgInfo()}.");
        }

        return null;
    }

    /**
     * @param User $user
     * @return FailedResponse|null
     */
    protected function checkIdentity(User $user)
    {
        if ($this->getUserByNameAndZip($user)) {
            $user->setStatus(User::STATUS_INACTIVE, false)->persist();
            $log = new IdentityLog();
            $log->setUser($user)
                ->setType(IdentityLog::TYPE_IDENTITY_LIMIT)
                ->setDescription("A Spendr account is already associated with this identity. Name: {$user->getFullName()} & Postal Code: {$user->getZip()}")
                ->persist();
            $user->addNote('Change status to inactive because of identity verification failed when sign up.', true, $user->getId());
            SlackService::warning('Duplicate user registration!', [
                'ID' => $user->getId(),
                'Issue Type' => IdentityLog::TYPE_IDENTITY_LIMIT
            ]);
            return new FailedResponse("A Spendr account is already associated with this identity. Please use a different identity or reach out to Spendr Support: {$this->supportMsgInfo()}.");
        }

        return null;
    }
}
