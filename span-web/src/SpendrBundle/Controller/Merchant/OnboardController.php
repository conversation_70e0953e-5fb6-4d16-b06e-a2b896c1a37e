<?php

namespace SpendrB<PERSON>le\Controller\Merchant;

use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Response\DeniedResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\DeniedException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrGroup;
use SpendrBundle\Entity\Location;
use SpendrBundle\Entity\SpendrMerchant;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\LocationService;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\Services\OnboardService;
use SpendrBundle\Services\YodleeErrorRecordService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use SpendrBundle\Services\SlackService;
use UsUnlockedBundle\Services\IDologyService;
use SpendrBundle\Services\SpendrYodleeService;
use SpendrBundle\Services\UserCardService;
use SpendrBundle\Services\UserService as SpendrUserService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class OnboardController extends BaseController
{
    public $protected = true;

    public function getAccessibleRoles()
	{
		return [
			Role::ROLE_SPENDR_PROGRAM_OWNER,
			Role::ROLE_SPENDR_GROUP_ADMIN,
            Role::ROLE_SPENDR_MERCHANT_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_MASTER_ADMIN,
			Role::ROLE_SPENDR_MERCHANT_OPERATOR_ADMIN
		];
	}

	/**
	 * @Route("/spendr/merchant/onboard/detail", methods={"POST"})
	 * @param Request $request
	 *
	 * @return Response
	 * @throws PortalException
	 * @throws \CoreBundle\Exception\FailedException
	 */
    public function detail(Request $request)
	{
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());

        $currentUser = $this->user;
		$merchant = MerchantService::saveDetails($request, $currentUser);
		$group = $merchant->getGroup();
        $group = OnboardService::getGroupDetailsData($group, $currentUser);
        $merchant = OnboardService::getMerchantDetailsData($currentUser, $merchant);

        if ($this->user->inTeams(
            array_merge(SpendrBundle::getMerchantAdminRoles(), [Role::ROLE_SPENDR_BANK])
        )) {
            return new SuccessResponse(['merchant' => $merchant]);
        } else {
            return new SuccessResponse([
                'group' => $group,
                'merchant' => $merchant
            ]);
        }
    }

	/**
	 * @Route("/spendr/merchant/onboard/resend-invitation/{merchant}", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return SuccessResponse
	 * @throws DeniedException
	 */
    public function resend(Request $request, SpendrMerchant $merchant)
    {
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());

        $this->validate($merchant);

		if ($merchant->getOnboardingStatus() === SpendrMerchant::ONBOARDING_STATUS_INITIAL) {
            UserService::sendResetPasswordEmail($merchant->getAdminUser());
        }
        return new SuccessResponse();
    }

	/**
	 * @Route("/spendr/merchant/onboard/change-email/{merchant}", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return SuccessResponse
	 * @throws PortalException
	 */
    public function changeEmail(Request $request, SpendrMerchant $merchant)
	{
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());

        $this->validate($merchant);

		if ($merchant->getOnboardingStatus() === SpendrMerchant::ONBOARDING_STATUS_INITIAL) {
            $email = $request->get('email');
            $admin = $merchant->getAdminUser();
            $admin->changeEmail($email, SpendrBundle::getAllMerchantAdminRoles())
                ->persist();

            $address = $merchant->getAddress();
            $address->setEmail($email);
            Util::persist($address);

            BrazeService::userTrack(null, null, null, [
                'external_id' => BrazeService::getExternalPrefix() . $admin->getId(),
                'email' => $admin->getEmail()
            ]);

            UserService::sendResetPasswordEmail($admin);
        }
        return new SuccessResponse([
            'id' => $merchant->getId(),
            'email' => $merchant->getAdminUser()->getEmail(),
        ]);
    }

    protected function validate(SpendrMerchant $merchant): void
    {
        $group = $merchant->getGroup();
        $user = $this->authSuperOrOtherAdmins();
        if ($user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
            $userMerchant = $user->getSpendrAdminMerchant();
            if (!Util::eq($merchant, $userMerchant)) {
                throw new DeniedException();
            }
        } else if ($user->inTeams([Role::ROLE_SPENDR_GROUP_ADMIN])) {
            $userGroup = $user->getCurrentGroup();
            if (!Util::eq($group, $userGroup)) {
                throw new DeniedException();
            }
        }
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/data", methods={"GET"})
	 * @param Request $request
	 * @param SpendrGroup $group
	 * @return Response
	 * @throws DeniedException
	 */
    public function data(Request $request, SpendrMerchant $merchant)
    {
    	$user = $this->user;
    	if ($user && $user->inTeams([Role::ROLE_SPENDR_MERCHANT_OTHER_ADMIN])) {
    		return new DeniedResponse();
		}
        $this->validate($merchant);
        $data = OnboardService::getMerchantDetailsData($user, $merchant);
        return new SuccessResponse($data);
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/business-type", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function businessType(Request $request, SpendrMerchant $merchant)
    {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $this->validate($merchant);

        $merchant->setBusinessType($request->get('type'))
            ->persist();

        $currentUser = $this->getUser();
        $group = OnboardService::getGroupDetailsData($merchant->getGroup(), $currentUser);
        $merchant = OnboardService::getMerchantDetailsData($currentUser, $merchant);

        if ($this->user->inTeams(
            array_merge(SpendrBundle::getMerchantAdminRoles(), [Role::ROLE_SPENDR_BANK])
        )) {
            return new SuccessResponse(['merchant' => $merchant]);
        } else {
            return new SuccessResponse([
                'group' => $group,
                'merchant' => $merchant
            ]);
        }
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/representative", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function representative(Request $request, SpendrMerchant $merchant)
    {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $this->validate($merchant);

        $reps = $request->request->all('reps');
        foreach ($reps as $i => $rep) {
            $user = new User();
            $user->setFirstName($rep['First Name'] ?? '')
                ->setLastName($rep['Last Name'] ?? '')
                ->setEmail($rep['Email'] ?? '')
                ->setMobilephone($rep['Phone'] ?? '')
                ->setAddress($rep['Street Address'] ?? '')
                ->setCity($rep['City'] ?? '')
                ->setZip($rep['Postal Code'] ?? '')
                ->setSource('spendr_representative')
                ->setCountryid($rep['CountryId'] ?? null)
                ->setStateid($rep['State'] ?? null);

            $oldHashKey = $rep['Hash Key'] ?? null;
            $isPreviousOwner = $rep['isPreviousOwner'] ?? false;
            $hashKey = IDologyService::ofac($user, true);
            if (!$isPreviousOwner) {
                if ($oldHashKey === $hashKey) {
                    continue; // Already verified
                }
            }

            // if (!Util::isLocal()) {
            //     /** @var ExternalInvoke $ei */
            //     list($ei) = IDologyService::ofac($user);
            //     if ($ei && $ei->isFailed()) {
            //         return new FailedResponse('OFAC check failed for the representative "' .
            //                                   $user->getName() . '": ' . $ei->getError());
            //     }
            // }

            $reps[$i]['Hash Key'] = $hashKey;
            if (isset($reps[$i]['isPreviousOwner'])) {
                unset($reps[$i]['isPreviousOwner']);
            }
            if (isset($reps[$i]['new'])) {
                unset($reps[$i]['new']);
            }

			if (isset($rep['file'])) {
				$url = $rep['file']['url'] ?? null;
				if ($url) {
					$cleanedUrl = strstr($url, "/attachments");
					$reps[$i]['file']['url'] = $cleanedUrl;
				}
			}
        }


        Util::updateMeta($merchant, 'representatives', false);
        Util::updateMeta($merchant, [
            'representatives' => $reps,
        ]);

        $currentUser = $this->getUser();
        $group = $merchant->getGroup();
        $groupData = OnboardService::getGroupDetailsData($group, $currentUser);
        $merchantData = OnboardService::getMerchantDetailsData($currentUser, $merchant);

        if ($this->user->inTeams(
            array_merge(SpendrBundle::getMerchantAdminRoles(), [Role::ROLE_SPENDR_BANK])
        )) {
            return new SuccessResponse(['merchant' => $merchantData]);
        } else {
            return new SuccessResponse([
                'group' => $groupData,
                'merchant' => $merchantData
            ]);
        }
    }

    /**
     * @Route("/spendr/merchant/{merchant}/onboard/docs", methods={"POST"})
     * @param Request        $request
     * @param SpendrMerchant $merchant
     *
     * @return Response
     * @throws DeniedException
     */
    public function docs(Request $request, SpendrMerchant $merchant)
    {
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $this->validate($merchant);

        $group = $merchant->getGroup();

        $type = $request->request->get('type');
        $file = $request->request->all('file');

        $docs = Util::meta($merchant, 'docs') ?? [];
        $docs[$type][$file['id']] = $file;

		foreach($docs as $k => $d) {
			foreach($d as $kChild => $child) {
				if (isset($child['url'])) {
					$url = $child['url'];
					$cleanedUrl = strstr($url, "/attachments");
					$docs[$k][$kChild]['url'] = $cleanedUrl;
				}
			}
		}

        Util::updateMeta($merchant, [
            'docs' => $docs,
        ]);

        $res = [];
		if (count($docs) > 0) {
			foreach ($docs as $k=>$v) {
				$res[$k] = array_values($v);
			}
		}

        if ($this->user->inTeams(
            array_merge(SpendrBundle::getMerchantAdminRoles(), [Role::ROLE_SPENDR_BANK])
        )) {
            return new SuccessResponse(['docs' => $res]);
        } else {
            $user = $this->user;
            $groupData = OnboardService::getGroupDetailsData($group, $user);
            $merchantData = OnboardService::getMerchantDetailsData($user, $merchant);
            return new SuccessResponse([
                'docs' => $res,
                'group' => $groupData,
                'merchant' => $merchantData
            ]);
        }
    }

    /**
     * @Route("/spendr/merchant/{merchant}/onboard/docs/remove", methods={"POST"})
     * @param Request        $request
     * @param SpendrMerchant $merchant
     *
     * @return Response
     * @throws DeniedException
     */
    public function removeDoc(Request $request, SpendrMerchant $merchant)
    {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $this->validate($merchant);

        $group = $merchant->getGroup();

        $type = $request->request->get('type');
        $docId = $request->request->get('docId'); // for multiple

        $docs = Util::meta($merchant, 'docs') ?? [];
        if (isset($docId)) {
			unset($docs[$type][$docId]);
		} else {
			unset($docs[$type]);
		}
        Util::updateMeta($merchant, 'docs', false);
        Util::updateMeta($merchant, [
            'docs' => $docs,
        ]);

		$res = [];
		if (count($docs) > 0) {
			foreach ($docs as $k=>$v) {
				$res[$k] = array_values($v);
			}
		}

        if ($this->user->inTeams(
            array_merge(SpendrBundle::getMerchantAdminRoles(), [Role::ROLE_SPENDR_BANK])
        )) {
            return new SuccessResponse(['docs' => $res]);
        } else {
            $currentUser = $this->user;
            $groupData = OnboardService::getGroupDetailsData($group, $currentUser);
            $merchantData = OnboardService::getMerchantDetailsData($currentUser, $merchant);
            return new SuccessResponse([
                'docs' => $res,
                'group' => $groupData,
                'merchant' => $merchantData
            ]);
        }
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/submit", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function submitMerchant(Request $request, SpendrMerchant $merchant)
    {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());
		$this->validate($merchant);

        $data = OnboardService::submitMerchant($merchant, $this->user);

        return new SuccessResponse($data);
    }

	/**
	 * @Route("/spendr/merchant/{merchant}/onboard/repeal", methods={"POST"})
	 * @param Request $request
	 * @param SpendrMerchant $merchant
	 *
	 * @return Response
	 * @throws DeniedException
	 * @throws PortalException
	 */
    public function repealMerchant(Request $request, SpendrMerchant $merchant)
    {
		$this->authSuperAdminOrAdmins($this->getAccessibleRoles());
		$this->validate($merchant);

        if (in_array($merchant->getOnboardingStatus(), [
            SpendrMerchant::ONBOARDING_STATUS_PENDING,
            SpendrMerchant::ONBOARDING_STATUS_APPROVED,
        ])) {
            $merchant->setOnboardingStatus(SpendrMerchant::ONBOARDING_STATUS_INITIAL);
            Util::updateMeta($merchant, 'bankDeniedAt', false);
            Util::updateMeta($merchant, 'bankApprovedAt', false);
            Util::updateMeta($merchant, 'bankSubmittedAt');
        }

        $data = OnboardService::getMerchantDetailsData($this->getUser(), $merchant);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/admin/spendr/merchant/all-locations/{merchant}", methods={"GET"})
     * @param SpendrMerchant $merchant
     * @return FailedResponse|SuccessResponse
     * @throws DeniedException
     */
    public function merchantLocations(SpendrMerchant $merchant)
    {
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $locations = LocationService::listLocationsForSelection($merchant);

        return new SuccessResponse($locations);
    }

    /**
     * @Route("/admin/spendr/merchant/card-bind-location/{merchant}", methods={"POST"})
     * @param SpendrMerchant $merchant
     * @param Request $request
     * @return DeniedResponse|FailedResponse|SuccessResponse
     * @throws DeniedException
     */
    public function cardBindLocation(SpendrMerchant $merchant, Request $request)
    {
        $this->authSuperAdminOrAdmins($this->getAccessibleRoles());
        $this->validate($merchant);

        $userCard = UserCard::find($request->get('cardId'));
		$locationIds = $request->get('locationIds');
        if (!$userCard || $merchant->getAdminUser() !== $userCard->getUser()) {
            return new FailedResponse('Invalid parameters.');
        }

		$locations = [];
		if ($locationIds) {
			$locations = $this->getLocationsByIds($locationIds);
			if (!$locations) {
				return new FailedResponse("Locations don't exist!");
			}
		}

		$relatedLocationsBefore = $userCard->getSpendrLocations();
		if ($relatedLocationsBefore) {
			/** @var Location $location */
			foreach ($relatedLocationsBefore as $key => $location) {
				$location->setBankCard(null);
				if (($key + 1) === count($relatedLocationsBefore)) {
					$location->persist();
				}
			}
		}

		if ($locations) {
			/** @var Location $location */
			foreach ($locations as $key => $location) {
				$location->setBankCard($userCard);
				if (($key + 1) === count($locations)) {
					$location->persist();
				}
			}
		}

        return new SuccessResponse();
    }

	private function getLocationsByIds($locationIds)
	{
		return Util::em()->getRepository(Location::class)
		->createQueryBuilder('l')
		->where(Util::expr()->in('l.id', ':locationIds'))
		->setParameter('locationIds', $locationIds)
		->distinct()
		->getQuery()
		->getResult();
	}
}
