<?php

namespace SpendrBundle\Controller;

use CoreBundle\Entity\UserToken;
use CoreBundle\Utils\Session;
use CoreBundle\Utils\Util;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

class DefaultController extends BaseController
{

    public $protected = true;

    /**
     * @Route("/spendr")
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
	public function indexAction(Request $request) {
        $params = $request->getQueryString() ? ('?' . $request->getQueryString()) : '';
	    if ($this->user) {
	        if ($this->user->inTeams(SpendrBundle::getMemberRoles())) {
                return $this->redirect('/static/spendr/web/#/spendr/consumer/main' . $params);
            } elseif ($this->user->inTeams(SpendrBundle::getMerchantEmployeeRoles())) {
                return $this->redirect('/static/spendr/web/#/spendr/merchant/main' . $params);
            }
        }
        return $this->redirect('/static/spendr/web/#/spendr/consumer/login' . $params);
	}

    /**
     * @Route("/static/spendr/web/")
     */
    public function webAction()
    {
        $script = '';
        $oldUser = null;
        if ($this->user) {
            $ut = UserToken::instance($this->user);
            UserToken::updateUseTime($ut);
            $token = $ut->getToken();
            if (Util::isDevDevice()) {
                $token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOjUwMDA3NDMxNiwidCI6MTYwODIxNzYwMSwiciI6IjZwenpvMmpoanAifQ.XuObqhOVJ-S0MCY-kgle3p-CEnVkD4wAEAYlOOzUZvI';
            }
            $token = str_replace('\\', '\\\\', json_encode(json_encode($token)));
            $script = 'localStorage.setItem("flutter.token", \'' . $token . '\');';
            $oldUser = Util::getImpersonatingUser();
            if ($oldUser) {
                $script .= 'window.impersonatingUser = "' . $oldUser->getId() . '";';
                $isSpendr = '';
                if ($oldUser->inTeams(SpendrBundle::getSpendrAdminRoles())) {
                    $isSpendr = 'true';
                }
                $script .= 'localStorage.setItem("impersonatingUser", \'' . $oldUser->getId() . '\');';
                $script .= 'localStorage.setItem("isSpendrAdminLoggedInAs", \'' . $isSpendr . '\');';
            }
        }

        $logout = $_COOKIE['LOGOUT'] ?? '';
        $content = file_get_contents(__DIR__ . '/../../../web/static/spendr/web/index.html');
        $content = str_replace('<body>', '<body><script>' . $script . '</script>', $content);

        if ($oldUser) {
            $content = str_replace('</body>', Util::render('@Portal/Common/login_as_exit.html.twig') . '</body>', $content);
        }

        $response = new Response($content);
        if ((string)$logout === 'true') {
            setcookie('LOGOUT', '', time() - 3600, '/static/spendr/web', '', false, false);
            return $this->redirect('/logout');
        }

        return $response;
    }

    /**
     * @Route("/static/spendr/web//version.json")
     */
    public function versionAction()
    {
        return new JsonResponse([
            'app_name' => 'Spendr',
            'version' => '1.0.0',
            'build_number' => '1',
        ]);
    }
}
