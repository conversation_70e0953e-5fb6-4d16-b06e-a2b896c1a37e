<?php

namespace SpendrBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use SalexUserBundle\Entity\User;

/**
 * MerchantAdmin
 *
 * @ORM\Table(name="spendr_merchant_admin", options={"comment":"Store the relationship between merchant and merchant admin, some Merchants might have multiple Merchant Admins"})
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\MerchantAdminRepository")
 */
class MerchantAdmin
{
	/**
	 * @var int
	 *
	 * @ORM\Column(name="id", type="integer")
	 * @ORM\Id
	 * @ORM\GeneratedValue(strategy="AUTO")
	 */
	private $id;

	/**
	 * @var User
	 * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $user;

	/**
	 * @var SpendrMerchant
	 * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrMerchant")
	 * @ORM\JoinColumn(name="merchant_id", referencedColumnName="id")
	 */
	private $merchant;

	// /**
	//  * @var SpendrGroup
	//  * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrGroup")
	//  * @ORM\JoinColumn(name="group_id", referencedColumnName="id")
	//  */
	// private $group;

	/**
	 * Get id
	 *
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * Set user
	 *
	 * @param User $user
	 *
	 * @return MerchantAdmin
	 */
	public function setUser(User $user)
	{
		$this->user = $user;

		return $this;
	}

	/**
	 * Get user
	 *
	 * @return User
	 */
	public function getUser()
	{
		return $this->user;
	}

    /**
     * Set merchant
     *
     * @param SpendrMerchant $merchant
     *
     * @return MerchantAdmin
     */
    public function setMerchant(SpendrMerchant $merchant)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return SpendrMerchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    // /**
    //  * Set group
    //  *
    //  * @param SpendrGroup $group
    //  *
    //  * @return MerchantAdmin
    //  */
    // public function setGroup(SpendrGroup $group)
    // {
    //     $this->group = $group;

    //     return $this;
    // }

    // /**
    //  * Get group
    //  *
    //  * @return SpendrGroup
    //  */
    // public function getGroup()
    // {
    //     return $this->group;
    // }
}
