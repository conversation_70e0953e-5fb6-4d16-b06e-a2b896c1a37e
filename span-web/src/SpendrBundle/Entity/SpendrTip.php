<?php

namespace SpendrBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use C<PERSON><PERSON><PERSON>le\Entity\Merchant;
use C<PERSON><PERSON><PERSON>le\Entity\Transaction;
use CoreBundle\Entity\BaseEntity;
use CoreBundle\Utils\Money;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use SalexUserBundle\Entity\User;

/**
 * SpendrTip
 *
 * @ORM\Table(name="spendr_tips")
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\SpendrTipRepository")
 * @Gedmo\SoftDeleteable()
 */
class SpendrTip extends BaseEntity implements ApiEntityInterface
{
    const STATUS_CREATED = 'created';
    const STATUS_PAID = 'paid';
    const STATUS_REFUNDED = 'refunded';

    public function toApiArray(bool $extra = false): array
    {
        return [
            'id' => $this->getId(),
            'amount' => $this->getAmount(),
            'amountText' => Money::format($this->getAmount(), 'USD', false),
            'type' => $this->getTipping()->getType(),
            'format' => $this->getTipping()->getFormat(),
            'status' => $this->getStatus(),
            'spendr_fee' => $this->getSpendrFee(),
            'tern_fee' => $this->getTernFee()
        ];
    }

    /**
     * Hook SoftDeleteable behavior
     * updates deletedAt field
     */
    use SoftDeleteableEntity;

    /**
     * @var Transaction
     * @ORM\OneToOne(targetEntity="ClfBundle\Entity\Transaction", mappedBy="tip")
     */
    private $transaction;

    /**
     * @var Merchant
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Merchant")
     */
    private $merchant;

    /**
     * @var Location
     *
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\Location")
     */
    private $location;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $employee;

    /**
     * @var User
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $consumer;

    /**
     * @var TippingEntity
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\TippingEntity")
     * @ORM\JoinColumn(name="tipping_id", referencedColumnName="id")
     */
    private $tipping;

    /**
     * @var int
     * @ORM\Column(name="amount", type="bigint", nullable=true)
     */
    private $amount;

    /**
     * @var int
     *
     * @ORM\Column(name="spendr_fee", type="bigint", nullable=true)
     */
    private $spendrFee;

    /**
     * @var int
     *
     * @ORM\Column(name="tern_fee", type="bigint", nullable=true)
     */
    private $ternFee;

    /**
     * @var string
     * @ORM\Column(name="status", type="string", length=64, nullable=true, options={"default" : "created"})
     */
    private $status;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @var \DateTime
     * @ORM\Column(name="paid_at", type="datetime", nullable=true)
     */
    private $paidAt;

     /**
     * @var Merchant
     *
     * @ORM\ManyToOne(targetEntity="ClfBundle\Entity\Merchant")
     * @ORM\JoinColumn(name="old_merchant_id", referencedColumnName="id", nullable=true)
     */
    private $oldMerchant;

    /**
     * Set transaction
     * @param $transaction
     * @return SpendrTip
     */
    public function setTransaction($transaction)
    {
        $this->transaction = $transaction;

        return $this;
    }

    /**
     * Get transaction
     * @return Transaction
     */
    public function getTransaction()
    {
        return $this->transaction;
    }

    /**
     * Set merchant
     *
     * @param Merchant $merchant
     *
     * @return SpendrTip
     */
    public function setMerchant($merchant)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return Merchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * Set location
     *
     * @param Location $location
     *
     * @return SpendrTip
     */
    public function setLocation($location)
    {
        $this->location = $location;

        return $this;
    }

    /**
     * Get location
     *
     * @return Location
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * Set employee
     *
     * @param User $employee
     *
     * @return SpendrTip
     */
    public function setEmployee($employee)
    {
        $this->employee = $employee;

        return $this;
    }

    /**
     * Get employee
     *
     * @return User
     */
    public function getEmployee()
    {
        return $this->employee;
    }

    /**
     * Set consumer
     *
     * @param User|null $consumer
     *
     * @return SpendrTip
     */
    public function setConsumer(User $consumer = null)
    {
        $this->consumer = $consumer;

        return $this;
    }

    /**
     * Get consumer
     *
     * @return User
     */
    public function getConsumer()
    {
        return $this->consumer;
    }

    /**
     * Set tipping
     * @param TippingEntity|null $tipping
     * @return SpendrTip
     */
    public function setTipping(TippingEntity $tipping = null)
    {
        $this->tipping = $tipping;

        return $this;
    }

    /**
     * Get tipping
     * @return TippingEntity
     */
    public function getTipping()
    {
        return $this->tipping;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return SpendrTip
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return int
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set spendr fee
     *
     * @param $fee
     * @return SpendrTip
     */
    public function setSpendrFee($fee)
    {
        $this->spendrFee = $fee;

        return $this;
    }

    /**
     * Get spendr fee
     *
     * @return int
     */
    public function getSpendrFee()
    {
        return $this->spendrFee;
    }

    /**
     * Set tern fee
     *
     * @param $fee
     * @return SpendrTip
     */
    public function setTernFee($fee)
    {
        $this->ternFee = $fee;

        return $this;
    }

    /**
     * Get tern fee
     *
     * @return int
     */
    public function getTernFee()
    {
        return $this->ternFee;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return SpendrTip
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return SpendrTip
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set paidAt
     *
     * @param \DateTime $paidAt
     *
     * @return SpendrTip
     */
    public function setPaidAt($paidAt)
    {
        $this->paidAt = $paidAt;

        return $this;
    }

    /**
     * Get paidAt
     *
     * @return \DateTime
     */
    public function getPaidAt()
    {
        return $this->paidAt;
    }

     /**
     * Set oldMerchant
     *
     * @param \ClfBundle\Entity\Merchant $oldMerchant
     *
     * @return SpendrTip
     */
    public function setOldMerchant(\ClfBundle\Entity\Merchant $oldMerchant = null)
    {
        $this->oldMerchant = $oldMerchant;

        return $this;
    }

    /**
     * Get oldMerchant
     *
     * @return \ClfBundle\Entity\Merchant
     */
    public function getOldMerchant()
    {
        return $this->oldMerchant;
    }
}
