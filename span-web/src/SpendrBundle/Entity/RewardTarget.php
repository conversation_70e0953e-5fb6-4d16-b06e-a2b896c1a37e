<?php


namespace SpendrBundle\Entity;


use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use SalexUserBundle\Entity\User;

/**
 * RewardTarget
 *
 * @ORM\Table(name="spendr_reward_targets")
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\RewardTargetRepository")
 */
class RewardTarget
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrReward")
     * @ORM\JoinColumn(name="reward_id", referencedColumnName="id")
     */
    private $reward;

    /**
     * @var int
     * @ORM\Column(name="consumer_id", type="integer", nullable=true)
     */
    private $consumerID;

    public static function getCountBy(SpendrReward $reward, User $user = null)
    {
        $q = Util::em()->getRepository(RewardTarget::class)
            ->createQueryBuilder('t')
            ->where('t.reward = :reward')
            ->setParameter('reward', $reward);
        if ($user) {
            $q->andWhere('t.consumerID = :uid')
                ->setParameter('uid', $user->getId());
        }
        return $q->select('count(t)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set reward
     * @param SpendrReward $reward
     * @return RewardTarget
     */
    public function setReward($reward)
    {
        $this->reward = $reward;

        return $this;
    }

    /**
     * Get reward
     * @return SpendrReward
     */
    public function getReward()
    {
        return $this->reward;
    }

    /**
     * Set consumer id
     * @param $consumerID
     * @return RewardTarget
     */
    public function setConsumerID($consumerID)
    {
        $this->consumerID = $consumerID;

        return $this;
    }

    /**
     * @return int
     */
    public function getConsumerID()
    {
        return $this->consumerID;
    }
}
