<?php

namespace SpendrBundle\Entity;

use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use Carbon\Carbon;
use CoreBundle\Entity\BaseEntity;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use J<PERSON>\Serializer\Annotation as Serializer;
use SpendrBundle\Services\TransactionService;

/**
 * SpendrReward
 *
 * @ORM\Table(name="spendr_rewards")
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\SpendrRewardRepository")
 */
class SpendrReward extends BaseEntity implements ApiEntityInterface
{
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const AMOUNT_TYPE_AMOUNT = 'dollar';
    const AMOUNT_TYPE_PERCENT = 'percent';
    const AMAOUNT_TYPES = [
        self::AMOUNT_TYPE_AMOUNT,
        self::AMOUNT_TYPE_PERCENT
    ];
    const SPEND_TYPE_OVER_AMOUNT = 'total_spend';
    const SPEND_TYPE_NUM_WITH_MIN_AMOUNT = 'spend_count_with_min_amount';
    const SPEND_TYPES = [
        self::SPEND_TYPE_OVER_AMOUNT,
        self::SPEND_TYPE_NUM_WITH_MIN_AMOUNT
    ];
    const TYPES = [
        UserCardLoad::EARN_TYPE_BANK_LINK,
        UserCardLoad::EARN_TYPE_SPEND_OVER,
        UserCardLoad::EARN_TYPE_SPEND_REWARD
    ];
    const DATE_TYPE_RECENT_DAYS = 'recent_days';
    const DATE_TYPE_PER_MONTH = 'per_month';
    const DATE_TYPE_DATE_RANGE = 'date_range';
    const DATE_TYPES = [
//        self::DATE_TYPE_RECENT_DAYS,
        self::DATE_TYPE_PER_MONTH,
        self::DATE_TYPE_DATE_RANGE
    ];

    /**
     * @var string
     * @ORM\Column(name="type", type="string", length=128, nullable=true)
     */
    private $type;

    /**
     * @var string
     * @ORM\Column(name="title", type="string", length=128, nullable=true)
     */
    private $title;

    /**
     * @var string
     * @ORM\Column(name="description", type="string", length=255, nullable=true)
     */
    private $desc;

    /**
     * @var int
     * @ORM\Column(name="amount", type="integer", nullable=true)
     */
    private $amount;

    /**
     * @var float
     * @ORM\Column(name="percentage", type="decimal", nullable=true)
     */
    private $percentage;

    /**
     * @var string
     * @ORM\Column(name="format", type="string", length=64, nullable=true, options={"default":"amount"}))
     */
    private $format;

    /**
     * @var int
     * @ORM\Column(name="spend_amount", type="integer", nullable=true)
     */
    private $spendAmount;

    /**
     * @var string
     * @ORM\Column(name="spend_type", type="string", length=64, nullable=true, options={"default":"total_spend"}))
     */
    private $spendType;

    /**
     * @var int
     * @ORM\Column(name="spend_count", type="integer", nullable=true)
     */
    private $spendCount;

    /**
     * @var int
     * @ORM\Column(name="count_limit", type="integer", nullable=true)
     */
    private $countLimit;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=64, nullable=true, options={"default":"active"})
     */
    private $status;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="start_at", type="datetime", nullable=true)
     */
    private $startAt;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="end_at", type="datetime", nullable=true)
     */
    private $endAt;

    /**
     * @var int
     * @ORM\Column(name="date_count", type="integer", nullable=true)
     */
    private $dateCount;

    /**
     * @var string
     * @ORM\Column(name="date_type", type="string", length=64, nullable=true)
     */
    private $dateType;

    /**
     * @var boolean
     * @ORM\Column(name="repeatable", type="boolean", options={"default": 0})
     */
    private $repeatable;

    /**
     * @var string
     *
     * @ORM\Column(name="attachment", type="string", length=255, nullable=true)
     */
    private $attachment;

    /**
     * @var \Doctrine\Common\Collections\Collection
     * @ORM\OneToMany(targetEntity="SpendrBundle\Entity\RewardTarget", mappedBy="reward")
     * @Serializer\Exclude()
     */
    private $targets;

    /**
     * @var \Doctrine\Common\Collections\Collection
     * @ORM\OneToMany(targetEntity="SpendrBundle\Entity\RewardLocation", mappedBy="reward")
     * @Serializer\Exclude()
     */
    private $locations;

    public function __construct()
    {
        $this->targets = new ArrayCollection();
        $this->locations = new ArrayCollection();
    }

    /**
     * @param $type
     * @return object|null
     * @throws \Doctrine\ORM\Exception\NotSupported
     */
    public static function findOneByType($type)
    {
        return Util::em()->getRepository(self::class)->findOneBy([
            'type' => $type,
            'status' => self::STATUS_ACTIVE
        ]);
    }

    /**
     * @param $type
     * @return array|int|string
     * @throws \Doctrine\ORM\Exception\NotSupported
     */
    public static function findListByType($type)
    {
        return Util::em()->getRepository(SpendrReward::class)->createQueryBuilder('r')
            ->where('r.status = :status')
            ->setParameter('status', SpendrReward::STATUS_ACTIVE)
            ->andWhere('r.type = :type')
            ->setParameter('type', $type)
            ->getQuery()
            ->getResult();
    }

    public function toApiArray(bool $extra = false): array
    {
        return [
            'ID' => $this->getId(),
            'Type' => $this->getType(),
            'Title' => $this->getTitle(),
            'Location IDs' => $this->getLocationsBy('id'),
            'Locations' => implode(', ', $this->getLocationsBy('name')),
            'Amount' => $this->getAmount() ? Money::formatAmountToNumber($this->getAmount()) : null,
            'Percentage' => $this->getPercentage() ?? null,
            'Reward Format' => $this->getFormat(),
            'Spend Amount' => Money::formatAmountToNumber($this->getSpendAmount()),
            'Spend Type' => $this->getSpendType(),
            'Spend Type Text' => $this->getSpendTypeText(),
            'Spend Count' => $this->spendCount,
            'Count Limit' => $this->getCountLimit(),
            'Date Type' => $this->getDateType(),
            'Date Count' => $this->getDateCount(),
            'Start Date' => Util::formatDateTime($this->getStartAt()),
            'End Date' => Util::formatDateTime($this->getEndAt()),
            'Repeatable' => $this->getRepeatable(),
            'Attachment' => $this->getAttachment(),
            'Created At' => Util::formatDateTime($this->getCreatedAt(), Util::DATE_TIME_FORMAT, Util::timezone())
        ];
    }

    public function toAppArray()
    {
        $endDays = 0;
        if ($this->getDateType() === self::DATE_TYPE_DATE_RANGE) {
            $endDays = Carbon::parse($this->getEndAt())->diffInDays(Carbon::now()) + 1;
        } elseif ($this->getDateType() === self::DATE_TYPE_PER_MONTH) {
            $endDays = Carbon::now()->endOfMonth()->diffInDays(Carbon::now()) + 1;
        }
        $purchased = 0;
        if ($this->getSpendCount()) {
            $purchasedData = TransactionService::getTransactionDataWithMinAmount(
                Util::user(),
                $this->getSpendAmount(),
                $this->getDateType() === SpendrReward::DATE_TYPE_PER_MONTH ? Carbon::now()->startOfMonth() : $this->getStartAt(),
                $this->getDateType() === SpendrReward::DATE_TYPE_PER_MONTH ? Carbon::now()->endOfMonth() : $this->getEndAt()
            );
            $purchased = $purchasedData['total'];
        }
        return [
            'id'          => $this->getId(),
            'title'       => $this->getTitle(),
            'type'        => $this->getType(),
            'format'      => $this->getFormat(),
            'amount'      => $this->getAmount(),
            'percent'     => $this->getPercentage(),
            'spendType'   => $this->getSpendType(),
            'spendAmount' => $this->getSpendAmount(),
            'spendCount'  => $this->getSpendCount(),
            'purchasedCount' => $purchased,
            'endDays'     => $endDays
        ];
    }

    public function getSpendTypeText()
    {
        if (!$this->getSpendType()) return null;
        if ($this->getSpendType() === self::SPEND_TYPE_OVER_AMOUNT) {
            return 'Spend over amount';
        }
        return 'Spend over # of txns with min amount';
    }

    /**
     * Set type
     *
     * @param string $type
     *
     * @return SpendrReward
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set title
     *
     * @param string $title
     *
     * @return SpendrReward
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Set desc
     *
     * @param string $desc
     *
     * @return SpendrReward
     */
    public function setDesc($desc)
    {
        $this->desc = $desc;

        return $this;
    }

    /**
     * Get desc
     *
     * @return string
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * Set amount
     *
     * @param integer $amount
     *
     * @return SpendrReward
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * Get amount
     *
     * @return integer
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Set percentage
     *
     * @param float $percentage
     *
     * @return SpendrReward
     */
    public function setPercentage($percentage)
    {
        $this->percentage = $percentage;

        return $this;
    }

    /**
     * Get percentage
     *
     * @return float
     */
    public function getPercentage()
    {
        return $this->percentage;
    }

    /**
     * Set format
     *
     * @param string $format
     *
     * @return SpendrReward
     */
    public function setFormat($format)
    {
        $this->format = $format;

        return $this;
    }

    /**
     * Get format
     *
     * @return string
     */
    public function getFormat()
    {
        return $this->format;
    }

    /**
     * Set spendAmount
     *
     * @param integer $spendAmount
     *
     * @return SpendrReward
     */
    public function setSpendAmount($spendAmount)
    {
        $this->spendAmount = $spendAmount;

        return $this;
    }

    /**
     * Get spendAmount
     *
     * @return integer
     */
    public function getSpendAmount()
    {
        return $this->spendAmount;
    }

    /**
     * Set spendType
     *
     * @param string $type
     *
     * @return SpendrReward
     */
    public function setSpendType($type)
    {
        $this->spendType = $type;

        return $this;
    }

    /**
     * Get spendType
     *
     * @return string
     */
    public function getSpendType()
    {
        return $this->spendType;
    }

    /**
     * Set spendCount
     *
     * @param integer $spendCount
     *
     * @return SpendrReward
     */
    public function setSpendCount($spendCount)
    {
        $this->spendCount = $spendCount;

        return $this;
    }

    /**
     * Get spendCount
     *
     * @return integer
     */
    public function getSpendCount()
    {
        return $this->spendCount;
    }

    /**
     * Set countLimit
     *
     * @param integer $countLimit
     *
     * @return SpendrReward
     */
    public function setCountLimit($countLimit)
    {
        $this->countLimit = $countLimit;

        return $this;
    }

    /**
     * Get countLimit
     *
     * @return integer
     */
    public function getCountLimit()
    {
        return $this->countLimit;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return SpendrReward
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set dateCount
     *
     * @param integer $dateCount
     *
     * @return SpendrReward
     */
    public function setDateCount($dateCount)
    {
        $this->dateCount = $dateCount;

        return $this;
    }

    /**
     * Get dateCount
     *
     * @return integer
     */
    public function getDateCount()
    {
        return $this->dateCount;
    }

    /**
     * Set dateType
     *
     * @param string $dateType
     *
     * @return SpendrReward
     */
    public function setDateType($dateType)
    {
        $this->dateType = $dateType;

        return $this;
    }

    /**
     * Get dateType
     *
     * @return string
     */
    public function getDateType()
    {
        return $this->dateType;
    }

    /**
     * Set startAt
     *
     * @param \DateTime $startAt
     *
     * @return SpendrReward
     */
    public function setStartAt($startAt)
    {
        $this->startAt = $startAt;

        return $this;
    }

    /**
     * Get startAt
     *
     * @return \DateTime
     */
    public function getStartAt()
    {
        return $this->startAt;
    }

    /**
     * Set endAt
     *
     * @param \DateTime $endAt
     *
     * @return SpendrReward
     */
    public function setEndAt($endAt)
    {
        $this->endAt = $endAt;

        return $this;
    }

    /**
     * Get endAt
     *
     * @return \DateTime
     */
    public function getEndAt()
    {
        return $this->endAt;
    }

    /**
     * Set repeatable
     *
     * @param $repeatable
     *
     * @return SpendrReward
     */
    public function setRepeatable($repeatable)
    {
        $this->repeatable = $repeatable;

        return $this;
    }

    /**
     * Get repeatable
     *
     * @return bool
     */
    public function getRepeatable()
    {
        return $this->repeatable;
    }

    /**
     * Set attachment
     * @param $attachment
     * @return SpendrReward
     */
    public function setAttachment($attachment)
    {
        $this->attachment = $attachment;

        return $this;
    }

    /**
     * Get attachment
     * @return string
     */
    public function getAttachment()
    {
        return $this->attachment;
    }

    /**
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTargets()
    {
        return $this->targets;
    }

    /**
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getLocations()
    {
        return $this->locations;
    }

    /**
     * @param $field
     * @return array
     */
    public function getLocationsBy($field)
    {
        if ($this->getLocations()) {
            $ids = [];
            /** @var RewardLocation $l */
            foreach ($this->getLocations() as $l) {
                $ids[] = Util::field($l->getLocation(), $field);
            }
            return $ids;
        }
        return [];
    }
}
