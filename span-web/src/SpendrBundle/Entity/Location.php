<?php

namespace SpendrBundle\Entity;


use Api<PERSON><PERSON>le\Entity\ApiEntityInterface;
use Core<PERSON><PERSON>le\Entity\Address;
use CoreBundle\Entity\BaseEntity;
use CoreBundle\Entity\UserCard;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>MS\Serializer\Annotation as Serializer;

/**
 * Location
 *
 * @ORM\Table(name="spendr_location", options={"These are the locations that roll up under Merchants"})
 * @ORM\Entity(repositoryClass="SpendrBundle\Repository\LocationRepository")
 */
class Location extends BaseEntity implements ApiEntityInterface
{
    public const STATUS_ACTIVE = 'Active';
    public const STATUS_INACTIVE = 'Inactive';

	public function toApiArray(bool $extra = false): array
	{
		$address = $this->getAddress();
		if (!$address) {
			$address = new Address();
			$this->setAddress($address)
				->persist();
		}
		$data = [
			'id' => $this->getId(),
			'name' => $this->getName(),
			'locationId' => $this->getNumber(),
            'changedToMerchant' => $this->getChangedToMerchant()
		];

		if ($extra) {
			array_merge($data, [
				'address' => $address->toApiArray($extra),
			]);
		}

		return $data;
	}

	public function generateNumber()
	{
		$default = 1000;
		$l = Util::em()->getRepository(self::class)
			->createQueryBuilder('l')
			->orderBy('l.id', 'desc')
			->setMaxResults(1)
			->getQuery()
			->getResult();
		if ($l && $l[0]) {
			$location = $l[0];
			if ($location->getNumber()) {
				return $location->getNumber() + 1;
			}
		}
		return $default;
	}

   /**
     * @param $id
     * @return static
     */
    public static function find($id)
    {
        return Util::em()->getRepository(self::class)->find($id);
    }

    public function isActive()
    {
        return $this->getStatus() === self::STATUS_ACTIVE;
    }

	/**
	 * Location constructor.
	 */
	public function __construct()
	{
		$this->setNumber(self::generateNumber())
        ->setChangedToMerchant(0);
        $this->admins = new ArrayCollection();
	}

	/**
	 * @var string
	 *
	 * @ORM\Column(name="name", type="string", length=180)
	 */
	private $name;

	/**
	 * @var string
	 *
	 * @ORM\Column(name="number", type="string", length=180, nullable=true)
	 */
	private $number;

	/**
	 * @var Address
	 *
	 * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Address")
	 */
	private $address;

	/**
	 * @var SpendrMerchant
	 *
	 * @ORM\ManyToOne(targetEntity="SpendrBundle\Entity\SpendrMerchant")
	 */
	private $merchant;

	/**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true)
	 */
	private $status;

	/**
	 * @var Collection
	 *
	 * @ORM\OneToMany(targetEntity="SpendrBundle\Entity\LocationEmployee", mappedBy="location")
	 */
	private $employees;

	/**
	 * @var Collection
	 *
	 * @ORM\OneToMany(targetEntity="SpendrBundle\Entity\Terminal", mappedBy="location")
	 */
	private $terminals;

   /**
     * @var string
     *
     * @ORM\Column(name="hours", type="string", length=1023, nullable=true)
     */
    private $hours;

    /**
     * @var string
     *
     * @ORM\Column(name="hours_weekend", type="string", length=1023, nullable=true)
     */
    private $hoursWeekend;

     /**
     * @var integer
     *
     * @ORM\Column(name="changed_to_merchant", type="integer", nullable=true, options={"default"=0})
     */
    protected $changedToMerchant;

    /**
     * @var string
     *
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * @ORM\ManyToMany(targetEntity="SalexUserBundle\Entity\User", mappedBy="locations")
     * @ORM\JoinTable(name="spendr_locations_admins",
     *      joinColumns={@ORM\JoinColumn(name="location_id", referencedColumnName="id", onDelete="cascade")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="admin_id", referencedColumnName="id", unique=false)}
     *     )
     * @Serializer\Exclude()
     */
    private $admins;

     /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $adminUser;

    /**
	 * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\UserCard")
	 * @ORM\JoinColumn(name="bank_card_id", referencedColumnName="id", onDelete="cascade")
	 */
	private $bankCard;

	/**
     * Set name
     *
     * @param string $name
     *
     * @return Location
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set number
     *
     * @param string $number
     *
     * @return Location
     */
    public function setNumber($number)
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Get number
     *
     * @return string
     */
    public function getNumber()
    {
        return $this->number;
    }

    /**
     * Set address
     *
     * @param Address $address
     *
     * @return Location
     */
    public function setAddress(Address $address = null)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return Address
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set merchant
     *
     * @param SpendrMerchant $merchant
     *
     * @return Location
     */
    public function setMerchant(SpendrMerchant $merchant = null)
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * Get merchant
     *
     * @return SpendrMerchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return Location
     */
    public function setStatus(string $status = null)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

	/**
	 * Add employee
	 *
	 * @param LocationEmployee $employee
	 *
	 * @return Location
	 */
	public function addEmployee(LocationEmployee $employee)
	{
		$this->employees[] = $employee;

		return $this;
	}

	/**
	 * Remove employee
	 *
	 * @param LocationEmployee $employee
	 */
	public function removeEmployee(LocationEmployee $employee)
	{
		$this->employees->removeElement($employee);
	}

	/**
	 * Get employees
	 *
	 * @return Collection
	 */
	public function getEmployees()
	{
		return $this->employees;
	}

	/**
	 * Add terminal
	 *
	 * @param Terminal $terminal
	 *
	 * @return Location
	 */
	public function addTerminal(Terminal $terminal)
	{
		$this->terminals[] = $terminal;

		return $this;
	}

	/**
	 * Remove terminal
	 *
	 * @param Terminal $terminal
	 */
	public function removeTerminal(Terminal $terminal)
	{
		$this->terminals->removeElement($terminal);
	}

	/**
	 * Get terminals
	 *
	 * @return Collection
	 */
	public function getTerminals()
	{
		return $this->terminals;
	}

    /**
     * Set hours
     *
     * @param string $hours
     *
     * @return Location
     */
    public function setHours($hours)
    {
        $this->hours = $hours;

        return $this;
    }

    /**
     * Get hours
     *
     * @return string
     */
    public function getHours()
    {
        return $this->hours;
    }

    /**
     * Set hoursWeekend
     *
     * @param string $hoursWeekend
     *
     * @return Location
     */
    public function setHoursWeekend($hoursWeekend)
    {
        $this->hoursWeekend = $hoursWeekend;

        return $this;
    }

    /**
     * Get hoursWeekend
     *
     * @return string
     */
    public function getHoursWeekend()
    {
        return $this->hoursWeekend;
    }

     /**
     * Set changedToMerchant
     *
     * @param integer $changedToMerchant
     *
     * @return Location
     */
    public function setChangedToMerchant($changedToMerchant)
    {
        $this->changedToMerchant = $changedToMerchant;

        return $this;
    }

    /**
     * Get changedToMerchant
     *
     * @return integer
     */
    public function getChangedToMerchant()
    {
        return $this->changedToMerchant;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return Location
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

     /**
     * Add admin
     *
     * @param \SalexUserBundle\Entity\User $admin
     *
     * @return Location
     */
    public function addAdmin(\SalexUserBundle\Entity\User $admin)
    {
        $this->admins[] = $admin;

        return $this;
    }

    /**
     * Remove admin
     *
     * @param \SalexUserBundle\Entity\User $admin
     */
    public function removeAdmin(\SalexUserBundle\Entity\User $admin)
    {
        $this->admins->removeElement($admin);
    }

    /**
     * Get admins
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getAdmins()
    {
        return $this->admins;
    }

    /**
     * Set adminUser
     *
     * @param \SalexUserBundle\Entity\User $adminUser
     *
     * @return Location
     */
    public function setAdminUser(\SalexUserBundle\Entity\User $adminUser = null)
    {
        $this->adminUser = $adminUser;

        return $this;
    }

    /**
     * Get adminUser
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getAdminUser()
    {
        return $this->adminUser;
    }

    /**
	 * Set bankCard
	 *
	 * @param UserCard $bankCard
	 *
	 * @return Location
	 */
	public function setBankCard(UserCard $bankCard = null)
	{
		$this->bankCard = $bankCard;

		return $this;
	}

	/**
	 * Get bankCard
	 *
	 * @return UserCard
	 */
	public function getBankCard()
	{
		return $this->bankCard;
	}
}
