<?php

namespace SpendrBundle\Traits;

use <PERSON><PERSON><PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON><PERSON>\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Entity\Role;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\Location;
use SpendrBundle\Services\MerchantService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;

trait TransactionTrait
{
	public function traitGetMerchantTotalRevenue(User $user, Request $request = null, $forSummary = false)
	{
		if (!$user) {
			return 0;
		}
		$expr = Util::expr();

		$query = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN)
			->andWhere('t.status = :status')
			->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED));

		$merchant = null;
		if ($user->inTeams(SpendrBundle::getMerchantAdminRoles()) && !$forSummary) {
			$merchant = $user->getSpendrAdminMerchant();
			if (!$merchant) {
				return 0;
			}
			$query = MerchantService::generateMergeMerchantQuery($query, 't.merchant', $merchant);

			$locationId = $request->get('locationId', 'all');
			if ($locationId === 'all') {
				if ($user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
					$query->join('t.location', 'l')
					->join('l.admins', 'locationAdmin')
					->andWhere('locationAdmin.id = :uid')
					->setParameter('uid', $user->getId());
				}
			} else {
				if (is_numeric($locationId)) {
					$location = Location::find($locationId);
					$query->andWhere('t.location = :location')
						->setParameter('location', $location);
				}
			}
		} else {
			$testMerchantIds = MerchantService::merchantIdsForTestArray();
			if ($testMerchantIds) {
				$query->andWhere($expr->notIn('m.id', ':ids'))
					->setParameter('ids', $testMerchantIds);
			}
		}

		if (!$forSummary) {
			$query = $this->queryByDateRange($query, 't.dateTime', $request);
		}

		$totalPurchase = (int)(
		(clone $query)->andWhere($expr->in('t.type', ':type'))
			->setParameter('type', TransactionType::gets([
			    TransactionType::NAME_PURCHASE,
                TransactionType::NAME_CHECKOUT_ONLINE
            ]))
			->select('sum(t.amount)-sum(t.spendrFee)')
			->getQuery()
			->getSingleScalarResult()
		);

		$totalRefund = (int)(
		(clone $query)->andWhere($expr->in('t.type', ':type'))
            ->setParameter('type', TransactionType::gets([
                TransactionType::NAME_REFUND,
                TransactionType::NAME_ONLINE_REFUND
            ]))
			->select('sum(t.amount)-sum(t.refundFee)+sum(t.spendrFee)')
			->getQuery()
			->getSingleScalarResult()
		);

		return round($totalPurchase - $totalRefund, 2);
	}

	public function getTransactionData(
		Request $request,
		User $user,
		$txnType = 'all',
		$txnStatus = TransactionStatus::NAME_COMPLETED,
		$queryType = 'all'
	){
		$query = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
            ->leftJoin('t.tip', 'st')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN);

		if ($user->inTeams(SpendrBundle::getMerchantAdminRoles())) {
			$query = MerchantService::generateMergeMerchantQuery($query, 't.merchant', $user->getSpendrAdminMerchant());

			$locationId = $request->get('locationId', 'all');
			if ($locationId === 'all') {
				if ($user->inTeams(SpendrBundle::getMerchantDashboardAdminRoles())) {
					$query->join('t.location', 'l')
					->join('l.admins', 'locationAdmin')
					->andWhere('locationAdmin.id = :uid')
					->setParameter('uid', $user->getId());
				}
			} else {
				if (is_numeric($locationId)) {
					$location = Location::find($locationId);
					$query->andWhere('t.location = :location')
						->setParameter('location', $location);
				}
			}
		}

		if ($txnType !== 'all') {
			$query->andWhere('t.type = :type')
				->setParameter('type', TransactionType::get($txnType));
		}

		if ($txnStatus !== 'all') {
			$query->andWhere('t.status = :status')
				->setParameter('status', TransactionStatus::get($txnStatus));
		}

		$query = $this->queryByDateRange($query, 't.dateTime', $request);

		if ($queryType === 'count') {
			return $query->select('count(distinct t)')
				->getQuery()
				->getSingleScalarResult();
		} else {
			$res = $query->select(
				'count(distinct t) as total, sum(t.amount) as amount, sum(t.spendrFee) as spendrFee, sum(t.refundFee) as refundFee,
				 count(distinct st) tipTotal, sum(st.amount) tipAmount, sum(st.spendrFee) tipFee')
				->getQuery()
				->getSingleResult();
			return $res ? [
			    $res['total'], $res['amount'], $res['spendrFee'], $res['refundFee'],
                $res['tipTotal'], $res['tipAmount'], $res['tipFee']
            ] : [null, null, null, null, null, null, null];
		}
	}
}
