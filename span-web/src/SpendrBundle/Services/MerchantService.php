<?php

namespace SpendrBundle\Services;

use Clf<PERSON><PERSON>le\ClfBundle;
use Clf<PERSON><PERSON>le\Entity\Merchant;
use Clf<PERSON><PERSON>le\Entity\MerchantStatus;
use CoreBundle\Entity\Address;
use CoreBundle\Entity\CardProgram;
use Core<PERSON><PERSON>le\Entity\CardProgramCardType;
use CoreBundle\Entity\Country;
use CoreB<PERSON>le\Entity\Role;
use CoreBundle\Entity\State;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\FailedException;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\MerchantAdmin;
use Spendr<PERSON>undle\Entity\SpendrGroup;
use Spendr<PERSON>undle\Entity\SpendrMerchant;
use S<PERSON><PERSON>Bundle\Entity\Location;
use Spendr<PERSON><PERSON>le\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use SpendrBundle\Services\UserService as ServicesUserService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use function Functional\pluck;

class MerchantService
{
    const KEY_MERCHANT_ADMIN_AND_EMPLOYEE = 's_m__a&e';
    const MERGED_OTHER_PARAM_NAME = 'mergeMerchant';
    const MERGED_INTO_PARAM_NAME = 'mergeIntoMerchant';

    public static function isLocationHasDummyMerchant(SpendrMerchant $merchant)
	{
		return $merchant && Util::meta($merchant, 'locationHasDummy');
	}

    public static function isMergedOtherMerchant(SpendrMerchant $merchant, $returnType = 'bool')
    {
        $idMetaName = self::MERGED_OTHER_PARAM_NAME;
        $statusMetaName = 'needMergeMerchant';
        if ($returnType === 'name') {
            return $idMetaName;
        }
        if ($returnType === 'id') {
            return $merchant && Util::meta($merchant, $statusMetaName) ? Util::meta($merchant, $idMetaName) : null;
        }
        return $merchant && Util::meta($merchant, $statusMetaName) && Util::meta($merchant, $idMetaName);
    }

    public static function isMergedIntoOtherMerchant(SpendrMerchant $merchant, $returnType = 'bool')
    {
        $idMetaName = self::MERGED_INTO_PARAM_NAME;
        if ($returnType === 'name') {
            return $idMetaName;
        }
        if ($returnType === 'id') {
            return $merchant ? Util::meta($merchant, $idMetaName) : null;
        }
        if ($returnType === 'object') {
            $otherMerchantId = Util::meta($merchant, $idMetaName);
            if ($merchant && $otherMerchantId) {
                $otherMerchant = SpendrMerchant::find($otherMerchantId);
                return $otherMerchant;
            } else {
                return null;
            }
        }
        return $merchant && Util::meta($merchant, $idMetaName);
    }

    public static function saveDetails(Request $request, User $user)
    {
        $em = Util::em();
        $groupId = $request->get('Group ID');
        $legalEntityName = $request->get('Legal Entity Name');
        $name = $request->get('Merchant Name');
        $email = $request->get('Email');
        $country = Country::find($request->get('CountryId'));
        $state = State::find($request->get('State'));
        $mobile = Util::inputPhone($request->get('Phone'), $country);
		$ateAmount = $request->get('Average Ticket Estimate Amount');
		$mteCount = $request->get('Monthly Ticket Estimate Count');
		$ateAmount = Money::normalizeAmountOrNull($ateAmount);
		$role = Role::ROLE_SPENDR_MERCHANT_ADMIN;

		$merchantAdmin = null;

        if ($user->inTeams(SpendrBundle::getAllMerchantAdminRoles())) {
            $group = $user->getCurrentGroup();
        } else {
            $group = SpendrGroup::find($groupId);
        }

        $id = $request->get('Merchant ID');
        $isNew = false;
        $user = null;
        if ($id) {
        	/** @var SpendrMerchant $merchant */
            $merchant = $em->getRepository(SpendrMerchant::class)
                ->find($id);
            if (!$merchant) {
                throw new FailedException('Invalid merchant!');
            }

            $user = $merchant ? $merchant->getAdminUser() : null;
            if ($user && $email !== $user->getEmail()) {
                $another = User::findPlatformUserByEmail($email, [$role]);
                if ($another) {
                    throw new FailedException('The email address has been registered by another merchant!');
                }
            }

            if ($merchant->getName() !== $name) {
                self::updateBrazeMerchantName($id, $name);
            }

            /** @var MerchantAdmin $merchantAdmin */
            $merchantAdmin = $em->getRepository(MerchantAdmin::class)
				->findOneBy([
					'user' => $user,
					'merchant' => $merchant
				]);
            // if ($merchantAdmin && !$merchantAdmin->getGroup()) {
			// 	$merchantAdmin->setGroup($group);
			// 	Util::persist($merchantAdmin);
			// }
            $isNew = false;
        } else {
            $another = $em->getRepository(SpendrMerchant::class)
                ->findOneBy([
                    'name' => $name,
                ]);
            if ($another) {
                throw new FailedException('The merchant name has been registered by another merchant!');
            }

            $another = User::findPlatformUserByEmail($email, [$role]);
            if ($another) {
                throw new FailedException('The email address has been registered by another merchant!');
            }

            $merchant = new SpendrMerchant();
            $isNew = true;
        }

        if ($isNew) {
            $merchant->setMeta(Util::j2s([
                'locationHasDummy' => true,
            ]));
        }

        $group = null;
        if ($groupId) {
            $group = SpendrGroup::find($groupId);
        }

        $merchant->setLegalEntityName($legalEntityName)
            ->setName($name)
            ->setDba($request->get('Doing Business As'))
            ->setOnboardingStatus(
                $merchant->getOnboardingStatus() ?? SpendrMerchant::ONBOARDING_STATUS_INITIAL
            )
			->setAverageTicketEstimateAmount($ateAmount)
			->setMonthlyTicketEstimateCount($mteCount)
			->setStoreHours($request->get('Store Hours'))
			->setStoreHoursWeekend($request->get('Store Hours Weekend'))
			->setWebsite($request->get('Website'))
			->setGroup($group)
            ->persist();

        $address = $merchant->getAddress() ?? new Address();
        $address->setCountry($country)
            ->setState($state)
            ->setCity($request->get('City'))
            ->setPostalCode($request->get('Postal Code'))
            ->setAddress1($request->get('Business Street Address'))
            ->setEmail($email)
            ->setPhone($request->get('Phone'));
        Util::persist($address);

        if ( ! $user) {
            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setSource('spendr_merchant_onboard');

            $user->setFirstName($merchant->getName())
                ->setLastName('Merchant Admin')
//                ->changeMobilePhone(Util::inputPhone($request->get('Phone'), $country), $role)
                ->setMobilephone($mobile)
                ->setAddress($request->get('Business Street Address'))
                ->setCountry($country)
                ->setState($state)
                ->setCity($request->get('City'))
                ->setZip($request->get('Postal Code'))
                ->changeEmail($email, $role)
                ->ensureRole($role)
                ->persist();

            $cp = Util::cardProgram();
            ServicesUserService::getDummyCard($user);

            UserService::sendResetPasswordEmail($user, $cp);

            BrazeService::userTrack(null, null, null, [
                'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
                'phone' => $user->getMobilephone(),
                'merchant_name' => $merchant->getName(),
                'merchant_role' => $role,
                'merchant_balance' => 0,
                'email_subscribe' => $user->getStatus() === User::STATUS_ACTIVE ? BrazeService::TYPE_OPTEDIN : BrazeService::TYPE_UNSUBSCRIBED
            ]);
        } else {
            if (
                $email !== $user->getEmail()
                || $mobile !== $user->getMobilephone()
                || $name !== $user->getFirstName()
            ) {
                $resetEmail = false;
                if ($email !== $user->getEmail()) {
                    $user->changeEmail($email, $role);
                    $resetEmail = true;
                }
                if ($mobile !== $user->getMobilephone()) {
                    $user->changeMobilePhone($mobile);
                }
                if ($name !== $user->getFirstName()) {
                    $user->setFirstName($name);
                }

                $user->persist();

                if ($resetEmail) {
                    UserService::sendResetPasswordEmail($user, Util::cardProgram());
                }

                BrazeService::userTrack(null, null, null, [
                    'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                    'first_name' => $user->getFirstName(),
                    'email' => $user->getEmail(),
                    'phone' => $user->getMobilephone(),
                ]);
            }
        }

        $merchant->setAddress($address)
            ->setAdminUser($user)
            ->persist();

        $user->ensureAccessiblePlatformProgram()
            ->persist();

		if (!$merchantAdmin) {
			$merchantAdmin = new MerchantAdmin();
			$merchantAdmin->setUser($user)
				->setMerchant($merchant);
				// ->setGroup($group);
			Util::persist($merchantAdmin);
		}
        return $merchant;
    }

    public static function queryAllMerchants()
    {
        $expr = Util::expr();

		$query = Util::em()->getRepository(SpendrMerchant::class)
			->createQueryBuilder('sm');

		$testMerchantIds = self::merchantIdsForTestArray();
		if ($testMerchantIds) {
			$query->where($expr->notIn('sm.id', $testMerchantIds));
		}

        return $query;
    }

    public static function getAllMerchants(SpendrGroup $group = null, $nullGroup = false)
    {
        $expr = Util::expr();

        $query = Util::em()->getRepository(SpendrMerchant::class)
            ->createQueryBuilder('m')
            ->where('m.id >= :firstM')
            ->setParameter('firstM', SpendrBundle::FIRST_MERCHANT_ID)
            ->andWhere('m.meta not like :metaMerge')
            ->setParameter('metaMerge', '%"' . self::MERGED_INTO_PARAM_NAME . '":true%');

        $testMerchantIds = self::merchantIdsForTestArray();
        if ($testMerchantIds) {
            $query->andWhere($expr->notIn('m.id', $testMerchantIds));
        }

        if ($group) {
            $query->andWhere($expr->orX(
                    $expr->eq('m.group', ':group'),
                    $expr->isNull('m.group')
                ))->setParameter('group', $group);
        } else {
            $query->andWhere('m.group is null');
        }

        return $query->getQuery()->getResult();
    }

    public static function getTotalActiveMerchantBalance(User $user)
    {
        $query = self::queryCurrentActiveMerchant();

        if ($user->inTeam(Role::ROLE_SPENDR_GROUP_ADMIN)) {
            $group = $user->getCurrentGroup();
            $query->andWhere('sm.group = :group')
            ->setParameter('group', $group);
        }

        return $query->join('sm.adminUser', 'u')
        ->join('u.cards', 'uc')
        ->join('uc.card', 'c')
        ->andWhere('c.cardProgram = :cardProgram')
        ->setParameter('cardProgram', CardProgram::spendr())
        ->andWhere('uc.type = :type')
        ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
        ->select('sum(uc.balance)')
        ->getQuery()
        ->getSingleScalarResult();
    }

    public static function queryCurrentActiveMerchant()
	{
		$expr = Util::expr();

		$query = Util::em()->getRepository(SpendrMerchant::class)
			->createQueryBuilder('sm')
			->where('sm.onboardingStatus = :onboardingStatus')
			->setParameter('onboardingStatus', SpendrMerchant::ONBOARDING_STATUS_APPROVED)
			->andWhere('sm.status = :status')
			->setParameter('status', MerchantStatus::active())
			->andWhere($expr->like('sm.meta', ':bankApprovedAt'))
			->setParameter('bankApprovedAt', '%"bankApprovedAt":%');

		$testMerchantIds = self::merchantIdsForTestArray();
		if ($testMerchantIds) {
			$query->andWhere($expr->notIn('sm.id', $testMerchantIds));
		}

		return $query;
	}

    public static function getCurrentActiveMerchants()
	{
		return self::queryCurrentActiveMerchant()
			->getQuery()
			->getResult();
	}

    public static function getCurrentActiveMerchantIds()
	{
		$rs = self::queryCurrentActiveMerchant()
			->select('sm.id')
			->getQuery()
			->getResult();
		return pluck($rs, 'id');
	}

    public static function getCurrentActiveMerchantCount()
	{
		$count = self::queryCurrentActiveMerchant()
			->select('count(distinct sm)')
			->getQuery()
			->getSingleScalarResult();

		return (int)$count;
	}

	public static function merchantIdsForTestArray($isApp = false)
	{
		if (Util::isLive()) {
			$arr = [
				23, // Meta
				25, // tracy_test_merchant
				26, // Vitta TEST
//				28, // Spendr Test Merchant
                30, // Tern TEST Merchant
                32, // Test_merchant
                36, // Spendr NEW Test Merchant
				37, // Spendr Test Onboard
				38, // New New Onboard Test
				40, // Testing 1
				41  // L:G Test Merchant
			];
			if ($isApp) {
			    array_push($arr, [
			        28
                ]);
            }
			return $arr;
		}

//		// for test
//		if (Util::isStaging()) {
//			return [
//				31
//			];
//		}

//		// for test
//		if (Util::isLocal()) {
//			return [
//				25
//			];
//		}

		return [];
	}

	public static function isMerchantForTest(SpendrMerchant $merchant)
	{
		if (Util::isLive()) {
			$testArray = self::merchantIdsForTestArray();
			if ($testArray && in_array($merchant->getId(), $testArray)) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	public static function getRealCards(User $merchantAdmin, $type = 'active')
    {
        $query = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'cpct')
            ->join('uc.user', 'u')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->andWhere('uc.type != :type')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY);

        $currentMerchant = SpendrMerchant::findByAdminUser($merchantAdmin);
        $query = self::generateMergeMerchantBalanceAdminQuery($query, 'u.id', $currentMerchant, 'id');

        if ($type === 'pending') {
            $query->andWhere('uc.status = :status')
                ->andWhere('uc.meta like :meta')
                ->setParameter('status', UserCard::STATUS_PENDING)
                ->setParameter('meta', '%"verification_status":"pending_manual_verification"%');
        } else if ($type === 'active') {
            $query->andWhere('uc.status = :status')
                ->setParameter('status', UserCard::STATUS_ACTIVE);
        }

        return $query->getQuery()
            ->getResult();
    }

    /**
     * Get merchant active real card
     * @param User $merchantAdmin
     * @return UserCard|null
     */
	public static function getActiveRealCard(User $merchantAdmin)
    {
        $uc = self::getRealCards($merchantAdmin);
        if ($uc && count($uc)) return $uc[0];
        return null;
    }

    public static function getVerifyPendingCard(User $merchantAdmin)
    {
        $uc = self::getRealCards($merchantAdmin, 'pending');
        if ($uc && count($uc)) return $uc[0];
        return null;
    }

    /**
     * @param $token
     * @param $merchant
     * @param UserCard|null $uc
     * @return array|string
     */
    public static function saveAuthPlaidCard($token, $merchant, $uc = null)
    {
        $info = PlaidService::getAccountInformation($token);
        if (!$info || !isset($info->accounts) || !isset($info->numbers)) {
            return 'No bank account information.';
        }
        $banks = [];
        $institutionId = $info->item->institution_id;
        $itemId = $info->item->item_id;
        $merchantAdmin = $merchant->getAdminUser();
        $cards = self::getRealCards($merchantAdmin);
        foreach ($info->accounts as $item) {
            $accountId = $item->account_id;
            $bankName = $item->name;
            $mask = $item->mask;
            $subtype = $item->subtype;
            if (strlen($bankName) > 64) {
                $bankName = $subtype . ' ' . $mask;
            }
            $account = array_first($info->numbers->ach, function ($item) use ($accountId) {
                return $item->account_id == $accountId;
            });
            $accountNum = $account ? $account->account : null;
            $routingNum = $account ? $account->routing : null;
            $encryptedAcctNum = SSLEncryptionService::encrypt($accountNum);
            $encryptedRouting = SSLEncryptionService::encrypt($routingNum);
            if (!$accountId || !$bankName || !$accountNum || !$routingNum) {
                continue;
            }
            foreach ($cards as $card) {
                if (($card->getAccountNumber()
                        && $card->getRoutingNumber()
                        && $accountNum === SSLEncryptionService::tryToDecrypt($card->getAccountNumber())
                        && $routingNum === SSLEncryptionService::tryToDecrypt($card->getRoutingNumber()))
                    || (Util::meta($card, 'mask') === $mask
                        && $card->getBankName() === $bankName
                        && Util::meta($card, 'institution_id') === $institutionId)
                ) {
                    $card->setStatus(UserCard::STATUS_INACTIVE)
                        ->persist();
                }
            }
            $meta = [
                'account_id' => $accountId,
                'mask' => $mask,
                'routing_mask' => substr($routingNum, -4),
                'institution_id' => $institutionId,
                'item_id' => $itemId,
                'subtype' => $subtype,
                'manual_link' => $uc && $uc->getStatus() == UserCard::STATUS_PENDING
            ];
            // Create the linked card as active card
            if (!$uc) {
                $uc = new UserCard();
                $uc->setUser($merchantAdmin)
                    ->setCard(CardProgramCardType::getForCardProgram(CardProgram::spendr()))
                    ->setType(UserCard::LL_TYPE_PLAID)
                    ->setPlaidAccessToken($token)
                    ->setHash(Uuid::uuid4());
            }
            $uc->setBankName($bankName)
                ->setAccountNumber($encryptedAcctNum)
                ->setRoutingNumber($encryptedRouting)
                ->setInitializedAt(new \DateTime())
                ->setStatus(UserCard::STATUS_ACTIVE)
                ->setCurrency('USD')
                ->setMeta(Util::j2s($meta))
                ->persist();
            $banks[] = [
                'id' => $uc->getId(),
                'type' => UserCard::LL_TYPE_PLAID,
                'manualLink' => $meta['manual_link'],
                'bankName' => $bankName,
                'accountNum' => UserCardService::getAccountNumMask($meta),
                'routing' => UserCardService::getRoutingNumMask($meta),
                'pendingCard' => false
            ];
        }

        return $banks;
    }

    public static function calculateAllMerchantsBalance()
	{
		$expr = Util::expr();
		$subQuery = Util::em()->getRepository(User::class)
			->createQueryBuilder('u')
			->join('u.merchants', 'm');
		$balanceQuery = Util::em()->getRepository(UserCard::class)
			->createQueryBuilder('uuc')
			->join('uuc.card', 'uuct')
			->join('uuct.cardProgram', 'uucpp')
			->where($expr->eq('uuc.user', $expr->any($subQuery->getDQL())))
			->andWhere($expr->eq('uucpp.id', ':cardProgram' ))
			->setParameter('cardProgram', CardProgram::spendr()->getId())
			->select('sum(uuc.balance)');

		return Util::moveQueryParameters($balanceQuery, $subQuery)
			->getQuery()
			->getSingleScalarResult();
	}

    /**
     * Update braze merchant admins/employees balance
     * @param $merchantId
     * @param $balance
     */
    public static function updateBrazeMerchantBalance($merchantId, $balance)
    {
        $ids = Data::instance()->smembers(self::KEY_MERCHANT_ADMIN_AND_EMPLOYEE . ':' . $merchantId);
        $attrs = [];
        foreach ($ids as $id) {
            $attrs[] = [
                'external_id' => BrazeService::getExternalPrefix() . $id,
                'merchant_balance' => Money::formatAmountToNumber($balance),
            ];
        }
        BrazeService::userTrack(null, null, null, $attrs, true);
    }

    public static function updateBrazeMerchantName($merchantId, $name)
    {
        $ids = Data::instance()->smembers(self::KEY_MERCHANT_ADMIN_AND_EMPLOYEE . ':' . $merchantId);
        $attrs = [];
        foreach ($ids as $id) {
            $attrs[] = [
                'external_id' => BrazeService::getExternalPrefix() . $id,
                'merchant_name' => $name
            ];
        }
        BrazeService::userTrack(null, null, null, $attrs, true);
    }

	public static function getMerchantsByGroup(SpendrGroup $group)
	{
		return Util::em()->getRepository(SpendrMerchant::class)
			->findBy([
				'group' => $group
			]);
	}

    public static function getMerchantActiveCards(User $currentUser, $merchantId = null)
    {
        if ($currentUser->inTeams(SpendrBundle::getMerchantAdminRoles())) {
            $merchant = $currentUser->getSpendrAdminMerchant();
            $merchantAdmin = \SpendrBundle\Services\UserService::getCurrentMerchantBalanceAdmin($currentUser);
        } else if ($merchantId) {
            /** @var SpendrMerchant $merchant */
            $merchant = SpendrMerchant::find($merchantId);
            if (!$merchant) {
                return 'Invalid merchant';
            }
            $merchantAdmin = $merchant->getAdminUser();
        } else {
            return 'Invalid request';
        }
        $data = [];
        $cards = self::getRealCards($merchantAdmin);
        $locations = LocationService::listLocationsForSelection($merchant);
        /** @var UserCard $card */
        foreach ($cards as $card) {
            $accountNumber = $card->getAccountNumber() ? SSLEncryptionService::tryToDecrypt($card->getAccountNumber()) : '';
            $meta = Util::meta($card);
            if ($card->getPlaidAccessToken() != null && $meta && isset($meta['mask'])) {
                $accountNumber = '***' . $meta['mask'];
            }
            if ($meta && isset($meta['manual_link'])) {
                $manualLink = $meta['manual_link'];
            } else {
                $manualLink = !isset($meta['institution_id']) || !$meta['institution_id'];
            }
            $location = null;
            if (isset($meta['locationId'])) {
                $location = array_first($locations, function ($item) use ($meta) {
                    return $item['value'] === $meta['locationId'];
                });
            }
            $data[] = [
                'id' => $card->getId(),
                'type' => $card->getType(),
                'default' => $meta['default'] ?? false,
                'manualLink' => $manualLink,
                'bankName' => $card->getBankName(),
                'accountNum' => $accountNumber,
                'routing' => $card->getRoutingNumber() ? SSLEncryptionService::tryToDecrypt($card->getRoutingNumber()) : '',
                'location' => $location ? [
                    'id' => $location['value'],
                    'name' => $location['label']
                ] : null
            ];
        }

        return $data;
    }

    // public static function createLocationBalanceUserForOldMerchant()
    // {
    //     if (Util::isStaging()) {
    //         // $merchantId = 53;
    //         $merchantId = 33;
    //     } else if (Util::isLive()) {
    //         $merchantId = 51;
    //     } else {
    //         // SlackService::warning('create location balance info for old merchant: Not allowed locally.');
    //         // return false;
    //         $merchantId = 25;
    //     }

    //     SlackService::info('Start create location balance info for merchant ' . $merchantId . ' ...');

    //     /** @var SpendrMerchant $merchant */
    //     $merchant = SpendrMerchant::find($merchantId);

    //     if (Util::meta($merchant, 'createdLocationBalanceInfo')) {
    //         SlackService::warning('create location balance info for old merchant: cannot be created repeatedly.', [
    //             'merchantId' => $merchantId
    //         ]);
    //         return false;
    //     }

    //     if (self::isLocationHasDummyMerchant($merchant)) {
    //         SlackService::warning('create location balance info for old merchant: it is already a merchant in the new mode, and cannot be created repeatedly.',
    //         [
    //             'merchantId' => $merchantId
    //         ]);
    //         return false;
    //     }

    //     // create location admin & balance
    //     $locations = Util::em()->getRepository(Location::class)
    //     ->findBy(
    //         [
    //             'merchant' => $merchant
    //         ]
    //     );

    //     $locationCount = 0;
    //     if ($locations) {
    //         /** @var Location $location */
    //         foreach ($locations as $location) {
    //             if ($location->getAdminUser()) {
    //                 continue;
    //             }
    //             LocationService::createLocationBalanceUser($merchant, $location, true);
    //             $locationCount++;
    //         }
    //     }

    //     Util::updateMeta($merchant, [
    //         'createdLocationBalanceInfo' => true
    //     ]);

    //     Util::flush();
    //     SlackService::info('Finished create location balance info, total location: ' . $locationCount);
    // }

    // public static function associateAllLocationsWithMerchantAdmins(SpendrMerchant $merchant = null)
    // {
    //     SlackService::info('Start associating locations with existing merchant admin...');

    //     $em = Util::em();
    //     if ($merchant) {
    //         $merchants[] = $merchant;
    //     } else {
    //         $merchants = $em->getRepository(SpendrMerchant::class)
    //         ->findAll();
    //     }

    //     $totalMerchants = 0;
    //     $totalAdmins = 0;
    //     if ($merchants) {
    //         /** @var SpendrMerchant $merchant */
    //         foreach ($merchants as $merchant) {
    //             if (Util::isStaging()) {
    //                 if (
    //                     self::isLocationHasDummyMerchant($merchant)
    //                     || $merchant->getId() === 25
    //                     || $merchant->getId() !== 33
    //                 ) {
    //                     continue;
    //                 }
    //             }
    //             $locations = $em->getRepository(Location::class)
    //                 ->findBy([
    //                     'merchant' => $merchant
    //                 ]);
    //             $merchantAdmins = $em->getRepository(MerchantAdmin::class)
    //                 ->findBy([
    //                     'merchant' => $merchant
    //                 ]);
    //             if ($merchantAdmins && $locations) {
    //                 /** @var MerchantAdmin $admin */
    //                 foreach ($merchantAdmins as $merchantAdmin) {
    //                     /** @var User $user */
    //                     $user = $merchantAdmin->getUser();
    //                     if (count($user->getLocations()) > 0) {
    //                         continue;
    //                     }
    //                     if ($user->isSpendrMerchantBalanceAdmin()) {
    //                         continue;
    //                     }

    //                     foreach ($locations as $location) {
    //                         $user->addLocation($location)
    //                         ->persist();
    //                     }

    //                     $totalAdmins++;
    //                 }
    //             }
    //             $totalMerchants++;
    //         }
    //     }

    //     Util::flush();

    //     SlackService::info('Finished the association, total merchants: ' . $totalMerchants . ', total admins: ' . $totalAdmins);
    // }

    public static function mergeOtherMerchant(SpendrMerchant $merchant, $returnType = 'id')
    {
        $needMergeMerchant = Util::meta($merchant, 'needMergeMerchant');
        $res = null;
		if ($needMergeMerchant) {
			$mergeMerchantID = Util::meta($merchant, 'mergeMerchant');
			if ($mergeMerchantID) {
                if ($returnType === 'id') {
                    $res = $mergeMerchantID;
                } else {
                    $mergeMerchant = SpendrMerchant::find($mergeMerchantID);
                    if ($mergeMerchant) {
                        $res = $mergeMerchant;
                    }
                }
			}
		}
        return $res;
    }

    public static function mergeIntoOtherMerchant(SpendrMerchant $merchant, $returnType = 'id')
    {
        $mergeToMerchantID = self::isMergedIntoOtherMerchant($merchant, 'id');
        $res = null;
        if ($mergeToMerchantID) {
            if ($returnType === 'id') {
                $res = $mergeToMerchantID;
            } else {
                $mergeToMerchant = SpendrMerchant::find($mergeToMerchantID);
                if ($mergeToMerchant) {
                    $res = $mergeToMerchant;
                }
            }
        }
        return $res;
    }

    public static function needMergeOtherMerchant(SpendrMerchant $currentMerchant, $returnType = 'id')
	{
		return self::mergeOtherMerchant($currentMerchant, $returnType);
	}

    public static function generateMergeMerchantQuery(
        QueryBuilder $query,
        $field,
        SpendrMerchant $currentMerchant,
        $fieldType = 'object' // object/id
    ) {
        $needMergeOtherMerchant = self::needMergeOtherMerchant($currentMerchant, 'object');
		$expr = Util::expr();
		if ($needMergeOtherMerchant) {
			$query->andWhere($expr->in($field, ':merchants'));
            if ($fieldType === 'id') {
                $query->setParameter('merchants', [$currentMerchant->getId(), $needMergeOtherMerchant->getId()]);
            } else {
                $query->setParameter('merchants', [$currentMerchant, $needMergeOtherMerchant]);
            }
		} else {
			$query->andWhere($expr->eq($field, ':merchant'))
            	->setParameter('merchant', $currentMerchant);
		}

        return $query;
	}

    public static function generateMergeMerchantBalanceAdminQuery(
        QueryBuilder $query,
        $field,
        SpendrMerchant $currentMerchant,
        $fieldType = 'object'
    ) {
        $currentMerchantBalanceAdmin = $currentMerchant->getAdminUser();
        $needMergeOtherMerchant = self::needMergeOtherMerchant($currentMerchant, 'object');
		$expr = Util::expr();
		if ($needMergeOtherMerchant) {
            $otherMerchantBalanceAdmin = $needMergeOtherMerchant->getAdminUser();
			$query->andWhere($expr->in($field, ':merchantBalanceAdmins'));
            if ($fieldType === 'id') {
                $query->setParameter('merchantBalanceAdmins', [$currentMerchantBalanceAdmin->getId(), $otherMerchantBalanceAdmin->getId()]);
            } else {
                $query->setParameter('merchantBalanceAdmins', [$currentMerchantBalanceAdmin, $otherMerchantBalanceAdmin]);
            }
		} else {
			$query->andWhere($expr->eq($field, ':merchantBalanceAdmin'))
            	->setParameter('merchantBalanceAdmin', $currentMerchantBalanceAdmin);
		}

        return $query;
	}

    // returnType: current, other, merge
    public static function getMerchantBalance(SpendrMerchant $currentMerchant = null, SpendrMerchant $needMergeOtherMerchant = null, $returnType = 'merge')
    {
        $result = 0;

        if (($returnType === 'merge' || $returnType === 'current') && !$currentMerchant) {
            return $result;
        }

        $currentMerchantBalance = 0;
        $otherMerchantBalance = 0;

        if ($returnType === 'current' || $returnType === 'merge') {
            $currentMerchantDummy = ServicesUserService::getDummyCard($currentMerchant->getAdminUser());
            if (!$currentMerchantDummy) {
                return $result;
            }
            $currentMerchantBalance = $currentMerchantDummy->getBalance();
        }

        if ($returnType === 'other' || $returnType === 'merge') {
            if (!$needMergeOtherMerchant) {
                $needMergeOtherMerchant = self::needMergeOtherMerchant($currentMerchant, 'object');
            }
            if ($needMergeOtherMerchant) {
                $otherMerchantDummy = ServicesUserService::getDummyCard($needMergeOtherMerchant->getAdminUser());
                if (!$otherMerchantDummy) {
                    return $result;
                }
                $otherMerchantBalance = $otherMerchantDummy->getBalance();
            }
        }

        if ($returnType === 'current') {
            return $currentMerchantBalance;
        }

        if ($returnType === 'other') {
            return $otherMerchantBalance;
        }

        if ($returnType === 'merge') {
            return $currentMerchantBalance + $otherMerchantBalance;
        }

        return 0;
    }

    public static function getMerchantBalanceAdmin(SpendrMerchant $currentMerchant, $locationId = null)
    {
        if (!$locationId) {
            return $currentMerchant->getAdminUser();
        }

        $location = Location::find($locationId);
        if (!$location) {
            return $currentMerchant->getAdminUser();
        }

        $locationMerchantId = $location->getMerchant()->getId();
        if ($locationMerchantId === $currentMerchant->getId()) {
            return $currentMerchant->getAdminUser();
        }

        $needMergeOtherMerchant = self::needMergeOtherMerchant($currentMerchant, 'object');
        if (!$needMergeOtherMerchant) {
            return null;
        }

        if ($locationMerchantId !== $needMergeOtherMerchant->getId()) {
            return null;
        }

        return $needMergeOtherMerchant->getAdminUser();
    }

    public static function listMerchantsForSelection(SpendrGroup $group = null, $nullGroup = false)
    {
        $merchants = self::getAllMerchants($group, $nullGroup);
        if (!$merchants) {
            return null;
        }

        $data = [];
        foreach ($merchants as $m) {
            $data[] = [
                'label' => $m->getName(),
                'value' => $m->getId()
            ];
        }

        return $data;
    }
}
