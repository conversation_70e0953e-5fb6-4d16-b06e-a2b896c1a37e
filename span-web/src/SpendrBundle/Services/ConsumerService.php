<?php

namespace SpendrBundle\Services;

use Carbon\Carbon;
use Clf<PERSON><PERSON>le\Entity\Account;
use Clf<PERSON><PERSON>le\Entity\Patient;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\Address;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\IpUsage;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\BankCardBlacklist;
use Spendr<PERSON><PERSON>le\Entity\IdentityLog;
use SpendrBundle\Entity\LocationEmployee;
use SpendrBundle\Entity\PlaidIdentity;
use SpendrBundle\Entity\SpendrRestrict;
use SpendrBundle\Entity\SpendrReward;
use SpendrBundle\Services\ACH\SpendrACHService;
use SpendrBundle\Services\Next\RemixService;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\Request;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;


class ConsumerService
{
    public const STEP_INITIAL = 'Initial';

    public const STEP_KYC_NOT_STARTED = 'KYC Not Started'; // not in use, = Initial
    public const STEP_KYC_FAILED_OFAC = 'KYC Failed (OFAC)';
    public const STEP_KYC_PASSED = 'KYC Passed'; // not in use, = Onboarded
    public const STEP_KYC_MANUALLY_APPROVED = 'KYC Manually Approved'; // not in use, = Onboarded

    public const STEP_ACTIVE = 'Active';
    public const STEP_CLOSED = 'Closed';
	public const STEP_INACTIVE = 'Inactive';

    public const LOAD_STATUS_INSTANT = 'instant';
    public const LOAD_STATUS_PREFUND = 'prefund';
    public const LOAD_STATUS_FREEZE = 'freeze';

    public const EARN_BANK_LINK = 2500; // first link bank earn $25
    public const EARN_SPEND_OVER = 1000; // spend over $100 earn $10
    public const EARN_BANK_LINK_LIMIT = 10000; // only first 10,000 people can earn $25

    public const INACTIVE_TYPE_DEVICE_ID = 'Device ID';
    public const INACTIVE_TYPE_IDENTITY_NOT_MATCH = 'Identity Not Match';
    public const INACTIVE_TYPE_BANKCARD_BLACKLIST = 'Bankcard Blacklist';
    public const INACTIVE_TYPE_ROUTING_LIMIT = 'Routing not support Deposit';

    public static function updateOnboardStatus(User $user, $persist = true)
    {
        $step = $user->getRegisterStep();

        while (true) {
            $status = $user->getStatus();
            if ($status === User::STATUS_CLOSED) {
                $step = self::STEP_CLOSED;
                break;
            }

            if ($status === User::STATUS_INACTIVE) {
				$step = self::STEP_INACTIVE;
				break;
            }

            if ($status === User::STATUS_ACTIVE) {
                if (!$user->getEmailVerified() &&
                    !Util::meta($user, 'mobilePhoneVerified') &&
                    !Util::meta($user, 'activeRegisterStep')
                ) {
                    $step = RegisterStep::EMAIL;
                    break;
                } else {
                    $step = self::STEP_ACTIVE;
                    break;
                }
			}

//            $kycStep = $user->getOfacStatus();
//            if ($kycStep === false) {
//                $step = self::STEP_INITIAL;
//                break;
//            }
//
//            if ($kycStep !== true) {
//                $step = self::STEP_KYC_FAILED_OFAC;
//                break;
//            }

            $step = self::STEP_ACTIVE;
            break;
        }

        if ($user->getRegisterStep() !== $step) {
            $user->setRegisterStep($step);

            if ($persist) {
                $user->persist();
            } else {
                Util::em()->persist($user);
            }

            return true;
        }
        return false;
    }

    public static function text($step)
	{
		$texts = [
			self::STEP_ACTIVE => 'Active',
			self::STEP_CLOSED => 'Closed',
			self::STEP_INACTIVE => 'Inactive',
			RegisterStep::INACTIVE => 'Inactive',
			RegisterStep::EMAIL => 'Onboard (SMS)',
		];
		return $texts[$step] ?? $step;
	}

    protected static function getRequestUserStatus(Request $request)
    {
        $status = strtolower((string)$request->get('status'));
        if ($status === 'true' || $status === 'active') {
            return User::STATUS_ACTIVE;
        }
        return User::STATUS_INACTIVE;
    }

    public static function saveClerkAction(Request $request)
    {
        $uid   = $request->get('uid');
        $email = $request->get('email');
        if (! $email) {
            return new FailedResponse('Email is required');
        }
        $status = static::getRequestUserStatus($request);
        $newUser  = false;
        $oldEmail = null;
        $oldPhone = null;
        if ($uid) {
            $user        = User::find($uid);
            $validResult = self::validateUser($user, $request, true);

            $oldEmail = $user->getEmail();
            $oldPhone = $user->getMobilephone();
        } else {
            $user = User::findPlatformUserByEmail($email, SpendrBundle::getMerchantEmployeeRoles());
            if ($user) {
                return new FailedResponse('Duplicated accounts with the same email address!');
            }
            $newUser     = true;
            $user        = new User();
            $validResult = self::validateUser($user, $request);
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus($status);
        }
        if ($validResult) {
            return $validResult;
        } else {
            $user = self::handleUser($user, $request);

            if ($newUser) {
                $cardType = CardProgramCardType::getForCardProgram(Util::cardProgram());
                $uc       = CardService::create($user, $cardType);

                if (! Util::isLive()) {
                    $sandbox               = Util::platform()->isTransferMex() ? 'transferMexSandbox' : 'faasSandbox';
                    $sandboxData[$sandbox] = true;
                    Util::updateMeta($uc, $sandboxData);
                }
            } else {
                $reason = $request->get('changeReason');
                $suffix = '';
                if ($reason) {
                    $suffix = ' with reason "'.$reason.'"';
                }

                if ($oldEmail !== $email) {
                    $user->addNote(
                        'Changed the email from '.$oldEmail.' to: '.$email.$suffix,
                        true,
                        Util::user()->getId()
                    );
                }
                if ($oldPhone !== $user->getMobilephone()) {
                    $user->addNote(
                        'Changed the mobile phone number from '.$oldPhone.' to '.$user->getMobilephone().$suffix,
                        true,
                        Util::user()->getId()
                    );
                }
            }

            return $user;
        }
    }

    public static function validPhoneNumber($rawMobilePhone, $mobilePhone, $formattedPhone)
    {
        $users  = null;
        $phones = Config::array(Config::CONFIG_WILD_PHONE_NUMBERS);
        if (! in_array($mobilePhone, $phones)) {
            $users = Util::em()->getRepository(User::class)->createQueryBuilder('u')->join(
                'u.teams',
                't'
            )->where(Util::expr()->in(
                't.name',
                ':roles'
            ))->Andwhere(Util::expr()->orX(
                Util::expr()->eq('u.phone', ':phone'),
                Util::expr()->eq('u.mobilephone', ':mobilephone'),
                Util::expr()->eq('u.workphone', ':workphone'),
                Util::expr()->eq('u.phone', ':raw_phone'),
                Util::expr()->eq('u.mobilephone', ':raw_mobilephone'),
                Util::expr()->eq('u.workphone', ':raw_workphone'),
                Util::expr()->eq('u.phone', ':format_phone'),
                Util::expr()->eq('u.mobilephone', ':format_mobilephone'),
                Util::expr()->eq('u.workphone', ':format_workphone')
            ))->setParameter(
                'roles',
                SpendrBundle::getMerchantEmployeeRoles()
            )->setParameter('phone', $mobilePhone)->setParameter(
                'mobilephone',
                $mobilePhone
            )->setParameter('workphone', $mobilePhone)->setParameter(
                'raw_phone',
                $rawMobilePhone
            )->setParameter('raw_mobilephone', $rawMobilePhone)->setParameter(
                'raw_workphone',
                $rawMobilePhone
            )->setParameter('format_phone', $formattedPhone)->setParameter(
                'format_mobilephone',
                $formattedPhone
            )->setParameter('format_workphone', $formattedPhone)->getQuery()->getResult();
        }
        return $users;
    }

    public static function getEmailUser($email)
    {
        $users = Util::em()->getRepository(User::class)->createQueryBuilder('u')->join(
            'u.teams',
            't'
        )->where(Util::expr()->in('t.name', ':roles'))->andWhere('u.email = :email')->setParameter(
            'roles',
            SpendrBundle::getMerchantEmployeeRoles()
        )->setParameter('email', $email)->setMaxResults(2)->getQuery()->getResult();
        return $users && count($users) ? $users[0] : null;
    }

    public static function createPatient(User $user, Account $account)
    {
        if (! $user) {
            return null;
        }
        $patient = new Patient();
        $address = Address::createFromUser($user, true);
        $patient->setUser($user)->setAccount($account)->setAddress($address);
        Util::em()->persist($patient);
    }

    public static function checkBannedStatus(User $newUser)
    {
        $users = Util::em()->getRepository(User::class)->findBy([ 'status' => User::STATUS_BANNED ]);

        $currentIp = Security::getClientIp();

        foreach ($users as $bannedUser) {
            $bannedIps = Util::em()->getRepository(\CoreBundle\Entity\IpUsage::class)->findBy([ 'users' => $bannedUser ]);
            $ipsArr    = [];
            /** @var IpUsage $bannedIp */
            foreach ($bannedIps as $bannedIp) {
                if ($bannedIp->getLoginIp()) {
                    $ipsArr[] = $bannedIp->getLoginIp();
                }
            }
            if (in_array($currentIp, $ipsArr)) {
                $newUser->addFlag(User::FLAG_BAD_AP_ASSOCIATION);
                break;
            }
        }
        return $newUser;
    }

    public static function validateUser(User $user = null, Request $request, bool $isEdit = false)
    {
        $emailAddress = trim($request->get('email'));
        $mobilePhone  = trim($request->get('phoneNumber'));
        $role         = trim($request->get('role'));

        if ((! $role) || ! in_array($role, SpendrBundle::getRoles())) {
            return new FailedResponse('Unknown role!');
        }

        $newUser = new User();
        $newUser->setEnabled(true);
        // valid phone number
        $rawMobilePhone = $mobilePhone;
        $mobilePhone    = str_replace([ ' ', '-', '(', ')', '.' ], '', $mobilePhone);
        if (! $mobilePhone || $mobilePhone === '+') {
            $message = 'Invalid phone number!';
            return new FailedResponse($message, self::errorMessage('phoneNumber', $message));
        }
        $mobilePhone = Util::formatManualInputPhoneNumber($mobilePhone);
        $formattedPhone = Util::formatFullPhone($mobilePhone);
        $users          = self::validPhoneNumber($rawMobilePhone, $mobilePhone, $formattedPhone);
        if ($users && count($users)) {
            foreach ($users as $u) {
                if (Util::neq($newUser, $u) && ! $isEdit) {
                    $message = 'The phone number has been registered before. Please login to your account or ask support for help.';
                    return new FailedResponse($message, self::errorMessage('phoneNumber', $message));
                }
            }
        }

        if (! $emailAddress && ! $isEdit) {
            return new FailedResponse('Invalid email address!', self::errorMessage('email', 'Invalid email address!'));
        }
        if (Config::isEmailInactive($emailAddress)) {
            $message = 'There is a problem with delivering to this email address,
                please use an alternate email address.';
            return new FailedResponse($message, self::errorMessage('email', $message));
        }
        $filtered = filter_var($emailAddress, FILTER_VALIDATE_EMAIL);
        if (! $filtered || $filtered !== $emailAddress) {
            $message = 'Invalid email address!';
            return new FailedResponse($message, self::errorMessage('email', $message));
        }

        // valid user
        $user = self::getEmailUser($emailAddress);
        if ($user && ! $isEdit) {
            $message = 'The email address has been registered before. '.' Please try to login or use another email address to register.';
            return new FailedResponse($message, self::errorMessage('email', $message));
        }

        $pin = trim($request->get('pin'));
        if ($pin != null && strlen(trim($pin)) != 4) {
            $message = 'Invalid pin!';
            return new FailedResponse($message, self::errorMessage('pin', $message));
        }

		$location = LocationService::getCurrentLocation();
    }

    public static function handleUser(User $user, Request $request)
    {
        $emailAddress = trim($request->get('email'));
        $firstName    = trim($request->request->get('firstName'));
        $lastName     = trim($request->request->get('lastName'));
        $dateOfBirth  = trim($request->get('dateOfBirth'));
        $mobilePhone  = trim($request->get('phoneNumber'));
        $countryId    = trim($request->get('countryId'));
        $stateId      = trim($request->get('stateId'));
        $city         = trim($request->get('city'));
        $postalCode   = trim($request->get('postalCode'));
        $address      = trim($request->get('address'));
        $role         = trim($request->get('role'));
        $pin          = trim($request->get('pin'));
        $uid          = trim($request->get('uid'));
        $country      = $countryId != null ? Country::find($countryId) : null;
        $state        = BaseState::find($stateId);
        $status = static::getRequestUserStatus($request);

        if ($dateOfBirth) {
            $bithAt = Util::timeLocal($dateOfBirth);
            $user->setBirthday(Util::toUTC($bithAt));
        }
        $mobilePhone = Util::formatManualInputPhoneNumber($mobilePhone);
        $formattedPhone = Util::formatFullPhone($mobilePhone);

        // set user data
        $userName = $emailAddress;
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setEmail($emailAddress);
        $user->setUsername($userName);
        $user->setMobilephone($formattedPhone);
        $user->setAddress($address);
        $user->setCountry($country);
        $user->setState($state);
        $user->setCity($city);
        $user->setZip($postalCode);

        $user->setEnabled(true);
        $user->setLockedStatus(User::LOCK_STATUS_UNLOCK);
        $user->setStatus($status);
        $user->setSource(User::SOURCE_ADMIN);
        $user->setRegistrationClient(User::CLIENT_MOBILE);
        Util::em()->persist($user);
        Util::em()->flush();

        $user->ensureRole($role);

        $cardType = CardProgramCardType::getForCardProgram(CardProgram::spendr());
        if (! $cardType) {
            return new FailedResponse('Unknown card type!');
        }
        $locationEmployees = Util::em()->getRepository(\SpendrBundle\Entity\LocationEmployee::class)->createQueryBuilder('le')->where('le.user = :user')->setParameters([
            'user' => $user
        ])->getQuery()->getResult();
        if (count($locationEmployees) > 0) {
            $locationEmployee = $locationEmployees[0];
        } else {
            $locationEmployee = new LocationEmployee();
            $locationEmployee->setUser($user);
			$location = LocationService::getCurrentLocation();
            if ($location) {
                $locationEmployee->setLocation($location);
                $locationEmployee->setMerchant($location->getMerchant());
            }
        }
        if (strlen($pin) > 0) {
            $locationEmployee->setPin($pin);
        }
        Util::em()->persist($locationEmployee);
        Util::em()->flush();
        return $user;
    }

    public static function errorMessage($type, $error)
    {
        return [
            'type'  => $type,
            'error' => $error,
        ];
    }

    public static function getUserActiveCardCount(User $user)
    {
        return Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'ucc')
            ->where(Util::expr()->eq('ucc.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user)
            ->andWhere('uc.status = :status')
            ->setParameter('status', UserCard::STATUS_ACTIVE)
            ->andWhere('uc.type = :type')
            ->setParameter('type', UserCard::LL_TYPE_PLAID)
            ->select('count(uc)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public static function getUserBankList(User $user, $type = null, $pending = false) {
        $status = [UserCard::STATUS_ACTIVE];
        if ($pending) {
            $status = [
                UserCard::STATUS_ACTIVE,
                UserCard::STATUS_PENDING
            ];
        }
        $rs = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->join('uc.user', 'u')
            ->andWhere(Util::expr()->in('uc.status', ':status'))
            ->andWhere('u.id = :userId')
            ->andWhere('uc.type != :type')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('userId', $user->getId())
            ->setParameter('status', $status)
            ->getQuery()
            ->getResult();
        $data = [];
        /** @var UserCard $r */
        foreach ($rs as $r) {
            // If the request is from app which contains plaid and the card is added by yodlee. It may be auto deleted.
            if ($type === 'plaid' && $r->getType() === UserCard::LL_TYPE_YODLEE) {
                // If the card is processing transaction, it can be deleted.
                // finish transaction: account_status in canceled, returned or account_status is settled and updated_at is 7 days ago.
                if (!self::getLoadPendingAmount([$r->getId()])) {
                    Util::em()->remove($r);
                    Util::em()->flush();
                    continue;
                }
            }
            $meta = Util::meta($r);
            $accountNumber = UserCardService::getAccountNumMask($meta);
            if ($r->getStatus() == UserCard::STATUS_PENDING || ($meta && isset($meta['verification_status']) && in_array($meta['verification_status'], [
                    PlaidService::VERIFY_STATUS_MANUAL_PENDING,
                    PlaidService::VERIFY_STATUS_PENDING]
                ))) {
                $data[] = [
                    'id' => $r->getId(),
                    'bankName' => $r->getBankName() ? $r->getBankName() : '',
                    'accountNumber' => $meta['name'],
                    'routingNumber' => $meta['mask'],
                    'type' => $r->getType(),
                    'pending' => true,
                    'manualLink' => true,
                    'error' => null
//                'available' => $available
                ];
                continue;
            }
            $data[] = [
                'id' => $r->getId(),
                'bankName' => $r->getBankName(),
                'accountNumber' => $accountNumber,
                'routingNumber' => UserCardService::getRoutingNumMask($meta),
                'type' => $r->getType(),
                'manualLink' => isset($meta['manual_link']) && $meta['manual_link'] ?? false,
                'error' => $meta['error'] ?? null
//                'available' => $available
            ];
        }
        return $data;
    }

    public static function getManualCards(User $user)
    {
        return Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->join('uc.user', 'u')
            ->andWhere('uc.status = :status')
            ->andWhere('uc.user = :user')
            ->andWhere('uc.type != :type')
            ->andWhere('uc.meta like :meta')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('user', $user)
            ->setParameter('status', UserCard::STATUS_ACTIVE)
            ->setParameter('meta', '%"manual_link":true%')
            ->getQuery()
            ->getResult();
    }

    public static function hasManuallyLinkedCards(User $user)
    {
        $cards = self::getManualCards($user);

        return $cards ? count($cards) : 0;
    }

    // Save card info when manual link successfully
    public static function updatePendingCard($token, UserCard $card, $user, $match = false)
    {
        $info = PlaidService::getAccountInformation($token);
        if (!$info || !isset($info->accounts) || !isset($info->numbers)) {
            return new FailedResponse('No bank account information.', [
                'reason' => 'No account',
            ]);
        }
        $accountId = $info->accounts[0]->account_id;
        $bankName = $info->accounts[0]->name;
        $mask = $info->accounts[0]->mask;
        $subtype = $info->accounts[0]->subtype;
        $type = $info->accounts[0]->type;
        if (strlen($bankName) > 64) {
            $bankName = $subtype . ' ' . $mask;
        }
        $account = array_first($info->numbers->ach, function ($item) use ($accountId) {
            return $item->account_id == $accountId;
        });
        $accountNum = $account ? $account->account : null;
        $routingNum = $account ? $account->routing : null;
        $institutionId = $info->item->institution_id;
        $itemId = $info->item->item_id;
        if (!$accountId || !$bankName || !$accountNum || !$routingNum || $type === 'credit') {
            return new FailedResponse('No bank account information.', [
                'reason' => 'No account',
            ]);
        }
        $platformMeta = Util::meta(Platform::spendr());
        $phone = $platformMeta['customerSupportPhone'] ?? null;
        $email = $platformMeta['customerSupportEmail'] ?? null;
        if (BankCardBlacklist::exists($accountNum, $routingNum)) {
            $card->setStatus(UserCard::STATUS_INACTIVE)->persist();
            return new FailedResponse(
                "Action cannot be completed. Please contact Spendr Support at {$phone} or {$email}.",
                [
                    'reason' => 'Bank account in blacklist',
                ]
            );
        }
        if (UserCardService::isNonDepositedRouting($routingNum)) {
            $card->setStatus(UserCard::STATUS_INACTIVE)->persist();
            Util::updateMeta($card, [
                'Issue Type' => self::INACTIVE_TYPE_ROUTING_LIMIT
            ]);
            return new FailedResponse("The bank you're trying to link is no longer supported with Spendr for reasons outside of our control. Please try linking a different bank account and reach out to Spendr Support at {$phone} or {$email} with any questions or concerns.", [
                'reason' => 'Not supported routing',
            ]);
        }

        $existed = false;
        $ucs = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->join('uc.user', 'u')
            ->join('u.teams', 't')
            ->andWhere('uc.status = :status')
            ->andWhere(Util::expr()->in('t.name', ':roles'))
            ->andWhere('u.id = :userId')
            ->andWhere('uc.type != :type')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('userId', $card->getUser()->getId())
            ->setParameter('roles', SpendrBundle::getConsumerRoles())
            ->setParameter('status', UserCard::STATUS_ACTIVE)
            ->getQuery()
            ->getResult();
        /** @var UserCard $uc */
		foreach ($ucs as $uc) {
            if (($uc->getAccountNumber()
                    && $uc->getRoutingNumber()
                    && $accountNum === SSLEncryptionService::tryToDecrypt($uc->getAccountNumber())
                    && $routingNum === SSLEncryptionService::tryToDecrypt($uc->getRoutingNumber()))
                || (Util::meta($uc, 'mask') === $mask
                    && $uc->getBankName() === $bankName
                    && Util::meta($uc, 'institution_id') === $institutionId)
            ) {
                $existed = true;
                break;
            }
        }
        if ($existed) {
            return new FailedResponse('The bank account you manually linked has existed already.', [
                'reason' => 'Account exists',
            ]);
        }

		// First link bank account and bank primary phone is not exists
        /** @var SpendrReward $config */
        $config = SpendrReward::findOneByType(UserCardLoad::EARN_TYPE_BANK_LINK);
        $canEarn = $config && self::canEarnLinkedAmount($user, $config);

        $meta = [
            'account_id' => $accountId,
            'mask' => $mask,
            'routing_mask' => substr($routingNum, -4),
            'institution_id' => $institutionId,
            'item_id' => $itemId,
            'subtype' => $subtype,
            'manual_link' => true
        ];
        $encryptedAcctNum = SSLEncryptionService::encrypt($accountNum);
        $encryptedRouting = SSLEncryptionService::encrypt($routingNum);
        $card->setBankName($bankName)
            ->setAccountNumber($encryptedAcctNum)
            ->setRoutingNumber($encryptedRouting)
            ->setInitializedAt(new \DateTime())
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setCurrency('USD')
            ->setMeta(Util::j2s($meta))
			->persist();

        // Save identity data
        if (!Util::meta($user, 'disableIdentityMatch') && $match) {
            $res = self::savePlaidCardIdentityData($card);
            if (is_bool($res)) {
                $user->addNote('No identity was run on this user when manually link card.', true, $user->getId());
            } elseif (is_string($res)) {
                // identity not match
                $card->setStatus(UserCard::STATUS_INACTIVE)->persist();
                Util::updateMeta($card, [
                    'Issue Type' => self::INACTIVE_TYPE_IDENTITY_NOT_MATCH
                ]);
                BrazeService::userTrack(null, [
                    'external_id' => BrazeService::getExternalPrefix() . $user->getId(),
                    'name' => 'Identity Failed',
                    'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z')
                ]);
                return new FailedResponse(
                    'Unable to link bank account. Spendr information does not match bank information. Please check your Spendr account details and ensure they match the bank you\'re trying to link. Please reach out to Spendr Support for assistance.',
                    [
                        'error' => 'Identity Not Match',
                        'reason' => 'Identity failed',
                    ]
                );
            }
        }
        $firstLink = false;
        if ($canEarn && $config->getAmount()) {
            $firstLink = true;
            LoadService::earnLoad($user, $config);
        }

        Log::debug(
        	'Changed card status to active',
			[
				'Card ID' => $card->getId(),
				'Card Status' => $card->getStatus()
			]
		);

        return $firstLink;
    }

    public static function nameBlacklist()
    {
        return Config::array(Config::CONFIG_SPENDR_USERNAME_BLACKLIST);
    }

    public static function nameFuzzyMatch($firstName, $lastName, $names)
    {
        if (!$names) return false;
        $config = Util::em()->getRepository(Config::class)
            ->getArray(Config::CONFIG_SPENDR_FUZZY_MATCH, [
                'score' => 0.5,
                'lastNameMinTreshold' => 0.6
            ]);
        $nameRes = NameFuzzyMatchService::validate(
            $firstName,
            $lastName,
            $names,
            $config['score'],
            $config['lastNameMinTreshold'],
        );
        if (
            $nameRes &&
            isset($nameRes['data']) &&
            isset($nameRes['data']['success']) &&
            $nameRes['data']['success'] &&
            isset($nameRes['data']['averageScore']) &&
            $nameRes['data']['averageScore'] >= $config['score']
        ) {
            return true;
        }
        Log::info('fuzzy match failed:', $nameRes);

        return false;
    }

    public static function savePlaidCardIdentityData(UserCard $uc)
    {
        $accountId = Util::meta($uc, 'account_id');
        $manualLink = Util::meta($uc, 'manual_link');
        if (!$accountId || $manualLink) return false;
        $res = PlaidService::getIdentityInformation($uc);
        if (!$res || !isset($res->accounts)) return false;
        $account = array_first($res->accounts, function ($item) use ($accountId) {
            return $item->account_id == $accountId;
        });
        if ($account && isset($account->owners) && count($account->owners)) {
            $owner = $account->owners[0];
            if (!count($owner->names)) return false;
            $user = $uc->getUser();
            // names
            $names = $owner->names;
            $name = $names[0];
            $name1 = count($names) > 1 ? $names[1] : null;
            $otherNames = count($names) > 2 ? array_slice($names, 2) : null;
            $phones = [];

            // If any of the name, phone #, zip doesn't match, We should change card status to inactive
            // Don't match the middle name
            $matchName = self::nameFuzzyMatch($user->getFirstName(), $user->getLastName(), $names);

            // phone_numbers
            [$homePhone, $workPhone, $officePhone, $mobile, $mobile1, $otherPhone] = null;
            if (count($owner->phone_numbers)) {
                foreach ($owner->phone_numbers as $phone) {
                    switch ($phone->type) {
                        case 'home':
                            $homePhone = $phone->data;
                            break;
                        case 'work':
                            $workPhone = $phone->data;
                            break;
                        case 'office':
                            $officePhone = $phone->data;
                            break;
                        case 'mobile':
                            $mobile = $phone->data;
                            break;
                        case 'mobile1':
                            $mobile1 = $phone->data;
                            break;
                        case 'other':
                            $otherPhone = $phone->data;
                            break;
                        default: break;
                    }
                }
                $phones = array_values(array_filter([$homePhone, $workPhone, $officePhone, $mobile, $mobile1, $otherPhone], function ($item) {
                    return $item;
                }));
                if ($phones && count($phones)) {
                    $rep = PlaidService::getEnv() == 'sandbox' ? [' ', '-', '+1'] : [' ', '-'];
                    $matchPhone = in_array(str_replace($rep, '', $user->getMobilephone()), $phones);
                } else {
                    $matchPhone = true;
                }
            } else {
                $matchPhone = true;
            }

            // addresses
            if (count($owner->addresses)) {
                $postCodes = [];
                $matchZip = false;
                $address = null;
                $address1 = null;
                foreach ($owner->addresses as $addr) {
                    array_push($postCodes, $addr->data->postal_code);
                    if ($addr->primary) $address = $addr;
                    else $address1 = $addr;
                    if (Util::startsWith($addr->data->postal_code, $user->getZip())) {
                        $matchZip = true;
                    }
                }
            } else {
                $matchZip = true;
                $postCodes = [];
                $address = null;
                $address1 = null;
            }

            if (!$matchName || (!$matchZip && !$matchPhone)) {
                $mask = Util::meta($uc, 'mask');
                $desc = "Spendr account is not matched to the identity when linking with {$uc->getBankName()}({$mask}):";
                if (!$matchName) {
                    $desc .= " Account name: {$user->getFullName()}, Identity names: " . implode(',', $names) . '.';
                }
                if (!$matchPhone) {
                    $desc .= " Account phone: {$user->getMobilephone()}, Identity phones: " . implode(',', $phones) . '.';
                }
                if (!$matchZip) {
                    $desc .= " Account Zip: {$user->getZip()}, Identity Zip: " . implode(',', $postCodes) . '.';
                }
                $log = new IdentityLog();
                $log->setUser($user)
                    ->setType(IdentityLog::TYPE_IDENTITY_NOT_MATCH)
                    ->setDescription($desc)
                    ->persist();
                $user->addNote('Identity data does not match the consumer profile.', true, $user->getId());
                if (!Util::isCommand()) {
                    SlackService::warning('User profile does not match the identity data!', [
                        'ID' => $user->getId(),
                        'Issue Type' => self::INACTIVE_TYPE_IDENTITY_NOT_MATCH
                    ]);
                }

                return 'Not matched';
            }

            $identity = new PlaidIdentity();
            $identity->setUser($user)
                ->setCard($uc)
                ->setName($name)
                ->setName1($name1)
                ->setOtherNames($otherNames ? json_encode($otherNames) : null)
                ->setHomePhone($homePhone)
                ->setWorkPhone($workPhone)
                ->setOfficePhone($officePhone)
                ->setMobile($mobile)
                ->setMobile1($mobile1)
                ->setOtherPhone($otherPhone)
                ->setAddress($address ? $address->data->city . ', ' . $address->data->street : null)
                ->setAddress1($address1 ? ($address1->data->city . ', ' . $address1->data->street) : null)
                ->setPostCode($address ? $address->data->postal_code : null)
                ->setPostCode1($address1 ? $address1->data->postal_code : null)
                ->setMeta(Util::j2s((array)$owner))
                ->persist();

            return $identity;
        }
        return false;
    }

    /**
     * @param User $user
     * @param UserCard $uc
     * @return PlaidIdentity|null
     */
    public static function getIdentity(User $user, UserCard $uc = null)
    {
        $query = Util::em()->getRepository(PlaidIdentity::class)
            ->createQueryBuilder('pi')
            ->where('pi.user = :user')
            ->setParameter('user', $user);
        if ($uc) {
            $query->andWhere('pi.card = :card')
                ->setParameter('card', $uc);
        }
        $identity = $query->orderBy('pi.id', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if ($identity && count($identity)) return $identity[0];
        return null;
    }

    public static function getLoadPendingAmount($cardIds)
    {
        $res = Util::em()->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->join('uct.userCard', 'uc')
            ->where(Util::expr()->in('uc.id', ':ucId'))
            ->andWhere(Util::expr()->notIn('uct.accountStatus',':status'))
            ->setParameter('ucId', $cardIds)
            ->setParameter('status', [
                UserCardTransaction::STATUS_LL_QUESTIONABLE,
                UserCardTransaction::STATUS_LL_CANCELED,
                UserCardTransaction::STATUS_LL_RETURNED,
                UserCardTransaction::STATUS_LL_SETTLED
            ]);
        return $res->select('sum(uct.txnAmount)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public static function isCanTransaction(User $user, $from = 'dashboard')
	{
		$text = $from === 'dashboard' ? 'This' : 'Your';
        if ($user->getStatus() === User::STATUS_ACTIVE && Util::meta($user, 'deleteApplyAt')) {
            return $text . ' account has applied to delete and cannot transaction.';
        }
        $spendrConsumerLoadStatus = self::getConsumerLoadStatus($user);
		if ($spendrConsumerLoadStatus === self::LOAD_STATUS_FREEZE) {
			return $text . ' account has been frozen and cannot transaction.';
		}

		if ($user->getStatus() === User::STATUS_INACTIVE || $user->getStatus() === User::STATUS_CLOSED) {
			return $text . ' account is inactive and cannot transaction.';
		}

		return true;
	}

	public static function getConsumerLoadStatus(User $consumer, $balanceCheckFailed = false)
	{
        $loadStatus = self::LOAD_STATUS_INSTANT;
        $fundingStatusByCheckBalance = null;
        $fundingStatusByCheckReturnTimes = null;
        $fundingStatusByCheckManuallyChanged = null;
        $fundingStatusByCheckPlaidBalance = null;

        // check balance
        $consumerDummy = UserService::getDummyCard($consumer);
		$balance = $consumerDummy->getBalance();
		$fundingStatusByCheckBalance = $balance < 0 ? self::LOAD_STATUS_PREFUND : self::LOAD_STATUS_INSTANT;

        // check manually changed value
        $manuallyChangedLoadStatus = Util::meta($consumer, 'manuallyChangeLoadStatusTo');
        $fundingStatusByCheckManuallyChanged = $manuallyChangedLoadStatus ? $manuallyChangedLoadStatus : null;

        // check returned times
        $fundingStatusByCheckReturnTimes = self::calculateLoadStatusByAchReturnTimes($consumer);

        if ($fundingStatusByCheckBalance === self::LOAD_STATUS_PREFUND) {
            if ($fundingStatusByCheckManuallyChanged) {
                $loadStatus = $fundingStatusByCheckBalance;
            } else {
                if ($fundingStatusByCheckReturnTimes === self::LOAD_STATUS_FREEZE) {
                    $loadStatus = $fundingStatusByCheckReturnTimes;
                } else {
                    $loadStatus = $fundingStatusByCheckBalance;
                }
            }
        } else { // instant
            if ($fundingStatusByCheckManuallyChanged) {
                $loadStatus = $fundingStatusByCheckManuallyChanged;
            } else {
                $loadStatus = $fundingStatusByCheckReturnTimes;
            }
        }

        // 4. check by Plaid balance request: https://app.asana.com/0/0/1207770112404062/f
        // When balance request failed,
        // if consumer hasn't made deposits, the status should from config CONFIG_SPENDR_NO_DEPOSIT_LOAD_STATUS,
        // otherwise if the consumer has 1+ stale returns, the status should be Prefund;
        // if no, the status should from config CONFIG_SPENDR_NO_STALE_RETURN_LOAD_STATUS
        if (
            $loadStatus === self::LOAD_STATUS_INSTANT
            && $balanceCheckFailed
        ) {
            $loadCount = LoadService::getConsumerCommonLoadCount($consumer);
            if (!$loadCount) {
                return Util::em()->getRepository(Config::class)
                    ->getValue(Config::CONFIG_SPENDR_NO_DEPOSIT_LOAD_STATUS, ConsumerService::LOAD_STATUS_INSTANT);
            }
            // todo: change returns judgement to stale returns and uncomment the else code
            $returnedTimes = FeeService::getConsumerAchReturnTimes($consumer);
            if ($returnedTimes > 0) {
                $loadStatus = self::LOAD_STATUS_PREFUND;
                $fundingStatusByCheckPlaidBalance = $loadStatus;
//            } else {
//                return Util::em()->getRepository(Config::class)
//                    ->getValue(Config::CONFIG_SPENDR_NO_STALE_RETURN_LOAD_STATUS, ConsumerService::LOAD_STATUS_INSTANT);
            }
        }

        // todo: need remove
        Log::debug('get load status: ', [
            'check balance' => $fundingStatusByCheckBalance,
            'manually change' => $fundingStatusByCheckManuallyChanged,
            'check return times' => $fundingStatusByCheckReturnTimes,
            'check plaid balance' => $fundingStatusByCheckPlaidBalance,
            'finally' => $loadStatus
        ]);

		return $loadStatus;
	}

    public static function calculateLoadStatusByAchReturnTimes(User $consumer)
    {
        $loadStatus = self::LOAD_STATUS_INSTANT;

        $returnedTimes = 0;
        if (Carbon::now() > Carbon::parse(SpendrBundle::getStaleReturnStartTime())) {
            $returnedTimes = self::getConsumerStaleReturnCount($consumer);
        } else {
            $returnedTimes = FeeService::getConsumerAchReturnTimes($consumer);
        }
        
        if ($returnedTimes <= 0) {
            $loadStatus = self::LOAD_STATUS_INSTANT;
        } else if (($returnedTimes > 0) && ($returnedTimes < 4)) {
			if ($returnedTimes === 3) {
				$loadStatus = self::LOAD_STATUS_PREFUND;
			} else {
                $loadStatus = self::LOAD_STATUS_INSTANT;
			}
		} else if ($returnedTimes >= 4) {
			$loadStatus = self::LOAD_STATUS_FREEZE;
        }

        return $loadStatus;
    }

    // If consumer first link bank account and total earn of first link is less than 10000
	public static function canEarnLinkedAmount(User $user, SpendrReward $reward)
    {
        // If the consumer added bank before
        Util::disableSoftDeletable();
        $added = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->join('uc.user', 'u')
            ->join('uc.card', 'cpct')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->andWhere(Util::expr()->isNotNull('uc.accountNumber'))
            ->andWhere('u.id = :userId')
            ->andWhere('uc.type != :type')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('userId', $user->getId())
            ->andWhere(Util::expr()->notLike('uc.meta', ':issue'))
            ->setParameter('issue', '%"Issue Type":"' . self::INACTIVE_TYPE_IDENTITY_NOT_MATCH . '"%')
            ->andWhere(Util::expr()->notLike('uc.meta', ':issue1'))
            ->setParameter('issue1', '%"Issue Type":"' . self::INACTIVE_TYPE_BANKCARD_BLACKLIST . '"%')
            ->select('count(uc)')
            ->getQuery()
            ->getSingleScalarResult();
        Util::enableSoftDeletable();

        $checkRes = LoadService::beforeEarnLoad($reward, $user);

        // If the current ip is new user.
//        $ip = Security::getClientIp();
//        $ipUsage = $this->em->getRepository(IpUsage::class)
//            ->createQueryBuilder('i')
//            ->join('i.users', 'u')
//            ->join('u.teams', 't')
//            ->where('i.login_ip = :ip')
//            ->setParameter('ip', $ip)
//            ->andWhere('i.users != :user')
//            ->setParameter('user', $user)
//            ->andWhere(Util::expr()->in('t.name', ':roles'))
//            ->setParameter('roles', SpendrBundle::getMemberRoles())
//            ->orderBy('i.login_time', 'desc')
//            ->select('count(i)')
//            ->getQuery()
//            ->getSingleScalarResult();

        return !$added && $checkRes;
    }

    // Check the current total number of people who have earned money
    public static function countEarnedRecord(SpendrReward $reward, User $user = null)
	{
	    if (!$reward) return 1;
		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->where('ucl.type = :type')
			->andWhere('ucl.status = :status')
			->andWhere('ucl.loadStatus = :loadStatus')
			->andWhere('ucl.promoType = :promoType')
			->andWhere('ucl.earnType = :earnType')
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->setParameter('status', UserCardLoad::STATUS_COMPLETED)
			->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
			->setParameter('promoType', PromotionService::PROMOTION_TYPE_EARN)
			->setParameter('earnType', $reward->getType());

		if ($user) {
			$query->join('ucl.userCard', 'uc')
				->andWhere('uc.user = :user')
				->setParameter('user', $user);
		}
		if (in_array($reward->getType(), [
		    UserCardLoad::EARN_TYPE_SPEND_OVER,
            UserCardLoad::EARN_TYPE_SPEND_REWARD
        ])) {
		    $query->join('ucl.reward', 'r')
                ->andWhere('r.type = :rewardType')
                ->setParameter('rewardType', $reward->getType())
                ->andWhere('r.spendType = :spendType')
                ->setParameter('spendType', $reward->getSpendType())
                ->andWhere('r.spendAmount = :spendAmount')
                ->setParameter('spendAmount', $reward->getSpendAmount());
		    if ($reward->getSpendType() === SpendrReward::SPEND_TYPE_NUM_WITH_MIN_AMOUNT) {
                $query->andWhere('r.spendCount = :spendCount')
                    ->setParameter('spendCount', $reward->getSpendCount());
            }
            if ($reward->getType() === UserCardLoad::EARN_TYPE_SPEND_REWARD) {
                $query->andWhere('r.dateType = :dateType')
                    ->setParameter('dateType', $reward->getDateType());
                if ($reward->getDateType() === SpendrReward::DATE_TYPE_PER_MONTH) {
                    $query->andWhere('date_format(ucl.initializedAt, \'%Y-%m\') = :date')
                        ->setParameter('date', Carbon::now()->format('Y-m'));
                } elseif ($reward->getDateType() === SpendrReward::DATE_TYPE_DATE_RANGE) {
                    $query->andWhere('ucl.initializedAt >= :start')
                        ->setParameter('start', $reward->getStartAt())
                        ->andWhere('ucl.initializedAt < :end')
                        ->setParameter('end', $reward->getEndAt());
                }
            }
        }

		return $query->select('count(ucl)')
			->getQuery()
			->getSingleScalarResult();
	}

	public static function countReferralEarned(User $user)
    {
        return Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->where('ucl.type = :type')
            ->andWhere('ucl.status = :status')
            ->andWhere('ucl.loadStatus = :loadStatus')
            ->andWhere('ucl.promoType = :promoType')
            ->andWhere('ucl.earnType = :earnType')
            ->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
            ->setParameter('status', UserCardLoad::STATUS_COMPLETED)
            ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED)
            ->setParameter('promoType', PromotionService::PROMOTION_TYPE_EARN)
            ->setParameter('earnType', UserCardLoad::EARN_TYPE_REFERRAL)
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user)
            ->select('count(ucl)')
            ->getQuery()
            ->getSingleScalarResult();
    }

	// Check if the consumer is in restricted state
    public static function isRestricted(User $user, $type = SpendrRestrict::RESTRICT_TYPE_LOAD)
    {
        $stateId = Util::meta($user, 'currentStateId');
        if (!$stateId && $user->getState()) {
            $stateId = $user->getState()->getId();
        }
        if ($stateId) {
            $restrict = SpendrRestrict::getByStateOrStateID($stateId);
            if (!$restrict) return false;

            return Util::meta($restrict, $type) || false;
        }

        return false;
    }

    public static function canDeposit(User $user, UserCard $dummy = null)
    {
        $isRestricted = self::isRestricted($user);
        if (!$isRestricted) {
            return true;
        }

        return self::needMakeWhole($user, $dummy);
    }

    public static function needMakeWhole(User $user, UserCard $dummy = null, $returnType = 'bool')
    {
        $isRestricted = self::isRestricted($user);
        if (!$isRestricted) {
            return false;
        }

        if (!$dummy) {
            $dummy = UserService::getDummyCard($user);
        }
        $consumerBalance = $dummy->getBalance();

        $needMakeWholeAmount = 0;

        $needMakeWhole = $isRestricted && ($consumerBalance < 0) ? true : false;

        if ($needMakeWhole) {
            $pendingMakeWholeLoadAmount = self::calculatePendingMakeWholeLoadAmount($user);
            $pendingMakeWholeLoadAmount = (int)$pendingMakeWholeLoadAmount;

            if ($pendingMakeWholeLoadAmount >= abs($consumerBalance)) {
                $needMakeWhole = false;

                if ($pendingMakeWholeLoadAmount > abs($consumerBalance)) {
                    SlackService::alert(
                        "The amount of make whole load in pending state exceeds the user's negative balance",
                        [
                            'user' => $user->getId(),
                            'pending amount' => $pendingMakeWholeLoadAmount,
                            'consumer balance' => $consumerBalance
                        ],
                        SlackService::GROUP_DEVS
                    );
                }
            } else {
                $needMakeWholeAmount = abs($consumerBalance) - $pendingMakeWholeLoadAmount;
            }
        }

        if ($returnType === 'amount') {
            return $needMakeWholeAmount;
        }
       
        return $needMakeWhole;
    }

    public static function calculatePendingMakeWholeLoadAmount(User $user)
    {
        $expr = Util::expr();
        return Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
			->where('ucl.type = :type')
			->andWhere(
                $expr->orX(
                    $expr->isNull('ucl.status'),
                    $expr->neq('ucl.status', ':status')
                )
            )
			->andWhere('ucl.loadStatus != :loadStatusLoaded')
			->andWhere('ucl.loadStatus != :loadStatusError')
			->andWhere('ucl.meta like :metaRL')
			->andWhere('ucl.meta like :metaRLFMW')
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->setParameter('status', UserCardLoad::STATUS_COMPLETED)
			->setParameter('loadStatusLoaded', UserCardLoad::LOAD_STATUS_LOADED)
			->setParameter('loadStatusError', UserCardLoad::LOAD_STATUS_ERROR)
			->setParameter('metaRL', '%"restrictLoad":true%')
			->setParameter('metaRLFMW', '%"restrictLoadForMakeWhole":true%')
            ->andWhere('uc.user = :user')
            ->setParameter('user', $user)
            ->select('sum(ucl.initialAmount)')
			->getQuery()
			->getSingleScalarResult();
    }
    
    public static function restrictedLoadStatus(User $user)
    {
        $stateId = Util::meta($user, 'currentStateId');
        if (!$stateId && $user->getState()) {
            $stateId = $user->getState()->getId();
        }
        if ($stateId) {
            $restrict = SpendrRestrict::getByStateOrStateID($stateId);
            if (!$restrict) return null;

            return $restrict->getLoadType();
        }

        return null;
    }

    // Get the lasted load bank card id
    public static function getLastLoadCardId(User $user)
    {
        $rs = Util::em()->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->join('uct.userCard', 'uc')
            ->where('uc.user = :user')
            ->setParameter('user', $user)
            ->andWhere('uct.tranCode = :type')
            ->setParameter('type', 'debit')
            ->orderBy('uct.id', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if ($rs) {
            $r = $rs[0];
            return $r->getUserCard() ? $r->getUserCard()->getId() : null;
        }
        return null;
    }

    public static function updateStaleReturn(User $consumer)
    {
        $userId = $consumer->getId();
        $consumerMeta = Util::meta($consumer);

        $isNewUser = $consumer->getCreatedAt() > Carbon::parse(SpendrBundle::getStaleReturnStartTime()) ? true : false;

        if (isset($consumerMeta['pendingStaleReturnTimesMark'])) {
            $consumer = self::initStaleReturnTimes(
                $consumer, 
                null, 
                $isNewUser
            );
            if (is_string($consumer)) {
                SlackService::alert('Update stale return. After the balance changes from negative to positive, init stale return times failed:', [
                    'Consumer ID' => $userId,
                    'is new user' => $isNewUser,
                    'error' => $consumer
                ], [SlackService::MENTION_TRACY]);
                return false;
            }
        }

        $consumerMeta = Util::meta($consumer);
        if (isset($consumerMeta['pendingStaleReturnTimesMark'])) {
            Log::debug(
                'Update stale return. This user needs to be initialized before updating.',
                [
                    'Consumer ID' => $userId,
                    'is new user' => $isNewUser,
                    'pendingStaleReturnTimesMark' => $consumerMeta['pendingStaleReturnTimesMark']
                ]
            );
            return false;
        }

        $updateRes = self::checkStaleReturnLoad($consumer);
        if (is_string($updateRes)) {
            SlackService::alert('Update stale return. After the balance changes from negative to positive, update stale return times failed when: ' . $updateRes, [
                'Consumer ID' => $userId
            ], [SlackService::MENTION_TRACY]);
            return false;
        }

        return true;
    }

    public static function initStaleReturnTimes(
        User $consumer, 
        UserCard $consumerDummy = null, 
        $isNewUser = false
    ) {
        if (!$consumer->inTeams(SpendrBundle::getConsumerRoles())) {
            return 'Invalid role.';
        }

        if ($isNewUser) {
            return 'New users do not need to be initialized.';
        }

        $meta = Util::meta($consumer);
        if (isset($meta['staleReturnTimes'])) {
            return 'The consumer has already been initialized.';
        }

        if (!$consumerDummy) {
            $consumerDummy = UserService::getDummyCard($consumer);
        }

        $balance = $consumerDummy->getBalance();
        $currentReturnTimes = FeeService::getConsumerAchReturnTimes($consumer);
        $currentFundingStatus = self::getConsumerLoadStatus($consumer);

        $staleReturnTimes = 0;
        $pendingStaleReturnTimesMark = false;
        if ($balance >= 0) {
            if ($currentFundingStatus === self::LOAD_STATUS_INSTANT) {
                $staleReturnTimes = 0;
            } else if ($currentFundingStatus === self::LOAD_STATUS_PREFUND) {
                $staleReturnTimes = 3;
            } else if ($currentFundingStatus === self::LOAD_STATUS_FREEZE) {
                $staleReturnTimes = $currentReturnTimes;
            }
        } else {
            if ($currentFundingStatus === self::LOAD_STATUS_FREEZE) {
                $staleReturnTimes = $currentReturnTimes;
            } else {
                $pendingStaleReturnTimesMark = true;
            }
        }

        $newMeta = $meta ? $meta : [];
        if ($pendingStaleReturnTimesMark) {
            $newMeta['pendingStaleReturnTimesMark'] = $pendingStaleReturnTimesMark;
        } else {
            $newMeta['staleReturnTimes'] = $staleReturnTimes;
            $newMeta['staleReturnTimesInitAt'] = Util::formatFullDateTime(Carbon::now());
            if (isset($newMeta['pendingStaleReturnTimesMark'])) {
                unset($newMeta['pendingStaleReturnTimesMark']);
            }
        }

        // todo: need remove, too many users
        Log::debug('init stale return times: ', [
            $balance,
            $currentReturnTimes,
            $currentFundingStatus,
            $staleReturnTimes,
            $pendingStaleReturnTimesMark,
            $newMeta
        ]);

        $consumer->setMeta(Util::j2s($newMeta))
            ->persist();
        
        if ($pendingStaleReturnTimesMark) {
            $note = 'Initialize stale return count. Do not calculate for now, initialize when the balance is positive.';
        } else {
            $note = 'Initialize stale return count. Total count: ' . $newMeta['staleReturnTimes'] . ', initialization time: ' . $newMeta['staleReturnTimesInitAt'] . '.';
        }
        $consumer->addNote($note);

        return $consumer;
    }

    private static function checkStaleReturnLoad(User $consumer)
    {
        if (!$consumer->isSpendrConsumer()) {
            return 'Only consumer role can perform this operation.';
        }

        $consumerDummy = UserService::getDummyCard($consumer);
        if (!$consumerDummy) {
            return 'Dummy card not found.';
        }

        $balance = $consumerDummy->getBalance();
        if ($balance < 0) {
            return 'Balance is less than 0, cannot perform this operation.';
        }

        $load = SpendrBundle::getStaleReturnStartTxn('load');
        if (!$load) {
            return 'Initial transaction not found.';
        }

        $expr = Util::expr();

        $query = Util::em()->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('uc.card', 'cpct')
            ->join('uc.user', 'u')
            ->join('u.teams', 't')
            ->where(Util::expr()->eq('cpct.cardProgram',':cardProgram'))
            ->setParameter('cardProgram', CardProgram::spendr())
            ->andWhere($expr->in('t.name', ':roles'))
            ->setParameter('roles', SpendrBundle::getConsumerRoles())
            ->andWhere('uc.user = :consumer')
            ->setParameter('consumer', $consumer)
            ->andWhere('ucl.id > :startId')
            ->setParameter('startId', $load->getId())
            ->andWhere('ucl.type = :typeLoad')
            ->setParameter('typeLoad', UserCardLoad::TYPE_LOAD_CARD)
            ->andWhere('ucl.loadStatus = :statusError')
            ->setParameter('statusError', UserCardLoad::LOAD_STATUS_ERROR)
            ->andWhere('ucl.transactionNo is not null')
            ->andWhere('ucl.meta like :metaReturnedByBank')
            ->setParameter('metaReturnedByBank', '%"returnedByBank":true%')
            ->andWhere('ucl.meta like :metaReturnedCode')
            ->setParameter('metaReturnedCode', '%"returnedCode":"' . SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS . '"%')
            ->andWhere($expr->notLike('ucl.meta', ':metaStaleReturnLoad'))
            ->setParameter('metaStaleReturnLoad', '%"staleReturnLoad"%');

        $loads = $query->distinct()
            ->getQuery()
            ->getResult();

        if ($loads) {
            foreach ($loads as $load) {
                $achTxn = AchTransactions::findTransactionByTranId($load->getTransactionNo());
                if (!$achTxn) {
                    continue;
                }

                $completelyBackAt = Util::meta($load, 'completelyBackToPartnerAt');
                $returnDate = Carbon::parse($achTxn->getReturnDate());
                $returnDueDate = $returnDate->addDays(14);
                
                Log::debug('return date: ', [
                    $returnDate,
                    $returnDueDate,
                    Carbon::now(),
                    $returnDueDate > Carbon::now()
                ]);

                // todo: Optimize the order of updating stale returns and offsetting pending balance in the future.
                $isStale = false;
                if ($completelyBackAt) {
                    if (Carbon::parse($completelyBackAt) < $returnDueDate) {
                        $isStale = false;
                    } else {
                        $isStale = true;
                    }
                } else {
                    if (Carbon::now() < $returnDueDate) {
                        continue;
                    } else {
                        $isStale = true;
                    }
                }

                Util::updateMeta($load, [
                    'staleReturnLoad' => $isStale
                ]);
    
                if ($isStale) {
                    $consumer->addNote('Stale return: A new "stale return" was generated at ' . Util::formatFullDateTime(Carbon::now()) . ', Load ID: ' . $load->getId());
                }
            }
        }

        return true;
    }

    public static function getConsumerStaleReturnCount(User $consumer)
    {
        $meta = Util::meta($consumer);
        $oldStaleReturnedTimes = isset($meta['staleReturnTimes']) ? $meta['staleReturnTimes'] : 0;
        $newStaleReturnedTimes = FeeService::getConsumerAchReturnTimes(
            $consumer, 
            SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS,
            false,
            true
        );

        $returnedTimes = $oldStaleReturnedTimes + $newStaleReturnedTimes;

        return $returnedTimes;
    }
}
