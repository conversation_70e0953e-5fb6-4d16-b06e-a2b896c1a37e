<?php

namespace SpendrBundle\Services\ACH;

use AppBundle\Command\InstantBackgroundCommand;
use CoreBundle\Controller\Cron\Spendr\FFBAchServiceController;
use CoreBundle\Entity\AchBatch;
use CoreBundle\Entity\AchTransactions;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Service;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SpendrBundle\Services\SlackService;
use SpendrBundle\Services\SpendrQueue;

class BatchService
{
    public static function findLatestFailedBatch()
    {
        $em = Util::em();
        $expr = Util::expr();
        $all = $em->getRepository(AchBatch::class)
            ->createQueryBuilder('ab')
            ->where($expr->isNull('ab.batchFileString'))
            ->andWhere($expr->isNull('ab.batchStatus'))
            ->andWhere('ab.program = :program')
            ->setParameter('program', AchBatch::ACH_BATCH_PROGRAM_SPENDR)
            ->orderBy('ab.id', 'desc')
            ->setMaxResults(5)
            ->getQuery()
            ->getResult();
        $atQuery = $em->getRepository(AchTransactions::class)
            ->createQueryBuilder('at')
            ->where('at.batchId = :batch_id')
            ->select('at.tranId')
            ->getDQL();
        $cardProgram = CardProgram::spendr();
        $card = CardProgramCardType::getForCardProgram($cardProgram);
        /** @var AchBatch $item */
        foreach ($all as $item) {
            $processing = $em->getRepository(UserCardTransaction::class)
                ->createQueryBuilder('uct')
                ->join('uct.userCard', 'uc')
                ->where('uc.card = :card')
                ->andWhere($expr->eq('uct.tranId', $expr->any($atQuery)))
                ->andWhere('uct.accountStatus = :account_status')
                ->setParameter('card', $card)
                ->setParameter('batch_id', $item->getId())
                ->setParameter('account_status', 'processing')
                ->select('count(distinct uct)')
                ->getQuery()
                ->getSingleScalarResult();
            if ($processing) {
                return $item;
            }
        }
        return null;
    }

    public static function resetLatestFailedBatch(AchBatch $batch = null)
    {
        if (!$batch) {
            $batch = self::findLatestFailedBatch();
        }
        if (!$batch) {
            return false;
        }
        if ($batch->getBatchFileString()) {
            throw new PortalException('The batch had been created successfully before!');
        }
        FFBAchServiceController::addAchBatchLock();
        $batchId = $batch->getId();
        $params = [
            'received',
            $batchId,
        ];
        $conn = Util::em()->getConnection();
        $conn->executeStatement(
            'update ach_transactions set tranStatus = ? where batchId = ?',
            $params
        );
        $updated = $conn->executeStatement('
            update user_card_transaction set account_status = ? where tran_id in (
                select tranId from ach_transactions where batchId = ?
            );
        ', $params);

        SlackService::info('Completed resetting the ACH batch', [
            'batch' => $batchId,
            'updated' => $updated,
        ], SlackService::GROUP_SERVER_DEVS);
        FFBAchServiceController::clearAchBatchLock();

        return $updated;
    }

    public static function checkAndSubmitResetFailedBatch()
    {
        if (FFBAchServiceController::isAchBatchLocked()) {
            throw new FailedException('The ACH batch is executing. Please wait.');
        }

        $batch = self::findLatestFailedBatch();
        if (!$batch) {
            throw new FailedException('No failed batch found!');
        }

        SpendrQueue::spendrResetFailedBatch();
    }
}
