<?php


namespace SpendrBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class UserCardService
{
	public static function verifyPlaidCard(UserCard $card, User $member)
	{
		if (
			!$card
			|| ($card && $member->getId() !== $card->getUser()->getId())
			|| $card->getStatus() === UserCard::STATUS_INACTIVE
		) {
			return 'Please select a valid bank card.';
		}
		if ($card->getStatus() === UserCard::STATUS_PENDING || !$card->getAccountNumber()) {
			return 'The card is not verified. Please select a valid bank card.';
		}

		// New version forbid the user card added through yodlee.
		if ($card->getType() !== UserCard::LL_TYPE_PLAID) {
			return 'We are now using Plaid to bind bank accounts. You can update the app and relink your account with plaid to continue using.';
		}
		$r = $card->getRoutingNumber() ? SSLEncryptionService::tryToDecrypt($card->getRoutingNumber()) : '';
		if (self::isNonDepositedRouting($r)) {
            $platformMeta = Util::meta(Platform::spendr());
            $phone = $platformMeta['customerSupportPhone'] ?? null;
            $email = $platformMeta['customerSupportEmail'] ?? null;
		    return "The bank you're depositing from is no longer supported with Spendr for reasons outside of our control. Please deposit from a different bank account and reach out to Spendr Support at {$phone} or {$email} with any questions or concerns.";
        }

		return true;
	}

	// If routing number is not able to deposit load
	public static function isNonDepositedRouting($routing) {
	    if (Util::user() && Data::get('spendr_can_add_non_deposited:' . Util::user()->getId())) return false;
	    return in_array($routing, ['*********']);
    }

    // if bank card is in list which subtype is incorrect
    public static function isIncorrectSubtype($id) {
	    return in_array($id, [745182]);
    }

	public static function queryPendingBalance(
		$pendingType = 'all'
	) {
		$expr = Util::expr();
		$query = Util::em()->getRepository(UserCard::class)
			->createQueryBuilder('uc')
			->join('uc.card', 'cpct')
			->join('uc.user', 'u')
			->where($expr->eq('cpct.cardProgram',':cardProgram'))
			->setParameter('cardProgram', CardProgram::spendr())
			->andWhere('uc.type = :type')
			->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY);

		if ($pendingType === 'all') {
			$query->andWhere('uc.pendingFees is not null')
				->andWhere('uc.pendingFees <> :pendingFee')
				->setParameter('pendingFee', '[]');
		} else {
			$query->andWhere('uc.pendingFees like :pendingFee')
				->setParameter('pendingFee', '%' . $pendingType . '%');
		}

		return $query;
	}

	// dataType: runningBalance / amount
	public static function getPendingBalanceData(
		$pendingType = 'all',
		$dataType = 'runningBalance'
	) {
		if ($dataType === 'runningBalance') {
			$data = [];
		} else if ($dataType === 'amount') {
			$data = null;
		} else {
			return null;
		}
		
		$query = self::queryPendingBalance($pendingType);

		$ucs = $query->getQuery()
			->getResult();

		if (!$ucs) {
			return $data;
		}

		$totalAmount = 0;
		/** @var UserCard $uc */
		foreach ($ucs as $uc) {
			$pfs = Util::s2j($uc->getPendingFees());
			$amount = 0;
			foreach ($pfs as $pf) {
				if (isset($pf['type'])) {
					if (
						$pendingType === 'all'
						&& in_array($pf['type'], [
							UserCard::PENDING_FEE_BACK_TO_PARTNER,
							UserCard::PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER
						])
					) {
						$amount += $pf['amount'];
					} else {
						if ($pf['type'] === $pendingType) {
							$amount += $pf['amount'];
						}
					}
				}
			}

			$totalAmount += $amount;

			if ($dataType === 'runningBalance') {
				if ($amount <= 0) {
					continue;
				}

				$amountText = Money::format($amount, 'USD', false);
				$amountText = '-' . $amountText;
				$amount = '-' . $amount;

				$data[] = [
					'Transaction' => 'Pending Fee',
					'Date & Time' => null,
					'Transaction ID' => $uc->getUser()->getId(),
					'Amount Value' => $amount,
					'Amount' => $amountText,
					'timestamp' => Carbon::now()->getTimestamp(),
					'tranCode' => 'DEBIT',
					'txnTime' => new \DateTime(),
//					'txnTypeForChart' => 'Pending Balance',
					'txnTypeForChart' => 'Debit',
				];
			}
		}

		if ($dataType === 'amount') {
			$data = $totalAmount;
		}

		return $data;
	}

	public static function getAccountNumMask($meta)
	{
		$accountNumMask = '';
		if ($meta && isset($meta['mask'])) {
			$accountNumMask = '***' . $meta['mask'];
		}
		return $accountNumMask;
	}

	public static function getRoutingNumMask($meta)
	{
		$routingNumMask = '';
		if ($meta && isset($meta['routing_mask'])) {
			$routingNumMask = '***' . $meta['routing_mask'];
		}
		return $routingNumMask;
	}
}
