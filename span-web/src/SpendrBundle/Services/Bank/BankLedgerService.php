<?php

namespace SpendrBundle\Services\Bank;

use CoreBundle\Entity\Attachment;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use SpendrBundle\Entity\SpendrTransaction;

class BankLedgerService
{
	public static function checkIfImportedBefore($type, $amountType = 'credit', $status = [], $batchId = null)
	{
		if (($type === 'ach_batch') && $batchId) {
			$descriptionLLC = ImportTransactionsService::DESC_SPENDR_LLC_ACH . ' ' . $batchId;
			$descriptionINC = ImportTransactionsService::DESC_SPENDR_INC_ACH . ' ' . $batchId;
		} else {
			return false;
		}

		if (($amountType !== 'credit') && ($amountType !== 'debit')) {
			return false;
		}

		if (!$status) {
			$status = [
				SpendrTransaction::STATUS_PENDING,
				SpendrTransaction::STATUS_COMPLETED
			];
		}

		$expr = Util::expr();
		$query = Util::em()->getRepository(SpendrTransaction::class)
			->createQueryBuilder('st')
			->where($expr->orX(
				$expr->eq('st.description', ':descLLC'),
				$expr->eq('st.description', ':descINC')
			))
			->setParameter('descLLC', $descriptionLLC)
			->setParameter('descINC', $descriptionINC)
			->andWhere(Util::expr()->in('st.status', ':statuses'))
			->setParameter('statuses', $status);
		if ($amountType === 'credit') {
			$query->andWhere('st.credit is not null');
		} else if ($amountType === 'debit') {
			$query->andWhere('st.debit is not null');
		}
		$records = $query->setMaxResults(1)
			->getQuery()
			->getResult();
		if (count($records)) {
			return true;
		}
		return false;
	}

	public static function importAction($attachmentId)
    {
		if (!$attachmentId) {
			return new FailedResponse('Invalid parameters.');
		}
		
		Util::longRequest();

        $attachment = Attachment::find($attachmentId);
        $path = $attachment->prepareForLocalRead();
        [
        	$imported,
			$duplicated,
			$errorRows,
			$invalidRows,
			$loadRows,
			$unloadRows,
			$partnerLoadRows,
			$bankServiceChargeRows,
			$bankAchFeeRows,
		] = ImportTransactionsService::import($path);
        if ($imported) {
            $msg = count($imported) . ' records were imported successfully. ';
            if ($loadRows) {
				ImportTransactionsService::handleLoadRows();
                Log::debug('Bank ledger import: handled load rows ' . count($loadRows));
			}
            if ($unloadRows) {
				ImportTransactionsService::handleUnloadRows();
                Log::debug('Bank ledger import: handled unload rows ' . count($unloadRows));
			}
            if ($partnerLoadRows) {
				ImportTransactionsService::handlePartnerLoadRows();
                Log::debug('Bank ledger import: handled partner load rows ' . count($partnerLoadRows));
			}
            if ($bankServiceChargeRows) {
				ImportTransactionsService::handleBankServiceChargeRows();
                Log::debug('Bank ledger import: handled bank service charge rows ' . count($bankServiceChargeRows));
			}
            if ($bankAchFeeRows) {
				ImportTransactionsService::handleBankAchLoadFeeRows();
                Log::debug('Bank ledger import: handled bank ach load fee rows ' . count($bankAchFeeRows));
			}
        } else {
            $msg = 'Nothing was imported. ';
        }
        if ($duplicated) {
            $msg .= count($duplicated) . ' duplicate records were skipped. ';
        }
        if ($errorRows) {
			$msg .= count($errorRows) . ' error records were skipped. ';
		}
        if ($invalidRows) {
			$msg .= count($invalidRows) . ' invalid records were skipped. ';
		}
        Log::debug('Bank ledger import: ' . $msg);
        return new SuccessResponse(null, $msg);
    }
}
