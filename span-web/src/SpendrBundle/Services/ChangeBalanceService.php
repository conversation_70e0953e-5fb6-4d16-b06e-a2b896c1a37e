<?php

namespace SpendrBundle\Services;

use Carbon\Carbon;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use SalexUserBundle\Entity\User;
use SpendrBundle\SpendrBundle;

class ChangeBalanceService
{
    public static function lockKey($userId)
    {
        if (!$userId) {
            return null;
        }
        $key = 'spendr_change_balance_lock_' . $userId;
        return $key;
    }

    public static function clearLock($userId) {
        $lockKey = self::lockKey($userId);
        if (!$lockKey) {
            return false;
        }
        Data::del($lockKey, true);
    }

    public static function waitOtherTxnsFinished($userId, $times = 1) {
        if ($times > 5) {
            return false;
        }

        $lockKey = ChangeBalanceService::lockKey($userId);
		if (!$lockKey) {
			return false;
		}

        Log::debug("spendr change balance lock - Wait for the user's other transactions to be completed:", [
            'lockKey' => $lockKey,
            'times' => $times,
        ]);

        if (Data::lockedHas($lockKey)) {
            sleep(1);
            $times++;
            return self::waitOtherTxnsFinished($userId, $times);
        } else {
            Log::debug("spendr change balance lock - Wait for the user's other transactions to be completed: Get.");
            return true;
        }
    }

    public static function afterBalanceBecomesPositive(User $user)
    {
        if ($user->inTeams(SpendrBundle::getConsumerRoles())) {
            $attrs = BrazeService::getUserAttributes($user);
            $userId = $user->getId();
            BrazeService::userTrack(null, [
                'external_id' => BrazeService::getExternalPrefix() . $userId,
                'name' => 'Account balance becomes positive',
                'time' => Carbon::now()->format('Y-m-d\TH:i:s\Z'),
            ], null, $attrs);
            SegmentService::track(null, [
                'userId' => BrazeService::getExternalPrefix() . $userId,
                'traits' => $attrs
            ]);

            ConsumerService::updateStaleReturn($user);
        }
    }
}