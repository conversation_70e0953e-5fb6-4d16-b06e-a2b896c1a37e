<?php

namespace SpendrBundle\Services;

use Carbon\Carbon;
use Clf<PERSON><PERSON>le\Entity\Transaction;
use Clf<PERSON><PERSON>le\Entity\TransactionStatus;
use Clf<PERSON><PERSON>le\Entity\TransactionType;
use CoreBundle\Entity\AchBatch;
use Core<PERSON>undle\Entity\CardProgram;
use CoreB<PERSON>le\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Query\Expr\Join;
use SalexUserBundle\Entity\User;
use SpendrBundle\Entity\SpendrMerchant;
use Spendr<PERSON>undle\Entity\SpendrTip;
use Spendr<PERSON><PERSON>le\Entity\SpendrTransaction;
use SpendrB<PERSON>le\Services\ACH\SpendrACHService;
use SpendrBundle\Services\Bank\ImportTransactionsService;
use SpendrBundle\SpendrBundle;

class FeeService
{
//	public const TO_SPENDR_MERCHANT_SETUP_FEE = 'Merchant setup fee to spendr';
//	public const TO_SPENDR_MERCHANT_ACCOUNT_CLOSURE_FEE = 'Merchant account closure fee to spendr';

    // merchant to spendr
    public const TO_SPENDR_TRANSACTION_FEE = 'Transaction fee to spendr';
    public const TO_SPENDR_TIP_FEE = 'Tip fee to spendr';

    // spendr to merchant
    public const TO_MERCHANT_REFUND_TRANSACTION_FEE = 'Refund transaction fee to merchant';

    // consumer to spendr
    public const TO_SPENDR_CONSUMER_ACH_RETURN_FEE = 'Consumer ach return fee to spendr';
    // monthly
    public const TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE = 'Consumer monthly inactivity fee to spendr';

    // spendr to bank
    public const TO_BANK_CONSUMER_LOAD_FEE = 'Consumer load fee to bank';
    public const TO_BANK_SPENDR_ACH_RETURN_FEE = 'Spendr ach return fee to bank';
    public const TO_BANK_SPENDR_WIRE_FEE = 'Spendr wire fee to bank';
    public const TO_BANK_SPENDR_ACH_SETUP_FEE = 'Spendr ach setup fee to bank';
    // monthly
    public const TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE = 'Spendr monthly account fee to bank';

    // spendr to tern
    public const TO_TERN_TRANSACTION_FEE = 'Transaction fee to tern';
    public const TO_TERN_CONSUMER_LOAD_FEE = 'Consumer load fee to tern';
    public const TO_TERN_MERCHANT_WITHDRAWAL_FEE = 'Merchant withdrawal fee to tern';
    public const TO_TERN_SPENDR_WITHDRAWAL_FEE = 'Spendr withdrawal fee to tern';
    // monthly
    public const TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE = 'Merchant monthly on file fee to tern';

    // bank to spendr
    public const TO_SPENDR_REFUND_MONTHLY_ACCOUNT_FEE = 'Bank refund monthly account fee to spendr';

//	public const TO_SPENDR_ROLLBACK_MONTHLY_TERN_FEE = 'Rollback monthly tern fee to spendr';

	// ucb - for correct data
	public const CORRECT_REFUND_SPENDR_FEE = 'Correct refund spendr fee'; // spendr to merchant
	public const CORRECT_REFUND_TERN_FEE = 'Correct refund tern fee'; // tern to spendr
	public const CORRECT_REFUND_REFUND_FEE = 'Correct refund refund fee'; // merchant to spendr, version 1
	public const CORRECT_REFUND_REFUND_FEE_TO_MERCHANT = 'Correct refund refund fee to merchant'; // spendr to merchant, version 2

	// for transfer to test balance account
	public const CORRECT_TRANSACTION_SPENDR_FEE = 'Correct transaction spendr fee';
	public const CORRECT_TRANSACTION_TERN_FEE = 'Correct transaction tern fee';

	// Hard-code other returned transactions, including misoperations, or return transactions caused by bank processing errors
	public static function otherWrongReturnedTxnDate() {
		return [
			Carbon::parse('2022-03-30'),
			Carbon::parse('2022-06-30')
		];
	}

	public static function allFeeNames() {
		return [
			self::TO_SPENDR_TRANSACTION_FEE,
			self::TO_TERN_TRANSACTION_FEE,
			self::TO_MERCHANT_REFUND_TRANSACTION_FEE,
			self::TO_SPENDR_CONSUMER_ACH_RETURN_FEE,
			self::TO_BANK_CONSUMER_LOAD_FEE,
			self::TO_TERN_CONSUMER_LOAD_FEE,
			self::TO_TERN_MERCHANT_WITHDRAWAL_FEE,
			self::TO_BANK_SPENDR_ACH_RETURN_FEE,
			self::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE,
			self::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE,
			self::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE,
			self::TO_SPENDR_REFUND_MONTHLY_ACCOUNT_FEE,
			self::TO_BANK_SPENDR_WIRE_FEE,
			self::TO_BANK_SPENDR_ACH_SETUP_FEE,
            self::TO_SPENDR_TIP_FEE,
		];
	}

	public static function spendrFeeNames() {
		return [
			self::TO_SPENDR_TRANSACTION_FEE,
			self::TO_SPENDR_CONSUMER_ACH_RETURN_FEE,
			self::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE,
			self::TO_SPENDR_REFUND_MONTHLY_ACCOUNT_FEE,
            self::TO_SPENDR_TIP_FEE,
		];
	}

	public static function ternFeeNames() {
		return [
			self::TO_TERN_CONSUMER_LOAD_FEE,
			self::TO_TERN_TRANSACTION_FEE,
			self::TO_TERN_MERCHANT_WITHDRAWAL_FEE,
			self::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE,
		];
	}

	public static function bankFeeNames() {
		return [
			self::TO_BANK_CONSUMER_LOAD_FEE,
			self::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE,
			self::TO_BANK_SPENDR_ACH_RETURN_FEE,
			self::TO_BANK_SPENDR_WIRE_FEE,
			self::TO_BANK_SPENDR_ACH_SETUP_FEE
		];
	}

	public static function getFeeItem($type = 'all') {
		$data = [
//			self::TO_SPENDR_MERCHANT_SETUP_FEE => [
//				'name' => 'Spendr Merchant Setup Fee',
//				'ratio' => null,
//				'fixed' => 25000,
//				'enable' => true,
//			],
			self::TO_SPENDR_TRANSACTION_FEE => [
				'ratio' => null,
				'fixed' => 50,
				'minFee' => null,
				'enable' => true,
			],
			self::TO_TERN_TRANSACTION_FEE => [
				'ratio' => null,
				'fixed' => 10,
				'enable' => false,
			],
//			self::TO_MERCHANT_REFUND_TRANSACTION_FEE => [
//				'ratio' => 5 / 100,
//				'fixed' => null,
//				'enable' => false,
//			],
			self::TO_SPENDR_CONSUMER_ACH_RETURN_FEE => [
				'firstTime' => [
					'ratio' => null,
					'fixed' => 0,
				],
				'secondTime' => [
					'ratio' => null,
					'fixed' => 1500,
				],
				'thirdTime' => [
					'ratio' => null,
					'fixed' => 2000,
				],
				'forthTime' => [
					'ratio' => null,
					'fixed' => 2000,
				],
				'enable' => true,
			],
//			self::TO_SPENDR_MERCHANT_ACCOUNT_CLOSURE_FEE => [
//				'name' => 'Merchant Account Closure Fee',
//				'ratio' => null,
//				'fixed' => 2500,
//				'enable' => true,
//			],
			self::TO_BANK_CONSUMER_LOAD_FEE => [
				'ratio' => 0.25 / 100,
				'fixed' => null,
				'minFee' => null,
				'enable' => false,
			],
			self::TO_TERN_CONSUMER_LOAD_FEE => [
				'ratio' => null,
				'fixed' => 10,
				'enable' => false,
			],
			self::TO_TERN_MERCHANT_WITHDRAWAL_FEE => [
				'ratio' => null,
				'fixed' => 10,
				'enable' => false,
			],
			// monthly fee
			self::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE => [
				'ratio' => null,
				'fixed' => 500,
				'enable' => true,
			],
			self::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE => [
				'ratio' => null,
				'fixed' => 51500,
				'enable' => false,
			],
			self::TO_BANK_SPENDR_WIRE_FEE => [
				'ratio' => null,
				'fixed' => 1500,
				'enable' => false,
			],
			self::TO_BANK_SPENDR_ACH_SETUP_FEE => [
				'ratio' => null,
				'fixed' => 250000,
				'enable' => false,
			],
			self::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE => [
				'ratio' => null,
				'fixed' => 5000,
				'enable' => false,
			],
		];

		$res = $data;
		if ($type !== 'all') {
			$res = $data[$type] ?? null;
		}

		return $res;
	}

	public static function calculate($type, $balance = 0) {
		$fee = self::getFeeItem($type);
		$feeValue = 0;
		if ($fee && self::hasFee($fee)) {
			if ($fee['fixed']) {
				$feeValue = $fee['fixed'];
			} else if ($fee['ratio'] && $balance) {
				$feeValue = $balance * (float)$fee['ratio'];
				$minFee = $fee['minFee'] ?? 0;
				if ($minFee && $feeValue < $minFee) {
					$feeValue = $minFee;
				}
			}
		}
		return round($feeValue, 0);
	}

	public static function hasFee($feeItem)
	{
		return $feeItem && ($feeItem['ratio'] || $feeItem['fixed']);
	}

	public static function hasFeeHistory(User $payer, $feeName, $entity)
	{
		if (!$payer || !$feeName || !$entity) {
			return null;
		}
		$expr = Util::expr();
		$rc = new \ReflectionClass($entity);
		$entityName = $rc->getName();
		$feeHistory = Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->where($expr->eq('ufh.entity', ':entity'))
			->setParameter('entity', $entityName)
			->andWhere($expr->eq('ufh.entityId', ':entityId'))
			->setParameter('entityId', $entity->getId())
			->andWhere($expr->eq('ufh.user', ':user'))
			->setParameter('user', $payer)
			->andWhere($expr->eq('ufh.feeName', ':feeName'))
			->setParameter('feeName', $feeName)
			->andWhere($expr->like('ufh.meta', ':spendrMeta'))
			->setParameter('spendrMeta',  '%"spendrMeta":true%')
			->getQuery()
			->getResult();
		if ($feeHistory) {
			return $feeHistory[0];
		}
		return false;
	}

//	/**
//	 * // Disregard
//	 *
//	 * // merchant setup fee
//	 * // merchant account closure fee
//	 *
//	 * @param SpendrMerchant $merchant
//	 * @param $feeType
//	 * @return \CoreBundle\Entity\UserCard|FailedResponse|float|int
//	 * @throws \Throwable
//	 */
//	public static function chargeMerchantToSpendrFee(SpendrMerchant $merchant, $feeType)
//	{
//		$feeItem = self::getFeeItem($feeType);
//		if (!$feeItem || ($feeItem && !self::hasFee($feeItem))) {
//			return new FailedResponse('No need to pay this fee!');
//		}
//		$fee = self::calculate($feeItem);
//		$feeName = $feeType;
//
//		$merchantAdminDummyCard = self::checkMerchantAdminDummyCard($merchant);
//		if ($merchantAdminDummyCard instanceof JsonResponse) {
//			return $merchantAdminDummyCard;
//		}
//		$merchantAdmin = $merchantAdminDummyCard->getUser();
//
//		$spendrDummyCard = UserService::getSpendrBalanceDummyCard('spendr');
//		if (!$spendrDummyCard) {
//			return new FailedResponse('Invalid spendr user card!');
//		}
//
//		$comment = sprintf(
//			'Charge %s on user %s at %s. Amount: %s',
//			strtolower($feeName),
//			$merchantAdmin->getId(),
//			Carbon::now(),
//			Money::format($fee, 'USD')
//		);
//
//		try {
//			$realFee = -$merchantAdminDummyCard->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, null);
//			UserFeeHistory::create($merchantAdmin, $realFee, $feeName, null, null, Platform::spendr());
//			$spendrDummyCard->updatePrivacyBalanceBy($realFee, $feeName, $comment, false, null);
//		} catch (Exception $e) {
//			SlackService::exception('Failed to charge the ' . strtolower($feeName), $e, [
//				'user' => $merchantAdmin->getId(),
//				'amount' => $fee,]);
//		}
//
//		return $fee;
//	}

	// Transaction fee
	public static function handleTransactionFee(Transaction $transaction)
	{
		if (!$transaction) {
			return null;
		}

		$feeToSpendr = self::chargeTransactionFeeToSpendr($transaction);
		$tipFeeToSpendr = self::chargeTransactionTipFeeToSpendr($transaction);
		$feeToTern = self::chargeTransactionFeeToTern($transaction);
		if (!$feeToSpendr && !$feeToTern) {
			return null;
		}

		return $feeToSpendr + $tipFeeToSpendr + $feeToTern;
	}

	/**
	 * Cannabis Merchant: $0.50 + 5%
	 * Non-cannabis merchant: $0.25 + 2.25%
	 *
	 * @param Transaction $transaction
	 * @param $amount
	 * @return int|null
	 */
	public static function getTransactionFeeToSpendr(Transaction $transaction, $amount)
	{
		if (!$transaction || !$amount) {
			return null;
		}

		/** @var SpendrMerchant $merchant */
		$merchant = $transaction->getMerchant();
		if (!$merchant) {
			return null;
		}
		$merchantBusinessType = $merchant->getBusinessType();

		$fee = 0;
		if ($merchantBusinessType === SpendrMerchant::BUSINESS_TYPE_CANNABIS) {
			//todo: Temporarily hardcoded, later changed to configurable in the admin dashboard
			if (
				(Util::isStaging() && $merchant->getId() === 53)
				// || (Util::isLive() && $merchant->getId() === 43)
				|| (Util::isLive() && $merchant->getId() === 53)
			) {
				// ------------ merchant 43 ------------
//				$dateTime = $transaction->getDateTime()->setTimezone(Util::tzNewYork());
//				$specialDate = Carbon::create('2023', '04', '26', '00', '00', '00', Util::tzNewYork());
//				if ($dateTime >= $specialDate) {
					// $fee = 50 + $amount * 0.05;
//				} else {
//					$fee = 50 + $amount * 0.035;
//				}
				// ------------ merchant 43 ------------

				// ------------ merchant 53 ------------
				$fee = 25 + $amount * 0.0225;
				// ------------ merchant 53 ------------
			} else if (
				(Util::isLive() && $merchant->getId() === 60)
				|| (Util::isStaging() && $merchant->getId() === 61)
			) {
				$dateTime = $transaction->getDateTime()->setTimezone(Util::tzNewYork());
				$specialDate = Carbon::create('2024', '08', '20', '07', '00', '00', Util::tzNewYork());

				if ($dateTime >= $specialDate) {
					$fee = 50 + $amount * 0.025;
				} else {
					$fee = 50 + $amount * 0.05;
				}
			} else {
				$fee = 50 + $amount * 0.05;
			}
		} else if ($merchantBusinessType === SpendrMerchant::BUSINESS_TYPE_NON_CANNABIS) {
			$fee = 25 + $amount * 0.0225;
		}

		return round($fee, 0);
	}

    public static function getTransactionTipFeeToSpendr(Transaction $t, $tipAmount)
    {
		$amount = $tipAmount;
		/** @var SpendrMerchant $merchant */
		$merchant = $t->getMerchant();
        if (!$merchant) {
            return null;
        }
        $merchantBusinessType = $merchant->getBusinessType();
        $fee = 0;
        if ($merchantBusinessType === SpendrMerchant::BUSINESS_TYPE_CANNABIS) {
            //todo: Temporarily hardcoded, later changed to configurable in the admin dashboard
						if (
							(Util::isStaging() && $merchant->getId() === 53)
							|| (Util::isLive() && $merchant->getId() === 53)
						) {
							$fee = $amount * 0.0225; // Live - merchant 53
						} else if (
							(Util::isLive() && $merchant->getId() === 60)
							|| (Util::isStaging() && $merchant->getId() === 61)
						) {
							$dateTime = $t->getDateTime()->setTimezone(Util::tzNewYork());
							$specialDate = Carbon::create('2024', '08', '20', '07', '00', '00', Util::tzNewYork());
			
							if ($dateTime >= $specialDate) {
								$fee = $amount * 0.025;
							} else {
								$fee = $amount * 0.05;
							}
						} else {
							$fee = $amount * 0.05;
						}
        } else if ($merchantBusinessType === SpendrMerchant::BUSINESS_TYPE_NON_CANNABIS) {
            $fee = $amount * 0.0225;
        }

        return round($fee, 0);
    }

	// refund amount * 5%
	public static function getRefundTransactionFeeToMerchant($refundAmount, Transaction $transaction)
	{
		if (!$transaction || !$refundAmount) {
			return null;
		}

		/** @var SpendrMerchant $merchant */
		$merchant = $transaction->getMerchant();
		if (!$merchant) {
			return null;
		}
		$merchantBusinessType = $merchant->getBusinessType();

		$fee = 0;
		if ($merchantBusinessType === SpendrMerchant::BUSINESS_TYPE_CANNABIS) {
			//todo: Temporarily hardcoded, later changed to configurable in the admin dashboard
			if (
				(Util::isStaging() && $merchant->getId() === 53)
				// || (Util::isLive() && $merchant->getId() === 43) //comment date: 2023/06/12
				|| (Util::isLive() && $merchant->getId() === 53)
			) {
				// $fee = $refundAmount * 0.035; // Live - merchant 43; comment date: 2023/06/12
				$fee = $refundAmount * 0.0225; // Live - merchant 53
			} else if (
				(Util::isLive() && $merchant->getId() === 60)
				|| (Util::isStaging() && $merchant->getId() === 61)
			) {
				$dateTime = $transaction->getDateTime()->setTimezone(Util::tzNewYork());
				$specialDate = Carbon::create('2024', '08', '20', '07', '00', '00', Util::tzNewYork());

				if ($dateTime >= $specialDate) {
					$fee = $refundAmount * 0.025;
				} else {
					$fee = $refundAmount * 0.05;
				}
			} else {
				$fee = $refundAmount * 0.05;
			}
		} else if ($merchantBusinessType === SpendrMerchant::BUSINESS_TYPE_NON_CANNABIS) {
			$fee = $refundAmount * 0.0225;
		}

		return round($fee, 0);
	}

	// charge merchant's fee to spendr
	public static function chargeTransactionFeeToSpendr(Transaction $transaction)
	{
		if (!$transaction) {
			return null;
		}
		$feeName = self::TO_SPENDR_TRANSACTION_FEE;

		$amount = $transaction->getAmount();

		$fee = self::getTransactionFeeToSpendr($transaction, $amount);

		if (!$fee) {
			return null;
		}
		$merchant = $transaction->getMerchant();
		$merchantAdmin = $merchant ? $merchant->getAdminUser() : null;
		$currentTransaction = $transaction;

		if (self::hasFeeHistory($merchantAdmin, $feeName, $currentTransaction)) {
			return null;
		}

		$currentTransaction->setSpendrFee($fee);
		Util::persist($currentTransaction);

		$comment = sprintf(
			'Charge %s on user %s at %s. Amount: %s',
			strtolower($feeName),
			$merchantAdmin->getSignature(),
			Carbon::now(),
			Money::format($fee, 'USD')
		);
		$merchantDummy = $merchantAdmin ? UserService::getDummyCard($merchantAdmin) : null;
		if ($merchantDummy) {
			$merchantDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $currentTransaction);
		}

		if (LocationService::beforeChangeLocationBalance(
			$currentTransaction->getLocation(),
			$feeName,
			$currentTransaction->getId()
		)) {
			$locationAdmin = $currentTransaction->getLocation()->getAdminUser();
			$locationDummy = UserService::getDummyCard($locationAdmin);
			$locationComment = sprintf(
				'Charge %s on user %s at %s. Amount: %s',
				strtolower($feeName),
				$locationAdmin->getSignature(),
				Carbon::now(),
				Money::format($fee, 'USD')
			);
			$locationDummy->updatePrivacyBalanceBy(-$fee, $feeName, $locationComment, false, $currentTransaction);
			UserFeeHistory::create($locationAdmin, $fee, $feeName, $currentTransaction, $locationComment, Platform::spendr());
		}

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		if ($spendrDummy) {
			$spendrDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $currentTransaction);
		}

		UserFeeHistory::create($merchantAdmin, $fee, $feeName, $currentTransaction, $comment, Platform::spendr());

        SlackService::tada('Transaction ' . $currentTransaction->getToken() . ', merchant paid transaction fee to Spendr platform.', [
            'Transaction Token' => $currentTransaction->getToken(),
            'Merchant Admin' => $merchantAdmin->getId(),
            'Spendr Admin' => $spendrDummy->getUser()->getId(),
            'Amount' => Money::format($currentTransaction->getAmount(), 'USD'),
            'Fee' => Money::format($fee, 'USD')
        ]);

		return $fee;
	}

	public static function chargeTransactionTipFeeToSpendr(Transaction $transaction)
    {
        // Consumer added tip and the tip needs charge fee
        $tipFee = 0;
        if (
            $transaction->getTip() &&
            $transaction->getTip()->getTipping() &&
            $transaction->getTip()->getTipping()->getChargeFee()
        ) {
            $tipFeeName = self::TO_SPENDR_TIP_FEE;
            $merchant = $transaction->getMerchant();
            $tipAmount = $transaction->getTip()->getAmount();
            $tipFee = self::getTransactionTipFeeToSpendr($transaction, $tipAmount);
            $transaction->getTip()->setSpendrFee($tipFee);
            Util::persist($transaction->getTip());

            if ($tipFee) {
                $merchantAdmin = $merchant ? $merchant->getAdminUser() : null;
                $comment = sprintf('Charge %s on user %s at %s. Tip Amount: %s, Tip Fee: %s',
                    strtolower($tipFeeName),
                    $merchantAdmin->getSignature(),
                    Carbon::now(),
                    Money::format($tipAmount, 'USD'),
                    Money::format($tipFee, 'USD')
                );
                $merchantDummy = $merchantAdmin ? UserService::getDummyCard($merchantAdmin) : null;
                if ($merchantDummy) {
                    $merchantDummy->updatePrivacyBalanceBy(-$tipFee, $tipFeeName, $comment, false, $transaction);
                }

				if (LocationService::beforeChangeLocationBalance(
					$transaction->getLocation(),
					$tipFeeName,
					$transaction->getId()
				)) {
					$locationAdmin = $transaction->getLocation()->getAdminUser();
					$locationDummy = UserService::getDummyCard($locationAdmin);
					$locationComment = sprintf('Charge %s on user %s at %s. Tip Amount: %s, Tip Fee: %s',
						strtolower($tipFeeName),
						$locationAdmin->getSignature(),
						Carbon::now(),
						Money::format($tipAmount, 'USD'),
						Money::format($tipFee, 'USD')
					);
					$locationDummy->updatePrivacyBalanceBy(-$tipFee, $tipFeeName, $locationComment, false, $transaction);
					UserFeeHistory::create($locationAdmin, $tipFee, $tipFeeName, $transaction, $locationComment, Platform::spendr());
				}

                $spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
                if ($spendrDummy) {
                    $spendrDummy->updatePrivacyBalanceBy($tipFee, $tipFeeName, $comment, false, $transaction);
                }

                UserFeeHistory::create($merchantAdmin, $tipFee, $tipFeeName, $transaction, $comment, Platform::spendr());

                SlackService::tada('Transaction ' . $transaction->getToken() . ', merchant paid tip fee to Spendr platform.', [
                    'Tip Amount' => Money::format($tipAmount, 'USD'),
                    'Tip Fee' => Money::format($tipFee, 'USD'),
                ]);
            }
        }

        return $tipFee;
    }

	// charge spendr's fee to tern
	public static function chargeTransactionFeeToTern(Transaction $transaction)
	{
		if (!$transaction) {
			return null;
		}
		$feeName = self::TO_TERN_TRANSACTION_FEE;
		$fee = self::calculateDiscountsByTotalAmount();
		if (!$fee) {
			return null;
		}

//		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
//		$spendrAdmin = $spendrDummy->getUser();
		$currentTransaction = $transaction;

//		if (self::hasFeeHistory($spendrAdmin, $feeName, $currentTransaction)) {
//			return null;
//		}

		$currentTransaction->setTernFee($fee);
		Util::persist($currentTransaction);

//		$comment = sprintf(
//			'Charge %s on Partner %s at %s. Amount: %s',
//			strtolower($feeName),
//			$spendrAdmin->getId(),
//			Carbon::now(),
//			Money::format($fee, 'USD')
//		);
//		if ($spendrDummy) {
//			$spendrDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $currentTransaction);
//		}
//
//		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');
//		if ($ternDummy) {
//			$ternDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $currentTransaction);
//		}
//
//		UserFeeHistory::create($spendrAdmin, $fee, $feeName, $currentTransaction, $comment, Platform::spendr());
//
//		SlackService::tada('Transaction ' . $currentTransaction->getToken() . ', Spendr paid transaction fee to Tern.', [
//			'Transaction Token' => $currentTransaction->getToken(),
//			'Spendr Admin' => $spendrAdmin->getId(),
//			'Tern Admin' => $ternDummy->getUser()->getId(),
//			'Amount' => Money::format($currentTransaction->getAmount(), 'USD'),
//			'Fee' => Money::format($fee, 'USD')
//		]);

		return $fee;
	}

	// Consumer load fee: Spendr to Bank and Tern
//	public static function chargeConsumerLoadFee(UserCardLoad $load, $amount, $correct = false)
//	{
//		if (!$load || !$amount) {
//			return null;
//		}
//
//		$feeToBank = self::chargeConsumerLoadFeeToBank($load, $amount, $correct);
//		$feeToTern = self::chargeConsumerLoadFeeToTern($load, $correct);
//
//		Util::flush();
//
//		return $feeToBank + $feeToTern;
//	}

	public static function consumerLoadFeeToBank($amount) {
		$fee = $amount * 0.0025;
		$fee = round($fee, 0);
		return $fee;
	}

	// 6 cents
	public static function achSameDayFeeToBank() {
		return 6;
	}

//	/**
//	 * Spendr to Bank
//	 * 0.25% (min of $500 in aggregate fees/month)
//	 * load
//	 * @param UserCardLoad $load
//	 * @param $amount
//	 * @param bool $correct
//	 * @return int|null
//	 * @throws \PortalBundle\Exception\PortalException
//	 */
//	public static function chargeConsumerLoadFeeToBank(UserCardLoad $load, $amount, $correct = false)
//	{
//		if (!$amount || !$load) {
//			return null;
//		}
//
//		$feeType = self::TO_BANK_CONSUMER_LOAD_FEE;
//		$fee = self::consumerLoadFeeToBank($amount);
//
//		if (!$fee) {
//			return null;
//		}
//
//		$spendrUserCard = UserService::getSpendrBalanceDummyCard('spendr');
//		$spendrAdmin = $spendrUserCard->getUser();
//
//		if (self::hasFeeHistory($spendrAdmin, $feeType, $load)) {
//			return null;
//		}
//
//		$correctText = $correct ? 'Correct data. ' : null;
//
//		// pay fee to bank
//		$comment = sprintf(
//			$correctText . 'Load ID: %s. Charge %s on Partner %s at %s. Amount: %s',
//			$load->getId(),
//			strtolower($feeType),
//			$spendrAdmin->getId(),
//			Carbon::now(),
//			Money::format($fee, 'USD')
//		);
//
//		if ($spendrUserCard) {
//			$spendrUserCard->updatePrivacyBalanceBy(-$fee, $feeType, $comment, false, $load);
//		}
//
//		$bankUserCard = UserService::getSpendrBalanceDummyCard('bank');
//		if ($bankUserCard) {
//			$bankUserCard->updatePrivacyBalanceBy($fee, $feeType, $comment, false, $load);
//		}
//
//		UserFeeHistory::create($spendrAdmin, $fee, $feeType, $load, $comment, Platform::spendr());
//
//		SlackService::tada($comment);
//
//		return $fee;
//	}

//	/**
//	 * $0.10
//	 * load
//	 * @param UserCardLoad $load
//	 * @param bool $correct
//	 * @return int
//	 * @throws \PortalBundle\Exception\PortalException
//	 */
//	public static function chargeConsumerLoadFeeToTern(UserCardLoad $load, $correct = false)
//	{
//		if (!$load) {
//			return null;
//		}
//
//		$fee = self::calculateDiscountsByTotalAmount();
//		if (!$fee) {
//			return null;
//		}
//		$feeName = self::TO_TERN_CONSUMER_LOAD_FEE;
//
//		$spendrUserCard = UserService::getSpendrBalanceDummyCard('spendr');
//		$spendrAdmin = $spendrUserCard->getUser();
//
//		if (self::hasFeeHistory($spendrAdmin, $feeName, $load)) {
//			return null;
//		}
//
//		$correctText = $correct ? 'Correct data. ' : null;
//
//		// pay fee to tern
//		$comment = sprintf(
//			$correctText . 'Load ID: %s. Charge %s on Partner %s at %s. Amount: %s',
//			$load->getId(),
//			strtolower($feeName),
//			$spendrAdmin->getId(),
//			Carbon::now(),
//			Money::format($fee, 'USD')
//		);
//
//		if ($spendrUserCard) {
//			$spendrUserCard->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $load);
//		}
//
//		$ternUserCard = UserService::getSpendrBalanceDummyCard('tern');
//		if ($ternUserCard) {
//			$ternUserCard->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $load);
//		}
//
//		UserFeeHistory::create($spendrAdmin, $fee, $feeName, $load, $comment, Platform::spendr());
//
//		SlackService::tada($comment);
//
//		return $fee;
//	}

	/**
	 * $0 - $500k,      $0.1
	 * $500001 - $1M,   $0.08
	 * $1000001 - $5M,  $0.06
	 * $5M+,            $0.04
	 *
	 * @return int
	 */
	public static function calculateDiscountsByTotalAmount()
	{
		$totalAmount = (int)(TransactionService::getCurrentMonthTotalAmount());
		$fee = 10;

		if ($totalAmount) {
			if (($totalAmount >= 0) && ($totalAmount <= 500 * 1000 * 100)) {
				$fee = 10;
			} else if (($totalAmount > (500000 * 100)) && ($totalAmount <= 1000000 * 100)) {
				$fee = 8;
			} else if (($totalAmount > 1000000 * 100) && ($totalAmount <= 5000000 * 100)) {
				$fee = 6;
			} else if ($totalAmount > 5000000 * 100) {
				$fee = 4;
			}
		}

		return (int)$fee;
	}

	/**
	 * 0 - 50,    $50
	 * 51 - 200,  $40
	 * 201 - 500, $30
	 * 500+,      $20
	 *
	 * @return int
	 */
	public static function calculateDiscountsByMerchantActiveCount()
	{
		$count = (int)(MerchantService::getCurrentActiveMerchantCount());
		$fee = 5000;

		if ($count) {
			if (($count >= 0) && ($count <= 50)) {
				$fee = 5000;
			} else if (($count > 50) && ($count <= 200)) {
				$fee = 4000;
			} else if (($count > 200) && ($count <= 500)) {
				$fee = 3000;
			} else if ($count > 500) {
				$fee = 2000;
			}
		}

		return (int)$fee;
	}

//	/**
//	 * Merchant Withdrawal fee
//	 * $0.10
//	 * unload
//	 * @param User $merchantAdmin
//	 * @param UserCardLoad $load
//	 * @return float|null
//	 * @throws \Throwable
//	 */
//	public static function chargeMerchantWithdrawalFeeToTern(User $merchantAdmin, UserCardLoad $load)
//	{
//		if (!$merchantAdmin || !$load) {
//			return null;
//		}
//
//		$fee = self::calculateDiscountsByTotalAmount();
//		if (!$fee) {
//			return null;
//		}
//		$feeName = self::TO_TERN_MERCHANT_WITHDRAWAL_FEE;
//
//		// pay fee to tern
//		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
//		$spendrAdmin = $spendrDummy->getUser();
//
//		if (self::hasFeeHistory($spendrAdmin, $feeName, $load)) {
//			return null;
//		}
//
//		$comment = sprintf(
//			'Unload ID: %s. Charge %s on Partner %s at %s. Amount: %s',
//			$load->getId(),
//			strtolower($feeName),
//			$spendrAdmin->getId(),
//			Carbon::now(),
//			Money::format($fee, 'USD')
//		);
//
//		if ($spendrDummy) {
//			$spendrDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $load);
//		}
//
//		$ternDummy = UserService::getSpendrBalanceDummyCard('tern');
//		if ($ternDummy) {
//			$ternDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $load);
//		}
//		UserFeeHistory::create($spendrAdmin, $fee, $feeName, $load, $comment, Platform::spendr());
//		Util::flush();
//
//		SlackService::tada($comment);
//
//		return $fee;
//	}

	// Refund transaction fee: Spendr refund 5% * $refundAmount transaction fee to merchant
	public static function handleRefundTransactionFee(Transaction $oldTransaction, Transaction $refundTransaction)
	{
		if (!$oldTransaction || !$refundTransaction) {
			return null;
		}
		$merchant = $oldTransaction->getMerchant();
		$merchantAdmin = $merchant ? $merchant->getAdminUser() : null;
		$feeName = self::TO_MERCHANT_REFUND_TRANSACTION_FEE;

		if (self::hasFeeHistory($merchantAdmin, $feeName, $refundTransaction)) {
			return null;
		}

		$merchantDummy = $merchantAdmin ? UserService::getDummyCard($merchantAdmin) : null;

		if (!$merchantDummy) {
			return null;
		}

		$refundAmount = $refundTransaction->getAmount();
		$returnToMerchantFee = self::getRefundTransactionFeeToMerchant($refundAmount, $refundTransaction);

		if (!$returnToMerchantFee) {
			return null;
		}

		$comment = sprintf(
			'Refund transaction: %s. Refund the %s to the merchant %s at %s. Amount: %s',
			$refundTransaction->getToken(),
			strtolower($feeName),
			$merchantAdmin->getSignature(),
			Carbon::now(),
			Money::format($returnToMerchantFee, 'USD')
		);

		$spendrAdmin = UserService::getSpendrBalanceAdminAccount('spendr');
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		if ($spendrDummy) {
			$spendrDummy->updatePrivacyBalanceBy(-$returnToMerchantFee, $feeName, $comment, false, $refundTransaction);
		}

		$merchantDummy->updatePrivacyBalanceBy(
			$returnToMerchantFee,
			$feeName,
			$comment,
			false,
			$refundTransaction,
		);

		// Change location balance
		if (LocationService::beforeChangeLocationBalance(
			$refundTransaction->getLocation(),
			$feeName,
			$refundTransaction->getId(),
		)) {
			$locationAdminUser = $refundTransaction->getLocation()->getAdminUser();
			$locationComment = sprintf(
				'Refund transaction: %s. Refund the %s to the location %s at %s. Amount: %s',
				$refundTransaction->getToken(),
				strtolower($feeName),
				$locationAdminUser->getSignature(),
				Carbon::now(),
				Money::format($returnToMerchantFee, 'USD')
			);
			$locationDummy = UserService::getDummyCard($locationAdminUser);
			$locationDummy->updatePrivacyBalanceBy(
				$returnToMerchantFee,
				$feeName,
				$locationComment,
				false,
				$refundTransaction,
			);
		}

		$refundTransaction->setRefundFee($returnToMerchantFee);
		Util::persist($refundTransaction);

		UserFeeHistory::create($spendrAdmin, $returnToMerchantFee, $feeName, $refundTransaction, $comment, Platform::spendr());

		Util::flush();

		SlackService::tada($comment);

		return $returnToMerchantFee;
	}

	public static function handleAchReturnFeeProcess(UserCardLoad $load)
	{
		if (
			!$load
			|| (!$load->getUserCard()->getUser()->isSpendrConsumer())
			|| !LoadService::isReturnedByBankLoad($load, SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS)
		) {
			return null;
		}

		self::chargeAchReturnFee($load);
	}

	public static function getConsumerAchReturnedQuery(
		User $consumer,
		$returnedCode = SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS,
		$initStaleReturn = false,
		$realStaleReturn = false
	){
		if (!$consumer) {
			return null;
		}
		$expr = Util::expr();
		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->andWhere($expr->eq('uc.user', ':consumer'))
			->setParameter('consumer', $consumer)
			->andWhere('ucl.type = :type')
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->andWhere('ucl.loadStatus = :loadStatus')
			->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_ERROR)
			->andWhere($expr->like('ucl.meta', ':returnedByBank'))
			->setParameter('returnedByBank', '%"returnedByBank":true%');

		if ($returnedCode) {
			$query->andWhere($expr->like('ucl.meta', ':returnedCode'))
				->setParameter('returnedCode', '%"returnedCode":"' . $returnedCode . '"%');
		}

		if ($initStaleReturn || $realStaleReturn) {
			$load = SpendrBundle::getStaleReturnStartTxn('load');
			if ($load) { 
				if ($realStaleReturn) {
					$query->andWhere('ucl.id > :startId');
				} else {
					$query->andWhere('ucl.id <= :startId');
				}
				
				$query->setParameter('startId', $load->getId());
			}
		}

		if ($realStaleReturn) {
			$query->andWhere($expr->like('ucl.meta',':metaStaleReturnLoad'))
				->setParameter('metaStaleReturnLoad', '%"staleReturnLoad":true%');
		}

		return $query;
	}

	public static function getConsumerAchReturnTimes(
		User $consumer,
		$returnedCode = SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS,
		$initStaleReturn = false,
		$realStaleReturn = false
	){
		$returnedTimes = (clone self::getConsumerAchReturnedQuery(
				$consumer, 
				$returnedCode, 
				$initStaleReturn,
				$realStaleReturn
			))
			->select('count(distinct ucl)')
			->getQuery()
			->getSingleScalarResult();

		$returnedTimes = (int)$returnedTimes;
		return $returnedTimes;
	}

// 	public static function updateConsumerLoadStatusOlder(User $consumer, UserCardLoad $currentLoad, $status)
// 	{
// 		$returnedTimes = self::getConsumerAchReturnTimes($consumer);
// 		$consumerDummy = UserService::getDummyCard($consumer);
// 		$balance = $consumerDummy->getBalance();

// 		$currentLoadStatus = Util::meta($consumer, 'spendrConsumerLoadStatus');

// 		if ($currentLoadStatus === ConsumerService::LOAD_STATUS_FREEZE) {
// 			return $currentLoadStatus;
// 		}

// 		$loadStatus = $currentLoadStatus ?? ConsumerService::LOAD_STATUS_INSTANT;
// 		if ($returnedTimes < 3) {
// 			$loadStatus = ConsumerService::LOAD_STATUS_INSTANT;
// 		} else if ($returnedTimes === 3) {
// 			if ($status === 'returned') {
// 				$loadStatus = ConsumerService::LOAD_STATUS_PREFUND;
// 			} else if ($status === 'settled') {
// 				if ($currentLoadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
// 					if ($balance >= 0) {
// 						$loadStatus = ConsumerService::LOAD_STATUS_INSTANT;
// 					}
// 				}
// 			}
// 		} else if ($returnedTimes > 3) {
// 			if ($status === 'returned') {
// 				if ($currentLoadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
// 					$loadStatus = ConsumerService::LOAD_STATUS_FREEZE;
// 				} else if ($currentLoadStatus === ConsumerService::LOAD_STATUS_INSTANT) {
// 					/** @var UserCardLoad $lastReturnedLoad */
// 					$lastReturnedLoad = self::getLastReturnedLoadForConsumer($currentLoad);
// 					$lastReturnedTime = $lastReturnedLoad->getLoadAt();
// 					if ($lastReturnedTime > Carbon::now()->subDay(60)) {
// 						$loadStatus = ConsumerService::LOAD_STATUS_FREEZE;
// 					} else {
// //						$loadStatus = ConsumerService::LOAD_STATUS_INSTANT; // maybe a bug, so change to prefund
// 						$loadStatus = ConsumerService::LOAD_STATUS_PREFUND;
// 					}
// 				}
// 			} else if ($status === 'settled') {
// 				if ($currentLoadStatus === ConsumerService::LOAD_STATUS_PREFUND) {
// 					if ($balance >= 0) {
// 						$loadStatus = ConsumerService::LOAD_STATUS_INSTANT;
// 					}
// 				} else if ($currentLoadStatus === ConsumerService::LOAD_STATUS_INSTANT) {
// 					$loadStatus = ConsumerService::LOAD_STATUS_INSTANT;
// 				}
// 			}
// 		}

// 		Util::updateMeta($consumer, [
// 			'spendrConsumerLoadStatus' => $loadStatus
// 		]);
// 		return $loadStatus;
// 	}

	// public static function updateConsumerLoadStatusOld(User $consumer, UserCardLoad $currentLoad, $status)
	// {
	// 	$returnedTimes = self::getConsumerAchReturnTimes($consumer);
	// 	if ($returnedTimes < 1) {
	// 		return null;
	// 	}

	// 	$consumerDummy = UserService::getDummyCard($consumer);
	// 	$balance = $consumerDummy->getBalance();

	// 	$currentLoadStatus = Util::meta($consumer, 'spendrConsumerLoadStatus');

	// 	if ($currentLoadStatus === ConsumerService::LOAD_STATUS_FREEZE) {
	// 		return $currentLoadStatus;
	// 	}

	// 	$loadStatus = $currentLoadStatus ?? ConsumerService::LOAD_STATUS_INSTANT;

	// 	if (($returnedTimes > 0) && ($returnedTimes < 4))
	// 	{
	// 		if ($balance >= 0) {
	// 			$loadStatus = ConsumerService::LOAD_STATUS_INSTANT;
	// 		} else {
	// 			$loadStatus = ConsumerService::LOAD_STATUS_PREFUND;
	// 		}
	// 	} else if ($returnedTimes >= 4) {
	// 		$loadStatus = ConsumerService::LOAD_STATUS_FREEZE;
	// 	}

	// 	Util::updateMeta($consumer, [
	// 		'spendrConsumerLoadStatus' => $loadStatus
	// 	]);

	// 	$msg = sprintf(
	// 		'Load ID: %s. Change the load status to %s on consumer %s. Current balance: %s. Current number of returns: %s',
	// 		$currentLoad->getId(),
	// 		$loadStatus,
	// 		$consumer->getSignature(),
	// 		Money::formatUSD($balance),
	// 		$returnedTimes
	// 	);
	// 	SlackService::tada($msg);

	// 	return $loadStatus;
	// }

	protected static function getLastReturnedLoadForConsumer(UserCardLoad $currentLoad)
	{
		if (!$currentLoad) {
			return null;
		}

		$currentLoadId = $currentLoad->getId();
		$consumer = $currentLoad->getUserCard()->getUser();
		$expr = Util::expr();
		$loads = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->where('ucl.id < :currentLoadId')
			->setParameter('currentLoadId', $currentLoadId)
			->andWhere('ucl.loadStatus = :loadStatus')
			->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_ERROR)
			->andWhere($expr->like('ucl.meta', ':metaReturned'))
			->setParameter('metaReturned', '%"returnedByBank":true%')
			->andWhere($expr->like('ucl.meta', ':metaReturnedCode'))
			->setParameter('metaReturnedCode', '%"returnedCode":"' . SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS . '"%')
			->andWhere('uc.user = :consumer')
			->setParameter('consumer', $consumer)
			->andWhere($expr->in('t.name', ':roles'))
			->setParameter('roles', SpendrBundle::getConsumerRoles())
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere('ucl.type = :type')
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->setMaxResults(1)
			->orderBy('ucl.loadAt', 'desc')
			->getQuery()
			->getResult();

		if (!$loads) {
			return null;
		}

		return $loads[0];
	}

	public static function getAchReturnFee(User $consumer)
	{
		if (!$consumer) {
			return null;
		}

		$returnedTimes = self::getConsumerAchReturnTimes($consumer);
		$fee = 0;
		if ($returnedTimes === 1) {
			$fee = 0;
		} else if ($returnedTimes === 2) {
			$fee = 1500;
		} else if ($returnedTimes >= 3) {
			$fee = 2000;
		}
		return $fee;
	}

	public static function chargeAchReturnFee(UserCardLoad $load, $fee = null)
	{
		if (!LoadService::isReturnedByBankLoad($load, SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS)) {
			return null;
		}

		$feeToSpendr = self::chargeAchReturnFeeToSpendr($load, $fee);
//		$feeToBank = self::chargeAchReturnFeeToBank($load);

		Util::flush();

//		return (int)$feeToSpendr + (int)$feeToBank;
		return (int)$feeToSpendr;
	}

	public static function chargeAchReturnFeeToSpendr(UserCardLoad $load, $fee = null)
	{
		if (!LoadService::isReturnedByBankLoad($load, SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS)) {
			return null;
		}

		$consumer = $load->getUserCard()->getUser();

		if (!$fee) {
			$fee = self::getAchReturnFee($consumer);
		}
		if (!$fee) {
			return null;
		}
		$feeName = self::TO_SPENDR_CONSUMER_ACH_RETURN_FEE;

		$consumerDummy = UserService::getDummyCard($consumer);
		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$consumerBalance = $consumerDummy->getBalance();

		if (self::hasFeeHistory($consumerDummy->getUser(), $feeName, $load)) {
			return null;
		}

		$text = '';
		if (Util::isLive() && $load->getId() === 423439) {
			$text = 'Correct fee. ';
		}

		$comment = sprintf(
			$text . 'Load ID: %s. Load failed, charge the ' . strtolower($feeName)
			. ' on consumer %s at %s. Amount: %s',
			$load->getId(),
			$consumer->getSignature(),
			Carbon::now(),
			Money::format($fee, 'USD')
		);
		$consumerDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $load);
		SlackService::tada($comment);

		$processFee = $fee;
		$pendingFee = 0;

		if ($consumerBalance > 0) {
			if ($consumerBalance < $fee) {
				$processFee = $consumerBalance;
				$pendingFee = $fee - $consumerBalance;
			}
		} else {
			$processFee = 0;
			$pendingFee = $fee;
		}

		if ($processFee > 0) {
			$spendrComment = sprintf(
				$text . 'Load ID: %s. Load failed, charge the ' . strtolower($feeName)
				. ' on consumer %s at %s. Amount: %s. Funds should be added: %s. Actually added: %s. Pending amount: %s.',
				$load->getId(),
				$consumer->getSignature(),
				Carbon::now(),
				Money::format($processFee, 'USD'),
				Money::format($fee, 'USD'),
				Money::format($processFee, 'USD'),
				Money::format($pendingFee, 'USD')
			);
			$spendrDummy->updatePrivacyBalanceBy($processFee, $feeName, $spendrComment, false, $load);
			SlackService::tada($spendrComment);
		}

		if ($pendingFee > 0) {
			$consumerDummy->updatePendingFee(
				UserCard::PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER,
				'load',
				$load->getId(),
				$pendingFee
			);
		}
		UserFeeHistory::create($consumerDummy->getUser(), $fee, $feeName, $load, $comment, Platform::spendr());

		return $fee;
	}

	// $12.5
	public static function toBankAchReturnFee()
	{
//		return 1250;
		return 1000;
	}

	public static function chargeAchReturnFeeToBank(UserCardLoad $load, UserCardBalance $ucb = null)
	{
		if (!LoadService::isReturnedByBankLoad($load, SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS)) {
			return null;
		}

		$spendrDummy = UserService::getSpendrBalanceDummyCard('spendr');
		$bankDummy = UserService::getSpendrBalanceDummyCard('bank');

		$fee = self::toBankAchReturnFee();
		$feeName = self::TO_BANK_SPENDR_ACH_RETURN_FEE;

		if (self::hasFeeHistory($spendrDummy->getUser(), $feeName, $load)) {
			return null;
		}

		if (!$ucb) {
			$comment = sprintf(
				'Load ID: %s. Load failed, charge the ' . strtolower($feeName) . ' on Partner %s at %s. Amount: %s',
				$load->getId(),
				$spendrDummy->getUser()->getId(),
				Carbon::now(),
				Money::format($fee, 'USD')
			);
		} else {
			$comment = $ucb->getComment();
		}


		if (!$ucb) {
			$spendrDummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, $load);
			$bankDummy->updatePrivacyBalanceBy($fee, $feeName, $comment, false, $load);
		}

		$text = null;
		if ($ucb) {
			$comment = 'Correct fee. ' . $comment;
		}

		UserFeeHistory::create($spendrDummy->getUser(), $fee, $feeName, $load, $comment, Platform::spendr());

		SlackService::tada($comment);

		return $fee;
	}

	public static function getFeeTotalAmount($feeNames = [], Carbon $start = null, Carbon $end = null)
	{
		if (!$feeNames || !is_array($feeNames)) {
			return null;
		}
		$expr = Util::expr();
		$query = Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh');

		$query->andWhere(Util::expr()->like('ufh.meta', ':spendrMeta'))
			->setParameter('spendrMeta',  '%"spendrMeta":true%')
			->andWhere($expr->in('ufh.feeName', $feeNames));

		if ($start) {
			$query->andWhere('ufh.time >= :start')
				->setParameter('start', Util::toUTC($start));
		}
		if ($end) {
			$query->andWhere('ufh.time <= :end')
				->setParameter('end', Util::toUTC($end));
		}

		$amount = $query->select('sum(ufh.amount)')
			->getQuery()
			->getSingleScalarResult();
		return (int)$amount;
	}

	public static function getSpendrTransactionFeeTotalAmount(
		SpendrMerchant $merchant = null,
		Carbon $start = null,
		Carbon $end = null
	)
	{
		$feeName = FeeService::TO_SPENDR_TRANSACTION_FEE;
		$expr = Util::expr();
		$query = Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->join('ClfBundle:Transaction', 'ct', Join::WITH, 'ufh.entityId = ct.id');

		$query->andWhere(Util::expr()->like('ufh.meta', ':spendrMeta'))
			->setParameter('spendrMeta',  '%"spendrMeta":true%')
			->andWhere($expr->in('ufh.feeName', ':feeName'))
			->setParameter('feeName', [$feeName]);

		if ($merchant) {
			$query->andWhere('ct.merchant = :mer')
				->setParameter('mer', $merchant);
		}

		if ($start) {
			$query->andWhere('ufh.time >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('ufh.time <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		$amount = $query->andWhere($expr->notIn('ct.type', ':merchantType'))
			->setParameter('merchantType', TransactionType::gets([
                TransactionType::NAME_REFUND,
                TransactionType::NAME_ONLINE_REFUND
            ]))
			->select('sum(ufh.amount)')
			->getQuery()
			->getSingleScalarResult();

		return (int)$amount;
	}

//	public static function getTernTransactionFeeTotalAmount(SpendrMerchant $merchant = null)
//	{
//		$feeName = FeeService::TO_TERN_TRANSACTION_FEE;
//		$expr = Util::expr();
//		$query = Util::em()->getRepository(UserFeeHistory::class)
//			->createQueryBuilder('ufh')
//			->join('ClfBundle:Transaction', 'ct', Join::WITH, 'ufh.entityId = ct.id')
//			->where(Util::expr()->like('ufh.meta', ':spendrMeta'))
//			->setParameter('spendrMeta',  '%"spendrMeta":true%')
//			->andWhere($expr->in('ufh.feeName', [$feeName]));
//
//		if ($merchant) {
//			$query->andWhere('ct.merchant = :merchant')
//				->setParameter('merchant', $merchant);
//		}
//
//		$amount = $query->select('sum(ufh.amount)')
//			->getQuery()
//			->getSingleScalarResult();
//		return (int)$amount;
//	}

	public static function calculateSpendrGrossProfit(Carbon $start = null, Carbon $end = null)
	{
		$in = self::getFeeTotalAmount(self::spendrFeeNames(), $start, $end);
		$out = self::getFeeTotalAmount([
			self::TO_MERCHANT_REFUND_TRANSACTION_FEE,
			self::TO_BANK_CONSUMER_LOAD_FEE,
			self::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE,
			self::TO_BANK_SPENDR_ACH_RETURN_FEE,
			self::TO_BANK_SPENDR_WIRE_FEE,
			self::TO_BANK_SPENDR_ACH_SETUP_FEE
		], $start, $end);
//		$ternFees = self::estimatedTernFees();

//		return $in - $out - $ternFees;
		return $in - $out;
	}

	// txn fee - refund fee + ACH return fee - pending ACH return fee
	public static function calculateSpendrRevenue(Carbon $start = null, Carbon $end = null)
	{
		$in = self::getFeeTotalAmount([
			self::TO_SPENDR_TRANSACTION_FEE,
			self::TO_SPENDR_CONSUMER_ACH_RETURN_FEE,
			self::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE
		], $start, $end);
		$refundFee = self::getFeeTotalAmount([self::TO_MERCHANT_REFUND_TRANSACTION_FEE], $start, $end);
		$pendingACHReturnFee = UserCardService::getPendingBalanceData(
			UserCard::PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER,
			'amount'
		);

		$revenue = $in - $refundFee - $pendingACHReturnFee;
		return (int)$revenue;
	}

	public static function calculateSpendrAchReturnFee(Carbon $start = null, Carbon $end = null)
	{
		$allFee = self::getFeeTotalAmount([self::TO_SPENDR_CONSUMER_ACH_RETURN_FEE], $start, $end);
		$pendingFee = UserCardService::getPendingBalanceData(
			UserCard::PENDING_ACH_RETURN_FEE_BACK_TO_PARTNER,
			'amount'
		);

		return (int)($allFee - $pendingFee);
	}

	public static function calculateSpendrTransactionRevenue(Carbon $start = null, Carbon $end = null, $type = null)
	{
		$income = self::getFeeTotalAmount([
			self::TO_SPENDR_TRANSACTION_FEE,
		], $start, $end);
		$expenses = self::getFeeTotalAmount([
			self::TO_MERCHANT_REFUND_TRANSACTION_FEE,
//			self::TO_TERN_TRANSACTION_FEE,
		], $start, $end);
		return $income - $expenses;
	}

	public static function calculateBankFee()
	{
		$income = self::getFeeTotalAmount(self::bankFeeNames());
		$expenses = self::getFeeTotalAmount([self::TO_SPENDR_REFUND_MONTHLY_ACCOUNT_FEE]);
		return $income - $expenses;
	}

	public static function estimatedBankFees(Carbon $start = null, Carbon $end = null)
	{
		$loadFee = self::calculateBankLoadFee($start, $end);
		$returnFee = self::calculateBankReturnedLoadFee($start, $end);
		$monthlyAccountFee = self::calculateBankMonthlyAccountFee($start, $end);
		$wireFee = self::calculateBankWireFee($start, $end);
		$achSetupFee = self::calculateBankAchSetupFee($start, $end);

		$fee = $loadFee + $returnFee + $monthlyAccountFee + $wireFee + $achSetupFee;

		return $fee;
	}

	public static function calculateBankLoadFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		$expr = Util::expr();
		$feeDiscount = 0.0025;

		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->leftJoin('CoreBundle:AchTransactions', 'acht', Join::WITH, 'ucl.transactionNo = acht.tranId')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', [
				Role::ROLE_SPENDR_CONSUMER,
				Role::ROLE_SPENDR_PROGRAM_OWNER
			])
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere('ucl.type = :type')
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->andWhere($expr->in('ucl.loadType', ':loadTypes'))
			->setParameter('loadTypes', [
                LoadService::LOAD_TYPE_INSTANT,
                LoadService::LOAD_TYPE_PREFUND
            ])
			->andWhere($expr->notIn('acht.tranStatus', ':tranStatuses'))
			->setParameter('tranStatuses', [
				UserCardTransaction::STATUS_LL_CANCELED,
				UserCardTransaction::STATUS_LL_PROCESSING,
				UserCardTransaction::STATUS_LL_RECEIVED,
			]);

		if ($start) {
			$query->andWhere('ucl.initializedAt >= :start')
				->setParameter('start', Util::timeUTC($start));
		}
		if ($end) {
			$query->andWhere('ucl.initializedAt <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		$sameDayFeeQuery = (clone $query)->leftJoin('CoreBundle:AchBatch', 'b', Join::WITH, 'acht.batchId = b.id')
			->andWhere(Util::expr()->in('b.batchStatus', ':batchStatuses'))
			->setParameter('batchStatuses', [
				UserCardTransaction::STATUS_LL_PROCESSING,
				UserCardTransaction::STATUS_LL_SENT
			])
			->andWhere('b.processType = :processType')
			->setParameter('processType', AchBatch::ACH_BATCH_PROCESS_TYPE_SAME_DAY)
			->andWhere('b.program = :program')
			->setParameter('program', AchBatch::ACH_BATCH_PROGRAM_SPENDR);

		if ($type === 'list') {
			return (clone $query)->getQuery()
				->getResult();
		} else {
			$loadFee = (int)((clone $query)->select('sum(round(ucl.initialAmount * 0.0025, 0))')
				->getQuery()
				->getSingleScalarResult());

			$sameDayCount = (int)($sameDayFeeQuery->select('count(distinct ucl)')
				->getQuery()
				->getSingleScalarResult());

			$sameDayFee = $sameDayCount ? $sameDayCount * FeeService::achSameDayFeeToBank() : 0;

			return $loadFee + $sameDayFee;
		}
	}

	public static function calculateBankReturnedLoadFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		$expr = Util::expr();

		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', [
				Role::ROLE_SPENDR_CONSUMER,
				Role::ROLE_SPENDR_PROGRAM_OWNER,
				Role::ROLE_SPENDR_MERCHANT_ADMIN
			])
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere($expr->in('ucl.type', ':types'))
			->setParameter('types', [
				UserCardLoad::TYPE_LOAD_CARD,
				UserCardLoad::TYPE_UNLOAD
			])
//			->andWhere($expr->orX(
//				$expr->like('ucl.meta', ':metaInstant'),
//				$expr->like('ucl.meta', ':metaPrefund'),
//				)
//			)
//			->setParameter('metaInstant', '%' . LoadService::LOAD_TYPE_INSTANT . '":true%')
//			->setParameter('metaPrefund', '%' . LoadService::LOAD_TYPE_PREFUND . '":true%')
			->andWhere('ucl.meta like :metaReturnedByBank')
//			->andWhere('ucl.meta like :metaReturnedCode')
			->setParameter('metaReturnedByBank', '%returnedByBank":true%');
//			->setParameter('metaReturnedCode', '%returnedCode":"' . SpendrACHService::RETURN_CODE_INSUFFICIENT_FUNDS . '%');

		if ($start) {
			$query->andWhere('ucl.initializedAt >= :start')
				->setParameter('start', Util::timeUTC($start));
		}
		if ($end) {
			$query->andWhere('ucl.initializedAt <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		if ($type === 'list') {
			return $query->getQuery()
				->getResult();
		} else {
			$returnCount = $query->select('count(distinct ucl)')
				->getQuery()
				->getSingleScalarResult();

			$returnCount += self::calculateOtherWrongReturnedTxn($start, $end);

			return $returnCount ? $returnCount * self::toBankAchReturnFee() : 0;
		}
	}

	// Hard-code other returned transactions, including misoperations, or return transactions caused by bank processing errors
	public static function calculateOtherWrongReturnedTxn(Carbon $start = null, Carbon $end = null, $type = 'count')
	{
		$count = 0;
		$list = [];
		if (Util::isLive()) {
			$otherWrongReturnedTxnDate = self::otherWrongReturnedTxnDate();
			if ($otherWrongReturnedTxnDate) {
				if ($start || $end) {
					/** @var Carbon $date */
					foreach ($otherWrongReturnedTxnDate as $date) {
						if ($start && $end) {
							if (($start >= $date) && ($date <= $end)) {
								$count++;
								$list[] = $date;
							}
						} else if ($start && !$end) {
							if ($start >= $date) {
								$count++;
								$list[] = $date;
							}
						} else if (!$start && $end) {
							if ($date <= $end) {
								$count++;
								$list[] = $date;
							}
						}
					}
				} else {
					$count += count($otherWrongReturnedTxnDate);
					$list = $otherWrongReturnedTxnDate;
				}
			}
		}

		return $type === 'list' ? $list : $count;
	}

	public static function calculateBankMonthlyAccountFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		$start = $start ? Util::timeUTC($start) : null;
		$end = $end ? Util::timeUTC($end) : null;
		$fee = self::calculate(self::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE);

		$systemStartDate = Carbon::parse('2022-02-01');
		$now = Carbon::now();

		$months = $systemStartDate->diffInMonths($now);

		$feeRecords = [];
		if ($months) {
			for ($i = 1; $i < $months + 1; $i++) {
				$feeTime = Carbon::parse($systemStartDate)->addMonthsWithoutOverflow($i);
				$feeRecord = [
					'Transaction' => 'Monthly Account Fee',
					'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT),
					'Transaction ID' => null,
					'Amount Value' => $fee,
					'Amount' => Money::format($fee, 'USD', false),
					'timestamp' => $feeTime->getTimestamp(),
					'time' => $feeTime
				];
				if ($start || $end) {
					if ($start && $end) {
						if (($feeTime >= $start) && ($feeTime <= $end)) {
							$feeRecords[] = $feeRecord;
						}
					} else if ($start && !$end) {
						if ($feeTime >= $start) {
							$feeRecords[] = $feeRecord;
						}
					} else if (!$start && $end) {
						if ($feeTime <= $end) {
							$feeRecords[] = $feeRecord;
						}
					}
				} else {
					$feeRecords[] = $feeRecord;
				}
			}
		}

		if ($type === 'list') {
			return $feeRecords;
		} else {
			return count($feeRecords) * $fee;
		}
	}

	public static function calculateBankWireFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		$fee = self::calculate(self::TO_BANK_SPENDR_WIRE_FEE);

		$expr = Util::expr();
		$query = Util::em()->getRepository(SpendrTransaction::class)
			->createQueryBuilder('st')
			->where('st.date > :date')
			->setParameter('date', Util::timeUTC('2022-02-01'))
			->andWhere($expr->orX(
				$expr->like('st.description', ':descriptionLLC'),
				$expr->like('st.description', ':descriptionINC'),
			))
			->setParameter('descriptionLLC', 'Wire In%' . ImportTransactionsService::DESC_SPENDR_LLC)
			->setParameter('descriptionINC', 'Wire In%' . ImportTransactionsService::DESC_SPENDR_INC)
			->andWhere('st.status = :status')
			->setParameter('status', SpendrTransaction::STATUS_COMPLETED)
			->andWhere('st.credit > 0');

		if ($start) {
			$query->andWhere('st.date >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('st.date <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		if ($type === 'list') {
			return $query->getQuery()
				->getResult();
		} else {
			$count = $query->select('count(distinct st)')
				->getQuery()
				->getSingleScalarResult();

			return $fee * $count;
		}
	}

	public static function calculateBankAchSetupFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		$fee = self::calculate(self::TO_BANK_SPENDR_ACH_SETUP_FEE);

		$query = Util::em()->getRepository(SpendrTransaction::class)
			->createQueryBuilder('st')
			->where('st.date > :date')
			->setParameter('date', Util::timeUTC('2022-02-01'))
			->andWhere('st.description = :description')
			->setParameter('description', ImportTransactionsService::DESC_BANK_SPENDR_ACH_FEE)
			->andWhere('st.meta like :meta')
			->setParameter('meta', '%achSetupFee":true%')
			->andWhere('st.status = :status')
			->setParameter('status', SpendrTransaction::STATUS_COMPLETED)
			->andWhere('st.debit = :debit')
			->setParameter('debit', $fee)
			->setMaxResults(1);

		if ($start) {
			$query->andWhere('st.date >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('st.date <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		if ($type === 'list') {
			return $query->getQuery()
				->getResult();
		} else {
			$count = $query->select('count(distinct st)')
				->getQuery()
				->getSingleScalarResult();

			return $count ? $fee : 0;
		}
	}

	public static function getCurrentBankFees(Carbon $start = null, Carbon $end = null)
	{
		$monthlyAccountFee = self::getBankMonthlyAccountFee($start, $end);
		$returnedLoadFee = self::getBankReturnedLoadFee($start, $end);
		$loadFee = self::getBankLoadFee($start, $end);
		$wireFee = self::getBankWireFee($start, $end);
		$achSetupFee = self::getBankAchSetupFee($start, $end);

		$fee = $loadFee + $returnedLoadFee + $monthlyAccountFee + $wireFee + $achSetupFee;

		return $fee;
	}

	public static function getBankMonthlyAccountFee(Carbon $start = null, Carbon $end = null)
	{
		$amount = self::getBankOrTernFeesFromUFH(FeeService::TO_BANK_SPENDR_MONTHLY_ACCOUNT_FEE, $start, $end);

		return $amount;
	}

	public static function getBankReturnedLoadFee(Carbon $start = null, Carbon $end = null)
	{
		$amount = self::getBankOrTernFeesFromUFH(FeeService::TO_BANK_SPENDR_ACH_RETURN_FEE, $start, $end);

		return $amount;
	}

	public static function getBankLoadFee(Carbon $start = null, Carbon $end = null)
	{
		$amount = self::getBankOrTernFeesFromUFH(FeeService::TO_BANK_CONSUMER_LOAD_FEE, $start, $end);

		return $amount;
	}

	public static function getBankWireFee(Carbon $start = null, Carbon $end = null)
	{
		$amount = self::getBankOrTernFeesFromUFH(FeeService::TO_BANK_SPENDR_WIRE_FEE, $start, $end);

		return $amount;
	}

	public static function getBankAchSetupFee(Carbon $start = null, Carbon $end = null)
	{
		$amount = self::getBankOrTernFeesFromUFH(FeeService::TO_BANK_SPENDR_ACH_SETUP_FEE, $start, $end);

		return $amount;
	}

	public static function getBankOrTernFeesFromUFHQuery($feeNames = [])
	{
		if (!$feeNames) {
			return null;
		}

		$partner = UserService::getSpendrBalanceAdminAccount('spendr');

		return Util::em()->getRepository(UserFeeHistory::class)
			->createQueryBuilder('ufh')
			->where('ufh.user = :partner')
			->setParameter('partner', $partner)
			->andWhere(Util::expr()->in('ufh.feeName', ':feeNames'))
			->setParameter('feeNames', $feeNames)
			->andWhere('ufh.meta like :meta')
			->setParameter('meta', '%"spendrMeta":true%');
	}

	public static function getBankOrTernFeesFromUFH($feeName, Carbon $start = null, Carbon $end = null)
	{
		$query = self::getBankOrTernFeesFromUFHQuery([$feeName]);

		if ($start) {
			$query->andWhere('ufh.time >= :start')
				->setParameter('start', $start);
		}
		if ($end) {
			$query->andWhere('ufh.time <= :end')
				->setParameter('end', $end);
		}

		$amount = $query->select('sum(ufh.amount)')
			->getQuery()
			->getSingleScalarResult();

		return $amount;
	}

	public static function estimatedTernFees(Carbon $start = null, Carbon $end = null)
	{
		$loadFee = self::calculateTernLoadFee($start, $end);
		$txnFee = self::calculateTernTxnFee($start, $end);
		$withdrawFee = self::calculateTernWithdrawFee($start, $end);
		$onFileFee = self::calculateTernMonthlyOnFileFee($start, $end);

		$fee = $loadFee + $txnFee + $withdrawFee + $onFileFee;

		return $fee;
	}

	public static function calculateTernLoadFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		if ($type !== 'amount' && $type !== 'list') {
			return null;
		}

		$fee = self::calculate(self::TO_TERN_CONSUMER_LOAD_FEE);
		if (!$fee) {
			return null;
		}

		$expr = Util::expr();

		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->leftJoin('CoreBundle:AchTransactions', 'acht', Join::WITH, 'ucl.transactionNo = acht.tranId')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', [
				Role::ROLE_SPENDR_CONSUMER,
				Role::ROLE_SPENDR_PROGRAM_OWNER
			])
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere('ucl.type = :type')
			->setParameter('type', UserCardLoad::TYPE_LOAD_CARD)
			->andWhere($expr->in('ucl.loadType', ':loadTypes'))
			->setParameter('loadTypes', [
                LoadService::LOAD_TYPE_INSTANT,
                LoadService::LOAD_TYPE_PREFUND
            ])
			->andWhere($expr->notIn('acht.tranStatus', ':tranStatuses'))
			->setParameter('tranStatuses', [
				UserCardTransaction::STATUS_LL_CANCELED,
			]);

		if ($start) {
			$query->andWhere('ucl.initializedAt >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('ucl.initializedAt <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		if ($type === 'amount') {
			$count = $query->select('count(distinct ucl)')
				->getQuery()
				->getSingleScalarResult();

			$fee = self::calculate(self::TO_TERN_CONSUMER_LOAD_FEE);
			return $count * $fee;
		} else {
			return $query->getQuery()
				->getResult();
		}
	}

	public static function calculateTernTxnFee(
		Carbon $start = null,
		Carbon $end = null,
		SpendrMerchant $merchant = null,
		$type = 'amount'
	)
	{
		if ($type !== 'amount' && $type !== 'list') {
			return null;
		}

		$fee = self::calculate(self::TO_TERN_TRANSACTION_FEE);
		if (!$fee) {
			return null;
		}

		$expr = Util::expr();
		$query = Util::em()->getRepository(Transaction::class)
			->createQueryBuilder('t')
			->join('t.merchant', 'm')
			->join('m.adminUser', 'u')
			->join('u.teams', 'r')
			->where('r.name = :role')
			->setParameter('role', Role::ROLE_SPENDR_MERCHANT_ADMIN)
			->andWhere('t.status = :status')
			->setParameter('status', TransactionStatus::get(TransactionStatus::NAME_COMPLETED))
			->andWhere($expr->in('t.type', ':types'))
			->setParameter('types', TransactionType::gets([
				TransactionType::NAME_PURCHASE,
				TransactionType::NAME_CHECKOUT_ONLINE,
				TransactionType::NAME_REFUND,
                TransactionType::NAME_ONLINE_REFUND
			]));

		$testMerchantIds = MerchantService::merchantIdsForTestArray();
		if ($testMerchantIds) {
			$query->andWhere($expr->notIn('m.id', ':testMerchantIds'))
				->setParameter('testMerchantIds', $testMerchantIds);
		}

		if ($merchant) {
			$query->andWhere('t.merchant = :merchant')
				->setParameter('merchant', $merchant);
		}

		if ($start) {
			$query->andWhere('t.txnTime >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('t.txnTime <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		if ($type === 'amount') {
			$count = $query->select('count(distinct t)')
				->getQuery()
				->getSingleScalarResult();

			return $count * $fee;
		} else {
			return $query->getQuery()
				->getResult();
		}
	}

	public static function calculateTernWithdrawFee(
		Carbon $start = null,
		Carbon $end = null,
		$type = 'amount',
		$role = null
	)
	{
		if ($type !== 'amount' && $type !== 'list') {
			return null;
		}

		$fee = self::calculate(self::TO_TERN_MERCHANT_WITHDRAWAL_FEE);
		if (!$fee) {
			return null;
		}

		if ($role === 'merchant') {
			$roles = SpendrBundle::getMerchantBalanceAdminRoles();
		} else if ($role === 'partner') {
			$roles = [Role::ROLE_SPENDR_PROGRAM_OWNER];
		} else {
			$roles = array_merge(
				SpendrBundle::getMerchantBalanceAdminRoles(),
				[Role::ROLE_SPENDR_PROGRAM_OWNER]
			);
		}

		$expr = Util::expr();
		$query = Util::em()->getRepository(UserCardLoad::class)
			->createQueryBuilder('ucl')
			->join('ucl.userCard', 'uc')
			->join('uc.user', 'u')
			->join('u.teams', 't')
			->join('uc.card', 'c')
			->join('c.cardProgram', 'cp')
			->leftJoin('CoreBundle:AchTransactions', 'acht', Join::WITH, 'ucl.transactionNo = acht.tranId')
			->where($expr->in('t.name', ':roles'))
			->setParameter('roles', $roles)
			->andWhere($expr->eq('cp.name',':name'))
			->setParameter('name', CardProgram::NAME_SPENDR)
			->andWhere('ucl.type = :type')
			->setParameter('type', UserCardLoad::TYPE_UNLOAD)
			->andWhere($expr->notIn('acht.tranStatus', ':tranStatuses'))
			->setParameter('tranStatuses', [
				UserCardTransaction::STATUS_LL_CANCELED,
			]);

		if ($start) {
			$query->andWhere('ucl.initializedAt >= :start')
				->setParameter('start', Util::timeUTC($start));
		}

		if ($end) {
			$query->andWhere('ucl.initializedAt <= :end')
				->setParameter('end', Util::timeUTC($end));
		}

		if ($type === 'amount') {
			$count = $query->select('count(distinct ucl)')
				->getQuery()
				->getSingleScalarResult();

			return $count * $fee;
		} else {
			return $query->getQuery()
				->getResult();
		}
	}

	public static function calculateTernMonthlyOnFileFee(Carbon $start = null, Carbon $end = null, $type = 'amount')
	{
		if ($type !== 'amount' && $type !== 'list') {
			return null;
		}

		$merchants = MerchantService::getCurrentActiveMerchants();
		if (!$merchants) {
			return null;
		}
		$fee = self::calculate(self::TO_TERN_MERCHANT_MONTHLY_ON_FILE_FEE);
		if (!$fee) {
			return null;
		}

		$feeRecords = [];
		/** @var SpendrMerchant $merchant */
		foreach ($merchants as $merchant) {
			$time = Carbon::now();
			$bankApprovedAt = Util::meta($merchant, 'bankApprovedAt');
			$bankApprovedAt = Carbon::parse($bankApprovedAt);

//			if ($date) {
//				$date = $date->endOfMonth();
//				if ($bankApprovedAt > $date) {
//					continue;
//				} else {
//					$time = $date;
//				}
//				$months = $time->diffInMonths($bankApprovedAt);
//				if ($months < 1) {
//					continue;
//				} else {
//					$months = 1;
//				}
//			} else {
				$months = $time->diffInMonths($bankApprovedAt);
//			}

			if ($months) {
				for ($i = 1; $i < $months + 1; $i++) {
					$feeTime = Carbon::parse($bankApprovedAt)->addMonthsWithoutOverflow($i);
					$feeRecord = [
						'Transaction' => 'Monthly On File Fee',
						'Date & Time' => Util::formatDateTime($feeTime, Util::DATE_TIME_FORMAT, Util::timezone()),
						'Transaction ID' => $merchant->getId(),
						'Amount Value' => $fee,
						'Amount' => Money::format($fee, 'USD', false),
						'timestamp' => $feeTime->getTimestamp(),
						'time' => $feeTime
					];
					if ($start || $end) {
						if ($start && $end) {
							if (($feeTime >= $start) && ($feeTime <= $end)) {
								$feeRecords[] = $feeRecord;
							}
						} else if ($start && !$end) {
							if ($feeTime >= $start) {
								$feeRecords[] = $feeRecord;
							}
						} else if (!$start && $end) {
							if ($feeTime <= $end) {
								$feeRecords[] = $feeRecord;
							}
						}
					} else {
						$feeRecords[] = $feeRecord;
					}
				}
			}
		}

		if ($type === 'amount') {
			return count($feeRecords) * $fee;
		} else {
			return $feeRecords;
		}
	}

	// todo
	public static function calculateTernFeeByLoad() {
		return self::calculate(self::TO_TERN_CONSUMER_LOAD_FEE);
	}

//	public static function chargeConsumerInactivityFee(UserCard $dummy, User $user, $fee)
//	{
//		$feeName = FeeService::TO_SPENDR_CONSUMER_MONTHLY_INACTIVITY_FEE;
//		$comment = sprintf(
//			'Charge %s on user %s at %s. Amount: %s',
//			strtolower($feeName),
//			$user->getSignature(),
//			Carbon::now(),
//			Money::format($fee, 'USD')
//		);
//		try {
//			$realFee = -$dummy->updatePrivacyBalanceBy(-$fee, $feeName, $comment, false, null);
//			UserFeeHistory::create($user, $realFee, $feeName, null, $comment, Platform::spendr());
//
//			// pay to spendr
//			$spendrUserCard = UserService::getSpendrBalanceDummyCard('spendr');
//			if ($spendrUserCard) {
//				$spendrUserCard->updatePrivacyBalanceBy($realFee, $feeName, $comment, false, null);
//			}
//
//			Log::debug('Charge ' . strtolower($feeName) . ' on user ' . $user->getId() . ' for '. Money::format($fee, 'USD'));
//		} catch (\Exception $e) {
//			SlackService::exception('Failed to charge the  ' . strtolower($feeName), $e, [
//				'user' => $user->getId(),
//				'amount' => $fee,
//			]);
//		}
//	}
}
