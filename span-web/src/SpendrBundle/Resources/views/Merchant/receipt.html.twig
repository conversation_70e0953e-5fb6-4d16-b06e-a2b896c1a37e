{% if target != 'consumer' %}
    <p>Below is the receipt that can be sent to the consumer <strong>{{ txn.consumer.name | default }} ({{ txn.consumer.email | default }})</strong>:</p>
    <hr>
{% endif %}

{% if isRefund %}
    <p>Here are the details of your refund.</p>
{% else %}
    <p>Thank you for the payment! Below is the transaction detail.</p>
{% endif %}

<style>
  .receipt-table {
    width: 100%;
    border: 1px solid #ddd;
    border-collapse: collapse;
    max-width: 700px;
    margin: 0 auto;
  }

  .receipt-table th, .receipt-table td {
    padding: 6px 12px;
    border: 1px solid #ddd;
  }

  .receipt-table th {
    text-align: right;
    background-color: #f6f6f6;
  }
</style>

<table class="receipt-table">
    <tr>
        <th>Transaction ID</th><td>{{ txn.orderId ?? txn.id }}</td>
    </tr>
    <tr>
        <th>Merchant</th><td>{{ txn.merchant.name | default }}</td>
    </tr>
    <tr>
        <th>Location</th><td>{{ txn.location.name | default }}</td>
    </tr>
    {% if isOnline == false %}
        <tr>
            <th>Terminal</th><td>{{ txn.terminal.name | default }}</td>
        </tr>
        <tr>
            <th>Clerk</th><td>{{ txn.locationEmployee.name | default }}</td>
        </tr>
    {% endif %}
    <tr>
        <th>Date time</th><td>{{ dateTime }}</td>
    </tr>
    {% if txn.tip %}
        <tr>
            <th>Subtotal</th><td>{{ txn.amount | money_format('USD') }}</td>
        </tr>
        <tr>
            <th>Tip Amount</th><td>{{ txn.tip.amount | money_format('USD') }}</td>
        </tr>
        <tr>
            <th>Total amount</th><td>{{ (txn.amount + txn.tip.amount) | money_format('USD') }}</td>
        </tr>
    {% else %}
        <tr>
            <th>Total amount</th><td>{{ txn.amount | money_format('USD') }}</td>
        </tr>
    {% endif %}
    {% if showBankFundsAndRewardFunds %}
        {% if bankFunds > 0 %}
            <tr>
                <th>Bank Funds</th><td>{{ bankFunds | money_format('USD') }}</td>
            </tr>
        {% endif %}
        {% if rewardFunds > 0 %}
            <tr>
                <th>Reward Funds</th><td>{{ rewardFunds | money_format('USD') }}</td>
            </tr>
        {% endif %}
    {% endif %}
</table>
<br>
{% if disclaimer %}
<div style="font-size: 10px !important; margin-bottom: 15px; font-style: italic; color: #74787E">{{ disclaimer }}</div>
{% endif %}
