{% extends 'layout/base-layout.html.twig' %}
{% block page_title %} Role Setting {% endblock %}


{% block page_content %}
    <div class="opera-right span3 pull-right">
        <a href="{{ path('admin_role') }}?prefix={{ prefix | url_encode }}" class="btn btn-default">Back</a>
    </div>
    <div class="row-fluid">
        <h3>Notes:</h3>
        <ul>
            <li>Module with "Edit" permission will always have "View" permission.</li>
            <li>Changes will be auto saved.</li>
        </ul>

        {% set readonlyModules = ['analytic_management',
            'user_managment__user__login_at', 'user_managment__user__add_user', 'id_document',
            'set_up_wizard', 'load_transaction_list', 'card_transaction_list',
            'card_decline_list', 'keymetrics_report', 'affiliate_report',
            'keymetrics_report', 'card_activity_report', 'email_history',
            'external_invoke', 'active_sessions', ] %}

        <div id="table-head-wrapper">
            <table class="table table-striped table-bordered">
                <thead>
                <tr>
                    <th style="width:200px">
                        <div style="width:118px;">Module</div>
                    </th>
                    {% for items in roleitem %}
                        <th style="min-width:80px">{{ items.name }}</th>
                    {% endfor %}
                </tr>
                </thead>
            </table>
        </div>
        <div id="table-body-wrapper">
            <table class="table table-striped table-bordered">
                <thead>
                <tr>
                    <th style="width:200px">Module</th>
                     {% for items in roleitem %}
                        <th style="min-width:80px">{{ items.name }}</th>
                     {% endfor %}
                </tr>
                </thead>
                <tbody>
                <input type="hidden" value="{{ menuids }}" id="menuids">
                {% for item in menu %}
                    {% if item.route or item.hasChildren %}
                        <tr>
                            <td>{{ item.identifier starts with 'clf_' ? 'UTC' : '' }} {{ item.label }}</td>
                            {% for itemss in roleitem %}
                                <td></td>
                            {% endfor %}
                        </tr>
                        {% if item.hasChildren %}
                            {% for child in item.children %}
                                <tr>
                                    <td class="pl-40">{{ child.label }}</td>
                                    {% for itemss in roleitem %}
                                        <td>
                                            <label for="{{ child.identifier }}{{ itemss.id }}">
                                                <input type="checkbox" id="{{ child.identifier }}{{ itemss.id }}"
                                                       value="{{ child.identifier }}@{{ itemss.id }}"
                                                       onclick="checkboxselect('{{ child.identifier }}@{{ itemss.id }}')"/>
                                                <span class="normal">
                                                    {% if child.identifier == 'set_up_wizard' %}
                                                        Edit
                                                    {% else %}
                                                        View
                                                    {% endif %}
                                                </span>
                                            </label>
                                            {% if child.identifier not in readonlyModules %}
                                                <br/><label for="{{ child.identifier }}{{ itemss.id }}_edit">
                                                    <input type="checkbox" id="{{ child.identifier }}{{ itemss.id }}_edit"
                                                           value="{{ child.identifier }}"
                                                           onclick="saveEditable('{{ child.identifier }}', {{ itemss.id }}, this.checked)"/>
                                                    <span class="normal">Edit</span>
                                                </label>
                                            {% endif %}
                                        </td>
                                    {% endfor %}
                                </tr>
                                {% for cc in child.children %}
                                    <tr>
                                        <td class="pl-60">
                                            {% if cc.label == 'Login at' %}
                                                Login as
                                            {% else %}
                                                {{ cc.label }}
                                            {% endif %}
                                            {% if cc.description is defined and cc.description %}
                                                <a href="javascript:" class="hint--top" aria-label="{{ cc.description }}">
                                                    <i class="fa fa-info-circle"></i></a>
                                            {% endif %}
                                        </td>
                                        {% for itemss in roleitem %}
                                            <td>
                                                <label for="{{ cc.identifier }}{{ itemss.id }}">
                                                    <input type="checkbox" id="{{ cc.identifier }}{{ itemss.id }}"
                                                           value="{{ cc.identifier }}@{{ itemss.id }}"
                                                           onclick="checkboxselect('{{ cc.identifier }}@{{ itemss.id }}')"/>
                                                    <span class="normal">View</span>
                                                </label>
                                                {% if cc.identifier not in readonlyModules %}
                                                    <br/><label for="{{ cc.identifier }}{{ itemss.id }}_edit">
                                                        <input type="checkbox" id="{{ cc.identifier }}{{ itemss.id }}_edit"
                                                               value="{{ cc.identifier }}"
                                                               onclick="saveEditable('{{ cc.identifier }}', {{ itemss.id }}, this.checked)"/>
                                                        <span class="normal">Edit</span>
                                                    </label>
                                                {% endif %}
                                            </td>
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td class="pl-40">{{ item.label }}</td>
                                {% for itemss in roleitem %}
                                    <td>
                                        <label for="{{ item.identifier }}{{ itemss.id }}">
                                            <input type="checkbox" id="{{ item.identifier }}{{ itemss.id }}"
                                                   value="{{ item.identifier }}@{{ itemss.id }}"
                                                   onclick="checkboxselect('{{ item.identifier }}@{{ itemss.id }}')"/>
                                            <span class="normal">View</span>
                                        </label>
                                        {% if item.identifier not in readonlyModules %}
                                            <br/><label for="{{ item.identifier }}{{ itemss.id }}_edit">
                                                <input type="checkbox" id="{{ item.identifier }}{{ itemss.id }}_edit"
                                                       value="{{ item.identifier }}"
                                                       onclick="saveEditable('{{ item.identifier }}', {{ itemss.id }}, this.checked)"/>
                                                <span class="normal">Edit</span>
                                            </label>
                                        {% endif %}
                                    </td>
                                {% endfor %}
                            </tr>
                        {% endif %}
                    {% endif %}
                {% endfor %}
                </tbody>
            </table>
        </div>

    </div>
    <!--/row-->
{% endblock %}
{% block javascripts_inline %}
<script>
    window.mainTableYOffset = 101;

    function saveEditable (module, role, editable) {
      $.post('/admin/role/update-editable', {
        module: module,
        role: role,
        editable: editable
      })
    }

    $(function () {
        var menuidsvalue=$('#menuids').val().split(";")
        $('input:checkbox').each(
                function () {
                    if($.inArray($(this).val(),menuidsvalue)>-1)
                    {
                        $(this).attr("checked","checked");
                    }
                }
        )

      var editable = {{ editable | json_encode | raw }};
      _.forEach(editable, function (v, k) {
        _.forEach(v, function (m) {
          $('#' + m + k + '_edit').attr('checked', 'checked');
        })
      })
    })
function checkboxselect(cid) {

    var cids=cid.split("@")
    var checked = $('#'+cids[0]+cids[1]).prop("checked");
    $.ajax({
        type: "get",
        cache: false,
        contentType: "application/json; charset=utf-8",
        url: "{{ path('admin_role_rolesettingajax')}}",
        data: {'menu_id': cids[0],'role_id':cids[1],'checked':checked},
        success: function(data) {

        }
    });
}
</script>
{% endblock %}