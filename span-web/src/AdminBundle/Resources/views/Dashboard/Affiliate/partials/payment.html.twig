<div class="tab-pane fade ph-15" id="payment_history">
    <h2>Payout History</h2>

    {% set affiliate = user.managingAffiliate %}
    {% set tenant = user.affiliateTenant %}

    <div id="main-box">
        <div class="form-advanced-search"></div>
        {% include '@Admin/Dashboard/Affiliate/partials/paymentList.html.twig' %}
    </div>

    <hr class="dark">

    <div class="form" id="payment_method_form">
        <h4 class="bold">Payout Method</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="form-group">
                    <label>Preferred Payout Method</label>
                    <select name="payment_method" id="payment_method_select" class="form-control"
                            onchange="onChangePaymentMethod()"
                            data-vv="{{ paymentMethod ? paymentMethod.type : '' }}">
                        <option value="">- Choose -</option>
                        <option value="contact">US Unlocked Card</option>
                        {#<option value="payoneer">Payoneer</option>#}
                        <option value="custom">Manually</option>
                    </select>
                </div>
                <div class="form-group hide" data-type="contact">
                    <label>US Unlocked Card Detail</label>
                    {% set hasActiveCard = false %}
                    <select class="form-control" id="payment_method_detail_contact">
                        {% for c in affiliate.contacts %}
                            <option value="{{ c.id }}" {% if not c.activeCard %}disabled{% endif %}>
                                {{ c.name }} ({{ c.id }})
                                -
                                {% if c.activeCard %}
                                    {{ c.activeCard.name }}
                                    {% set hasActiveCard = true %}
                                {% else %}
                                    No card
                                {% endif %}
                                {% if c.main %}
                                    (Main)
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>
                    {% if not hasActiveCard %}
                        <div class="callout callout-warning mt-5">
                            <i class="fa fa-warning mr-5"></i> You don't have an issued active card.
                            <a href="/choose-card" class="ml-5">Sign up now!</a>
                        </div>
                    {% endif %}
                </div>
                <div class="form-group hide" data-type="custom">
                    <label>Manual payout method</label>
                    <input type="text" class="form-control" id="payment_method_detail_custom"
                           placeholder="How do you prefer the commission be paid to you?"
                           value="{{ paymentMethod ? paymentMethod.detail : '' }}"/>
                </div>
            </div>
        </div>

        {% if editable(Module.ID_DASHBOARD) %}
            <div class="form-group mt-20">
                <a href="javascript:;" class="btn btn-primary" onclick="savePreferredPaymentMethod()">Save</a>
            </div>
        {% endif %}
    </div>
</div>

<style>
    hr.dark {
        border-top: 1px solid #d0d0d0;
        margin: 30px 0;
    }
</style>

<script>
  function reload (url) {
    commonSimpleAdvancedLoad(url || '/admin/dashboard/affiliate/payments');
  }

  function onChangePaymentMethod () {
    var $select = $('#payment_method_select');
    var $form = $('#payment_method_form');
    var type = $select.val();
    $form.find('.form-group[data-type]').addClass('hide');
    $form.find('.form-group[data-type="' + type + '"]').removeClass('hide');
  }

  {% if editable(Module.ID_DASHBOARD) %}
      function savePreferredPaymentMethod () {
        var $select = $('#payment_method_select');
        var type = $select.val();
        $.post('/admin/dashboard/affiliate/save-payment-method', {
          type: type,
          detail: $('#payment_method_detail_' + type).val()
        }, function () {
          ts.success();
        });
      }
  {% else %}
      $(function () {
        $('#payment_method_form').find('input, select, textarea').attr('disabled', 'disabled');
      })
  {% endif %}

  $(function () {
    setTimeout(onChangePaymentMethod, 1000);
  });
</script>