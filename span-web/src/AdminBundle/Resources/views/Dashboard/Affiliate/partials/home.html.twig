<div class="tab-pane fade ph-15" id="affiliate_home">
    <h2>Affiliate Home</h2>

    {% set affiliate = user.managingAffiliate %}
    {% set tenant = user.affiliateTenant %}

    {% if affiliate %}
        <div class="panel panel-default">
            <div class="panel-body text-center">
                <h3 class="mt-5">Your affiliate URL:</h3>
                <div>
                    <a href="{{ host() }}/choose-card?aff_id={{ affiliate.affId }}" class="font-18" target="_blank">
                        {{ host() }}/choose-card?aff_id={{ affiliate.affId }}</a>

                    {% if editable(Module.ID_DASHBOARD) %}
                        <a href="javascript:" class="ml-10" onclick="$('#edit-aff-id-dialog').modal('show')">
                            <i class="fa fa-pencil font-18"></i>
                        </a>
                    {% endif %}
                </div>
                {% if host() == 'https://account.usunlocked.com' or host() == 'http://usu.span.hans' or host() == 'http://usu.span.local' %}
                    <div style="margin-top: 13px; color: #666; border-top: 1px dotted #eee; padding: 13px 10% 0;">
                        It works too if you add the parameter <code>aff_id</code> to the domain <code>https://www.usunlocked.com</code>.
                        For instance: <a href="https://www.usunlocked.com/service?aff_id={{ affiliate.affId }}" target="_blank">https://www.usunlocked.com/service?aff_id={{ affiliate.affId }}</a>
                    </div>
                {% endif %}
            </div>
        </div>

        <h4 class="bold">Commissions</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <p>As a <strong>{{ affiliate.name }}</strong> affiliate, you earn commissions for any accounts
                    registered via the link below who have loaded their card.</p>
                <p class="bold">
                    You currently have unpaid commissions of
                    <span class="bold green">{{ unpaid | default('0') | money_format('USD') }}</span>
                    {% if editable(Module.ID_DASHBOARD) %}
                        {% if unpaid %}
                            <a href="javascript:" onclick="requestPayment()"
                               class="btn btn-primary btn-sm ml-30">Request Payment</a>
                        {% endif %}
                    {% endif %}
                </p>
                <hr>
                <h5><strong>Affiliate revenue share details:</strong></h5>
                <table class="table table-condensed table-bordered table-striped table-hover">
                    <thead>
                    <tr>
                        <th>Type</th>
                        <th>Currency</th>
                        <th>Flat</th>
                        <th>%</th>
                        <th>
                            Start
                            <div class="description">Starts after how many cards have been created</div>
                        </th>
                    </tr>
                    </thead>
                    <tbody>

                    {% set shares = affiliate.revenueSharesKeyedByType %}
                    <tr>
                        <td>Membership Fee</td>
                        <td>{{ shares.membership_fee.currency | default }}</td>
                        <td>{{ shares.membership_fee.fixed | default(null) | money_format(shares.membership_fee.currency | default('USD')) }}</td>
                        <td>{{ shares.membership_fee.ratio | default }}</td>
                        <td>{{ shares.membership_fee.start | default }}</td>
                    </tr>
                    <tr>
                        <td>Load Amount</td>
                        <td>{{ shares.load_amount.currency | default }}</td>
                        <td>{{ shares.load_amount.fixed | default(null) | money_format(shares.load_amount.currency | default('USD')) }}</td>
                        <td>{{ shares.load_amount.ratio | default }}</td>
                        <td>{{ shares.load_amount.start | default }}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <h4 class="bold">Domains</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <p>As a <strong>{{ affiliate.name }}</strong> affiliate, you also earn commissions for anyone who comes to our site via one
                    of your registered domains, creates a new account, and subsequently ships a package with us.</p>
                <p class="bold">Your registered domains:</p>
                <ul>
                    {% for domain in domains %}
                        <li>
                            <a href="{{ domain }}" target="_blank">
                                {{ domain }}
                                <i class="fa fa-external-link"></i>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <h4 class="bold">Preference</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <form class="form" id="pref-form" method="post">
                    <div class="form-group">
                        <label>Default language:</label>
                        {% if editable(Module.ID_DASHBOARD) %}
                            <select name="language" class="form-control" data-vv="{{ affiliate.language | default('en') }}">
                                {% for ln in languages %}
                                    <option value="{{ ln.code }}">{{ ln.name }}</option>
                                {% endfor %}
                            </select>
                        {% else %}
                            <span class="label label-primary">{{ affiliate.language | default('en') }}</span>
                        {% endif %}
                        <div class="help-block">This will be the default language in the consumer portal, and also
                            in the emails will be sent to consumers.</div>
                    </div>
                    {% if editable(Module.ID_DASHBOARD) %}
                    <div class="mt-10">
                        <a href="javascript:;" class="btn btn-primary" onclick="savePreference()">Save</a>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
    {% else %}
        <div class="callout callout-warning">
            <p>You are not bind to an affiliate yet. Please contact administrator.</p>
        </div>
    {% endif %}

    <hr class="dark">

    <form class="form" id="main-form" method="post">
        {% include '@Admin/Dashboard/Affiliate/partials/detail.html.twig' %}

        {% if editable(Module.ID_DASHBOARD) %}
        <div class="form-group mt-20">
            <a href="javascript:;" class="btn btn-primary" onclick="saveAffiliateHome()">Save</a>
        </div>
        {% endif %}
    </form>

    <div class="modal fade" id="edit-aff-id-dialog" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Edit Affiliate ID
                </div>
                <div class="modal-body">
                    <form  method="post" id="add-text-form">
                        <input type="text" name="aff_id" class="form-control" value="{{ affiliate.affId }}" />
                    </form>

                    <p class="mt-15">NOTE: Remember to update the links in your website after changing the affiliate ID. The old link will be invalid (won't be tracking).</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="editAffId()">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    hr.dark {
        border-top: 1px solid #d0d0d0;
        margin: 30px 0;
    }

    #affiliate_home label {
        font-weight: normal;
    }
</style>

<script>
    {% if editable(Module.ID_DASHBOARD) %}
        function saveAffiliateHome () {
          const data = $('#main-form').serializeArray();
          ts.modal();
          $.post('/admin/dashboard/affiliate/save-contact', data, function (resp) {
            if (resp.success) {
              ts.success();
            } else {
              ts.msg(resp.message);
            }
          });
        }

        function savePreference () {
          const data = $('#pref-form').serializeArray();
          ts.modal();
          $.post('/admin/dashboard/affiliate/save-preference', data, function (resp) {
            if (resp.success) {
              ts.success();
            } else {
              ts.msg(resp.message);
            }
          });
        }

        function requestPayment () {
          ts.confirm('Are you sure that you want to request payment?', function () {
            $.post('/admin/dashboard/affiliate/request-payment', function (resp) {
              if (resp.success) {
                ts.success(undefined, function () {
                  location.reload();
                });
              } else {
                ts.msg(resp.message);
              }
            });
          });
        }

        function editAffId () {
          var affId = $('#edit-aff-id-dialog input[name=aff_id]').val().trim();
          if (!affId) {
            return ts.error('Please enter the Affiliate ID!');
          }
          ts.modal();
          $.post('/admin/dashboard/affiliate/edit-aff-id', {
            aff_id: affId
          }, function (resp) {
            if (resp.success) {
              ts.success(undefined, function () {
                location.reload();
              });
            } else {
              ts.msg(resp.message);
            }
          });
        }
    {% else %}
        $(function () {
          $('#main-form').find('input, select, textarea').attr('disabled', 'disabled');
        });
    {% endif %}
</script>
