{% extends 'layout/base-layout.html.twig' %}
{% block page_title %}Affiliates{% endblock %}


{% block page_content %}
    <div class="row-fluid">
        {% include '@Admin/Affiliate/Default/search.html.twig' %}

        <div id="main-box">
            {% include '@Admin/Affiliate/Default/list.html.twig' %}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteAffiliateConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteAffiliateConfirmModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteAffiliateConfirmModalLabel">{{ 'affiliate.title.delete'|trans }}</h4>
                </div>
                <div class="modal-body">
                    {{ 'affiliate.msg.delete'|trans }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                    <button type="button" class="btn btn-danger confirm-button">{{ 'btn.confirm'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Share Modal -->
    <div class="modal fade" id="revenueAffiliateConfirmModal" tabindex="-1" role="dialog" aria-labelledby="revenueAffiliateConfirmModalLabel">
        <div class="modal-dialog" role="document" style="width:1000px;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="revenueAffiliateConfirmModalLabel">{{ 'affiliate.title.revenueshare'|trans }}</h4>
                </div>
                <div class="modal-body">
                    <div class="panel panel-default">
                        <div class="panel-heading">{{ 'affiliate.title.revenueshare'|trans }}</div>
                        <table class="table" id="revenueShareTable">
                            <thead>
                            <tr class="tr-vt">
                                <th>{{ 'affiliate.column.revenue.type'|trans }}</th>
                                <th>{{ 'affiliate.column.revenue.currency'|trans }}</th>
                                <th>{{ 'affiliate.column.revenue.fixed'|trans }}</th>
                                <th class="w-100">{{ 'affiliate.column.revenue.ratio'|trans }}</th>
                                <th>Cost</th>
                                <th>
                                    Start
                                    <div class="description">Starts after how many cards have been created</div>
                                </th>
                                <th>{{ 'column.action'|trans }}</th>
                            </tr>
                            </thead>
                            <tbody id="revenueShareList">
                            <tr>
                                <td class="pt-14">Membership Fee</td>
                                <input type="hidden" id="mf_id">
                                <input type="hidden" id="mf_aff_id">
                                <td>
                                    <select id="mf_currency_select" class="form-control">
                                        <option value="USD">USD</option>
                                        {% for code in allCurrencyCodes %}
                                            <option value="{{ code }}">{{ code }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td><input type="text" id="mf_fixed" class="form-control" style="width: 100px"></td>
                                <td><input type="text" id="mf_ratio" class="form-control ib w-50"> %</td>
                                <td><input type="text" id="mf_cost" class="form-control" style="width: 100px"></td>
                                <td><input type="number" id="mf_start" class="form-control w-100"></td>
                                <td>
                                    <div>
                                        <a href="#" onclick="saveRevenueInfo('mf');" class="btn btn-primary">
                                            Save for future transactions</a>
                                    </div>
                                    <div class="mt-5">
                                        <a href="#" onclick="saveRevenueInfo('mf', true);" class="btn btn-danger">
                                            Save and update transactions since </a>
                                        <input class="w-150 form-control datepicker va-m mt-5 block" id="mf_since">
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="pt-14">Load Amount</td>
                                <input type="hidden" id="la_id">
                                <input type="hidden" id="la_aff_id">
                                <td>
                                    <select id="la_currency_select" class="form-control">
                                        <option value="USD">USD</option>
                                        {% for code in allCurrencyCodes %}
                                            <option value="{{ code }}">{{ code }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td><input type="text" id="la_fixed" class="form-control" style="width: 100px"></td>
                                <td><input type="text" id="la_ratio" class="form-control ib w-50"> %</td>
                                <td><input type="text" id="la_cost" class="form-control" style="width: 100px"></td>
                                <td><input type="number" id="la_start" class="form-control w-100"></td>
                                <td>
                                    <div>
                                        <a href="#" onclick="saveRevenueInfo('la');" class="btn btn-primary">
                                            Save for future transactions</a>
                                    </div>
                                    <div class="mt-5">
                                        <a href="#" onclick="saveRevenueInfo('la', true);" class="btn btn-danger">
                                            Save and update transactions since </a>
                                        <input class="w-150 form-control datepicker va-m mt-5 block" id="la_since">
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'btn.cancel'|trans }}</button>
                </div>
            </div>
        </div>
    </div>

    <!--/row-->
{% endblock %}

{% block javascripts_inline %}
    <script src="/node_modules/clipboard/dist/clipboard.min.js"></script>
    <script>
        $(function () {
            var clipboard = new Clipboard('.clipboard-btn');
            clipboard.on('success', function (e) {
                ts.success('Successfully copied to clipboard: ' + e.text);
            });
        });

        // triggered when delete confirm modal is about to be shown
        $("#deleteAffiliateConfirmModal").on("show.bs.modal", function(e) {
            // Pass form reference to modal
            var form = $(e.relatedTarget).siblings('form');
            $(this).find(".confirm-button").data('form', form);
        });

        // action on confirm
        $("#deleteAffiliateConfirmModal .confirm-button").on("click", function(){
            $(this).data('form').submit();
        });

        $("#affiliateTable").DataTable({
            "paging": false,
            "lengthChange": false,
            "searching": false,
            "ordering": true,
            "info": true,
            "autoWidth": false
        });

        var $modal = $("#revenueAffiliateConfirmModal");

        //Clear data when close
        $modal.on("hide.bs.modal", function(e) {
            if ($(e.target).is('input.datepicker')) {
                return;
            }
            $("#mf_id").val('');
            $("#mf_fixed").val('');
            $("#mf_ratio").val('');
            $("#mf_since").val('');
            $("#mf_cost").val('');
            $("#mf_start").val('');
            $("#mf_currency_select option:first").prop("selected", 'selected');

            $("#la_id").val('');
            $("#la_fixed").val('');
            $("#la_ratio").val('');
            $("#la_since").val('');
            $("#la_cost").val('');
            $("#la_start").val('');
            $("#la_currency_select option:first").prop("selected", 'selected');
        });

        $modal.on("show.bs.modal", function(e) {
            var affId = $(e.relatedTarget).data('id');
            if (!affId) {
                return;
            }
            $("#mf_aff_id").val(affId);
            $("#la_aff_id").val(affId);
            $.post("{{ path('admin_get_affiliate_revenue_share') }}", {affiliate_id:affId}, function(data){
                if (data.hasOwnProperty('membership_fee')) {
                    var mfInfo = data['membership_fee'];
                    $("#mf_id").val(mfInfo['id']);
                    $("#mf_fixed").val(mfInfo['fixed']);
                    $("#mf_ratio").val(mfInfo['ratio']);
                    $("#mf_cost").val(mfInfo['cost']);
                    $("#mf_start").val(mfInfo['start']);
                    if(mfInfo['currency'] != null){
                        $("#mf_currency_select").val(mfInfo['currency']);
                    }else {
                        $("#mf_currency_select option:first").prop("selected", 'selected');
                    }
                }else {
                    $("#mf_id").val('');
                    $("#mf_fixed").val('');
                    $("#mf_ratio").val('');
                    $("#mf_cost").val('');
                    $("#mf_start").val('');
                    $("#mf_currency_select option:first").prop("selected", 'selected');
                }
                if (data.hasOwnProperty('load_amount')) {
                    var laInfo = data['load_amount'];
                    $("#la_id").val(laInfo['id']);
                    $("#la_fixed").val(laInfo['fixed']);
                    $("#la_ratio").val(laInfo['ratio']);
                    $("#la_cost").val(laInfo['cost']);
                    $("#la_start").val(laInfo['start']);
                    if(laInfo['currency'] != null){
                        $("#la_currency_select").val(laInfo['currency']);
                    }else {
                        $("#la_currency_select option:first").prop("selected", 'selected');
                    }

                }else {
                    $("#la_id").val('');
                    $("#la_fixed").val('');
                    $("#la_ratio").val('');
                    $("#la_cost").val('');
                    $("#la_start").val('');
                    $("#la_currency_select option:first").prop("selected", 'selected');
                }
            }, "json");
        });

        function saveRevenueInfo(target, updateSince) {
            var id, type, currency, fixed, ratio, affId, start;
            if('mf' == target){
                id = $("#mf_id").val();
                type = 'membership_fee';
                currency = $("#mf_currency_select").val();
                fixed = $("#mf_fixed").val();
                ratio = $("#mf_ratio").val();
                affId = $("#mf_aff_id").val();
                start = $("#mf_start").val();
            }else if('la' == target){
                id = $("#la_id").val();
                type = 'load_amount';
                currency = $("#la_currency_select").val();
                fixed = $("#la_fixed").val();
                ratio = $("#la_ratio").val();
                affId = $("#la_aff_id").val();
                start = $("#la_start").val();
            }else {
                alert("Do not support selected revenue type!");
                return false;
            }

            var regex = /^(([1-9]{1}\d*)|([0]{1}))(\.(\d){1,2})?$/;
            if(fixed != '') {
                if (!regex.test(fixed)) {
                    alert('Please enter number with two decimal places at most in fixed filed!');
                    return false;
                }
            }
            if(ratio != '') {
                if (!regex.test(ratio)) {
                    alert('Please enter number with two decimal places at most in ratio filed!');
                    return false;
                }
                if (ratio < 0 || ratio > 100) {
                    alert('Please enter 0 - 100 in ratio field!');
                    return false;
                }
            }

            var since = null;
            if (updateSince) {
                since = $('#' + target + '_since').val();
                if (!since || since === '') {
                    ts.error('Please enter the date since which will need to update the rev share!');
                    return false;
                }
            }

            ts.modal();
            $.post("{{ path('admin_set_affiliate_revenue_share') }}", {
                id: id,
                type: type,
                currency: currency,
                fixed: fixed,
                ratio: ratio,
                affiliate_id: affId,
                since: since,
                start: start,
                cost: $('#' + target + '_cost').val(),
            }, function(data){
                ts.close();
                if (data.status == 'success') {
                    if(data.type == 'membership_fee'){
                        $("#mf_id").val(data.updateId);
                    }else if(data.type == 'load_amount'){
                        $("#la_id").val(data.updateId);
                    }
                    alert('Revenue share configuration is saved successfully!')
                } else {
                    if(data.errMsg != null){
                        alert(data.errMsg);
                    }else {
                        alert('Failed to save revenue share configuration!');
                    }
                }
            },"json");

        }
    </script>
{% endblock %}
