<table class="table table-striped table-bordered table-hover" id="affiliateTable">
    <thead>
    <tr>
        <th>ID</th>
        <th>Type</th>
        <th>Name</th>
        <th>Affiliate Id</th>
        <th>Pending members</th>
        <th>Linked members</th>
        <th>Unpaid commission</th>
        <th>Payout method</th>
        <th>Revenue Share Summary</th>
        <th>Action</th>
    </tr>
    </thead>
    <tbody>
    {% for item in list %}
        <tr>
            <td>{{ item.getId() }}</td>
            <td>{{ item.getAffType() | humanize }}</td>
            <td>{{ item.getAffName()}}</td>
            <td>{{ item.getAffId() }}</td>
            <td>{{ item.pendingConsumerCount() }}</td>
            <td>{{ item.linkedConsumerCount() }}</td>
            <td>{{ item.unpaidAmountUSD | defaultEx(null) | money_format('USD') }}</td>
            <td>{{ item.payoutMethodText }}</td>
            <td>{{ item.revenueShareSummary | raw }}</td>
            <td>
                <div class="btn-group">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                        {{ 'btn.actions'|trans }} <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                        {% if editable(Module.ID_AFFILIATE) %}
                            <li>
                                <a href="#" data-toggle="modal" data-id="{{ item.getId() }}" data-target="#revenueAffiliateConfirmModal">{{ 'affiliate.btn.revenueshare'|trans }}</a>
                            </li>
                            <li>
                                <a href="/admin/affiliate/{{ item.id }}/edit">Edit</a>
                            </li>
                            <li>
                                <a href="#" data-toggle="modal" data-target="#deleteAffiliateConfirmModal">{{ 'btn.delete'|trans }}</a>
                                <form action="{{ path('admin_affiliate_delete',{'affiliate_id':item.getId() }) }}" method="post"></form>
                            </li>
                        {% endif %}
                        <li>
                            {% if item.tenant %}
                                <a href="/admin/tenants/contacts/{{ item.tenant.id }}" target="_blank">
                                    Tenant contacts
                                    <i class="fa fa-external-link ml-5"></i>
                                </a>
                            {% endif %}
                            {% if item.admin %}
                                {% if editable(Module.ID_USER) %}
                                    <a href="/admin/user_modify/modify?user_id={{ item.admin.id }}" target="_blank">
                                        View user detail
                                        <i class="fa fa-external-link ml-5"></i>
                                    </a>
                                {% endif %}
                            {% endif %}
                        </li>
                        <li class="divider"></li>
                        <li class="dropdown-header">Copy</li>
                        <li>
                            <a href="javascript:" class="clipboard-btn"
                               data-clipboard-text="{{ host }}/choose-card?aff_id={{ item.affId }}">
                                Copy affiliate link</a>
                        </li>
                        <li>
                            <a href="javascript:" class="clipboard-btn"
                               data-clipboard-text="{{ item.affId }}">Copy affiliate Id</a>
                        </li>
                        <li class="divider"></li>
                        <li class="dropdown-header">Export</li>
                        <li>
                            <a href="/admin/affiliate/{{ item.id }}/export/hits">Affiliate hits</a>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>
    {% endfor %}
    </tbody>
</table>
<div class="opera-right span3 pull-right">
    {{ knp_pagination_render(list) }}
</div>
