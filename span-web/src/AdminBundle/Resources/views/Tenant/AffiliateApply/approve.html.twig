{% extends 'layout/base-layout.html.twig' %}
{% block page_title %}
    Approve affiliate application
{% endblock %}

{% block page_content %}
    <form action="/admin/tenant/affiliate-apply/{{ apply.id }}/approve" method="post"
          id="main-form" onsubmit="return checkMainForm();">
        <div class="box box-default collapsed-box">
            <div class="box-header with-border">
                <h3 class="box-title">
                    {{ apply.companyName }}
                    <span class="silver ml-20">{{ apply.website }}</span>
                </h3>

                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body read-only-form-ex">
                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>First Name</label>
                        <input type="text" class="form-control" name="firstName" value="{{ apply.firstName }}"/>
                    </div>
                    <div class="form-group col-sm-6">
                        <label>Last Name</label>
                        <input type="text" class="form-control" name="lastName" value="{{ apply.lastName }}"/>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>Email</label>
                        <input type="text" class="form-control" name="email" value="{{ apply.email }}"/>
                    </div>
                    <div class="form-group col-sm-6">
                        <label>Company Name</label>
                        <input type="text" class="form-control" name="companyName" value="{{ apply.companyName }}"/>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>Website URL</label>
                        <input type="text" class="form-control" name="website" value="{{ apply.website }}"/>
                    </div>
                    <div class="form-group col-sm-6">
                        <label>Describe your website and target audience</label>
                        <input type="text" class="form-control" name="audience" value="{{ apply.audience }}"/>
                    </div>
                </div>
            </div>
        </div>

        <h4 class="bold">Step 1: Create an user</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <ul class="mb-0">
                    <li>Add "Affiliate" role so that he can access affiliate portal</li>
                    <li>Add "Consumer" role so that he can sign up a card to receive commission</li>
                </ul>
            </div>
        </div>

        <h4 class="bold">Step 2: Create affiliate info</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <ul class="mb-0">
                    <li>Generate affiliate record and link</li>
                </ul>
            </div>
        </div>

        <h4 class="bold">Step 3: Connect user to affiliate</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <label for="tenant_source_user" class="cursor block mb-10 normal">
                    <input type="radio" name="tenant_source" value="user" v-model="tenantSource"
                           id="tenant_source_user" class="mr-5" checked>
                    Connect to affiliate directly
                </label>
                <label for="tenant_source_new" class="cursor block normal">
                    <input type="radio" name="tenant_source" value="new" v-model="tenantSource"
                           id="tenant_source_new" class="mr-5">
                    Connect via a new tenant:
                </label>
                <fieldset class="ml-15 row" :disabled="tenantSource != 'new'">
                    <div class="form-group col-sm-6">
                        <label>Type:</label>
                        <select name="tenant_type" class="form-control">
                            {% for type in types %}
                                <option value="{{ type }}" {% if type == 'marketing_partner' %}selected{% endif %}>
                                    {{ type | humanize }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group col-sm-6">
                        <label>Name:</label>
                        <input type="text" class="form-control" name="tenant_name" :disabled="tenantSource != 'new'">
                    </div>
                </fieldset>

                <label for="tenant_source_exist" class="cursor block normal">
                    <input type="radio" name="tenant_source" value="exist" v-model="tenantSource"
                           id="tenant_source_exist" class="mr-5">
                    Connect via an exist tenant:
                </label>
                <div class="ml-30">
                    <select class="form-control" name="tenant" id="tenant_source_select"
                            :disabled="tenantSource != 'exist'">
                        {% for type, ts in tenants %}
                            {% if type in types %}
                                {% for t in ts %}
                                    <option value="{{ t.id }}">{{ type | humanize }} - {{ t.name }}</option>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <div class="form-group">
            <input type="submit" class="btn btn-primary mr-15" value="Save"/>
            <a href="/admin/tenant/affiliate-apply" class="btn btn-default">Cancel</a>
        </div>
    </form>

    <style>
    </style>
{% endblock %}

{% block javascripts_inline %}
    <script>
        var app = new Vue({
          el: '#main-form',
          data: {
            tenantSource: 'user'
          }
        });

        function checkMainForm() {
          var $form = $('#main-form');
          if ($form.find('#tenant_source_new')[0].checked) {
            var $input = $form.find('[name=tenant_name]');
            if ($.trim($input.val()) === '') {
              $input.animated('flash').focus();
              return false;
            }
          }
          return true;
        }
    </script>
{% endblock %}