<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/10/17
 * Time: 下午2:14
 */

namespace AdminBundle\Controller;

use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Util;
use EsSoloBundle\Services\EsSoloService;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\SpendrBundle;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;
use TransferMexBundle\Entity\ImportPayoutRecord;
use CoreBundle\Entity\BaseState;
use CoreBundle\Utils\Log;
use TransferMexBundle\Entity\EmployerSubCompany;

class CommonController extends BaseController
{

    /**
     * @Route("/admin/download")
     * @param Request $request
     * @return BinaryFileResponse
     */
    public function downloadFileAction(Request $request)
    {
        $path = $request->get('path');
        if (!$path) {
            throw PortalException::temp('Unknown path!');
        }
        $path = Util::uploadDir() . $path;
        Util::verifyPathSecurity($path);
        $response = new BinaryFileResponse($path);
        $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT);
        return $response;
    }

    /**
     * @Route("/admin/affiliateTenant.json", name="admin_affiliate_tenant_type")
     * @param Request $request
     *
     * @return Response
     */
    public function affiliateWithTenant(Request $request)
    {
        $tenantType = $request->get('tenant_type');
        $affiliates = $this->getDoctrine()->getManager()
            ->getRepository(\CoreBundle\Entity\Affiliate::class)->findBy(array('affType' =>$tenantType));
        $arr = array();
        foreach ($affiliates as $affiliate)
        {
            $arr[$affiliate->getId()] = $affiliate->getAffName();
        }
        return new Response(json_encode($arr));
    }

    /**
     * @Route("/admin/user/set-current-employer",methods={"POST"})
     * @return SuccessResponse
     */
    public function setCurrentEmployer(Request $request) {
      $all = $request->request->all();
      $user = $this->getUser();
      $user->setCurrentEmployer($all['currentEmployer']);
      Util::persist($user);
      return new SuccessResponse();
    }
    /**
     * @Route("/admin/user/profile")
     * @return SuccessResponse
     */
    public function profileAction() {
        $user = $this->authAdmin();

        EsSoloService::ensureApiInvokerOnUser($user);

        $data = $user->toApiArray(true);

        $reads = $user->getPermissions();
        
        $group = $user->getAdminGroup();
        $currentEmployerId = Util::meta($user, 'currentEmployer');
        if ( $currentEmployerId) {
          $currentEmployer = User::find($currentEmployerId);
          $group = $currentEmployer->getAdminGroup();
        }
        $data['permissions'] = $reads;
        $data['uPermissions'] = Util::meta($user, 'uPermissions') ?? [];
        $data['isReloadable'] =  $group ?  $group->getMemberLoadType() : false;
        $data['editableModules'] = $user->getEditableModules();
        $data['menus'] = $this->prepareMenus($user, $reads);
        $data['defaultUrl'] = $user->getDefaultUrl();
        $data['consumer'] = $user->isConsumer();
        $data['dashboard'] = $user->getDashboardType();
        $data['impersonating'] = Util::getImpersonatingUser() !== null;
        $data['preference'] = $user->getPreferences();
        $data['isGranted'] = Util::$security->isGranted('IS_IMPERSONATOR');
        $data['cpKey'] = null;
        $data['adminLayout'] = 'a'; // Old admin portal layout
        $data['env'] = $this->getParameter('kernel.environment');
        $data['live'] = Util::isLive();
        $data['switchedCp'] = Util::$request->cookies->has(Util::COOKIE_CARD_PROGRAM);
        $data['twoFAEnabled'] = $user->isTwoFAEnabled();
        if (Bundle::isCashOnWeb()) {
            $data['locator'] = $user->getLoadLocator() ? $user->getLoadLocator()->getName() : '';
        }
        if ($user->isPlatformRelated()) {
            $data['platforms'] = Util::toApiArray($user->getOpenPlatforms(), true);
        }

        if (Bundle::isUsUnlocked()) {
            $data['cpKey'] = 'cp_usu';
        }

        $data = Bundle::common('apiAdminUserProfile', [$data], $data);
        if ( $currentEmployerId) {
            $data['group'] = $group->toApiArray();
        }
        // get kyc required for platform
        $data['KYCRequired'] = false;
        $data['clientsRequired'] = false;
        $data['enableLoadTypeConfigure'] = false;
        $data['employerDashboardInstitutionName'] = null;
        $data['employerDashboardInstitutionAddress'] = null;
        $data['employerDashboardRoutingNumber'] = null;
        $data['employerDashboardAccountNumber'] = null;

        // get customer support info
        $data['customerSupportPhone'] = null;
        $data['customerSupportEmail'] = null;

        $data['clientLogo'] = null;
        $data['platformLogoUrl'] = null;
        $data['hasW2'] = false;
        $platform = Util::platform();
        if ($platform) {
          	$platformMeta = Util::meta($platform);
          	$data['KYCRequired'] = $platformMeta['kycRequire'] ?? $data['KYCRequired'];;
          	$data['clientsRequired'] = $platformMeta['clientsRequire'] ?? $data['clientsRequired'];
            $data['employerDashboardInstitutionName'] = $platformMeta['institutionName'] ?? $data['employerDashboardInstitutionName'];
            $data['employerDashboardInstitutionAddress'] = $platformMeta['institutionAddress'] ?? $data['employerDashboardInstitutionAddress'];
            $data['employerDashboardRoutingNumber'] = $platformMeta['routingNumber'] ?? $data['employerDashboardRoutingNumber'];
            $data['employerDashboardAccountNumber'] = $platformMeta['accountNumber'] ?? $data['employerDashboardAccountNumber'];
            $data['customerSupportPhone'] = $platformMeta['customerSupportPhone'] ?? $data['customerSupportPhone'];
            $data['customerSupportEmail'] = $platformMeta['customerSupportEmail'] ?? $data['customerSupportEmail'];
            $data['currentEmployer'] = Util::meta($user, 'currentEmployer');

            $data['enableLoadTypeConfigure'] = $platformMeta['enableLoadTypeConfigure'] ?? $data['enableLoadTypeConfigure'];
            if ($user->inTeams([
                Role::ROLE_FAAS_CLIENT,
                Role::ROLE_FAAS_MEMBER,
                Role::ROLE_TRANSFER_MEX_EMPLOYER,
                Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN
              ])) {
              $client = $user->inTeams([
                  Role::ROLE_FAAS_CLIENT,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER
                ]) ? $user : null;
              if ($user->inTeams([Role::ROLE_FAAS_MEMBER])) {
                $client = $user->getPrimaryGroupAdmin();
              }
              if ($user->inTeams([
                  Role::ROLE_FAAS_CLIENT_ADMIN,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN
                ])) {
                $client = $user->ensureConfig()->getGroup()->getPrimaryAdmin();
              }

              if ($data['currentEmployer']) {
                $currentEmployer = User::find($data['currentEmployer']);
                $meta = Util::s2j($currentEmployer->getMeta());
                $data['clientLogo'] = $meta['clientLogo'] ?? $data['clientLogo'];
                $data['employerDashboardInstitutionName'] = $meta['Institution Name'] ?? $data['employerDashboardInstitutionName'];
                $data['employerDashboardInstitutionAddress'] = $meta && isset($meta['Institution Address']) ? $meta['Institution Address'] : $data['employerDashboardInstitutionAddress'];
                $data['employerDashboardRoutingNumber'] = SSLEncryptionService::decryptWhenNecessary($meta['Routing Number'] ?? $data['employerDashboardRoutingNumber']);
                $data['employerDashboardAccountNumber'] = SSLEncryptionService::decryptWhenNecessary($meta['Account Number']?? $data['employerDashboardAccountNumber']);
                $data['payrollInfo'] = $this->getPayrollInof($currentEmployer);
                $data['hasW2'] = in_array($currentEmployer->getId(), Config::array('transfermex_enable_w2s_employer_ids'));
              } else if ($client) {
                $meta = Util::s2j($client->getMeta());
                $data['clientLogo'] = $meta['clientLogo'] ?? $data['clientLogo'];
                $data['employerDashboardInstitutionName'] = $meta['Institution Name'] ?? $data['employerDashboardInstitutionName'];
                $data['employerDashboardInstitutionAddress'] = $meta && isset($meta['Institution Address']) ? $meta['Institution Address'] : $data['employerDashboardInstitutionAddress'];
                $data['employerDashboardRoutingNumber'] = SSLEncryptionService::decryptWhenNecessary($meta['Routing Number'] ?? $data['employerDashboardRoutingNumber']);
                $data['employerDashboardAccountNumber'] = SSLEncryptionService::decryptWhenNecessary($meta['Account Number']?? $data['employerDashboardAccountNumber']);
                $data['payrollInfo'] = $this->getPayrollInof($client);
                $data['hasW2'] = in_array($client->getId(), Config::array('transfermex_enable_w2s_employer_ids'));
                $data['isPrimaryEmployer'] = Util::meta($client, 'isPrimaryEmployer');
              }
			      }

            if ($platform->isSpendr()) {
                $data['isSpendrAdminLogin'] = SpendrBundle::isSpendrAdminLoggedInAs();
                if ($user->inTeams(SpendrBundle::getMerchantBalanceAdminRoles())) {
                    $data['isSpendrEmployeeCSLogin'] = SpendrBundle::isSpendrEmployeeCSLoggedInAs();
                    $data['isSpendrEmployeeComplianceLogin'] = SpendrBundle::isSpendrEmployeeComplianceLoggedInAs();
                    $data['isSpendrEmployeeAccountantLogin'] = SpendrBundle::isSpendrEmployeeAccountantLoggedInAs();
                    $data['isSpendrEmployeeLogin'] = SpendrBundle::isSpendrEmployeeLoggedInAs();
                }
            }
            $subEmployers = [];
            if ($platform->isTransferMex()) {
              if ($user->inTeams([
                  Role::ROLE_FAAS_CLIENT,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN_ACH,
                  Role::ROLE_TRANSFER_MEX_EMPLOYER_ADMIN
                ])) {
                  $subEmployers = EmployerSubCompany::getSubEmployersOptions($user);
                }
            }
            $data['subEmployers'] = $subEmployers;
            $data['platformLogoUrl'] = $platform->getLogoUrl() ?? $data['platformLogoUrl'];
        }

        $data['isMasterLogin'] = Util::isMasterAdminLoggedInAs();
        $data['isReadOnlyAdmin'] = Util::meta($user, 'readOnlyAdmin');
        $data['isDebugLogin'] = Util::isMasterAdminLoggedInAs() && in_array( Util::getImpersonatingUser()->getEmail(), [
            '<EMAIL>',
            '<EMAIL>',
        ]);
        return new SuccessResponse($data);
    }

    protected function getPayrollInof(User $user) {
        $pendingCount = Util::em()->getRepository(ImportPayoutRecord::class)
                      ->createQueryBuilder('im')
                      ->where(Util::expr()->notIn('im.executeStatus', ":executeStatus"))
                      ->andWhere(Util::expr()->eq('im.employer', ':employer'))
                      ->setParameter('executeStatus', [ImportPayoutRecord::EXECUTE_CANCELED, ImportPayoutRecord::EXECUTE_COMPLETE])
                      ->setParameter('employer', $user)
                      ->select('count(distinct im)')
                      ->getQuery()
                      ->getSingleScalarResult();
       if ($pendingCount) {
          $str = 'There ' . ( $pendingCount > 1 ? 'are' : 'is') . ' ' . $pendingCount . ' payroll batch' . ($pendingCount > 1 ? 'es': ''). ' waiting for batch processing!';
          return $str;
       }
       return '';
    }
    protected function prepareMenus(User $user, $reads)
    {
        $platform = Util::platform();
        if ($platform) {
            $defined = Bundle::isBundleMethodDefined('getMenusForCurrentUser', $platform);
            if ($defined) {
                $menus = Bundle::common('getMenusForCurrentUser', [], [], $platform);
            } else {
                $menus = [];
            }

            if (!in_array($platform->getName(), [
                Platform::NAME_ES_SOLO,
                Platform::NAME_FIS,
                Platform::NAME_TERN_COMMERCE,
            ])) {
                return $menus;
            }
        }

        if (!$platform || $platform->isTernCommerce()) {
            $menus = [];
            $demo = Util::isDemo();
            foreach (Module::TREE as $item) {
                if ($demo && $item['id'] === Module::ID_RESHIPPER_MANAGEMENT) {
                    continue;
                }
                $item['route'] = $item['route'] ? $this->generateUrl($item['route']) : null;
                $children = [];
                foreach ($item['children'] as $child) {
                    if ($demo && $child['id'] === Module::ID_RESHIPPER_MANAGEMENT) {
                        continue;
                    }
                    if (in_array($child['id'], $reads, true)) {
                        if ($child['route']) {
                            $child['route'] = $this->generateUrl($child['route']);
                        }
                        $children[] = $child;
                    }
                }
                if ($children) {
                    $item['children'] = $children;
                }
                if (in_array($item['id'], $reads, true) || $children) {
                    $menus[] = $item;
                }
            }
        }

        // Ensure the users list
        if ($user->isSuperAdmin()) {
            $hasUsersList = false;
            foreach ($menus as $menu) {
                if ($menu['id'] === Module::ID_USER) {
                    $hasUsersList = true;
                } else if (isset($menu['children'])) {
                    foreach ($menu['children'] as $child) {
                        if ($child['id'] === Module::ID_USER) {
                            $hasUsersList = true;
                            break;
                        }
                    }
                }
                if ($hasUsersList) {
                    break;
                }
            }
            if (!$hasUsersList) {
                $index = 0;
                foreach ($menus as $i => $menu) {
                    if ($menu['id'] === Module::ID_DASHBOARD) {
                        $index = $i + 1;
                        break;
                    }
                }
                array_splice($menus, $index, 0, [
                    [
                        'id' => Module::ID_USER,
                        'name' => 'User Management',
                        'route' => 'admin_user',
                        'icon' => 'fa fa-fw fa-users',
                        'mdIcon' => 'mdi-account-multiple',
                        'mdRoute' => 'user-management/user',
                        'children' => [],
                    ]
                ]);
            }
        }

        return $menus;
    }

    /**
     * @Route("/admin/user/preference", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function setPreference(Request $request) {
        $all = $request->request->all();
        $user = $this->getUser();
        $user->setPreference($all);

        return new SuccessResponse($user->getPreferences());
    }

    /**
     * @Route("/admin/user/switch-role", methods={"GET"})
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function switchRole(Request $request) {
        $role = $request->get('role');
        $user = $this->getUser();
        $user->setCurrentRole($role);
        Util::persist($user);
        return new RedirectResponse('/admin');
    }

    /**
     * @Route("/admin/country/{country}/states", methods={"GET"})
     * @param Request $request
     * @param Country $country
     *
     * @return SuccessResponse
     */
    public function states(Request $request, Country $country) {
        $states = $country->getSortedStates();
        return new SuccessResponse(Util::toApiArray($states));
    }


    /**
     * @Route("/admin/uniteller/countries", methods={"GET"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function uniTellerCountries(Request $request) {
        $countries =  Util::em()->getRepository(BaseState::class)
                        ->createQueryBuilder('t')
                        ->join('t.country', 'c')
                        ->where(Util::expr()->eq('t.isUniTellerSupport', ':isUniTeller'))
                        ->setParameter('isUniTeller', 1)
                        ->select('c.id, c.name')
                        ->groupBy('c.id')
                        ->getQuery()
                        ->getArrayResult();
        return new SuccessResponse($countries);
    }
}
