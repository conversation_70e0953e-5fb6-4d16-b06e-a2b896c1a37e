<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/11/3
 * Time: 下午6:28
 */

namespace AdminBundle\Controller;


use AdminBundle\Controller\UsUnlocked\AnalyticsControllerTrait;
use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use Faker\Factory;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use CoreBundle\Entity\UsuSystemDailyActivity;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;

class AnalyticsController extends BaseController
{
    use AnalyticsControllerTrait;

    protected $tzUTC;
    protected $tzUser;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_ANALYTIC_MANAGEMENT);
    }

    /**
     * @param $start Carbon
     * @param $end Carbon
     * @return string
     */
    protected function getRangeType($start, $end)
    {
        $rangeType = 'Y';
        if ($start && $end) {
            if ($start->diffInYears($end) < 3) {
                $rangeType = 'Y-m';
                if ($start->diffInMonths($end) < 3) {
                    $rangeType = 'Y-W\W';
                    if ($start->diffInWeeks($end) < 3) {
                        $rangeType = 'Y-m-d';
                    }
                }
            }
        }
        return $rangeType;
    }

    protected function getRangeKey($dateString, $type)
    {
        if (is_string($dateString) || !$dateString) {
            $date = new Carbon($dateString, $this->tzUTC);
        } else {
            $date = Carbon::instance($dateString);
        }
        $date->setTimezone($this->tzUser);

        $format = $date->format($type);
        if ($date->month > 1 && $format === $date->year . '-01W') {
            $format = ($date->year + 1) . '-' . explode('-', $format)[1];
        }
        return $format;
    }

    protected function getEndTime(Request $request, $field = 'end')
    {
        $end = $request->get($field);
        if ($end) {
            $end = Util::timeUTC($end);
            $now = Carbon::now();
            if ($end->gt($now)) {
                $end = $now;
            }
            $end->addDay();
        }
        return $end;
    }

    protected function query(QueryBuilder $q, $timeField = null, $userPrefix = null)
    {
        $request = Util::request();

        $q->join('uc.card', 'c');
        if (!$userPrefix) {
            $q->join('uc.user', 'uuu');
            $userPrefix = 'uuu';
        }

        $expr = $q->expr();
        $q->andWhere($expr->in('c.cardProgram', ':__cps'))
            ->setParameter('__cps', Util::user()->getOpenCardPrograms());

        Util::queryRealConsumers($q, $userPrefix);

        $cardProgram = $request->get('cardProgram');
        if ($cardProgram) {
            $q->andWhere('c.cardProgram = :cardProgram')
                ->setParameter('cardProgram', $cardProgram);
        }

        $start = $request->get('start');
        $end = $this->getEndTime($request);
        if ($timeField) {
            if ($start) {
                $start = Util::timeUTC($start);
                $q->andWhere($timeField . ' >= :start')
                    ->setParameter('start', $start);
            }
            if ($end) {
                $q->andWhere($timeField . ' < :end')
                    ->setParameter('end', $end);
            }
        }

        return [$q, $start, $end];
    }

    protected function retrieveLimitedValues(array $rs, $count = 6)
    {
        $result = [];
        foreach ($rs as $i => $r) {
            if ($i < $count) {
                $result[] = [
                    'value' => (int)$r['value'],
                    'name' => wordwrap($r['name'], 25, "\n", true),
                ];
            } else {
                if (!isset($result[$count])) {
                    $result[] = [
                        'value' => 0,
                        'name' => 'Other',
                    ];
                }
                $result[$count]['value'] += $r['value'];
            }
        }
        return $result;
    }

    protected function retrieveLimitedValuesN(array $rs, $count = 6)
    {
        $result = [];
        foreach ($rs as $i => $r) {
            if ($i < $count) {
                $result[] = [
                    'value' => (int)$r['value'],
                    'name' => $r['name'],
                ];
            } else {
                if (!isset($result[$count])) {
                    $result[] = [
                        'value' => 0,
                        'name' => 'Other',
                    ];
                }
                $result[$count]['value'] += $r['value'];
            }
        }
        return $result;
    }
    protected function queryN(QueryBuilder $q, $timeField = null)
    {
        $request = Util::request();

        $q->join('uc.card', 'c');
        $expr = $q->expr();
        $q->andWhere($expr->in('c.cardProgram', ':__cps'))
            ->setParameter('__cps', Util::user()->getOpenCardPrograms());

        $cardProgram = $request->get('cardProgram');
        if ($cardProgram) {
            $q->andWhere('c.cardProgram = :cardProgram')
                ->setParameter('cardProgram', $cardProgram);
        }

        if ($timeField) {
            $start = $request->get('compareStart');
            if ($start) {
                $start = Util::timeUTC($start);
                $q->andWhere($timeField . ' >= :start')
                    ->setParameter('start', $start);
            }
            $end = $request->get('compareEnd');
            if ($end) {
                $end = Util::timeUTC($end)->addDay();
                $q->andWhere($timeField . ' < :end')
                    ->setParameter('end', $end);
            }
        }
    }

    /**
     * @Route("/admin/analytics", name="admin_analytics")
     */
    public function indexAction() {
        $em = Util::em();
        return $this->render('@Admin/Analytics/index.html.twig', [
            'cardPrograms' => Util::user()->getOpenCardPrograms(),
        ]);
    }

    /**
     * @Route("/admin/analytics/load-activity", name="admin_analytics_load_activity")
     * @param Request $request
     * @return SuccessResponse
     */
    public function loadActivity(Request $request) {
        $this->tzUTC = Util::tzUTC();
        $this->tzUser = $this->getUser()->getDateTimeZone();

        $cacheKey = $this->createCacheKey($request, 'AnalyticsController', 'ADMIN');
        $result = null;
        $timeout = 86400;
        Log::debug($cacheKey);
        if (!$result) {
            $result = Data::getArray($cacheKey);
        }
        if (!$result) {
          $em = Util::em();
          $q = $em->getRepository(\CoreBundle\Entity\UserCard::class)
              ->createQueryBuilder('uc');
          $q->where('uc.issued = :issued')
              ->setParameters([
                  'issued' => 1,
              ]);

          $this->query($q);

          $qLoad = clone $q;
          $qLoad->join('uc.loads', 'ucl')
              ->andWhere('ucl.loadStatus = :loadStatus')
              ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED);

          $start = $request->get('start');
          if ($start) {
              $start = Util::timeUTC($start);
              $qLoad->andWhere('ucl.loadAt >= :start')
                  ->setParameter('start', $start);
          }
          $end = $this->getEndTime($request);
          if ($end) {
              $qLoad->andWhere('ucl.loadAt < :end')
                  ->setParameter('end', $end);
          }

          $rs = $qLoad->orderBy('ucl.loadAt')
              ->select('uc.id, min(ucl.loadAt) loadAt, ucl.loadAmountUSD loadAmountUSD')
              ->groupBy('ucl.id')
              ->getQuery()
              ->getArrayResult();

          $demoRs = [];
          if (Util::isDemo()) {
              $faker = Factory::create();
              $count = $faker->numberBetween(30, 100);
              for ($i = 0; $i < $count; $i++) {
                  $demoRs[] = [
                      'id' => $faker->unique(),
                      'loadAt' => $faker->dateTimeBetween($start, $end)->format('c'),
                      'loadAmountUSD' => $faker->numberBetween(5000, 100000),
                      'reload' => $faker->boolean,
                  ];
              }
              $rs = array_merge($rs, $demoRs);
              Util::usort($rs, [
                  'loadAt' => true,
              ]);
          }

          $dates = [];
          $rangeType = $this->getRangeType($start, $end);
          $dataA = $this->dataWithLoadAt($rs, $dates, $rangeType, 'all');
          $dates = $dataA['dates'];

          $qFirst = clone $qLoad;

          $qFirst->join('uc.card', 'card')
              ->join('card.cardType', 'ct')
              ->andWhere('ct.isreloaded = 1');

          $rs = $qFirst->orderBy('ucl.loadAt')
              ->select('uc.id, min(ucl.loadAt) loadAt, ucl.loadAmountUSD loadAmountUSD')
              ->groupBy('ucl.id')
              ->getQuery()
              ->getArrayResult();

          if ($demoRs) {
              $rs = array_merge($rs, array_filter($demoRs, function ($d) {
                  return $d['reload'];
              }));
              Util::usort($rs, [
                  'loadAt' => true,
              ]);
          }

          $dataR = $this->dataWithLoadAt($rs, $dates, $rangeType, 'reload');
          $dates = $dataR['dates'];

          $qReload = clone $qLoad;
          $qReload->join('uc.card', 'card')
              ->join('card.cardType', 'ct')
              ->andWhere('ct.isreloaded = 0');
          $rs = $qReload->orderBy('ucl.loadAt')
              ->select('uc.id, min(ucl.loadAt) loadAt, ucl.loadAmountUSD loadAmountUSD')
              ->groupBy('ucl.id')
              ->getQuery()
              ->getArrayResult();

          if ($demoRs) {
              $rs = array_merge($rs, array_filter($demoRs, function ($d) {
                  return !$d['reload'];
              }));
              Util::usort($rs, [
                  'loadAt' => true,
              ]);
          }

          $dataF = $this->dataWithLoadAt($rs, $dates, $rangeType, 'first');
          $dates = $dataF['dates'];

          $result = [
              'dates' => array_keys($dates),
              'all' => [],
              'first' => [],
              'reload' => [],
          ];
          $result['allLoad'] = $dataA['loadAmount'];
          $result['reloadLoad'] = $dataR['loadAmount'];
          $result['firstLoad'] = $dataF['loadAmount'];

          foreach ($dates as $date => $meta) {
              $result['all'][] = $meta['all'];
              $result['first'][] = $meta['first'];
              $result['reload'][] = $meta['reload'];
          }
          Data::setArray($cacheKey, $result, $timeout);
        }
        return new SuccessResponse($result);
    }

    function dataWithLoadAt($rs, $dates, $rangeType, $type) {
        $loadAmount = 0;
        foreach ($rs as $r) {
            $key = $this->getRangeKey($r['loadAt'], $rangeType);
            if (!isset($dates[$key])) {
                $dates[$key] = [
                    'all' => 0,
                    'first' => 0,
                    'reload' => 0,
                ];
            }
            $dates[$key][$type] += $r['loadAmountUSD'];
            $loadAmount += $r['loadAmountUSD'];
        }
        $data['dates'] = $dates;
        $data['loadAmount'] = $loadAmount;
        return $data;
    }

    /**
     * @Route("/admin/analytics/total-cards")
     * @param Request $request
     * @return SuccessResponse
     */
    public function totalCards(Request $request) {
        $cacheKey = $this->createCacheKey($request, 'AnalyticsController', 'ADMIN');
        $result = null;
        $timeout = 86400;
        Log::debug($cacheKey);
        if (!$result) {
            $result = Data::getArray($cacheKey);
        }
        if (!$result) {
          $q = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)
              ->createQueryBuilder('uc');
          $q->where('uc.issued = :issued')
              ->andWhere('uc.status = :status')
              ->setParameters([
                  'issued' => 1,
                  'status' => UserCard::STATUS_ACTIVE,
              ]);

          $this->query($q);

          $rs = $q->select('uc.issuedAt')
              ->orderBy('uc.issuedAt')
              ->getQuery()
              ->getArrayResult();
          $dates = [];
          $user = $this->getUser();
          $tz = $user->getDateTimeZone();
          foreach ($rs as $r) {
              /** @var \DateTime $time */
              $time = $r['issuedAt'];
              $time->setTimezone($tz);
              $date = $time->format(Util::DATE_FORMAT_SEARCH);
              if (!isset($dates[$date])) {
                  $dates[$date] = [
                      'time' => Carbon::instance($time),
                      'sum' => 0,
                  ];
              }
              $dates[$date]['sum']++;
          }

          $result = [];
          $sum = 0;
          $start = $request->get('start');
          if ($start) {
              $start = Util::timeUTC($start);
          }
          $end = $this->getEndTime($request);
          foreach ($dates as $date => $meta) {
              $sum += $meta['sum'];
              /** @var Carbon $time */
              $time = $meta['time'];
              if ($start && $time->lt($start)) {
                  continue;
              }
              if ($end && $time->gte($end)) {
                  continue;
              }
              $result[] = [ $date, $sum ];
          }
          Data::setArray($cacheKey, $result, $timeout);
        }

        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/total-active-cards")
     * @param Request $request
     * @return SuccessResponse
     */
    public function totalActiveCards(Request $request) {
        $this->tzUTC = Util::tzUTC();
        $this->tzUser = $this->getUser()->getDateTimeZone();
        $cacheKey = $this->createCacheKey($request, 'AnalyticsController', 'ADMIN');
        $result = null;
        $timeout = 86400;
        Log::debug($cacheKey);
        if (!$result) {
            $result = Data::getArray($cacheKey);
        }
        if (!$result) {
          $em = Util::em();
          $q = $em->getRepository(\CoreBundle\Entity\UserCard::class)
              ->createQueryBuilder('uc');
          $q->where('uc.issued = :issued')
              ->setParameters([
                  'issued' => 1,
              ]);

          $this->query($q);

          $qLoad = clone $q;
          $qLoad->join('uc.loads', 'ucl')
              ->andWhere('ucl.loadStatus = :loadStatus')
              ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED);

          $start = $request->get('start');
          if ($start) {
              $start = Util::timeUTC($start);
              $qLoad->andWhere('ucl.loadAt >= :start')
                  ->setParameter('start', $start);
          }
          $end = $this->getEndTime($request);
          if ($end) {
              $qLoad->andWhere('ucl.loadAt < :end')
                  ->setParameter('end', $end);
          }

          $rs = $qLoad->orderBy('ucl.loadAt')
              ->select('uc.id, min(ucl.loadAt) loadAt')
              ->groupBy('uc.id')
              ->getQuery()
              ->getArrayResult();
          $dates = [];
          $rangeType = $this->getRangeType($start, $end);
          foreach ($rs as $r) {
              $key = $this->getRangeKey($r['loadAt'], $rangeType);
              if (!isset($dates[$key])) {
                  $dates[$key] = [
                      'loaded' => 0,
                      'spend' => 0,
                  ];
              }
              $dates[$key]['loaded']++;
          }

          $qSpend = clone $q;
          $qSpend->join('uc.usages', 'uct')
              ->andWhere($qSpend->expr()->in('uct.actualTranCode', ':actualTranCode'))
              ->setParameter('actualTranCode', [
                  UserCardTransaction::CODE_POS_PURCHASE,
                  UserCardTransaction::CODE_PURCHASE_RETURN,
              ]);

          if ($start) {
              $qSpend->andWhere('uct.txnTime >= :start')
                  ->setParameter('start', $start);
          }
          if ($end) {
              $qSpend->andWhere('uct.txnTime < :end')
                  ->setParameter('end', $end);
          }

          $rs = $qSpend->orderBy('uct.txnTime')
              ->select('uc.id, min(uct.txnTime) txnTime')
              ->groupBy('uc.id')
              ->getQuery()
              ->getArrayResult();
          foreach ($rs as $r) {
              $key = $this->getRangeKey($r['txnTime'], $rangeType);
              if (!isset($dates[$key])) {
                  $dates[$key] = [
                      'loaded' => 0,
                      'spend' => 0,
                  ];
              }
              $dates[$key]['spend']++;
          }

          $result = [
              'dates' => array_keys($dates),
              'loaded' => [],
              'spend' => [],
          ];
          foreach ($dates as $date => $meta) {
              $result['loaded'][] = $meta['loaded'];
              $result['spend'][] = $meta['spend'];
          }
          Data::setArray($cacheKey, $result, $timeout);
        }
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/monthly-fee")
     * @param Request $request
     * @return SuccessResponse
     */
    public function monthlyFee(Request $request) {
        $em = Util::em();
        $q = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->where('ucl.actualTranCode = :tranCode')
            ->setParameter('tranCode', UserCardTransaction::CODE_MONTHLY_FEE);

        $this->query($q, 'ucl.txnTime');

        $rs = $q->select('ucl.txnAmountUSD')
            ->getQuery()
            ->getArrayResult();

        $total = 0;
        foreach ($rs as $r) {
            $total += $r['txnAmountUSD'];
        }
        $totalStr = Money::formatAmount($total, 'USD', '');
        $count = count($rs);

        $result = [
            'total' => $totalStr,
            'count' => $count,
            'average' => $count !== 0 ? $total / $count : 0,
        ];
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/mcc")
     * @param Request $request
     * @return SuccessResponse
     */
    public function mcc(Request $request) {
        $em = Util::em();
        $q = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('ucl.merchant', 'm')
            ->join('m.merchantType', 'mt');

        $this->query($q, 'ucl.txnTime');

        $rs = $q->select('count(distinct ucl) value, mt.merchantType name')
            ->groupBy('mt.merchantType')
            ->orderBy('value', 'desc')
            ->getQuery()
            ->getArrayResult();

        $result = $this->retrieveLimitedValues($rs);
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/merchant")
     * @param Request $request
     * @return SuccessResponse
     */
    public function merchant(Request $request) {
        $em = Util::em();
        $q = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('ucl.merchant', 'm');

        $this->query($q, 'ucl.txnTime');

        $rs = $q->select('count(distinct ucl) value, m.merchantCustName name')
            ->groupBy('m.merchantCustName')
            ->orderBy('value', 'desc')
            ->getQuery()
            ->getArrayResult();

        $result = $this->retrieveLimitedValues($rs);
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/card-program/chart-setting")
     * @param Request $request
     * @return SuccessResponse
     */
    public function getChartSettingAction(Request $request) {
        $settings = Config::get(Config::CONFIG_GLOBAL_CHART_SETTINGS);
        $platform = Util::platform();
        if ($platform) {
            $cpSettings = $platform->getChartSetting();
            if ($cpSettings) {
                $settings = $cpSettings;
            }
        }
        return new SuccessResponse(Util::s2j($settings));
    }

    /**
     * @Route("/admin/get/card-program")
     * @return SuccessResponse
     */
    public function getCardProgram () {
        $maps = Util::user()->getOpenCardPrograms()->getValues();
        $result = [];
        /** @var CardProgram $map*/
        foreach ($maps as $map) {
            $result[] = [
                'id'   => $map->getId(),
                'name' => $map->getName(),
            ];
        }
        return new SuccessResponse($result);
    }
    /**
     * @Route("/admin/analytics/usage-activity")
     * @param Request $request
     * @return SuccessResponse
     */
    public function getUsageActivity (Request $request) {
        $this->tzUTC = Util::tzUTC();
        $this->tzUser = $this->getUser()->getDateTimeZone();

        $em = Util::em();
        $q = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->leftJoin('uct.userCard', 'c')
            ->leftJoin('c.card', 'cpct')
            ->leftJoin('cpct.cardType', 'ct');

        $expr = $q->expr();
        $q->andWhere($expr->in('cpct.cardProgram', ':__cps'))
            ->setParameter('__cps', Util::user()->getOpenCardPrograms());

        $cardProgram = $request->get('cardProgram');
        if ($cardProgram) {
            $q->andWhere('cpct.cardProgram = :cardProgram')
                ->setParameter('cardProgram', $cardProgram);
        }

        if (Bundle::isUsUnlocked()) {
            $metaLike = Util::isLive() ? '{"privacySandbox":false%' : '{"privacySandbox":true%';
            $q->andWhere($expr->notIn('uct.accountStatus', ':accountStatuses'))
                ->andWhere($expr->like('c.meta', ':userCardMeta'))
                ->setParameter('accountStatuses', [
                    PrivacyAPI::TRANSACTION_STATUS_VOIDED,
                    PrivacyAPI::TRANSACTION_STATUS_EXPIRED,
                ])
                ->setParameter('userCardMeta', $metaLike);
        }

        $start = $request->get('start');
        if ($start) {
            $start = Util::timeUTC($start);
        }
        $end = $this->getEndTime($request);

        $ids = [];

        $qSpend = clone $q;

        if ($start) {
            $qSpend->andWhere('uct.txnTime >= :start')
                ->setParameter('start', $start);
        }
        if ($end) {
            $qSpend->andWhere('uct.txnTime < :end')
                ->setParameter('end', $end);
        }

        $rs = $qSpend->orderBy('uct.txnTime')
            ->select(' min(uct.txnTime) txnTime, ct.id card_type_id, uct.txnAmountUSD txnAmountUSD')
            ->groupBy('uct.id')
            ->getQuery()
            ->getArrayResult();

        if (Util::isDemo()) {
            $demoRs = [];
            $faker = Factory::create();

            $cardTypes = $this->getDoctrine()
                ->getRepository(\CoreBundle\Entity\CardType::class)
                ->findAll();
            /** @var CardType $cardType */
            foreach ($cardTypes as $cardType) {
                $count = $faker->numberBetween(30, 100);
                for ($i = 0; $i < $count; $i++) {
                    $demoRs[] = [
                        'txnTime' => $faker->dateTimeBetween($start, $end),
                        'card_type_id' => $cardType->getId(),
                        'txnAmountUSD' => $faker->numberBetween(100, 300000),
                    ];
                }
            }
            $rs = array_merge($rs, $demoRs);
        }

        foreach ($rs as $r) {
            $key = $r['card_type_id'];
            if (!isset($ids[$key])) {
                $ids[$key] = [
                    'spend' => 0,
                ];
            }
            $ids[$key]['spend'] += $r['txnAmountUSD'];
        }

        $result = [
            'ids' => array_keys($ids),
            'data' => [],
        ];
        foreach ($ids as $date => $meta) {
            $result['data'][] = $meta['spend'];
        }
        $usageType = [];
        $dates = $result['ids'];
        foreach ($dates as $date) {
            /** @var CardType $ct */
            $ct = $this->getDoctrine()
                ->getRepository(\CoreBundle\Entity\CardType::class)
                ->find($date);
            $usageType[] = $ct->getName();
        }
        $result['usageType'] = $usageType;
        return new SuccessResponse($result);
    }

    public function getSearchParams(Request $request) {
        $option = [];
        if ($request) {
            $all = array_merge(
                $request->query->all(),
                $request->request->all()
            );

            //Prepare option array for search function
            $option = [];
            $option['cardBalanceFrom'] = $all['cardbalancemin'] ?? '';
            $option['cardBalanceTo'] = $all['cardbalancemax'] ?? '';
            $option['localBalanceFrom'] = $all['localbalancemin'] ?? '';
            $option['localBalanceTo'] = $all['localbalancemax'] ?? '';
            $option['LoadPaymentsTimesA'] = $all['LoadPaymentsTimesA'] ?? '';
            $option['LoadPaymentsTimesB'] = $all['LoadPaymentsTimesB'] ?? '';
            $option['LoadPaymentsAmountA'] = $all['LoadPaymentsAmountA'] ?? '';
            $option['LoadPaymentsAmountB'] = $all['LoadPaymentsAmountB'] ?? '';
            $option['CardTransactionsTimesA'] = $all['CardTransactionsTimesA'] ?? '';
            $option['CardTransactionsTimesB'] = $all['CardTransactionsTimesB'] ?? '';
            $option['CardTransactionsTimesDateA'] = $all['CardTransactionsTimesDateA'] ?? '';
            $option['CardTransactionsTimesDateB'] = $all['CardTransactionsTimesDateB'] ?? '';
            $option['CardTransactionsAmountA'] = $all['CardTransactionsAmountA'] ?? '';
            $option['CardTransactionsAmountB'] = $all['CardTransactionsAmountB'] ?? '';
            $option['CardTransactionsAmountDateA'] = $all['CardTransactionsAmountDateA'] ?? '';
            $option['CardTransactionsAmountDateB'] = $all['CardTransactionsAmountDateB'] ?? '';
            $option['affiliate_tenant_type'] = $all['affiliate_tenant_type'] ?? '';
            $option['affiliate_select'] = $all['affiliate_select'] ?? '';
            $option['merchant_custom_name'] = $all['merchant_custom_name'] ?? '';
            $option['email_status'] = $all['email_status'] ?? '';
            $option['vip'] = $all['vip'] ?? '';
            $option['ucCreatedFrom'] = $all['start'] ?? '';
            $option['ucCreatedTo'] = $all['end'] ?? '';
            $option['cardProgram'] = $all['cardProgram'] ?? '';
        }
        return $option;
    }

    /**
     * @Route("/admin/analytics/users")
     * @param Request      $request
     *
     * @return SuccessResponse
     */
    public function getUsers (Request $request)
    {
        return $this->getUsersCommon($request);
    }

    public function getUsersCommon (Request $request, QueryBuilder $q = null) {
        if (Bundle::isUsUnlocked()) {
            return $this->getUsersForUsUnlocked($request, $q);
        }

        if (!$q) {
            $repo = $this->em->getRepository(\SalexUserBundle\Entity\User::class);
            $option = $this->getSearchParams($request);
            $q = $repo->advancedSearch($option);
        }

        $activeQ = clone $q;
        $expr = $q->expr();
        $active = $activeQ->andWhere(
            $expr->orX(
                $expr->isNull('u.status'),
                $expr->like('u.status', ':status')
            )
        )->setParameter('status',User::STATUS_ACTIVE)
            ->select('count(distinct u.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $inActiveQ = clone $q;
        $inActive = $inActiveQ->andWhere('u.status = :status')
            ->setParameter('status', User::STATUS_INACTIVE)
            ->select('count(distinct u.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $bannedQ = clone $q;
        $banned = $bannedQ->andWhere('u.status = :status')
            ->setParameter('status', User::STATUS_BANNED)
            ->select('count(distinct u.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $closedQ = clone $q;
        $closed = $closedQ->andWhere('u.status = :status')
            ->setParameter('status', User::STATUS_CLOSED)
            ->select('count(distinct u.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $underReviewQ = clone $q;
        $underReview = $underReviewQ->andWhere('u.status = :status')
            ->setParameter('status', User::STATUS_UNDER_REVIEW)
            ->select('count(distinct u.id)')
            ->getQuery()
            ->getSingleScalarResult();

        if (Util::isDemo()) {
            $faker = Factory::create();
            $active = $faker->numberBetween(800, 3000);
            $inActive = $faker->numberBetween(100, 300);
            $banned = $faker->numberBetween(30, 80);
            $closed = $faker->numberBetween(50, 100);
            $underReview = $faker->numberBetween(100, 400);
        }

        $result['data'] = [$active, $inActive, $banned, $closed, $underReview];
        $result['types'] = ['Active', 'Inactive', 'Banned', 'Closed', 'Under review'];

        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/revenue")
     * @param Request $request
     * @return SuccessResponse
     */
    public function getRevenue (Request $request) {
        $this->tzUTC = Util::tzUTC();
        $this->tzUser = $this->getUser()->getDateTimeZone();

        $em = Util::em();
        $query = $em->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('ucl');
        $query->join('ucl.userCard', 'uc')
            ->join('uc.user', 'u')
            ->join('uc.card', 'c')
            ->join('c.cardProgram', 'cp')
            ->join('c.cardType', 'ct');
        $expr = $query->expr();
        $query->where($expr->in('ucl.type', ':types'))
            ->andWhere($expr->in('ucl.loadStatus', ':statuses'))
            ->andWhere($expr->isNotNull('u.affiliate'))
            ->andWhere($expr->in('cp', ':__cps'))
            ->setParameters([
                'types' => [
                    UserCardLoad::TYPE_LOAD_CARD,
                ],
                'statuses' => UserCardLoad::RECEIVED_STATUS_ARRAY,
                '__cps' => Util::user()->getOpenCardPrograms(),
            ]);

        $start = $request->get('start');
        if ($start) {
            $start = Util::timeUTC($start);
            $query->andWhere('ucl.loadAt >= :start')
                ->setParameter('start', $start);
        }
        $end = $this->getEndTime($request);
        if ($end) {
            $query->andWhere('ucl.loadAt < :end')
                ->setParameter('end', $end);
        }
        $cardProgram = $request->get('cardProgram');
        if ($cardProgram) {
            $query->andWhere('cp = :cardProgram')
                ->setParameter('cardProgram', $cardProgram);
        }
        $rs = $query->orderBy('ucl.loadAt')
            ->select('ucl.id, min(ucl.loadAt) loadAt, ucl.membershipCommissionUSD membershipCommissionUSD, ucl.loadCommissionUSD loadCommissionUSD')
            ->groupBy('uc.id')
            ->getQuery()
            ->getArrayResult();

        if (Util::isDemo()) {
            $faker = Factory::create();
            $count = $faker->numberBetween(300, 1000);
            for ($i = 0; $i < $count; $i++) {
                $rs[] = [
                    'id' => $faker->unique(),
                    'loadAt' => $faker->dateTimeBetween($start, $end)->format('c'),
                    'membershipCommissionUSD' => $faker->numberBetween(10, 1000),
                    'loadCommissionUSD' => $faker->numberBetween(10, 1000),
                ];
            }
        }

        $dates = [];
        $rangeType = $this->getRangeType($start, $end);
        $total = 0;
        foreach ($rs as $r) {
            $key = $this->getRangeKey($r['loadAt'], $rangeType);
            if (!isset($dates[$key])) {
                $dates[$key] = [
                    'total' => 0,
                ];
            }
            $fee = $r['membershipCommissionUSD'] + $r['loadCommissionUSD'];
            $dates[$key]['total'] = $dates[$key]['total'] + $fee;
            $total = $total + $fee;
        }
        $result = [
            'dates' => array_keys($dates),
            'data' => [],
        ];

        foreach ($dates as $date => $meta) {
            $result['data'][] = $meta['total'];
        }
        $result['totalFee'] = $total;
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/active-cards")
     * @param Request $request
     * @return SuccessResponse
     */
    public function activeCards (Request $request) {
        $start = $request->get('start');
        if ($start) {
            $start = Util::timeUTC($start);
        }
        $end = $this->getEndTime($request);

        $q = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)
            ->createQueryBuilder('uc');
        $q->where('uc.issued = :issued')
            ->andWhere('uc.status = :status')
            ->andWhere('uc.accountNumber is not null')
            ->setParameters([
                'issued' => 1,
                'status' => UserCard::STATUS_ACTIVE,
            ]);

        $this->query($q);

        $rs = $q->select('uc.id, uc.issuedAt')
            ->distinct()
            ->orderBy('uc.issuedAt')
            ->getQuery()
            ->getArrayResult();

        if (Util::isDemo()) {
            $demoRs = [];
            $faker = Factory::create();
            $count = $faker->numberBetween(30, 100);
            for ($i = 0; $i < $count; $i++) {
                $demoRs[] = [
                    'issuedAt' => $faker->dateTimeBetween($start, $end),
                ];
            }
            $rs = array_merge($rs, $demoRs);
            Util::usort($rs, [
                'issuedAt' => true,
            ]);
        }

        $dates = [];
        $user = $this->getUser();
        $tz = $user->getDateTimeZone();
        foreach ($rs as $r) {
            /** @var \DateTime $time */
            $time = $r['issuedAt'];
            if (!$time) {
                continue;
            }
            $time->setTimezone($tz);
            $date = $time->format(Util::DATE_FORMAT_SEARCH);
            if (!isset($dates[$date])) {
                $dates[$date] = [
                    'time' => Carbon::instance($time),
                    'sum' => 0,
                ];
            }
            $dates[$date]['sum']++;
        }

        $result = [];
        $sum = 0;
        $rangeType = $this->getRangeType($start, $end);
        foreach ($dates as $date => $meta) {
            $sum += $meta['sum'];
            /** @var Carbon $time */
            $time = $meta['time'];
            if ($start && $time->lt($start)) {
                continue;
            }
            if ($end && $time->gte($end)) {
                continue;
            }
            $key = $this->getRangeKey($date, $rangeType);
            if (!isset($result[$key])) {
                $result[$key] = 0;
            }
            $result[$key] = $sum;
        }
        $results = [
            'dates' => array_keys($result),
            'data' => [],
        ];

        foreach ($result as $date => $cnt) {
            $results['data'][] = $cnt;
        }
        return new SuccessResponse($results);
    }
    /**
     * @Route("/admin/analytics/card-fees")
     * @param Request $request
     * @return SuccessResponse
     */
    public function cardFees (Request $request) {
        if (Bundle::isUsUnlocked()) {
            return $this->getFeesForUsUnlocked($request);
        }

        $query = $this->em->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('ucl');
        $query->join('ucl.userCard', 'uc');
        $expr = $query->expr();
        $query->where($expr->in('ucl.type', ':types'))
            ->andWhere($expr->in('ucl.loadStatus', ':statuses'))
            ->setParameters([
                'types' => [
                    UserCardLoad::TYPE_LOAD_CARD,
                    UserCardLoad::TYPE_UNLOAD,
                ],
                'statuses' => UserCardLoad::RECEIVED_STATUS_ARRAY,
            ]);
        list($query, $start, $end) = $this->query($query, 'ucl.loadAt');

        $rs = $query->orderBy('ucl.loadAt')
            ->select('ucl.id, min(ucl.loadAt) loadAt, ucl.membershipFeeUSD membershipFeeUSD,
            ucl.replacementFeeUSD replacementFeeUSD, ucl.loadFeeUSD loadFeeUSD, ucl.unloadFeeUSD unloadFeeUSD')
            ->groupBy('uc.id')
            ->getQuery()
            ->getArrayResult();

        if (Util::isDemo()) {
            $faker = Factory::create();
            $count = $faker->numberBetween(300, 800);
            for ($i = 0; $i < $count; $i++) {
                $rs[] = [
                    'ucl.id' => $faker->unique(),
                    'loadAt' => $faker->dateTimeBetween($start, $end)->format('c'),
                    'membershipFeeUSD' => $faker->numberBetween(50, 1000),
                    'replacementFeeUSD' => $faker->numberBetween(50, 1000),
                    'loadFeeUSD' => $faker->numberBetween(50, 1000),
                    'unloadFeeUSD' => $faker->numberBetween(50, 1000),
                ];
            }
        }

        $dates = [];
        $rangeType = $this->getRangeType($start, $end);
        $total = 0;
        $membershipFee = 0;
        $replacementFee = 0;
        $loadFee = 0;
        $unloadFee = 0;
        foreach ($rs as $r) {
            $key = $this->getRangeKey($r['loadAt'], $rangeType);
            if (!isset($dates[$key])) {
                $dates[$key] = [
                    'total' => 0,
                ];
            }
            $membershipFee += $r['membershipFeeUSD'];
            $replacementFee += $r['replacementFeeUSD'];
            $loadFee += $r['loadFeeUSD'];
            $unloadFee += $r['unloadFeeUSD'];
            $fee = $r['membershipFeeUSD'] + $r['replacementFeeUSD'] + $r['loadFeeUSD'] + $r['unloadFeeUSD'];
            $dates[$key]['total'] += $fee;
            $total = $total + $fee;
        }
        $result = [
            'dates' => array_keys($dates),
            'total' => [],
        ];

        foreach ($dates as $date => $meta) {
            $result['total'][] = $meta['total'];
        }
        $result['totalFee'] = $total;
        $result['data'] = array_map(function ($fee) {
            return Money::formatAmount($fee, 'USD', '');
        }, [$membershipFee, $replacementFee, $loadFee, $unloadFee]);
        $result['types'] = ['Membership', 'Replacement', 'Load', 'Unload'];
        return new SuccessResponse($result);
    }

    /**
     * @Route("/admin/analytics/merchant-with-rate")
     * @param Request $request
     * @return SuccessResponse
     */
    public function merchantNew(Request $request) {
        $em = Util::em();
        $q = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('ucl.merchant', 'm');
        $rateQ = clone $q;

        $this->query($q, 'ucl.txnTime');
        $total = (clone $q)->select('count(distinct ucl)')
            ->getQuery()->getSingleScalarResult();
        $rs = (clone $q)->select('count(distinct ucl) value', 'm.merchantCustName name')
            ->groupBy('m.merchantCustName')
            ->orderBy('value', 'desc')
            ->getQuery()
            ->getArrayResult();

        if (Util::isDemo()) {
            $faker = Factory::create();
            $merchants = $this->em->getRepository(\CoreBundle\Entity\Merchant::class)
                ->createQueryBuilder('m')
                ->select('m.merchantCustName')
                ->distinct()
                ->getQuery()
                ->getArrayResult();
            $count = $faker->numberBetween(8, 15);
            $merchants = $faker->randomElements($merchants, $count);
            $merchants = array_map(function ($merchant) {
                return $merchant['merchantCustName'];
            }, $merchants);
            for ($i = 0; $i < $count; $i++) {
                $rs[] = [
                    'value' => $faker->numberBetween(100, 400),
                    'name' => $merchants[$i],
                ];
            }
        }

        $result = $this->retrieveLimitedValuesN($rs);
        $this->queryN($rateQ, 'ucl.txnTime');
        $lastTotal = (clone $rateQ)->select('count(distinct ucl)')
            ->getQuery()->getSingleScalarResult();

        $lastResult = [];
        foreach ($result as $r) {
            $name = $r['name'];
            if ($name === 'Other') {
                continue;
            }
            $rateQ->andWhere('m.merchantCustName = :name')
                ->setParameter('name', $name);
            $a = $rateQ->select('count(distinct ucl) value, m.merchantCustName name')
                ->groupBy('m.merchantCustName')
                ->orderBy('value', 'desc')
                ->getQuery()
                ->getResult();
            if (count($a)) {
                $element = array_first($a);
            } else {
                $element = [
                    'value' => 0,
                    'name'  => $name,
                ];
            }
            $lastResult[] = [
                'value' => (int)$element['value'],
                'name'  => $element['name'],
            ];
        }

        foreach ($lastResult as $l) {
            $lastTotal -= $l['value'];
        }
        $other = $lastTotal;
        $lastResult[] = [
            'value' => $other,
            'name' => 'Other',
        ];
        foreach ($result as $i =>$r) {
            $lastR = $lastResult[$i];
            $lastV = $lastR['value'];
            $rV = $r['value'];
            $proportion = $total ? number_format($rV / $total * 100, 2) : 0;
            if ($lastV !== 0) {
                $rate = number_format(abs($rV - $lastV) / $lastV  * 100);
            } else {
                $rate = '∞';
            }

            if ($lastV < $rV) {
                $trend = 1;
            } else {
                $trend = 0;
            }
            $result[$i]['name'] = wordwrap($r['name'], 25, "\n", true);
            $result[$i]['proportion'] = $proportion;
            $result[$i]['trend'] = $trend;
            $result[$i]['rate'] = $rate;
        }
        return new SuccessResponse($result);
    }
    /**
     * @Route("/admin/analytics/mcc-with-rate")
     * @param Request $request
     * @return SuccessResponse
     */
    public function mccNew(Request $request) {
        $em = Util::em();
        $q = $em->getRepository(\CoreBundle\Entity\UserCardTransaction::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('ucl.merchant', 'm')
            ->join('m.merchantType', 'mt');
        $rateQ = clone $q;

        $this->query($q, 'ucl.txnTime');
        $total = (clone $q)->select('count(distinct ucl)')
            ->getQuery()->getSingleScalarResult();
        $rs = (clone $q)->select('count(distinct ucl) value', 'mt.merchantType name')
            ->groupBy('mt.merchantType')
            ->orderBy('value', 'desc')
            ->getQuery()
            ->getArrayResult();

        if (Util::isDemo()) {
            $faker = Factory::create();
            $merchants = $this->em->getRepository(\CoreBundle\Entity\MerchantType::class)
                ->createQueryBuilder('m')
                ->select('m.merchantType')
                ->distinct()
                ->getQuery()
                ->getArrayResult();
            $count = $faker->numberBetween(8, 15);
            $merchants = $faker->randomElements($merchants, $count);
            $merchants = array_map(function ($merchant) {
                return $merchant['merchantType'];
            }, $merchants);
            for ($i = 0; $i < $count; $i++) {
                $rs[] = [
                    'value' => $faker->numberBetween(200, 1000),
                    'name' => $merchants[$i],
                ];
            }
        }

        $result = $this->retrieveLimitedValuesN($rs);

        $this->queryN($rateQ, 'ucl.txnTime');
        $lastTotal = (clone $rateQ)->select('count(distinct ucl)')
            ->getQuery()->getSingleScalarResult();

        $lastResult = [];
        foreach ($result as $r) {
            $name = $r['name'];
            if ($name === 'Other') {
                continue;
            }
            $rateQ->andWhere('mt.merchantType = :name')
                ->setParameter('name', $name);
            $a = $rateQ->select('count(distinct ucl) value, m.merchantCustName name')
                ->groupBy('mt.merchantType')
                ->orderBy('value', 'desc')
                ->getQuery()
                ->getResult();
            if (count($a)) {
                $element = array_first($a);
            } else {
                $element = [
                    'value' => 0,
                    'name'  => $name,
                ];
            }
            $lastResult[] = [
                'value' => (int)$element['value'],
                'name'  => $element['name'],
            ];
        }

        foreach ($lastResult as $l) {
            $lastTotal -= $l['value'];
        }
        $other = $lastTotal;
        $lastResult[] = [
            'value' => $other,
            'name' => 'Other',
        ];
        foreach ($result as $i =>$r) {
            $lastR = $lastResult[$i];
            $lastV = $lastR['value'];
            $rV = $r['value'];
            $proportion = $total ? round($rV / $total * 100, 2) : 0;
            if ($lastV !== 0) {
                $rate = round(abs($rV - $lastV) / $lastV * 100) ;
            } else {
                $rate = '∞';
            }

            if ($lastV < $rV) {
                $trend = 1;
            } else {
                $trend = 0;
            }
            $result[$i]['name'] = wordwrap($r['name'], 25, "\n", true);
            $result[$i]['proportion'] = $proportion;
            $result[$i]['trend'] = $trend;
            $result[$i]['rate'] = $rate;
        }

        return new SuccessResponse($result);
    }
     /**
     * @Route("/admin/analytics/total-account-balance")
     * @param Request $request
     * @return SuccessResponse
     */
    public function totalAccountBalance (Request $request) {
      $start = $request->get('start');
      if ($start) {
          $start = Util::timeUTC($start);
      }
      $end = $this->getEndTime($request);

      $q = Util::em()->getRepository(\CoreBundle\Entity\UsuSystemDailyActivity::class)
          ->createQueryBuilder('usd');
      $qLoad = clone $q;
      $start = $request->get('start');
      if ($start) {
          $start = Util::timeUTC($start);
          $qLoad->andWhere('usd.date >= :start')
              ->setParameter('start', $start);
      }
      $end = $this->getEndTime($request);
      if ($end) {
          $qLoad->andWhere('usd.date < :end')
              ->setParameter('end', $end);
      }

      $rs = $qLoad->distinct()
                  ->orderBy('usd.date')
                  ->getQuery()
                  ->getResult();

      $dates = [];
      $data = [
        'totalIssuedCards' => [],
        'totalAccountBalance' => [],
        'usableSpendingLimit' => []
      ];

      foreach ($rs as $r) {
          /**  @var UsuSystemDailyActivity $r */
          $key = $this->getRangeKey($r->getDate(), 'Y-m-d');
          if (!isset($dates[$key])) {
              $dates[$key] = [];
          }
          if (!isset($data['totalIssuedCards'][$key])) {
            $data['totalIssuedCards'][$key] = 0;
          }
          $data['totalIssuedCards'][$key] = $r->getTotalIssuedCards();

          if (!isset($data['totalAccountBalance'][$key])) {
            $data['totalAccountBalance'][$key] = 0;
          }
          $data['totalAccountBalance'][$key] = Money::formatAmountToNumber($r->getTotalAccountBalance());

          if (!isset($data['usableSpendingLimit'][$key])) {
            $data['usableSpendingLimit'][$key] = 0;
          }
          $data['usableSpendingLimit'][$key] = Money::formatAmountToNumber($r->getUsableSpendingLimit());
      }
      $result = [
          'dates' => array_keys($dates),
          'chartData' => $data,
      ];

      return new SuccessResponse($result);
    }
}
