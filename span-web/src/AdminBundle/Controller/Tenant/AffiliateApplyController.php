<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 02/03/2018
 * Time: 16:20
 */

namespace AdminBundle\Controller\Tenant;


use AdminBundle\Controller\BaseController;
use AdminBundle\Response\MsgResponse;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\AffiliateApply;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\Tenant;
use CoreBundle\Entity\TenantUser;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class AffiliateApplyController extends BaseController
{
    use DbTrait;

    /**
     * DefaultController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_AFFILIATE_APPLICATIONS);
    }

    /**
     * @Route("/admin/tenant/affiliate-apply", name="admin_affiliate_apply")
     * @throws \LogicException
     */
    public function index()
    {
        $paginator  = $this->get('knp_paginator');
        return $this->render('@Admin/Tenant/AffiliateApply/index.html.twig', [
            'list' => $paginator->paginate([]),
        ]);
    }

    public function query(Request $request)
    {
        $query = $this->em->getRepository(\CoreBundle\Entity\AffiliateApply::class)
            ->createQueryBuilder('aa');

        $params = new QueryListParams($query, $request, 'aa');
        $params->orderBy = [
            'aa.time' => 'desc',
        ];
        return $this->queryListForPaginator($params);
    }

    /**
     * @Route("/admin/tenant/affiliate-apply-list/{page}/{limit}")
     * @param Request $request
     * @param int $page
     * @param int $limit
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \LogicException
     */
    public function list(Request $request, $page = 1, $limit = 10)
    {
        $paginator  = $this->get('knp_paginator');
        $query = $this->query($request);
        return $this->render('@Admin/Tenant/AffiliateApply/list.html.twig', [
            'list' => $paginator->paginate($query, $page, $limit),
        ]);
    }

    /**
     * @Route("/admin/tenant/affiliate-apply/{apply}/detail")
     * @param AffiliateApply $apply
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function detail(AffiliateApply $apply)
    {
        return $this->render('@Admin/Tenant/AffiliateApply/detail.html.twig', [
            'apply' => $apply,
        ]);
    }

    /**
     * @Route("/admin/tenant/affiliate-apply/{apply}/decline", methods={"POST"})
     * @param AffiliateApply $apply
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function decline(AffiliateApply $apply)
    {
        $apply->setStatus(AffiliateApply::STATUS_DECLINED);
        Util::persist($apply);

        Email::sendWithTemplate([
            $apply->getEmail() => $apply->getFirstName() . ' ' . $apply->getLastName(),
        ], Email::TEMPLATE_AFFILIATE_APPLY_DECLINED, [
            'name' => $apply->getFirstName() . ' ' . $apply->getLastName(),
        ]);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/tenant/affiliate-apply/{apply}/delete", methods={"POST"})
     * @param AffiliateApply $apply
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     */
    public function delete(AffiliateApply $apply)
    {
        $this->em->remove($apply);
        $this->em->flush();

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/tenant/affiliate-apply/{apply}/approve", methods={"GET"})
     * @param AffiliateApply $apply
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function approvePage(AffiliateApply $apply)
    {
        if ($apply->getStatus() === AffiliateApply::STATUS_APPROVED) {
            return new MsgResponse('This application had already been approved!');
        }
        return $this->render('@Admin/Tenant/AffiliateApply/approve.html.twig', [
            'apply' => $apply,
            'types' => Affiliate::getTypes(),
            'tenants' => Tenant::findAll(),
        ]);
    }

    /**
     * @Route("/admin/tenant/affiliate-apply/{apply}/approve", methods={"POST"})
     * @param AffiliateApply $apply
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function approve(AffiliateApply $apply, Request $request)
    {
        $u = $this->em->getRepository(\SalexUserBundle\Entity\User::class)
            ->findOneBy([
                'email' => $apply->getEmail(),
            ]);
        $newUser = true;
        if ($u) {
            $newUser = false;
            $apply->connectToUser($u);
        } else {
            $u = $apply->createUser();
        }

        foreach ([Role::ROLE_CONSUMER, Role::ROLE_AFFILIATE] as $item) {
            $u->ensureRole($item);
        }
        Util::persist($u);

        $all = $request->request->all();
        $source = $all['tenant_source'];
        if ($source === 'user') {
            $affiliate = new Affiliate();
            $affiliate->setAdmin($u)
                ->setAffName($u->getName())
                ->setPayoutMethod(Affiliate::PAYOUT_METHOD_CONTACT)
                ->setPayoutContact($u)
                ->persist();
        } else {
            if ($source === 'exist') {
                $tenant = $this->em->getRepository(\CoreBundle\Entity\Tenant::class)
                    ->find($all['tenant']);
            } else {
                $class = Tenant::getClassOfType($all['tenant_type']);
                /** @var Tenant $tenant */
                $tenant = new $class();
                $tenant->setName($all['tenant_name']);
                Util::persist($tenant);
            }

            $main = $tenant->getContacts()->filter(function (TenantUser $tenantUser) {
                return $tenantUser->getMain();
            });
            $tu = new TenantUser();
            $tu->setTenant($tenant);
            $tu->setUser($u);
            $tu->setMain($main->isEmpty());
            Util::persist($tu);

            $affiliate = $tenant->getAffiliate();
            if (!$affiliate) {
                $affiliate = new Affiliate();
                $affiliate->setTenant($tenant)
                    ->setAffName($tenant->getName())
                    ->setPayoutMethod(Affiliate::PAYOUT_METHOD_CONTACT)
                    ->setPayoutContact($u)
                    ->persist();
            }
        }

        $apply->setStatus(AffiliateApply::STATUS_APPROVED)
            ->setUser($u)
            ->setAffiliate($affiliate);
        Util::persist($apply);

        $params = [];
        if ($newUser) {
            $password = Util::generatePassword();
            $u->setPassword(Util::encodePassword($u, $password));
            $params['password'] = $password;
        }
        Util::persist($u);
        Email::sendWithTemplateToUser($u, Email::TEMPLATE_AFFILIATE_APPLY_APPROVED, $params);

        return new MsgResponse('Successfully approved and added to tenant!', true,
            '/admin/tenant/affiliate-apply/' . $apply->getId() . '/detail');
    }
}