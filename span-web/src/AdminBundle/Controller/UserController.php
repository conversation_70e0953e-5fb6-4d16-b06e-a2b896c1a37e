<?php

namespace AdminB<PERSON>le\Controller;

use AdminBundle\Controller\Developer\DefaultController;
use AdminBundle\Form\Type\UserSecurityType;
use AdminBundle\Response\FailedMsgResponse;
use AdminBundle\Response\MsgResponse;
use Carbon\Carbon;
use CoreBundle\Attribute\Template;
use CoreBundle\Constant\IdentityType;
use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\BaseState;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\IpUsage;
use CoreBundle\Entity\KycProvider;
use CoreBundle\Entity\Module;
use CoreBundle\Entity\Notes;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\State;
use CoreBundle\Entity\UserBillingAddress;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Entity\UserPin;
use CoreBundle\Entity\UserPinLog;
use CoreBundle\Entity\UserToken;
use CoreBundle\Repository\EmailRepository;
use CoreBundle\Repository\UserCardLoadRepository;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\RoleService;
use CoreBundle\Services\UserService;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\JwtUtil;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\S3Storage;
use CoreBundle\Utils\Traits\ExcelTrait;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use EsSoloBundle\Services\EsSoloService;
use FOS\UserBundle\Util\TokenGenerator;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Form\UserType;
use SalexUserBundle\Mailer\SwiftMailer;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Mautic\MauticEventService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\UserService as UsuUserService;
use UsUnlockedBundle\Services\Sumsub\SumsubService;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\PayPal\PayPalAPI;

/**
 * UserManager
 * Class UserController
 * @package AdminBundle\Controller
 */
class UserController extends BaseController
{
    use ExcelTrait;

    /**
     * UserController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->authPermission(Module::ID_USER, [
            '/admin/logout',
            '/admin/user_modify/profile',
            '/admin/user_modify/checkOldPsd',
            '/admin/profile-modify/security',
            '/admin/user/generate-api-token',
            '/admin/user/generate-api-keys'
        ]);
    }

    /**
     * @Route("/admin/logout", name="admin_logout")
     */
    public function logoutAction(Request $request)
    {
        return Util::clearCookie($request);
    }

    public function getSearchFilters()
    {
        $request = $this->request;
        $em = $this->em;
        $user = $this->authAdmin();

        return array(
            'keyword'        => $request->get('keyword', ''),
            'processor'      => $em->getRepository(\CoreBundle\Entity\Processor::class)->processors(),
            'programmanager' => $em->getRepository(\CoreBundle\Entity\ProgramManager::class)->programmanagers(),
            'countrys'       => $em->getRepository(\CoreBundle\Entity\Country::class)->countries(),
            'states'         => $em->getRepository(\CoreBundle\Entity\State::class)->findAllMinFields(),
            'cardtype'       => $em->getRepository(\CoreBundle\Entity\CardType::class)->cardTypes(),
            'roles'          => $em->getRepository(\CoreBundle\Entity\Role::class)->roles(),
            'platforms'      => Util::ascName($user->getOpenPlatforms()),
            'cardprograms'   => Util::ascName($user->getOpenCardPrograms()),
            'reshipperaddre' => $em->getRepository(\CoreBundle\Entity\Reshipper::class)->reshippers(),
            'regions'        => $em->getRepository(\CoreBundle\Entity\Country::class)->regions(),
            'affiliates'     => $em->getRepository(\CoreBundle\Entity\Affiliate::class)->findAll(),
            'kycProviders'   => $em->getRepository(KycProvider::class)->findAll(),
        );
    }

    /**
     * @Route("/admin/user_index/{page}/{limit}",defaults={"page" = 1,"limit" = 10},name="admin_user")
     */
    #[Template()]
    public function indexAction(Request $request, $page, $limit)
    {
        $this->authAdmin();

        $paginator  = $this->get('knp_paginator');
        $pagination = $paginator->paginate([], $page, $limit);

        return array_merge($this->getSearchFilters(), array(
            'users'          => $pagination,
        ));
    }

    /**
     * @Route("/admin/user_filter_page/", name="admin_user_filter_page")
     */
    public function filterPageAction(Request $request)
    {
        $keyword = $request->get("filter");
        $page    = $request->get("page", 1);

        $usersQuery = $this->getDoctrine()->getRepository(\SalexUserBundle\Entity\User::class)->createQueryBuilder("u");
        if ($keyword) {
            $usersQuery = $usersQuery->where("u.username LIKE :keyword")->setParameters(array(
                "keyword" => '%'.$keyword.'%',
            ));
        }

        $paginator  = $this->get('knp_paginator');
        $pagination = $paginator->paginate($usersQuery, $page, 2);

        return $this->render("@Admin/User/page.html.twig", array( "users" => $pagination ));
    }

    public function birthday($birthday){
        $age = strtotime($birthday);
        if($age === false){
            return false;
        }
        list($y1,$m1,$d1) = explode("-",date("Y-m-d",$age));
        $now = strtotime("now");
        list($y2,$m2,$d2) = explode("-",date("Y-m-d",$now));
        $age = (int)$y2 - (int)$y1;
        if((int)($m2.$d2) < (int)($m1.$d1))
            $age -= 1;
        return $age;
    }

    /**
     * @Route("/admin/user_new/new",name="admin_user_new")
     * @param Request $request
     * @return ErrorResponse|array|RedirectResponse|Response
     */
    #[Template()]
    public function newAction(Request $request) {
        $this->authPermission(Module::ID_USER_ADD_USER);

        $user = new User();
        $form = $this->createForm(UserType::class, $user, array(
            'action' => $this->generateUrl('admin_user_new'),
            'method' => 'POST',
        ));

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);
            $flagsname = '';
            $email = $form->getData()->getEmail();
            if (is_null($email)) {
                return array( 'users' => $user, 'form' => $form->createView(), 'errorFlag' => false );
            }
            $em = $this->getDoctrine()->getManager();
            $repo = $em->getRepository(\SalexUserBundle\Entity\User::class);
            $other = $repo->findOneBy(['username' => $form->getData()->getUsername()]);
            if ($other) {
                return new ErrorResponse('Username duplicates. Failed to create new user.');
            }
            $other = User::findPlatformUserByEmail($email);
            if ($other) {
                return new ErrorResponse('Email duplicates. Failed to create new user.');
            }

            if (array_key_exists('flagsn', $_POST)) {
                foreach ($_POST['flagsn'] as $flagsn) {
                    $flagsname = $flagsname.$flagsn.';';
                }
                $user->setFlagsname($flagsname);
            }
            $firstPSD  = $_POST['salex_user_profile']['plainPassword']['first'];
            $secondPSD = $_POST['salex_user_profile']['plainPassword']['second'];
            if ($firstPSD != $secondPSD) {
                return $this->render('@Admin/User/new.html.twig', array(
                    'users'     => $user,
                    'form'      => $form->createView(),
                    'errorFlag' => true,
                ));
            }
            $user->setEnabled(true);
            $user->setRoles([ 'ROLE_ADMIN' ]);
            $user->setSource(User::SOURCE_ADMIN);
            $user->setRegisterStep(RegisterStep::CONSUMER_PERSONAL_INFO_ADDED);

            $platform = Util::platform();
            if ($platform) {
                $user->addAccessiblePlatform($platform);
            }

            $em->persist($user);
            $em->flush();

            EsSoloService::ensureApiInvokerOnUser($user);

            return $this->redirect($this->generateUrl('admin_user'));
        }

        return [
            'users'     => $user,
            'form'      => $form->createView(),
            'errorFlag' => FALSE,
            'roles'     => RoleService::filter(),
        ];

    }

    /**
     * @Route("/admin/user_modify/modify",name="admin_user_modify")
     */
    #[Template()]
    public function modifyAction(Request $request)
    {
        Util::longRequest();

        $this->authAdmin();

        $page      = $request->get("page", 1);
        $em        = $this->getDoctrine()->getManager();
        $flagsname = '';
        /** @var User $user */
        $user       = $em->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array(
            "id" => $request->get("user_id"),
        ));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }

        $this->own($user);

        $accessibleCardPrograms = $this->getUser()->getOpenCardPrograms();

        //Affiliate
        $affiliateTenantType = '';
        $affiliates = '';
        /** @var Affiliate $affiliate */
        $affiliate = $user->getAffiliate();
        if($affiliate)
        {
            $affiliateTenantType = $affiliate->getAffType();
            $affiliates = $em->getRepository(\CoreBundle\Entity\Affiliate::class)->findBy(array('affType'=>$affiliateTenantType));
        }

        $notes      = $em->getRepository(\CoreBundle\Entity\Notes::class)->findBy(array( 'toname' => $user->getId() ),
            array( 'createdtime' => 'DESC' ));
        $reshipper      = $em->getRepository(\CoreBundle\Entity\Reshipper::class)->findBy(array(
            'accountValidation'=>false,
            'idValidation'=>false,
        ));
        // get platform partner role
        $platformRole =  array_map(function (Role $r)  {
          $item = $r->getId();
          return $item;
        },Util::em()->getRepository(\CoreBundle\Entity\Role::class)
                ->createQueryBuilder('r')
                ->where('r.name like :name')
                ->setParameter('name', '%Platform Partner%')
                ->getQuery()
                ->getResult());

        if ($user) {
            // Fill role values first
            $profile = $request->request->all('salex_user_profile');
            if (!isset($profile['teams'])) {
                $profile['teams'] = $user->getTeams()->map(function (Role $_role) {
                    return $_role->getId();
                })->toArray();
                $request->request->set('salex_user_profile', $profile);
            } else {
               // set partner need check unique
               $oldRole = $user->getTeams()->map(function (Role $_role) {
                    return $_role->getId();
                })->toArray();
               if (!empty(array_intersect($platformRole,$oldRole)) && empty(array_intersect($oldRole, $profile['teams']))) {
                  return new ErrorResponse("The Partner user can't change role!");
               }
               if (!empty(array_intersect($platformRole,$profile['teams']))) {
                  if ( count($profile['teams']) > 1) {
                    return new ErrorResponse("The Partner user only has one role!");
                  }
                  $qb = Util::em()->getRepository(\SalexUserBundle\Entity\User::class)
                  ->createQueryBuilder('u')
                  ->leftJoin('u.teams', 'r');
                  $expr = $qb->expr();
                  if ( Util::em()->getRepository(\SalexUserBundle\Entity\User::class)
                      ->createQueryBuilder('u')
                      ->leftJoin('u.teams', 'r')
                      ->where(Util::expr()->eq('r.id', ':id'))
                      ->setParameter('id', $profile['teams'][0] )
                      ->andWhere(Util::expr()->neq('u.id', ':uid'))
                      ->setParameter('uid', $user->getId() )
                      ->select('count(distinct u)')
                      ->getQuery()
                      ->getSingleScalarResult()) {
                      return new ErrorResponse('There is only one user can set the partner role!');
                  }
               }
            }

            $typeArr       = array(
                IdentityType::DL  => IdentityType::DL,
                IdentityType::PSP => IdentityType::PSP,
                IdentityType::ID  => IdentityType::ID,
            );
            $cardStatusArr = array(
                UserCard::STATUS_ACTIVE   => UserCard::STATUS_ACTIVE,
                UserCard::STATUS_INACTIVE => UserCard::STATUS_INACTIVE,
                UserCard::STATUS_CLOSED => UserCard::STATUS_CLOSED,
            );
            $bannedReasons = $this->getDoctrine()->getRepository(\CoreBundle\Entity\Config::class)
                ->getArray(Config::CONFIG_USER_BANNED_REASON);
            /** @var UserIdVerify $idVerify */
            $idVerify   = $user->getIdVerify();
            $countryArr = array();
            //TCSPAN2-33 country list with card program
            /*$countries  = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->findBy([], [
                'name' => 'asc',
            ]);*/
            $cardProgramIds = [];
            foreach ($user->getCards() as $card)
            {
                /** @var CardProgram $cp */
                $cp = $card->getCard()->getCardProgram();
                if (!Util::includes($accessibleCardPrograms, $cp)) {
                    continue;
                }
                $cardProgramIds[] = $cp->getId();
            }
            $countries = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->getCountriesWithCardPrograms($cardProgramIds);
            /** @var Country $country */
            foreach ($countries as $country) {
                $countryArr[ucwords($country->getName())] = $country->getId();
            }
            $idVerifyStatusArr = array(
                UserIdVerify::STATUS_NONE       => UserIdVerify::STATUS_NONE,
                UserIdVerify::STATUS_INVALID    => UserIdVerify::STATUS_INVALID,
                UserIdVerify::STATUS_INCOMPLETE => UserIdVerify::STATUS_INCOMPLETE,
                UserIdVerify::STATUS_EXPIRED    => UserIdVerify::STATUS_EXPIRED,
                UserIdVerify::STATUS_ACCEPTED   => UserIdVerify::STATUS_ACCEPTED,
                UserIdVerify::STATUS_MANUAL_ACCEPTED   => UserIdVerify::STATUS_MANUAL_ACCEPTED,
            );
            $formOptions = array(
                'action' => $this->generateUrl('admin_user_modify'),
                'method' => 'POST',
                //TCSPAN2-33 country list with card program
                'attr' => [
                    'countries' => $countryArr,
                ],
            );
            if (Bundle::isRootOrUsu()) {
                unset($formOptions['attr']['countries']);
            }
            $form = $this->createForm(UserType::class, $user, $formOptions);
            $formsecurity      = $this->createForm(UserSecurityType::class, $user);
            $node              = new Notes();
            $formnote          = $this->createFormBuilder($node)->add('notes', TextType::class, array(
                'label' => 'Notes',
            ))->getForm();
            $userEmail         = $user->getEmail();
            $emails            = EmailRepository::getEmails($userEmail);
            /*$paginationEmail   = $paginator->paginate($emails, $page, 5);*/

            $cards       = $user->getIssuedCards()->filter(function (UserCard $uc) use ($accessibleCardPrograms) {
                return Util::includes($accessibleCardPrograms, $uc->getCard()->getCardProgram());
            });
            $validateCards = [];
            /** @var UserCard $uc */
            foreach ($cards as $uc) {
                /** @var CardProgram $cp */
                $cp = $uc->getCard()->getCardProgram();
                if (!Util::includes($accessibleCardPrograms, $cp)) {
                    continue;
                }
                $d = Util::e2a($uc);
                $d['localBalance'] = $uc->getLocalBalance();
                $d['status'] = $uc->getStatus();
                $validateCards[] = $d;
            }
            $loadCardArr = array();
            Util::disableSoftDeletable();
            /** @var UserCardLoadRepository $repo */
            $repo = Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class);
            $loadCards = $repo->findLoadByStatusAndType(UserCardLoad::TYPE_LOAD_CARD, null, $user, 0);
            foreach ($cards as $card) {
                /** @var UserCardLoad $loadCard */
                foreach ($loadCards as $loadCard) {
                    if (Util::eq($loadCard->getUserCard(), $card)) {
                        $loadCardArr[] = $loadCard;
                    }
                }
            }
            $rainUserId = UsuUserService::getRainUserId($user);
            $rainKycPending = Util::meta($user, 'rainKycPending');
            $rainApplicationReason = Util::meta($user, 'applicationReason');
            $IPArr               = $em->getRepository(\CoreBundle\Entity\IpUsage::class)->findBy(array( 'users' => $user ));
            $result = [
                '_user'            => $this->getUser(),
                'discounts'        => [],
                'validateCards'    => $validateCards,
                'user'             => $user,
                'cardStatusArr'    => $cardStatusArr,
                'bannedReasons'    => $bannedReasons,
                'closureReasons'   => Config::array(Config::CONFIG_USER_CLOSURE_REASON),
                'idVerityTypeArr'  => $typeArr,
                'countries'        => $countryArr,
                'idVerityArr'      => $idVerifyStatusArr,
                'idVerities'       => Util::asc( $user->getIdVerifies() ),
                'IPArr'            => $IPArr,
                'loadCardArr'      => $loadCardArr,
                'cards'            => $cards,
                /*'emails'           => $paginationEmail,*/
                'emails'           => $emails,
                'form'             => $form->createView(),
                'formsecurity'     => $formsecurity->createView(),
                'formnote'         => $formnote->createView(),
                'user_id'          => $request->get("user_id"),
                'flags'            => $user->getFlagsname(),
                'toname'           => $user->getUsername(),
                'fromname'         => $this->getUser()->getUsername(),
                'notes'            => $notes,
                'addressn'         => $user->getAddress(),
                'addressLine'      => $user->getAddressline(),
                'countyid'         => $user->getCountryid(),
                'stateid'          => $user->getStateid(),
                'phone'            => $user->getPhone(),
                'mobilephone'      => $user->getMobilephone(),
                'workphone'        => $user->getWorkphone(),
                'zip'              => $user->getZip(),
                'gender'           => $user->getGender(),
                'status'           => $user->getStatus(),
                'locked_status'    => $user->getLockedStatus(),
                'birthday'         => $user->getBirthday(),
                'errorInfo'        => false,
                'isConsumer'       => $user->isConsumer(),
                'allowResetPwd'    => !$user->getParticipatedPlatforms()->isEmpty(),
                'reshipper'        => $reshipper,
                'affiliates'       => $affiliates,
                'affiliate_tenant_type' => $affiliateTenantType,

                'roles'     => RoleService::filter(),
                'sumsubKycStatus' => $user->getSumsubKycStatus(),
                'sumsubApplicantId' => $user->getSumsubApplicantId(),
                'rainUserId' => $rainUserId,
                'rainUserCanReset' => $user->isUsuSumSubIdVerified() &&
                                      $rainUserId &&
                                      $rainKycPending && $user->getRainApplicationStatus() != 'pending',
                'rainKycError' => ($rainUserId && $rainKycPending && $rainApplicationReason) ?
                    $rainApplicationReason :
                    null,
                'rainApplicationStatus' => $user->getRainApplicationStatus(),
                'paypalSubscription' => UsuUserService::getPayPalSubscriptionSummary($user),
                'paypalSubscriptionId' => UsuUserService::getPayPalSubscriptionId($user),
            ];

            if ($request->isMethod('POST')) {
                // close cards in 'cards info'
                if (array_key_exists('cardStatusId', $_POST)) {
                    $cardStatusIdArr = explode(',', $_POST['cardStatusId']);
                    $cardNums = null;
                    $uc = null;
                    foreach ($cardStatusIdArr as $cardId) {
                        /** @var UserCard $userCard */
                        $userCard = $em->getRepository(\CoreBundle\Entity\UserCard::class)->find($cardId);
                        $uc = $userCard;
                        $userCard->setStatus(UserCard::STATUS_INACTIVE);
                        $cardNums = $cardNums . $userCard->getPan('mask') . ',';
                        $em->persist($userCard);
                        $em->flush();

                        if ($userCard->isUsUnlocked() && $userCard->isOneTimeCard()) {
                            $service = PrivacyAPI::getForUserCard($userCard);
                            if ($userCard->getAccountNumber()) {
                                $service->closeCard($userCard);
                            }
                            $userCard->setStatus(UserCard::STATUS_CLOSED, false, false)
                                ->persist();
                        }
                    }
                    $template = Email::TEMPLATE_CARD_CLOSED;
                    Email::sendWithTemplateToUser($user, $template, [
                        'card' => rtrim($cardNums,','),
                    ], $uc ? $uc->getCard()->getCardProgram() : null);
                    return $result;
                }
                // change load history table.
                if (array_key_exists('cardIndex', $_POST)) {
                    $index = (int) $_POST['cardIndex'];
                    $targetUc = $cards[$index];
                    $loadCardArr = array();
                    foreach ($loadCards as $loadCard) {
                        if (Util::eq($loadCard->getUserCard(), $targetUc)) {
                            $loadCardArr[] = $loadCard;
                        }
                    }
                    return $this->render('@Admin/User/loadHistoryList.html.twig',[
                        'loadCardArr' => $loadCardArr,
                    ]);
                }
                $form->handleRequest($request);
                if (is_null($form->getData()->getEmail())) {
                    return $result;
                }

                // if ($form->isValid()) {
                if (array_key_exists('flagsn', $_POST)) {
                    foreach ($_POST['flagsn'] as $flagsn) {
                        $flagsname = $flagsname.$flagsn.';';
                    }
                    $user->setFlagsname($flagsname);
                }
                $changedIdVerifyFlag = false;
                $newIdVerify = new UserIdVerify();
                $newIdVerify->setUser($user);
                if ($idVerify) {
                    $newIdVerify->setCountry($idVerify->getCountry());
                    $newIdVerify->setProvider($idVerify->getProvider());
                    $newIdVerify->setType($idVerify->getType());
                    $newIdVerify->setIssueAt($idVerify->getIssueAt());
                    $newIdVerify->setExpireAt($idVerify->getExpireAt());
                    $newIdVerify->setStatus($idVerify->getStatus());
                    $newIdVerify->setResult($idVerify->getResult());
                    $newIdVerify->setBornAt($idVerify->getBornAt());
                    $newIdVerify->setNumber($idVerify->getNumber());
                    $newIdVerify->setDisabledFields($idVerify->getDisabledFields());
                    foreach ($idVerify->getFiles() as $file) {
                        $newIdVerify->addFile($file);
                    }
                } else {
                    $newIdVerify->setProvider(KycProvider::find(KycProvider::NETVERIFY));
                }

                if (array_key_exists('idVerityCountry', $_POST)) {
                    $idCountry = $em->getRepository(\CoreBundle\Entity\Country::class)->find($_POST['idVerityCountry']);
                    if (!$idVerify || !Util::eq($idVerify->getCountry(), $idCountry)) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setCountry($idCountry);
                }
                if (array_key_exists('idVerityStatus', $_POST)) {
                    if (!$idVerify || $idVerify->getStatus() !== $_POST['idVerityStatus']) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setStatus($_POST['idVerityStatus']);
                }
                if (array_key_exists('idVerityType', $_POST)) {
                    if (!$idVerify || $idVerify->getType() !== $_POST['idVerityType']) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setType($_POST['idVerityType']);
                }
                if (array_key_exists('expire_date', $_POST)) {
                    if (!$idVerify || !Util::eq($idVerify->getExpireAt(), date_create($_POST['expire_date']))) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setExpireAt(date_create($_POST['expire_date']));
                    if (!$idVerify || !$_POST['expire_date']) {
                        $newIdVerify->setExpireAt(new Carbon('+30 years'));
                    }
                }
                if (array_key_exists('issue_date', $_POST)) {
                    if (!$idVerify || !Util::eq($idVerify->getIssueAt(), date_create($_POST['issue_date']))) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setIssueAt(date_create($_POST['issue_date']));
                    if (!$idVerify || !$_POST['issue_date']) {
                        $newIdVerify->setIssueAt(new Carbon('last year'));
                    }
                }
                if (array_key_exists('id_Number', $_POST)) {
                    if (!$idVerify|| trim($idVerify->getNumber()) !== trim($_POST['id_Number'])) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setNumber($_POST['id_Number']);
                    if (!$idVerify || !$_POST['id_Number']) {
                        $newIdVerify->setNumber($user->getId());
                    }
                }
                if (array_key_exists('id_reason', $_POST)) {
                    if (!$idVerify || trim($idVerify->getReason()) !== trim($_POST['id_reason'])) {
                        $changedIdVerifyFlag = true;
                    }
                    $newIdVerify->setReason($_POST['id_reason']);
                }
                $userStatus = $user->getStatus();
                if ($userStatus === User::STATUS_BANNED) {
                    $user->addFlag(User::FLAG_BANNED_USER);
                } elseif ($user->hasFlag(User::FLAG_BANNED_USER)) {
                    $user->removeFlag(User::FLAG_BANNED_USER);
                }
                if (array_key_exists('addressn', $_POST)) {
                    $user->setAddress($_POST['addressn']);
                }
                if (array_key_exists('addressLine', $_POST)) {
                    $user->setAddressLine($_POST['addressLine']);
                }
                if (array_key_exists('bannedReason', $_POST) && ( $user->getStatus() === User::STATUS_BANNED )) {
                    $user->setBannedReason(json_encode($_POST['bannedReason']));
                }
                if (array_key_exists('closureReason', $_POST) && ( $user->getStatus() === User::STATUS_CLOSED )) {
                    $user->setClosureReason(Util::j2s($request->get('closureReason')));
                }
                if ($changedIdVerifyFlag) {
                    $em->persist($newIdVerify);
                    $em->flush();

                    $user->ensureConfig()->setIdVerification($newIdVerify)->persist();
                }
                if (isset($_POST['affiliateselect'])) {
                    $user->setAffiliate($em->getRepository(\CoreBundle\Entity\Affiliate::class)->find($_POST['affiliateselect']));
                }

                //Quick fix on state id is not saved, may need
                $user->setStateid($form->get('stateid')->getViewData());

                $em->persist($user);
                $em->flush();

                Util::updateJson($user, 'meta', [
                    'demoAccount' => $request->get('demoAccount') === 'on',
                ]);

                RegisterStep::updateUser($user);

                return $this->redirect('/admin/user_modify/modify?user_id=' . $user->getId() . '&saved=true');
            }

            /** @var Collection $uiv */
            $uiv = $result['idVerities'];
            if ($uiv->isEmpty()) {
                $result['idVerities'] = new ArrayCollection([
                    new UserIdVerify(),
                ]);
            }

            if ($user->isAffiliate()) {
                $result['allAffiliates'] = Affiliate::findAll();
                $result['accessibleAffiliates'] = $user->getAccessibleAffiliates()->map(function (Affiliate $a) {
                    return $a->getId();
                })->toArray();
                $result['lockedAccessibleAffiliates'] = array_map(function (Affiliate $a) {
                    return $a->getId();
                }, $user->getLockedAccessibleAffiliates());
            }

            if ($user->hasNonConsumerRole() && !$user->isSuperAdmin()) {
                if ($user->isPlatformRelated() || $user->isApiInvoker()) {
                    $result['platforms'] = $em->getRepository(\CoreBundle\Entity\Platform::class)->findBy([], [
                        'name' => 'asc',
                    ]);
                    $result['accessiblePlatforms'] = $user->getAccessiblePlatforms()->map(function (Platform $a) {
                        return $a->getId();
                    })->toArray();
                }

                $result['cardPrograms'] = $em->getRepository(\CoreBundle\Entity\CardProgram::class)->findBy([], [
                    'platform' => 'asc',
                    'name' => 'asc',
                ]);
                $result['accessibleCardPrograms'] = $user->getAccessibleCardPrograms()->map(function (CardProgram $a) {
                    return $a->getId();
                })->toArray();
            }

            if ($user->isApiInvoker()) {
                DefaultController::prepareForUser($user, $result);
            }

            return $result;
        }

        return new ErrorResponse('Unknown user!');
    }

    /**
     * @Route("/admin/user_modify/notes",name="admin_user_notes")
     */
    #[Template()]
    public function notesAction(Request $request)
    {

        $em   = $this->getDoctrine()->getManager();
        $repo = $em->getRepository(\SalexUserBundle\Entity\User::class);
        $user = $repo->findOneBy(array( "id" => $request->get("user_id") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }
        if ($user) {
            $node     = new Notes();
            $formnote = $this->createFormBuilder($node)->add('notes', TextType::class, array(
                'label' => 'Notes',
            ))->getForm();
            if ($request->isMethod('POST')) {
                $formnote->handleRequest($request);
                $node->setFromname(User::getIdByUsername($request->get("fromname")));
                $node->setToname(User::getIdByUsername($request->get("toname")));
                $node->setCreatedtime(date('Y-m-d H:i:s', time()));
                $em->persist($node);
                $em->flush();

                return $this->redirect($this->generateUrl('admin_user_modify',
                    array( 'user_id' => $request->get("user_id") )));
            }

//            return array( 'formsecurity' => $formsecurity->createView(), 'user_id' =>  $request->get("user_id"));
        }
    }

    /**
     * @Route("/admin/user_modify/ajaxnotes",name="admin_user_ajaxnotes")
     */
    #[Template()]
    public function ajaxnotesAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        $repo = $em->getRepository(\SalexUserBundle\Entity\User::class);
        $user = $repo->findOneBy(array( "id" => $_GET['user_id']));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '. $_GET['user_id']);
        }
        $node     = new Notes();
        $node->setNotes($_GET['noteval']);
        $node->setFromname(User::getIdByUsername($_GET['fromname']));
        $node->setToname(User::getIdByUsername($_GET['toname']));
        $node->setCreatedtime(date('Y-m-d H:i:s', time()));
        $em->persist($node);
        $em->flush();
        exit('ok');
    }

    /**
     * @Route("/admin/user_modify/ajaxnoteslist",name="admin_user_ajaxnoteslist")
     */
    #[Template()]
    public function ajaxnoteslistAction(Request $request)
    {
        $em   = Util::em();
        $query      = $em->createQuery('SELECT p FROM CoreBundle:Notes p
                WHERE p.toname = :toname ORDER BY p.createdtime DESC')
            ->setParameter('toname', User::getIdByUsername($_GET['toname']));
        $notes      = $query->getResult();
        $paginator  = $this->get('knp_paginator');
        $page       = $request->get("page", 1);
        $pagination = $paginator->paginate($notes, $page, 5);
        return $this->render('@Admin/User/notes.html.twig',[
            'notes' => $notes,
        ]);
    }

    /**
     * @Route("/admin/user_modify/addressmodify",name="admin_user_addressmodify")
     */
    #[Template()]
    public function addressmodifyAction(Request $request)
    {

        $em   = $this->getDoctrine()->getManager();
        $user = $em->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array( "id" => $request->get("user_id") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }
        if ($user) {
            if ($request->isMethod('POST')) {
                if ( ! empty($_POST['addressn']) && ! empty($_POST['addressLine']) && ! empty($_POST['provinces'])
                    && ! empty($_POST['citys']) && ! empty($_POST['phone']) && ! empty($_POST['mobilePhone'])
                    && ! empty($_POST['workPhone']) && ! empty($_POST['ZIP'])) {
                    $user->setAddress($_POST['addressn']);
                    $user->setAddressline($_POST['addressLine']);
                    $user->setCountryid($_POST['provinces']);
                    $user->setStateid($_POST['citys']);
                    $user->setPhone($_POST['phone']);
                    $user->setMobilephone($_POST['mobilePhone']);
                    $user->setWorkphone($_POST['workPhone']);
                    $user->setZip($_POST['ZIP']);
                    $em->persist($user);
                    $em->flush();

                    return $this->redirect($this->generateUrl('admin_user_modify',
                        array( 'user_id' => $request->get("user_id") )));
                } else {
                    return $this->redirect($this->generateUrl('admin_user_modify',
                        array( 'user_id' => $request->get("user_id") )));
                }
            }

        }
    }

    /**
     * @Route("/admin/user_modify/security",name="admin_user_modify_security")
     * @throws \PortalBundle\Exception\PortalException
     */
    #[Template()]
    public function securityAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        /** @var User $user */
        $user = $em->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array( "id" => $request->get("user_id") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }

        $username = $user->getUsername();

        $formsecurity      = $this->createForm(UserSecurityType::class, $user);
        $formsecurity->handleRequest($request);

        $newPassword = $user->getPlainPassword();
        $checked = UserService::checkNewPasswordSecurity($newPassword, $user);
        if (gettype($checked) === 'string') {
            return new FailedMsgResponse($checked);
        }

        $password = Util::encodePassword($user, $newPassword);
        $user->setUsername($username);
        $user->setPassword($password);
        $em->persist($user);
        $em->flush();

        UserService::postActionsAfterChangingPassword($user, $newPassword);

        return $this->redirect('/admin/user_modify/modify?user_id=' . $user->getId() . '&saved=true');
    }

    /**
     * @Route("/admin/profile-modify/security",name="admin_profile_modify_security")
     */
    #[Template()]
    public function profileSecurityAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        $user = $this->getUser();
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }
        if ($user) {
            $formsecurity = $this->createFormBuilder($user)->add('plainPassword', RepeatedType::class, array(
                'type'            => PasswordType::class,
                'invalid_message' => 'Password must match',
                'options'         => array( 'attr' => array( 'class' => 'password-field' ) ),
                'required'        => true,
                'first_options'   => array( 'label' => 'Password', 'error_bubbling' => true ),
                'second_options'  => array( 'label' => 'Repeat Password' ),
            ))->getForm();
            $formsecurity->handleRequest($request);
            $form = $request->get('form') ?? [];
            $oldPassword = $form['password'] ?? null;
            $encoder  = Util::getPasswordEncoder();
            if (!$encoder->isPasswordValid($user, $oldPassword)) {
                return new FailedMsgResponse('Incorrect original password!');
            }

            Util::updateJson($user, 'meta', [
                'restrictIP' => $request->get('restrict_ip') === 'on',
                'passwordExpiresDisabled' => !!$request->get('passwordExpiresDisabled'),
            ]);

            $newPassword = $user->getPlainPassword();
            $checked = UserService::checkNewPasswordSecurity($newPassword, $user, true);
            if (gettype($checked) === 'string') {
                return new FailedMsgResponse($checked);
            }

            if ($newPassword) {
                $user->setPassword(Util::encodePassword($user, $newPassword));
                $em->persist($user);
                $em->flush();

                UserService::postActionsAfterChangingPassword($user, $newPassword);

                return $this->redirect('/admin/logout');
            }

            return new MsgResponse('Successfully saved changes.');
        }
        return new FailedMsgResponse('Unknown user!');
    }

    /**
     * @Route("/admin/user_modify/profile",name="admin_user_modify_profile")
     */
    #[Template()]
    public function profileAction(Request $request)
    {
        $em        = $this->getDoctrine()->getManager();
        /** @var User $user */
        $user      = $this->getUser();
        $flagsname = '';
        $state = null;
        $states = [];
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }
        if ($user) {
            if ($user->getStateid()){
                $state = Util::em()->getRepository(BaseState::class)
                    ->find($user->getStateid());
            }
            if ($user->getCountryid()){
                $userCountry = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->find($user->getCountryid());
                $countryStates = Util::em()->getRepository(BaseState::getSpecificClass())
                    ->findBy(['country' => $userCountry]);
                /** @var State $countryState */
                foreach ($countryStates as $countryState){
                    if ($countryState === $state){
                        continue;
                    }
                    $states[$countryState->getId()] = $countryState->getStateName();
                }
            }
            $form         = $this->createForm(UserType::class, $user, array(
                'action' => $this->generateUrl('admin_user_modify_profile'),
                'method' => 'POST',
            ));
            $formsecurity = $this->createFormBuilder($user)->add('password', PasswordType::class, array(
                'label' => 'Original password (required)',

            ))->add('plainPassword', RepeatedType::class, array(
                'type'            => PasswordType::class,
                'invalid_message' => 'Password must match',
                'options'         => array( 'attr' => array( 'class' => 'password-field' ) ),
                'required'        => true,
                'first_options'   => array( 'label' => 'Password', 'error_bubbling' => true ),
                'second_options'  => array( 'label' => 'Repeat Password' ),
            ))->getForm();
            $reload = false;
            if ($request->isMethod('POST')) {
              if (count($_POST) === 1){
                  if (array_key_exists('countryid',$_POST['salex_user_profile'])){
                      $countryId = $_POST['salex_user_profile']['countryid'];
                      $states = [];
                      if ($countryId != $user->getCountryid()){
                          $state = null;
                      }
                      $userCountry = Util::em()->getRepository(\CoreBundle\Entity\Country::class)->find($countryId);
                      $countryStates = Util::em()->getRepository(BaseState::getSpecificClass())
                          ->findBy(['country' => $userCountry]);
                      /** @var State $countryState */
                      foreach ($countryStates as $countryState){
                          if ($state !== null && $countryState === $state){
                              continue;
                          }
                          $states[$countryState->getId()] = $countryState->getStateName();
                      }
                  }
                  //$form->handleRequest($request);
                  return array(
                      'formsecurity' => $formsecurity->createView(),
                      'user_id'      => $request->get("user_id"),
                      'form'         => $form->createView(),
                      'flags'        => $user->getFlagsname(),
                      'addressn'     => $user->getAddress(),
                      'addressLine'  => $user->getAddressline(),
                      'countyid'     => $user->getCountryid(),
                      'stateid'      => $user->getStateid(),
                      'state'        => $state,
                      'states'       => $states,
                      'errorInfo'    => false,
                  );
              }
                //$form->handleRequest($request);
                if (is_null($form->getData()->getEmail())) {
                    return array(
                        'form'         => $form->createView(),
                        'formsecurity' => $formsecurity->createView(),
                        'user_id'      => $request->get("user_id"),
                        'flags'        => $user->getFlagsname(),
                        'addressn'     => $user->getAddress(),
                        'addressLine'  => $user->getAddressline(),
                        'countyid'     => $user->getCountryid(),
                        'stateid'      => $user->getStateid(),
                        'states'       => $states,
                        'state'        => $state,
                        'errorInfo'    => false,
                    );
                }
                if (array_key_exists('flagsn', $_POST)) {
                    foreach ($_POST['flagsn'] as $flagsn) {
                        $flagsname = $flagsname.$flagsn.';';
                    }
                    $user->setFlagsname($flagsname);
                }
                $cId = $_POST['salex_user_profile']['countryid'];
                $stateId = $_POST['stateSelectName'];
                $firstName =  $_POST['salex_user_profile']['firstName'];
                $lastName =  $_POST['salex_user_profile']['lastName'];
                $gender =  $_POST['salex_user_profile']['gender'];
                $birthdayArr = $_POST['salex_user_profile']['birthday'];
                $birthdayStr = $birthdayArr['year'] . '-' . $birthdayArr['month'] . '-' . $birthdayArr['day'];
                $birthday = \DateTime::createFromFormat('Y-m-d',$birthdayStr);
                $phone =  $_POST['salex_user_profile']['phone'];
                $mobilePhone =  $_POST['salex_user_profile']['mobilephone'];
                $zip = $_POST['salex_user_profile']['zip'];
                $user->setFirstName($firstName);
                $user->setLastName($lastName);
                $user->setGender($gender);

                if ($birthday) {
                    $user->setBirthday($birthday);
                }

                $user->setPhone($phone);
                $user->setMobilephone($mobilePhone);
                $user->setZip($zip);
                $user->setAddress($_POST['addressn']);
                $user->setCountryid($cId);
                $user->setStateid($stateId);
                $user->setAddressLine($_POST['addressLine']);
                $files = $request->files->get('salex_user_profile');
                $profiles = $files['profile_picture_file'];
                if (array_first($profiles)) {
                    $previous = $user->getProfilePicture();
                    $user->setProfilePictureFile(array_first($profiles));
                    $platform = Util::platform();
                    if ($previous && $platform && $platform->isSpendr()) {
                        S3Storage::deleteBackupObject(User::AVATAR_S3_PREFIX . $previous);
                        if (Util::isStaging()) {
                            Log::debug('User avatar deleted', [
                                User::AVATAR_S3_PREFIX . $previous
                            ]);
                        }
                    }
                }
                if (isset($_POST['salex_user_profile']['profile_picture_file'])) {
                    if ($_POST['salex_user_profile']['profile_picture_file']['delete']) {
                        $user->setProfilePicture(null);
                    }
                }
                //$user->setCountryid($_POST['citys']);
                //$user->setStateid($_POST['provinces']);
                $em->persist($user);
                $em->flush();

                $newIsDemo = $request->get('demo_mode') === 'on';
                Util::updateJson($user, 'meta', [
                    'demoAccount' => $newIsDemo,
                ]);

                $reload = true;
            }

            $FAVerifyType = Util::meta($user, $this->getPlatformName() . '-twoFactorType');
            $result = array(
                'formsecurity' => $formsecurity->createView(),
                'user_id'      => $request->get("user_id"),
                'form'         => $form->createView(),
                'flags'        => $user->getFlagsname(),
                'addressn'     => $user->getAddress(),
                'addressLine'  => $user->getAddressline(),
                'countyid'     => $user->getCountryid(),
                'stateid'      => $user->getStateid(),
                'state'        => $state,
                'states'       => $states,
                'errorInfo'    => false,
                'reload'       => $reload,
                'verifyTypeRecord' => $this->verifyTypeChangeAction($user->getId()),
                'isAppVerifyType' => $FAVerifyType == 'app',
                'FAVerifyType' => $FAVerifyType
            );
            return array_merge($result, $this->ipusageAction($user->getId(), true));
        }
    }

     /**
     * get platform name
     */
    protected function getPlatformName() {
      $platformName = Util::platform() ? Util::platform()->getName() : 'Tern';
      if (Util::isStaging()) {
        $platformName = $platformName . '(Staging)';
      }
      if (Util::isDevDevice()) {
        $platformName = $platformName . '(Local)';
      }
      return $platformName;
  }

    /**
     * @Route("/admin/user_modify/checkOldPsd",name="admin_check_oldPsd")
     *
     */
    public function checkOldPassword(){
        $oldPsd = $_GET['oldPsd'];
        $user = $this->getUser();
        $encoder  = Util::getPasswordEncoder();
        return new Response(json_encode($encoder->isPasswordValid($user,$oldPsd)));
    }

    /**
     * @Route("/admin/user_delete/delete",name="admin_user_delete")
     */
    #[Template()]
    public function deleteAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        $user = $em->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array( "id" => $request->get("user_id") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No administrator found for id '.$request->get("user_id"));
        }

        /** @var Collection $ucs */
        $ucs = $user->getIssuedCards();
        if ($ucs->count()) {
            throw new PortalException('Failed to delete this user as he/she has issued a user card.');
        }

        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            if ($uc->getLoads()->count()) {
                throw new PortalException('Failed to delete this user as he/she has load card requests.');
            }
        }

        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            if ($uc->getLastTransaction()) {
                throw new PortalException('Failed to delete this user as he/she has card transactions.');
            }
        }

        $now = Carbon::now();
        // remove the user card
        foreach ($ucs as $uc) {
          $uc->setDeletedAt($now);
          $this->em->persist($uc);
        }
        $user->setDeletedAt($now);
        $em->persist($user);
        $em->flush();

        $user->addNoteByCurrentUser('User deleted');

        if (Util::isMdRequest()) {
            return new SuccessResponse();
        }

        return $this->redirect($this->generateUrl('admin_user'));
    }

      /**
     * @Route("/admin/user_reset/sumsub",name="admin_user_reset_sumsub")
     */
    #[Template()]
    public function resetSumsubAction(Request $request)
    {
        $em   = $this->getDoctrine()->getManager();
        $user = $em->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array( "id" => $request->get("user_id") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No administrator found for id '.$request->get("user_id"));
        }
        SumsubService::resetApplicant($user);

        return $this->redirect($this->generateUrl('admin_user'));
    }

    /**
     * @Route("/admin/user_delete/erase/{user}", name="admin_user_delete_erase")
     */
    #[Template()]
    public function deleteAndEraseAction(Request $request, User $user)
    {
        $me = $this->authAdmin();
        if (!$me->inTeams([
            Role::ROLE_MASTER_ADMIN,
            Role::ROLE_CONSUMER_SERVICE_AGENT,
        ])) {
            return new FailedResponse('Denied the erase action.');
        }

        $v = 'deleted';
        $user->setStatus(User::STATUS_CLOSED, 'deleted by ' . $me->getEmail())
            ->setFirstName($v)
            ->setLastName($v)
            ->setUsername($v)
            ->setEmail($v)
            ->setPlainPassword(Util::randString())
            ->setAddress($v)
            ->setAddressline($v)
            ->setCity($v)
            ->setZip($v)
            ->setBirthday(null)
            ->setPhone($v)
            ->setMobilephone($v)
            ->setWorkphone($v)
            ->persist()
        ;

        /** @var UserIdVerify $uiv */
        foreach ($user->getIdVerifies() as $uiv) {
            $uiv->setNumber(null);
        }
        Util::flush();

        if (Util::isMdRequest()) {
            return new SuccessResponse();
        }

        return $this->redirect($this->generateUrl('admin_user'));
    }

    /**
     * @Route("/admin/user_view/userview",name="user_view")
     */
    #[Template()]
    public function userviewAction(Request $request)
    {
        $user_id = $request->get("user_id");
        $em      = $this->getDoctrine()->getManager();
        $user   = $em->getRepository(\SalexUserBundle\Entity\User::class)->find($user_id);
        $this->own($user);

        //$apis=$role->getApis();
        $state      = Util::em()->getRepository(BaseState::class)
            ->findOneBy(array( 'id' => $user->getStateid() ));
        $state      = is_null($state) ? '' : ucwords($state->getStateName());
        $bannedFlag = false;
        if ($user->getStatus() === User::STATUS_BANNED) {
            $bannedFlag = true;
        }

        return array(
            'users'      => $user,
            'status'     => ucwords($user->getStatus()),
            'state'      => $state,
            'bannedFlag' => $bannedFlag,
        );
    }

    //Begin Add by Bob Wen Bao on 2017-02-22 for quickly adding users with admin portal access
    /**
     * @Route("/admin/user_new/quick",name="admin_user_quick")
     */
    public function quickNewAction(Request $request)
    {
        $this->authPermission(Module::ID_USER_ADD_USER);

        $user     = new User();
        $user->setEnabled(true);
        $username = $request->get("add_username");
        $email    = $request->get("add_email");
        $password = $request->get("add_password");
        if ($username == null || $email == null || $password == null || ! filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return new Response("Please enter the valid information!");
        }
        if ($request->isMethod('POST')) {

            try {
                $em = $this->getDoctrine()->getManager();
                $user->setUsername($username)->setEmail($email)->setPassword($password);
                $user->setSource(User::SOURCE_ADMIN);
                $em->persist($user);
                $em->flush();
            } catch (\Exception $e) {
                switch (get_class($e)) {
                    case 'Doctrine\DBAL\Exception\UniqueConstraintViolationException':
                        return new Response("The user already exists!");
                        break;
                    default:
                        return new Response(get_class($e)."Error when adding new users, please try later !");
                }
            }
        }
        $user = $this->getDoctrine()->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array(
            'email'    => $email,
            'username' => $username,
        ));

        return new Response($user->getId().";".$user->getUsername().";".$user->getFirstName().";".$user->getLastName());
    }
    //End

    /**
     * @Route("/admin/user_new/select2filter",name="admin_user_select2filter")
     */
    public function select2filterAction(Request $request)
    {
        $q               = $request->get("q");
        $select2Response = [];

        if ($q) {
            $users = $this->getDoctrine()->getRepository(\SalexUserBundle\Entity\User::class)->findLikeUserName($q);

            foreach ($users as $user) {
                $select2Response[] = [ "id" => $user->getId(), "text" => $user->getUsername() ];
            }
        }

        return new JsonResponse($select2Response);
    }

    /**
     * @Route("/admin/user-verify-type",name="admin_verify_type")
     */
    #[Template()]
    public function verifyTypeChangeAction($userid = null, $limit = false)
    {
        $userid   = $userid ?: $_GET['user_id'];
        $platform = Util::platform();
        $query  = $this->getDoctrine()->getRepository(\CoreBundle\Entity\FAverifyType::class)
            ->createQueryBuilder('f')
            ->where('f.users = :user')
            ->setParameter('user', $userid);
        if ($platform) {
          $query->andWhere('f.platform = :platform')
          ->setParameter('platform', $platform->getId());
        } else {
          $query->andWhere(Util::expr()->isNull('f.platform'));
        }
        $res =  $query->orderBy('f.createdAt', 'desc')
            ->setMaxResults($limit ? 100 : null)
            ->getQuery()
            ->getResult();

        return $res;
    }


    /**
     * @Route("/admin/user-ipusage",name="admin_ipusage")
     */
    #[Template()]
    public function ipusageAction($userid = null, $limit = false)
    {
        $userid   = $userid ?: $_GET['user_id'];
        $ipusagex  = $this->getDoctrine()->getRepository(\CoreBundle\Entity\IpUsage::class)
            ->createQueryBuilder('a')
            ->where('a.users = :user')
            ->setParameter('user', $userid)
            ->orderBy('a.login_time', 'desc')
            ->setMaxResults($limit ? 100 : null)
            ->getQuery()
            ->getResult();
        $em            = Util::em();
        $ipusagearray  = array();
        $ipusagearrays = array();
        foreach ($ipusagex as $item) {
            if ( ! in_array($item->getLoginIp(), $ipusagearray)) {
                $ipusagearray[] = $item->getLoginIp();
            }
        }
        foreach ($ipusagearray as $item) {
            $query           = $em->createQuery('SELECT p FROM CoreBundle:IpUsage p
            WHERE p.login_ip = :loginip ORDER BY p.login_time DESC')->setParameter('loginip',
                $item);
            $ipusagearrays[] = $query->setMaxResults(1)->getResult()[0];
        }
        Util::usort($ipusagearrays, [''], function (IpUsage $a, $index = null) {
            return -$a->getLoginTime()->getTimestamp();
        });

        return Array( 'ipusage' => $ipusagearrays, 'ipusagearray' => $ipusagearray, 'ipusageall' => $ipusagex );
    }

    /**
     * @Route("/admin/user-smspin",name="admin_smspin")
     */
    #[Template()]
    public function smspinAction()
    {
        $user = $this->authAdmin();
        if (!$user->authPermission('user_managment__user__sms_pin')) {
            throw new AccessDeniedHttpException();
        }

        $userId      = $_GET['user_id'];
        $user        = $this->getDoctrine()->getRepository(\SalexUserBundle\Entity\User::class)->find($userId);
        $userPinInfo = array();
        $userPin                     = $this->getDoctrine()->getRepository(\CoreBundle\Entity\UserPin::class)->findBy(array(
            'user' => $user,
        ));
        $userPinRequestLog           = $this->getDoctrine()->getRepository(\CoreBundle\Entity\UserPinLog::class)->findBy(array(
            'user' => $user,
            'type' => UserPinLog::TYPE_REQUEST,
        ));
        $userPinUsageLog             = $this->getDoctrine()->getRepository(\CoreBundle\Entity\UserPinLog::class)->findBy(array(
            'user' => $user,
            'type' => UserPinLog::TYPE_CHECK,
        ));
        $userPinInfo[0] = array(
            'user'          => $userId,
            'pin'           => $userPin,
            'pinLogRequest' => $userPinRequestLog,
            'pinLogUsage'   => $userPinUsageLog,
        );

        return Array( 'userPin' => $userPinInfo );
    }

    /**
     * @Route("/admin/user-pin-reset",name="admin_user_reset_pin")
     */
    public function resetTimesAction(Request $request)
    {
        $user = $this->authAdmin();
        if (!$user->authEditable('user_managment__user__sms_pin')) {
            throw new AccessDeniedHttpException();
        }

        $userId = $request->get('user_id');
        $target = $request->get('target');
        $em = $this->getDoctrine()->getManager();
        $user = $em
            ->getRepository(\SalexUserBundle\Entity\User::class)
            ->find($userId);
        if('fail' == $target)
        {
            $user->setPinFail(0);
            $existPins = $em->getRepository(\CoreBundle\Entity\UserPin::class)
                ->findBy(array('user'=>$user,
                    'status'=>array(UserPin::STATUS_VALIDATED, UserPin::STATUS_NEW)));
            foreach ($existPins as $pin)
            {
                $pin->setPin(random_int(100,999));
            }
        }elseif ('request' == $target)
        {
            $user->setPhoneSms(0)->setMobilePhoneSms(0)->setWorkPhoneSms(0)
                ->setPhoneVoice(0)->setMobilePhoneVoice(0)->setWorkPhoneVoice(0);
        }
        $em->flush();
        return $this->redirect($this->generateUrl('admin_smspin',array('user_id'=>$userId)),301);
    }

    public function cardloadpartner($cardloadpartner)
    {
        if ($cardloadpartner) {
            $partner = $cardloadpartner->getName();
        } else {
            $partner = '';
        }

        return $partner;
    }

    /**
     * @Route("/admin/user_modify/reshipperaddress",name="admin_user_reshipperaddress")
     */
    #[Template()]
    public function reshipperaddressAction(Request $request)
    {

        $reshipperid = isset($_GET["reshipperid"]) ? $_GET["reshipperid"] : "";
        $em        = Util::em();
        $query     = $em->createQuery('SELECT p FROM CoreBundle:ReshipperAddress p where p.userCard is null');
        $provinces      = $query->getResult();
        $resultarray=array();
        foreach ($provinces as $item)
        {
            if($item->getReshipper()->getId()==$reshipperid)
            {
                $resultarray[]=array('r_id'=>$item->getId(),'r_name'=>$item->getAddressTypeName());
            }
        }
        $provinces_json = json_encode($resultarray);
        exit($provinces_json);
    }


    /**
     * @Route("/admin/user_modify/reshipperaddressde",name="admin_user_reshipperaddressde")
     */
    #[Template()]
    public function reshipperaddressdeAction(Request $request)
    {
        $uc = $this->em->getRepository(\CoreBundle\Entity\UserCard::class)->find($request->get('uc'));
        /** @var UserBillingAddress $uba */
        $uba = $uc->getBillingAddress();
        $userreshipperid = $uba ? $uba->getId() : 0;

        /** @var User $user */
        $user = $uc->getUser();
        $userid = $user->getId();

        $em = $this->getDoctrine()->getManager();
        $urs = $em->getRepository(\CoreBundle\Entity\UserBillingAddress::class)->findOneBy(array("id" => $userreshipperid));
        if (!$urs) {
            throw $this->createNotFoundException(
                'No administrator found for id ' . $userreshipperid
            );
        }
        /** @var User $user */
        $user = $em->getRepository(\SalexUserBundle\Entity\User::class)->find($userid);
        if ($user->getRegisterStep() === RegisterStep::CONSUMER_ALL_SET){
            $this->edituserset($userid);
        }
        $em->remove($urs);
        $em->flush();
        exit('ok');
    }

    /**
     * @Route("/admin/user_modify/reshipperaddressback",name="admin_user_reshipperaddressback")
     */
    #[Template()]
    public function reshipperaddressbackAction(Request $request)
    {
        $uc = $this->em->getRepository(\CoreBundle\Entity\UserCard::class)->find($request->get('uc'));
        /** @var UserBillingAddress $uba */
        $uba = $uc->ensureBillingAddress();
        Util::persist($uba);
        $userreshipperid = $uba->getId();

        /** @var User $user */
        $user = $uc->getUser();
        $userid = $user->getId();

        $reshipperid = isset($_GET["reshipperid"]) ? $_GET["reshipperid"] : "";
        $reshipperaddressid = isset($_GET["reshipperaddressid"]) ? $_GET["reshipperaddressid"] : "";
        $suiteAddress = isset($_GET["suiteAddress"]) ? $_GET["suiteAddress"] : "";
        $this->deleteuserreshipper($userreshipperid);
        $this->adduserreshipper($userid,$reshipperid,$reshipperaddressid, $suiteAddress, $uc);
        exit('ok');
    }

    public function deleteuserreshipper($id){
        $em = $this->getDoctrine()->getManager();
        $urs = $em->getRepository(\CoreBundle\Entity\UserBillingAddress::class)->findOneBy(array("id" => $id));
        if (!$urs) {
            throw $this->createNotFoundException(
                'No administrator found for id ' . $id
            );
        }
        $em->remove($urs);
        $em->flush();
    }
    public function adduserreshipper($userid,$reshipperid,$reshipperaddressid, $suiteAddress, UserCard $uc)
    {
        $em = $this->getDoctrine()->getManager();
        $user = $this->getDoctrine()
            ->getRepository(\SalexUserBundle\Entity\User::class)
            ->find($userid);
        $reshipper = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\Reshipper::class)
            ->find($reshipperid);
        $reshipperaddress = $this->getDoctrine()
            ->getRepository(\CoreBundle\Entity\ReshipperAddress::class)
            ->find($reshipperaddressid);
        $UserBillingAddress=new UserBillingAddress();
        $UserBillingAddress->setUser($user)
            ->setReshipper($reshipper)
            ->setReshipperAddress($reshipperaddress)
            ->setCustomAddress($suiteAddress)
            ->setUserCard($uc);
        $uc->setBillingAddress($UserBillingAddress);
        $em->persist($UserBillingAddress);
        $em->persist($uc);
        $em->flush();
    }
    public function edituserset($userid){
        $em = $this->getDoctrine()->getManager();
        $user = $this->getDoctrine()
            ->getRepository(\SalexUserBundle\Entity\User::class)
            ->find($userid);
        $user->setRegisterStep('register_consumer_billing_address_back');
        $em->flush();
    }

    public function getUserIdVerifyLastUpdateDate($userIdVerify){
        $userids=array();
        $userIdVerifyxs=array();
        foreach ($userIdVerify as $xtiem){
            if($xtiem->getUser()){
                if (!in_array($xtiem->getUser()->getId(), $userids))
                    $userids[] = $xtiem->getUser()->getId();
            }
        }
        foreach ($userids as $uitem)
        {
            $max=0;
            $userIdVerifyx=null;
            foreach ($userIdVerify as $xtiem){
                if($xtiem->getUser()){
                    if($xtiem->getUser()->getId()==$uitem) {
                        $id = $xtiem->getId();
                        if ($id > $max) {
                            $max = $id;
                            $userIdVerifyx = $xtiem;
                        }
                    }
                }
            }
            $userIdVerifyxs[]=$userIdVerifyx;
        }
        return $userIdVerifyxs;
    }

    /**
     * @Route("/admin/user/generate-api-token/{user}", methods={"POST"})
     * @param User $user
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function generateApiTokenAction(Request $request, User $user) {
        if (!$user->canEditApiConfig()) {
            return new FailedResponse('Permission denied!');
        }
        $type = $request->get('type', 'Immediately');
        $oldKey = $user->getApiToken();
        $expireAt = Carbon::now()->subHour();
        switch ($type) {
            case 'Immediately': break;
            case 'minus':
              $expireAt = Carbon::now()->addMinutes(10);
            break;
            case 'hour':
              $expireAt = Carbon::now()->addHours(1);
            break;
            case 'hours':
              $expireAt = Carbon::now()->addHours(6);
            break;
            case 'day':
              $expireAt = Carbon::now()->addHours(24);
            break;
            default: break;
        }
        Util::updateMeta($user, [
          'oldApiKey' => [
            'key'       => $oldKey,
            'expireAt'  => $expireAt
          ]
        ]);
        $user->setApiToken(JwtUtil::encode($user->getId()));
        Util::persist($user);

        return new SuccessResponse($user->getApiToken());
    }

    /**
     * @Route("/admin/user/remove-api-token/{user}", methods={"POST"})
     * @param User $user
     * @return FailedResponse|SuccessResponse
     */
    public function removeApiTokenAction(Request $request, User $user) {
      if (!$user->canEditApiConfig()) {
          return new FailedResponse('Permission denied!');
      }
      Util::updateMeta($user, [
        'oldApiKey' => [
          'key'       => '',
          'expireAt'  => Carbon::now()->subHour()
        ]
      ]);
      $user->setApiToken(null)
          ->persist();
      return new SuccessResponse();
    }

    /**
     * @Route("/admin/user/generate-api-keys/{user}", methods={"POST"})
     * @param User $user
     * @param Request $request
     * @return FailedResponse|SuccessResponse
     */
    public function generateApiKeysAction(Request $request, User $user) {
        if (!$user->canEditApiConfig()) {
            return new FailedResponse('Permission denied!');
        }
        $type = $request->get('type', 'Immediately');
        $ut = UserToken::createApiAccessKeys($user, $type);

        return new SuccessResponse([
            'access' => $ut->getToken(),
            'secret' => $ut->getSecret(),
        ]);
    }

     /**
     * @Route("/admin/user/remove-api-keys/{user}", methods={"POST"})
     * @param User $user
     * @return FailedResponse|SuccessResponse
     */
    public function removeApiKeysAction(Request $request, User $user) {
      if (!$user->canEditApiConfig()) {
          return new FailedResponse('Permission denied!');
      }
      UserToken::removeApiAccessKeys($user);

      return new SuccessResponse();
    }

    /**
     * @Route("/admin/user/essolo/update-gtp-phone-number")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function updateGtpPhoneNumber(Request $request) {
        $uid = $request->get('userId');
        $user = $this->em->getRepository(User::class)->find($uid);
        if (!$user) {
            return new FailedResponse('Unknown user!');
        }
        $this->own($user);

        $uc = $user->getOneCardInPlatform(Platform::esSolo());
        if (!$uc) {
            return new FailedResponse('Unknown user card!');
        }
        $oldPhone = Util::meta($uc, 'gtpPhoneNumber') ?: '';

        if ($request->isMethod('post')) {
            $phone = $request->get('phone', $oldPhone);
            Util::updateMeta($uc, [
                'gtpPhoneNumber' => $phone,
            ]);
        }

        return new SuccessResponse($oldPhone);
    }

    /**
     * @Route("/admin/profile-modify/resetFaVerifyType",name="admin_profile_reset_fa_verify_type")
     */
    #[Template()]
    public function profileResetFAVerifyAction(Request $request)
    {
        $user = $this->getUser();
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }
        if ($user) {
            $platformName = $this->getPlatformName();
            Util::updateMeta($user, [
              $platformName . '-twoFactorSecret' => ''
            ]);
            return new MsgResponse('Successfully reset.');
        }
        return new FailedMsgResponse('Unknown user!');
    }

     /**
     * @Route("/admin/user_modify/resetPassword",name="admin_reset_password")
     *
     */
    public function resetPassword(Request $request){
        $em   = $this->getDoctrine()->getManager();
        /** @var User $user */
        $user = $em->getRepository(\SalexUserBundle\Entity\User::class)->findOneBy(array( "id" => $request->get("user_id") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("user_id"));
        }

        $tokenGenerator = new TokenGenerator();
        $user->setConfirmationToken($tokenGenerator->generateToken());
        $user->setPasswordRequestedAt(new \DateTime());
        Util::persist($user);

        SwiftMailer::sendResettingEmail($user, true);

        return new SuccessResponse();
    }

    /**
     * @Route("/admin/user_modify/removeEmailFromBlackList",name="admin_remove_email_from_balcklist")
     *
     */
    public function removeEmailFromBlackList(Request $request){
      $em   = $this->getDoctrine()->getManager();
      /** @var User $user */
      $user = $em->getRepository(User::class)->findOneBy(array( "id" => $request->get("userId") ));
      if ( ! $user) {
          throw $this->createNotFoundException('No user found for id '.$request->get("userId"));
      }

      Config::activateInactiveEmail($user->getEmail());

      return new SuccessResponse();
    }

       /**
     * @Route("/admin/user_modify/waiveSubscriptionFee",name="waive_subscription_fee")
     *
     */
    public function waiveSubscriptionFee(Request $request){
      $em   = $this->getDoctrine()->getManager();
      /** @var User $user */
      $user = $em->getRepository(User::class)->findOneBy(array( "id" => $request->get("userId") ));
      if ( ! $user) {
          throw $this->createNotFoundException('No user found for id '.$request->get("userId"));
      }
      Util::updateMeta($user, [
        'paypalSubscriptionFree' => true
      ]);
      return new SuccessResponse();
    }

    /**
     * @Route("/admin/user_modify/addSubscriptionId",name="add_subscription_id")
     *
     */
    public function addSubscriptionId(Request $request){
        $em   = $this->getDoctrine()->getManager();
        /** @var User $user */
        $user = $em->getRepository(User::class)->findOneBy(array( "id" => $request->get("userId") ));
        if ( ! $user) {
            throw $this->createNotFoundException('No user found for id '.$request->get("userId"));
        }
        $subscriptionId = trim($request->get("subscriptionId"));
        try {
            $api = PayPalAPI::get();
            [$ei, $data] = $api->getSubscriptDetail($subscriptionId, false);
            if ($ei->isFailed()) {
                return new FailedResponse($ei->getMessage());
            }
            $subscription = PayPalSubscription::findBySubscriptionId($subscriptionId);
            if ($subscription && Util::neq($subscription->getUser(), $user)) {
              return new FailedResponse('The subscription info has been associated with the user ' . $subscription->getUser()->getSignature() . ' please check.');
            }
            $subscription = new PayPalSubscription();
            $subscription->setUser($user);
            $plan = PayPalSubscription::getPlanById($data['plan_id']);
            $subscription->setSubscriptionId($subscriptionId)
                        ->setQuantity($data['quantity'])
                        ->setStatus($data['status'])
                        ->setPlanType($plan)
                        ->persist();
            MauticEventService::submitPayPalSubscription($subscription);
        } catch (\Exception $e) {
            return new FailedResponse($e->getMessage());
        }
        return new SuccessResponse();
    }

    /**
     * @Route("/admin/user_modify/add-as-internal-tester")
     *
     */
    public function addAsInternalTester(Request $request){
        $uid = $request->get('userId');
        $user = User::find($uid);
        if ($user) {
            $list = Config::array(Config::CONFIG_TESTERS_MAIL);
            $op = $request->get('op', 'add');
            if ($op === 'add') {
                $list[] = $user->getEmail();
            } else if ($op === 'delete' || $op === 'remove') {
                $list = array_intersect($list, [
                    $user->getEmail(),
                ]);
            }
            $list = array_values(array_unique($list));
            Config::setJson(Config::CONFIG_TESTERS_MAIL, $list);
        }
        return new SuccessResponse();
    }
}
