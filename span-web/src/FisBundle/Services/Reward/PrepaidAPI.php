<?php


namespace FisBundle\Services\Reward;


use CoreB<PERSON>le\Entity\ExternalInvoke;
use CoreBundle\Entity\Platform;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use PortalBundle\Exception\PortalException;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\UriInterface;
use SalexUserBundle\Entity\User;
use SkuxBundle\Services\SlackService;

class PrepaidAPI
{
    public const PURSE_NO = 15;

    public const TRANSFER_TYPE_ACCT = 'Acct2Acct';
    public const TRANSFER_TYPE_PURSE = 'Purse2Purse';

    public const REASON_CODE_ACCEPTANCE = '1318'; // To add comment to account

    public const PURSE_STATUS_ACTIVE = 'ACTIVE'; // 2
    public const PURSE_STATUS_CLOSED = 'CLOSED'; // 3
    public const PURSE_STATUS_SUSPENDED = 'SUSPENDED'; // 6
    public const PURSE_STATUS_EXPIRED = 'EXPIRED'; // 7

    public const MAP_OF_PURSE_STATUSES = [
        self::PURSE_STATUS_ACTIVE => 2,
        self::PURSE_STATUS_CLOSED => 3,
        self::PURSE_STATUS_SUSPENDED => 6,
        self::PURSE_STATUS_EXPIRED => 7,
    ];

    public const MAP_OF_PURSE_STATUSES_REVERSED = [
        2 => self::PURSE_STATUS_ACTIVE,
        3 => self::PURSE_STATUS_CLOSED,
        6 => self::PURSE_STATUS_SUSPENDED,
        7 => self::PURSE_STATUS_EXPIRED,
    ];

    public $live = true;

    public $scope; // empty for FIS CashBack, and skux for SKUx

    /**
     * RapidService constructor.
     *
     * @param null|false $live
     */
    public function __construct($live = null, $scope = null)
    {
        if ($live === null) {
            $live = Util::isLive();
        }
        $this->live = $live;
        $this->scope = $scope;
    }

    protected function isSKUx()
    {
        return $this->scope === 'skux';
    }

    protected function getParameter($key)
    {
        if ($key === 'fis_sub_program_id') {
            $request = Util::request();
            $subProgramId = $request->headers->get('x-sub-program');
            if ($subProgramId) {
                return $subProgramId;
            }
        }

        $key = $this->live ? $key : ($key . '_test');
        if ($this->scope) {
            $key = $this->scope . '_' . $key;
        }
        return Util::getParameter($key);
    }

    protected function request($method, $endpoint, $params = [], $saveEi = true)
    {
        $method = strtoupper($method);

        $url = $this->getParameter('fis_url');

        $context = $params;
        if (!empty($context['SSN'])) {
            $context['SSN'] = Util::maskPan($context['SSN'], 3, true);
        }
        if (!$this->live) {
            $context['__sandbox'] = true;
        }

        $multipleLines = $params['__multipleLines'] ?? false;
        unset($params['__multipleLines']);

        $ei = ExternalInvoke::create('fis_' . $method . '_' . $endpoint, $context, null, $saveEi);

        $client = new Client();
        try {
            $params = array_merge([
                'UserID' => $this->getParameter('fis_user_id'),
                'PWD' => $this->getParameter('fis_pwd'),
                'SourceID' => $this->getParameter('fis_source_id'),
            ], $params);
            $debug = fopen("DEBUG_REQUESTS_FIS.txt", "a+");
            $response = $client->request($method, $url . $endpoint, [
                'allow_redirects' => [
                    'max' => 10,
                    'on_redirect' => function (
                        RequestInterface $request,
                        ResponseInterface $response,
                        UriInterface $uri
                    ) {
                        Log::debug('FIS redirects from ' . $request->getUri() . ' to ' . $uri);
                    },
                ],
                'form_params' => $params,
                'cert' => $this->getParameter('fis_cert_path'),
                'debug' => $debug,
            ]);
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            if (!$response) {
                $ei->fail(null, $exception->getMessage())
                    ->persist();
                Log::exception($exception->getMessage(), $exception);
                $this->alertSKUx($ei);

                throw PortalException::create('Empty response when call FIS API: ' . $exception->getMessage());
            }

            $code = $response->getStatusCode();
            $rawContent = $response->getBody()->getContents();
            if ($code === 403) {
                $msg = 'Access Denied!';
            } else {
                $content = $rawContent ?: '0 Unknown error';
                $msg = $content . ' (' . $exception->getMessage() . ')';
            }

            $ei->fail($rawContent, $msg)
                ->persist();
            $this->alertSKUx($ei);

            throw PortalException::create('Error occurred when call FIS API: ' . $msg);
        }

        $rawContent = $response->getBody()->getContents();
        $default = '0 Unknown error';
        $content = $rawContent ?: $default;
        if (!$content) {
            $msg = $content;
            $ei->fail($rawContent, $msg)
                ->persist();
        } else if ($content[0] === '0') {
            $all = explode('|', $content);
            $msg = substr($all[0] ?? $default, 2);
            $ei->fail($rawContent, $msg)
                ->persist();
        } else if (Util::startsWith($content, '<?xml ')) {
            $ei->succeed($content);
            $content = $this->parseXmlResponse($ei, $content);
            if ($saveEi) {
                $ei->persist();
            }
        } else {
            $ei->succeed($content);

            if ($saveEi) {
                $ei->persist();
            }

            $content = rtrim(substr($content, 2), '^');
            if ($multipleLines) {
                $rows = array_filter(explode('^', $content) ?? []);
                $content = array_map(function ($row) {
                    return explode('|', $row);
                }, $rows);
            } else {
                $content = explode('|', $content);
            }
        }
        $this->alertSKUx($ei);

        return [$ei, $content];
    }

    public function alertSKUx(ExternalInvoke $ei)
    {
        if (!$ei->isFailed() || !$this->isSKUx()) {
            return;
        }
        $error = $ei->getError() ?? 'Unknown FIS error';
        try {
            SlackService::prepareForPlatform(Platform::skux());
            SlackService::alert('Failed to call FIS API: ' . $error, [
                'id' => $ei->getId(),
                'type' => $ei->getType(),
                'request' => $ei->getRequest(true),
                'response' => $ei->getResponse(),
            ]);
        } catch (\Throwable $t) {
            Log::warn('Failed to alert SKUx for FIS error: ' . $error, [
                'ei' => $ei->getId(),
            ]);
        }
    }

    public function getPersonInfo($personId)
    {
        $params = [
            'ProxyType' => 3, // CAN
            'ClientID' => $this->getParameter('fis_client_id'),
            'SubProgID' => $this->getParameter('fis_sub_program_id'),
            'PersonID' => $personId,
            'SkipCreateRec' => 0,
        ];
        return $this->request('post', 'A2A/CO_GetPersonInfo.asp', $params);
    }

    /**
     * FIS Get card information by proxy key.
     *
     * @param string $proxyKey
     */
    public function getCardInformationByProxyKey($proxyKey)
    {
        $params = [
            'ProxyKey' => $proxyKey,
            'ClientID' => $this->getParameter('fis_client_id'),
        ];
        $request = $this->request('post', 'A2A/CO_GetCardInfo_Status_ByProxy.asp', $params);
	    $requestData = $request[1];
        return [
            "CardNum" => $requestData[0],
            "CardStatus" => $requestData[1],
            "ExpDate" => $requestData[2],
            "CVX2" => $requestData[3],
            "MaskedCardNum" => Util::maskPan($requestData[0] ?? '', 4),
        ];
    }

    public function assignCard($params = [], $packageId = null)
    {
        $params = array_merge([
            'ProxyType' => 3, // CAN
            'ClientID' => $this->getParameter('fis_client_id'),
            'SubProgID' => $this->getParameter('fis_sub_program_id'),
            'PkgID' => $packageId ?? $this->getParameter('fis_package_id'),
        ], $params);

        $resp = $this->request('post', 'A2A/CO_AssignCard.asp', $params);
        return $this->maskCardDetailsAfterAssignment($resp);
    }

    public function assignCardToExistingPerson($personId, $params = [], $packageId = null)
    {
        $params = array_merge([
            'ProxyType' => 3, // CAN
            'ClientID' => $this->getParameter('fis_client_id'),
            'SubProgID' => $this->getParameter('fis_sub_program_id'),
            'PkgID' => $packageId ?? $this->getParameter('fis_package_id'),
            'PersonID' => $personId,
        ], $params);

        $resp = $this->request('post', 'A2A/CO_AssignCard2ExistingPerson.asp', $params);
        return $this->maskCardDetailsAfterAssignment($resp);
    }

    protected function maskCardDetailsAfterAssignment($resp)
    {
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $resp;
        if ($ei && !empty($data[3]) && !$ei->isFailed()) {
            $data[0] = Util::maskPan($data[0] ?? '', 4);
            $data[2] = '***';
            $data[3] = '01/01/2000 12:00:00 AM';
            $ei->setResponse('1 ' . implode('|', $data) . '^')
                ->persist();
            $resp[1] = $data;
        }
        return $resp;
    }

    public function loadValue($proxyKey, $subProgId, $amountInCents, $comment = '', $purseNo = null, $reasonCode = 0, $refNo = null)
    {
        $params = [
            'ProxyKey' => $proxyKey, // string, 19
            'SubProgID' => $subProgId ?? $this->getParameter('fis_sub_program_id'), // int, 10
            'Amount' => Money::formatAmountToNumber($amountInCents), // money, 19.4
            'RefNo' => $refNo ?? Util::randTimeNumber(8), // string, 23
            'ReasonCode' => $reasonCode, // int, 5
            'Comment' => $comment, // string, 255
        ];
        if ($purseNo) {
            $params['PurseNo'] = $purseNo; // int, 3
        }
        return $this->request('post', 'A2A/CO_LoadValue_ByProxy.asp', $params);
    }

    public function openToBuy($proxyKey, $subProgId = null, $purseNo = null)
    {
        $params = [
            'ProxyKey' => $proxyKey, // string, 19
            'SubProgID' => $subProgId ?? $this->getParameter('fis_sub_program_id'), // int, 10
        ];
        if ($purseNo) {
            $params['PurseNo'] = $purseNo; // int, 3
        }
        return $this->request('post', 'A2A/CO_OTB_ByProxy.asp', $params);
    }

    public function adjustValue($proxyKey, $clientId, $amountInCents, $comment, $purseNo = null, $adjType = 'DEBIT', $refNo = null)
    {
        $params = [
            'ProxyKey' => $proxyKey, // string, 19
            'ClientID' => $clientId ?? $this->getParameter('fis_client_id'), // int, 10
            'AdjType' => $adjType, // CREDIT, DEBIT, LOAD VALUE, DDA_TO, DDA_FROM
            'Amount' => Money::formatAmountToNumber($amountInCents),
            'RefNo' => $refNo ?? Util::randTimeNumber(8),
            'Comment' => $comment,
        ];
        if ($purseNo) {
            $params['PurseNo'] = $purseNo; // int, 3
        }
        return $this->request('post', 'A2A/CO_AdjustValue.asp', $params);
    }

    public function accountToAccountTransfer($senderProxyKey, $receiverProxyKey, $clientId, $amountInCents, $comment,
                                             $senderPurse = null, $receiverPurse = null, $type = self::TRANSFER_TYPE_PURSE)
    {
        $params = [
            'TransferType' => $type,
            'SenderProxyKey' => $senderProxyKey,
            'ReceiverProxyKey' => $receiverProxyKey,
            'ClientID' => $clientId ?? $this->getParameter('fis_client_id'),
            'intReason' => 0,
            'strComment' => $comment,
            'curAmount' => Money::formatAmountToNumber($amountInCents),
        ];

        if ($senderPurse) {
            $params['SenderPurse'] = $senderPurse;
        }
        if ($receiverPurse) {
            $params['ReceiverPurse'] = $receiverPurse;
        }

        return $this->request('post', 'A2A/CO_Acct2AcctTransferFunds.asp', $params);
    }

    public function checkCredential($resp = 'html')
    {
        return $this->request('post', 'A2A/CO_CheckUseridPassword.asp', [
            'resp' => $resp,
        ]);
    }

    public function getFromPurseProxy()
    {
        return $this->getParameter('fis_from_purse_proxy');
    }

    /**
     * @param $proxyKey
     * @param string $purseNumber
     *
     * @return array
     * @throws PortalException
     */
    public function getPurseBalance($proxyKey, $purseNumber = null)
    {
        $params = [
            'ClientID' => $this->getParameter('fis_client_id'),
            'Proxy' => $proxyKey, // string, 19
            'IncludeExpiredPurse' => 1,
            'IncludeSummaryBalance' => 1,
        ];
        if ($purseNumber) {
            $params['PurseNumber'] = $purseNumber;
        }
        return $this->request('post', 'A2ASOA/v2008/001/Purse.asmx/GetPurseBalance_ByCUID', $params);
    }

    public function postPurseStatus($proxyKey, $purseNo, $status)
    {
        if (!is_numeric($status)) {
            $status = self::MAP_OF_PURSE_STATUSES[strtoupper($status)];
        }
        $params = [
            'ClientID' => $this->getParameter('fis_client_id'),
            'ProxyKey' => $proxyKey,
            'PurseNo' => $purseNo,
            'Status' => $status,
        ];
        return $this->request('post', 'A2A/CO_PostPurseStatus_ByCard.asp', $params);
    }

    public function getGlobalReasons()
    {
        return $this->request('post', 'A2A/CO_GlobalReasons.asp');
    }

    public function getCardTransactions($proxyKey, $startDate = null, $days = 30)
    {
        $params = [
            'ClientID' => $this->getParameter('fis_client_id'),
            'SubProgID' => $this->getParameter('fis_sub_program_id'),
            'ProxyKey' => $proxyKey,
            'TxnType' => 32767, // 0 by default, and 32767 for Everything
            'StartDate' => $startDate ?? date('Y-m-d'),
            'Days' => $days,
            '__multipleLines' => true,
        ];
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $this->request('post', 'A2A/CO_GetCardTxns.asp', $params);
        if ($ei && $ei->isFailed()) {
            return [$ei, $data];
        }
        $result = [];
        foreach ($data as $row) {
            $origin = $row;
            foreach ($row as $k => $v) {
                if ($v === '<NULL>') {
                    $row[$k] = null;
                }
            }
            $result[] = [
                '_' => $origin,
                'Account' => $row[0] ?? null,
                'TranDate' => $row[1] ?? null,
                'PostDate' => $row[2] ?? null,
                'Description' => $row[3] ?? null,
                'Reference' => $row[4] ?? null,
                'Amt' => $row[5] ?? null,
                'Inserted' => $row[6] ?? null,
                'Merchant' => $row[7] ?? null,
                'TxnType' => $row[8] ?? null,
                'RequestCode' => $row[9] ?? null,
                'StrategyName' => $row[10] ?? null,
                'Comment' => $row[11] ?? null,
                'MerchantNo' => $row[12] ?? null,
                'MCC' => $row[13] ?? null,
                'ResponseCode' => $row[14] ?? null,
                'SettleAmount' => $row[15] ?? null,
                'CountryName' => $row[16] ?? null,
                'AuthCode' => $row[17] ?? null,
                'LocalAmount' => $row[18] ?? null,
                'ReasonDescription' => $row[19] ?? null,
                'ResponseDescription' => $row[20] ?? null,
                'UserId' => $row[21] ?? null,
                'CustomIndicator' => $row[22] ?? null,
                'TranResult' => $row[23] ?? null,
                'Reversed' => $row[24] ?? null,
                'ClientRefNum' => $row[25] ?? null,
                'BuyerLoaderId' => $row[26] ?? null,
                'LocalTranCurrCode' => $row[27] ?? null,
                'LocalCurrCode' => $row[28] ?? null,
                'MerchAddr' => $row[29] ?? null,
                'TxnLevel' => $row[30] ?? null,
                'IssuingCurrCode' => $row[31] ?? null,
                'UTCPostDate' => $row[32] ?? null,
                'UTCInserted' => $row[33] ?? null,
            ];
        }
        return [$ei, $result];
    }

    public function createAccountComment($proxyKey, $comment, $reasonCode)
    {
        $params = [
            'ClientID' => $this->getParameter('fis_client_id'),
            'SubProgID' => $this->getParameter('fis_sub_program_id'),
            'ProxyKey' => $proxyKey,
            'Comment' => $comment,
            'ReasonCode' => $reasonCode,
        ];
        return $this->request('post', 'A2A/CO_CreateAccountComment_ByProxy.asp', $params);
    }

    public function parseXmlResponse(ExternalInvoke $ei, $response)
    {
        $xml = Util::xml2array($response);
        $errorNumber = $xml['ServiceResponse']['ErrorNumber'] ?? 1;
        if ($errorNumber !== '0') {
            $errorDesc = $xml['ServiceResponse']['ErrorDescription'] ?? 'Unknown error';
            $ei->fail(null, 'Failed to query the purse balance: ' . $errorDesc . ' (' . $errorNumber . ')')
                ->persist();
            return [];
        }
        $collection = $xml['PayloadCollection']['DataCollection'] ?? [];
        $keys = array_keys($collection);
        if (count($keys) === 1 && is_string($keys[0])) {
            $collection = $collection[$keys[0]];
        }
        return Util::ensureArray($collection);
    }
}
