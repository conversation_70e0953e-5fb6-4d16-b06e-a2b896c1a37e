<?php


namespace FisBundle\Services;


use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;
use FisBundle\Entity\ProgramType;
use FisBundle\FisBundle;
use SalexUserBundle\Entity\User;

class ProgramTypeService
{
    public static function getTree()
    {
        $user = Util::user();
        $platform = $user ? $user->getFisCurrentPlatform() : 'PPM';
        $all = Util::em()->getRepository(ProgramType::class)
            ->findBy([
              'platform' => $platform
            ], [
                'name' => 'ASC',
            ]);
        $all = Util::toApiArray($all);
        $result = [];
        foreach ($all as $item) {
            if (!$item['parent_id']) {
                $item['children'] = [];
            }
            $result[$item['id']] = $item;
        }
        foreach ($result as $id => $item) {
            if ($item['parent_id']) {
                $result[$item['parent_id']]['children'][] = $item;
                unset($result[$id]);
            }
        }
        return array_values($result);
    }

    public static function getSelectOptions()
    {
        $tree = static::getTree();
        $result = [
            [
                'value' => '',
                'label' => 'All',
            ],
            [
                'value' => '',
                'label' => '',
                'className' => 'select-separator',
            ],
        ];
        foreach ($tree as $root) {
            $list = [];
            $res = [];
            if (isset($root['type']) && $root['type'] == 'bin') {
              $list = explode(',', str_replace(PHP_EOL, ',', $root['bins']));
            } else if (isset($root['type']) && $root['type'] == 'subprogram') {
              $list = explode(',', str_replace(PHP_EOL, ',', $root['subprograms']));
            } else if (isset($root['type']) && $root['type'] == 'client') {
              $list = explode(',', str_replace(PHP_EOL, ',', $root['programs']));
            }
            foreach ($list as $item) {
              $res[] = (int)$item;
            }
            $result[] = [
                'value' => $root['id'],
                'label' => $root['name'],
                'type'  => isset($root['type']) ? $root['type'] : null,
                'list'  => $res
            ];
            foreach ($root['children'] as $sub) {
                $result[] = [
                    'value' => $sub['id'],
                    'label' => $sub['name'],
                    'className' => 'indent',
                ];
            }
        }
        if (count($result) > 2) {
            $result[] = [
                'value' => '',
                'label' => '',
                'className' => 'select-separator',
            ];
        }
        $result[] = [
            'value' => '__unassigned__',
            'label' => 'Unassigned BINs',
        ];
        return $result;
    }

    public static function query()
    {
        return Util::em()->getRepository(ProgramType::class)
            ->createQueryBuilder('pt');
    }

    public static function getKeyedTypeNames()
    {
        $rs = static::query()
            ->where('pt.bins is not null')
            ->orWhere('pt.programs is not null')
            ->orWhere('pt.subPrograms is not null')
            ->getQuery()
            ->getResult();
        $result = [];
        /** @var ProgramType $r */
        foreach ($rs as $r) {
            $name = $r->getName();
            $bins = $r->getBins(true);
            foreach ($bins as $bin) {
                $result[$bin] = $name;
            }
            $programs = $r->getPrograms(true);
            foreach ($programs as $program) {
                $result[$program] = $name;
            }
            $subprograms = $r->getSubPrograms(true);
            foreach ($subprograms as $subprogram) {
                $result[$subprogram] = $name;
            }
        }
        return $result;
    }

    public static function checkDuplicatedBINs(ProgramType $pt)
    {
        if (!$pt) {
            return [];
        }
        $checks = $pt->getBins(true);
        if (!$checks) {
            return [];
        }
        $result = [];
        $rs = static::query()
            ->where('pt.parent is null')
            ->andWhere('pt.bins is not null')
            ->getQuery()
            ->getResult();
        /** @var ProgramType $r */
        foreach ($rs as $r) {
            if (Util::eq($pt, $r)) {
                continue;
            }
            $exists = $r->getBins(true);
            $sames = array_intersect($checks, $exists);
            foreach ($sames as $same) {
                $result[$same] = $r->getName();
            }
        }
        return $result;
    }

    public static function queryAccessibleClients(QueryBuilder $q, $prefix = 'a', User $user = null)
    {
        $user = $user ?? Util::user();
        if ($user && $user->isProgramOwner()) {
            $programs = $user->getFisCurrentPrograms();
            if (is_array($programs)) {
                if (!$programs) {
                    $programs = ['__UNKNOWN_PROGRAM__'];
                }
                $q->andWhere($q->expr()->in($prefix . '.clientName', ':__accessibleClients'))
                    ->setParameter('__accessibleClients', $programs);
            }
        }
        return self::queryCurrentProcessor($q, $prefix);
    }

    private static function queryCurrentProcessor(QueryBuilder $q, $prefix = 'a')
    {
        $user = Util::user();
        if ($user) {
            $processor = $user->getFisCurrentPlatform();
            $all = FisBundle::getProcessors();
            $processorName = '__UNKNOWN_PROCESSOR__';
            if ($processor && isset($all[$processor])) {
                $processorName = $all[$processor];
            }

            $q->andWhere($q->expr()->in($prefix . '.fileProcessorName', ':__processorName'))
                ->setParameter('__processorName', $processorName);
        }
        return $q;
    }
}
