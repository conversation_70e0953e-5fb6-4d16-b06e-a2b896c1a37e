<?php


namespace UsUnlockedBundle\Controller;


use Carbon\Carbon;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserPin;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\JsonResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\UserPinService;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use PragmaRX\Google2FA\Google2FA;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\CardProcessorHub;
use UsUnlockedBundle\Services\OneTimeCardService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\Rain\RainService;
use UsUnlockedBundle\Services\UserService;

class PinController extends BaseController
{
    /**
     * @Route("/p/pin/pre-send/{method}")
     * @param Request $request
     * @param         $method
     *
     * @return FailedResponse|SuccessResponse
     */
    public function preSend(Request $request, $method)
    {
        if ($this->user->isClosedOrBanned()) {
            return new FailedResponse('Invalid user status!');
        }

        if ($method === 'email') {
            \CoreBundle\Services\UserService::sendVerificationCodeEmail($this->user);
        } else if ($method === 'sms') {
            UserPinService::create($this->user, UserPin::PHONE_TYPE_MOBILE, UserPIN::MSG_TYPE_SMS);
        } else if ($method === 'voice') {
            UserPinService::create($this->user, UserPin::PHONE_TYPE_MOBILE, UserPIN::MSG_TYPE_VOICE);
        }
        return new SuccessResponse(null, 'The verification code has been sent. It will expire after 2 minutes.');
    }

    /**
     * @Route("/p/pin/pre-verify/{method}/{code}")
     * @param Request $request
     * @param         $method
     * @param         $code
     *
     * @return JsonResponse
     */
    public function preVerify(Request $request, $method, $code)
    {
        if ($method === 'email') {
            $system = \CoreBundle\Services\UserService::getVerificationCode($this->user);
            $verified = $system === $code;
        } else {
            $verified = UserPinService::verify($this->user, $code);
        }
        return new JsonResponse($verified, $verified ? '' : 'Invalid code!');
    }

    /**
     * @Route("/p/pin/send")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function send(Request $request)
    {
        if (Util::READONLY) {
            return new FailedResponse('Cannot access the card details now! Please wait a moment or ask support.');
        }

        if ($this->user->isClosedOrBanned()) {
            return new FailedResponse('Invalid user status!');
        }

        $method = $request->get('method', UserPin::MSG_TYPE_SMS);
        UserPinService::create($this->user, UserPin::PHONE_TYPE_MOBILE, $method);
        return new SuccessResponse(null, 'Unlock code has been sent. It will expire after 20 minutes.');
    }

    /**
     * @Route("/p/pin/verify")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function validate(Request $request)
    {
       if ($this->platform->isDisableUsuPinFunction() && !$this->platform->isCashOnWeb()) {
           return new FailedResponse('We are currently under maintenance. Please check back later and try again. We will get you shopping again in no time!');
       }

        $code = $request->get('code');
        if (!$code) {
            return new FailedResponse('Unknown unlock code!');
        }

        $this->checkCanUseNewUsu();

        if ($request->get('authCheck') === 'check') {
          $g = new Google2FA();
          $secret = Util::meta($this->user, 'twoFactorSecret');
          try {
            $verified = $g->verify($code, $secret);
          } catch (\Exception $exception) {
            $verified = false;
          }
        } else {
          $verified = UserPinService::verify($this->user, $code);
        }
        if (!$verified) {
            return new FailedResponse('Invalid unlock code!');
        }

        if (CardProcessorHub::isPaused($this->user)) {
            throw PortalException::temp('Failed to access the card since the card service is not available now. Please retry a moment later.');
        }

        if (Util::READONLY) {
            return new FailedResponse('Cannot access the card details now! Please wait a moment or ask support.');
        }

        $user = $this->getUser();
        if ($user->getStatus() !== User::STATUS_ACTIVE) {
            throw PortalException::temp('Access denied to the card details since your account status is '
                                        . Util::humanize($user->getStatus()));
        }

        $data = null;
        $uc = null;
        $type = $request->get('type');
        $detail = null;
        if ($type === 'merchantCard') {
            $uc = $this->checkAndCreateMerchantCard($request, $detail);
        } else if ($type === 'oneTimeCard') {
            $uc = $this->checkAndCreateOneTimeCard($request, $detail);
        }

        if ($uc) {
            if (!$detail) {
                $detail = CardProcessorHub::viewCard($uc);
            }
            if (!$detail) {
                return new FailedResponse('Failed to get card details!');
            }

            $data = $uc->toApiArrayForPrivacy();

            $usePopup = true;
            if ($usePopup) {
                $data['detail'] = [
                    'pan' => Util::formatPan($uc->getPan()),
                    'exp' => '**/**',
                    'cvv' => '***',
                ];
                if ($uc->isRainCard()) {
                    if (Util::isServer()) {
                        $cd = RainService::viewCardDetail($uc);
                    } else {
                        $cd = [];
                    }

                    $data['isRainCard'] = true;
                    $data['rainCardInfo'] = $detail;
                    $data['sessionKey'] = Util::meta($uc, 'sessionKey');
                    $data['expire'] = RainService::getExpiration($cd);
                } else {
                  $data['hostedUI'] = PrivacyAPI::getForUserCard($uc)->hostedUI($uc);
                }
            } else {
                $exp = isset($detail['exp_month'], $detail['exp_year'])
                    ? ($detail['exp_month'] . '/'. substr($detail['exp_year'], 2))
                    : '**/**';
                $data['detail'] = [
                    'pan' => Util::formatPan($detail['pan'] ?? $uc->getPan()),
                    'exp' => $exp,
                    'cvv' => $detail['cvv'] ?? '***',
                ];
            }
        }

        return new SuccessResponse($data);
    }

    protected function checkAndCreateMerchantCard(Request $request, &$detail)
    {
        $uc = UserCard::find($request->get('card'));
        if (!$uc) {
            throw new PortalException('Unknown card!');
        }
        $this->own($uc);

        if (!$uc->isIssued()) {
            if (!UserService::hasBalance($this->user)) {
                throw PortalException::temp('Please load your account first!');
            }

            $unusedOpenCount = 0;
            $cardTypes = [
                PrivacyAPI::CARD_TYPE_MERCHANT_LOCKED,
                PrivacyAPI::CARD_TYPE_UNLOCKED,
            ];
            $provider = UserCard::CARD_PROVIDER_RAIN;
            if ($this->platform->isCashOnWeb()) {
              $ucs = $uc->getUser()->getOpenCardsInCashOnWeb($cardTypes, provider: $provider);
            } else {
              $ucs = $uc->getUser()->getOpenCardsInUsUnlocked($cardTypes, provider: $provider);
            }
            /** @var UserCard $_uc */
            foreach ($ucs as $_uc) {
                if (!$_uc->getMerchant()) {
                    $unusedOpenCount++;
                }
            }
            $user = $uc->getUser();
            $maxUnusedOpenCount = $user->getUnusedOpenCardLimit();
            if ($unusedOpenCount >= $maxUnusedOpenCount) {
                throw PortalException::temp('You already have ' . $maxUnusedOpenCount . ' unused open cards. Please use them before issuing more.');
            }

            $maxOpenCount = $user->getOpenCardLimit();
            if ($ucs->count() >= $maxOpenCount) {
                throw PortalException::temp('Your current limit of open cards has been met! Please contact support and we will be happy to assist you in enabling additional cards if necessary. <br /><br />'
                    . 'You may also use your One-Time Use Card.');
            }

            // Check card limit
            $this->checkCardLimit($this->user, $this->platform);

            if (Util::isServer()) {
                // Issue the card
                $ei = $uc->createCard($request->getRequestUri(), false);
                if ($ei && $ei->isFailed()) {
                    throw new PortalException('Failed to issue card: ' . $ei->getError());
                }
            }
        }

        return $uc;
    }

    protected function checkAndCreateOneTimeCard(Request $request, &$detail)
    {
        $uc = UserCard::find($request->get('card'));

        // Check if the card is closed. Issue a new card if the current one is already closed.
        if ($uc) {
            if ($uc->isClosed()) {
                $uc = null;
            } else {
                if ($uc->isRainCard()) {
                  $rainCardData = RainService::viewCardDetail($uc);
                  $state = $rainCardData['status'] && RainService::isNotActiveCard($rainCardData['status']) ? UserCard::STATUS_CLOSED : null;
                } else {
                  $detail = PrivacyService::viewCard($uc);
                  $state = $detail['state'] === PrivacyAPI::CARD_STATE_CLOSED ? UserCard::STATUS_CLOSED : null;
                }
                if ($state === UserCard::STATUS_CLOSED) {
                    $uc->setStatus(UserCard::STATUS_CLOSED, false, false)
                        ->setNativeStatus($state)
                        ->persist();
                    $uc = null;
                    $detail = null;
                }
            }
        }

        if (!$uc) {
            if (!UserService::hasBalance($this->user)) {
                throw PortalException::temp('Please load your account first!');
            }

            if ($this->platform->isCashOnWeb()) {
              $all = $this->user->getCardsInCashOnWeb(PrivacyAPI::CARD_TYPE_SINGLE_USE);
            } else {
              $all = $this->user->getCardsInUsUnlocked(PrivacyAPI::CARD_TYPE_SINGLE_USE);
            }

            // Put in an artificial 30-second (changed to 3 minutes) delay between the time you can make another single-use card
            // after it has changed. This will inhibit the user from getting another card number as fast and
            // give time for transactions to clear out...on top of the other precautions put in
            /** @var UserCard $last */
            $last = $all->last();
            if ($last) {
                $time = $last->getIssuedAt() ?: $last->getCreatedAt();
                $usage = $last->getLastTransaction();
                if ($usage) {
                    $time = $usage->getCreatedAt();
                }
                if ($time && $time->getTimestamp() + 180 > time()) {
                    throw PortalException::temp('Please wait for at least 3 minutes to issue a new card.');
                }
            }

            // Check card limit
            $this->checkCardLimit($this->user, $this->platform);

            // Limit the issuing count in the past 24 hours
            $issuedCount = 0;
            $now = Carbon::now();
            $limit = $this->user->getDailyIssuingOneTimeCardLimit();
            /** @var UserCard $_uc */
            foreach ($all as $_uc) {
                $issuedAt = Carbon::instance($_uc->getIssuedAt());
                if ($issuedAt->addHours(24)->gt($now)) {
                    $issuedCount++;

                    if ($issuedCount >= $limit) {
                        throw PortalException::temp('Your today\'s limit of One-Time Use cards has been met! Please contact support and we will be happy to assist you in enabling additional cards if necessary. <br /><br />'
                                                  . 'You may also use other cards.');
                    }
                }
            }

            if (Util::isServer()) {
                if ($this->platform->isCashOnWeb()) {
                    $uc = OneTimeCardService::createForCowUser($this->user);
                } else {
                    $uc = OneTimeCardService::createForUser($this->user);
                }
            }
        }

        return $uc;
    }

    protected function checkCardLimit(User $user, $platform) {
        if ($platform->isCashOnWeb()) {
          $all = $user->getCardsInCashOnWeb();
        } else {
          $all = $user->getCardsInUsUnlocked();
        }
        $issuedCount = 0;
        $now = Carbon::now();
        $limit = $user->getDailyIssuingCardLimit();
        /** @var UserCard $_uc */
        foreach ($all as $_uc) {
            if (!in_array($_uc->getType(), [
                PrivacyAPI::CARD_TYPE_SINGLE_USE,
                PrivacyAPI::CARD_TYPE_MERCHANT_LOCKED,
                PrivacyAPI::CARD_TYPE_UNLOCKED,
            ])) {
              continue;
            }
            $issuedAt = Carbon::instance($_uc->getIssuedAt());
            if ($issuedAt->addHours(24)->gt($now)) {
                $issuedCount++;

                if ($issuedCount >= $limit) {
                    throw PortalException::temp('Your today\'s limit of cards has been met! Please contact support and we will be happy to assist you in enabling additional cards if necessary. <br /><br />');
                }
            }
        }

    }

    /**
     * @Route("/p/pin/setupAuth")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function setupAuth(Request $request)
    {
        $platform = Util::platform() ?? Platform::ternCommerce();
        $g = new Google2FA();
        $secret = $g->generateSecretKey();
        $res = [
          'secret' => $secret,
          'QRL' => Util::getQrCodeUrl($g->getQRCodeUrl($platform->getFixedName(), $this->user->getName(), $secret)),
        ];
        return new SuccessResponse($res);
    }

    /**
     * @Route("/p/pin/disable-auth")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function disableAuth(Request $request)
    {
        $code = $request->get('code');
        $system = Util::meta($this->user, 'twoFactorSecretPrivateKey');
        if ($code === $system) {
            Util::updateMeta($this->user, 'twoFactorSecret');
            return new SuccessResponse();
        }
        return new FailedResponse('Invalid private key!');
    }

    /**
     * @Route("/p/pin/checkAuth")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function checkAuth(Request $request)
    {
        $g = new Google2FA();
        $secret = $request->get('secret');
        $code = $request->get('code');
        try {
          $verified = $g->verify($code, $secret);
        } catch (\Exception $exception) {
          $verified = false;
        }
        if ($verified) {
          $private = Util::generate_key(16);
          Util::updateMeta($this->user, [
            'twoFactorSecret' => $secret,
            'twoFactorSecretPrivateKey' => $private,
          ]);
          return new SuccessResponse(null, 'Congrats! Please save the following private key in a secure place to disable/reset the app auth: ' . $private);
        }

        return new FailedResponse('Failed to set up app authentication! Please enter the code on authenticator app and try agian!');
    }
}
