<?php


namespace UsUnlockedBundle\Controller;


use ApiBundle\Entity\Coin;
use Carbon\Carbon;
use CoreBundle\Constant\AlternativePaymentSupported;
use CoreBundle\Constant\GlobalCollectSupported;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\Promotion;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\APIServices\AlternativePaymentService;
use CoreBundle\Services\UserPinService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use PortalBundle\Util\RegisterStep;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Coinflow\CoinflowService;
use UsUnlockedBundle\Services\ComplianceService;
use UsUnlockedBundle\Services\LoadService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\ReferService;
use UsUnlockedBundle\Services\UserService;
use UsUnlockedBundle\UsUnlockedBundle;
use function Functional\sum;
use CoreBundle\Services\APIServices\RapydPaymentService;

class LoadController extends BaseController
{
    protected function translateResponse($response)
    {
        if ($response instanceof ErrorResponse) {
            throw new FailedException($response->getMessage());
        }
        if ($response instanceof FailedResponse) {
            throw new FailedException($response->getMessage(), $response->getResult());
        }
        if ($response instanceof RedirectResponse) {
            $target = $response->getTargetUrl();
            $msg = 'Failed to load resources. ';
            if ($target === '/consumer-register/id-verification') {
                $msg .= 'Please complete the id verification first.';
            }
            throw new FailedException($msg, [
                'url' => $target,
            ]);
        }
        return $response;
    }

    protected function checkLoadingAvailable()
    {
        if (Util::READONLY || ( $this->cardProgram && $this->cardProgram->isCashOnWeb())) {
            throw new FailedException('Loading is not available now. Please wait a moment or contact support.');
        }
        $this->checkCanUseNewUsu();
    }

    /**
     * @Route("/p/load/init")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function init(Request $request)
    {
        $this->checkLoadingAvailable();

        $ctrl = $this->get('app.portal_register_load_card_controller');
        $request->query->set('step', 1);
        $response = $this->translateResponse($ctrl->LoadCardPage($request));

        $min = $response['currencies'][0]['min'];
        $max = $response['currencies'][0]['max'];
        $default = 100;
        if ($default < $min) {
            $default = $min;
        }
        if ($default > $max) {
            $default = $max;
        }
        return new SuccessResponse([
            'init' => $default,
            'min' => $min,
            'max' => $max,
            'canBeReferred' => true, // ReferService::canBeReferred($this->user),
        ]);
    }

    /**
     * @Route("/p/load/methods")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function methods(Request $request)
    {
        $this->checkLoadingAvailable();

        $user = $this->user;
        $referCode = $request->get('referCode');
        if ($referCode) {
            \CoreBundle\Utils\Log::debug('User ' . $user->getId() . ' entered refer code ' . $referCode);
            ReferService::refer($user, $referCode);
            Promotion::applyUsuOneYearMembershipFee($user, $referCode);
        }
        Promotion::applyUsuThanksLoadFee($user, $referCode);

        $ctrl = $this->get('app.portal_register_load_card_controller');
        $request->request->set('currency', 'USD');
        $request->request->set('amount', $request->get('loadAmount'));
        $load = $ctrl->LoadCardSetAmount($request);

        $request->query->set('step', 2);
        $request->query->set('load', $load->getId());
        $response = $this->translateResponse($ctrl->LoadCardPage($request));

        if (Util::isStaging()) {
            Log::debug('Load methods response', $response);
        }

        $result = array_values($response['loadMethods']);

        $dev = Util::isDev() || Util::hasSuffix($user->getEmail(), [
            '@ternitup.com',
            '@terncommerce.com',
        ]);

        // Remove testing load methods
        $result = array_filter($result, function ($item) use ($dev) {
            if (!array_key_exists('name', $item)) {
                Log::warn('Load method item does not have a name', [
                    'item' => $item,
                ]);
                return false;
            }
            $name = $item['name'];
            if ($name === GlobalCollectSupported::PAYNAME_WESTERNUNION) {
                return false;
            }
            if (!$dev && in_array($name, [
                GlobalCollectSupported::PAYNAME_ALIPAY,
                GlobalCollectSupported::PAYNAME_WECHAT_PAY,
                GlobalCollectSupported::PAYNAME_SKRILL,
            ])) {
                return false;
            }
            return true;
        });

        // Sort the load methods
        $orders = [
            AlternativePaymentSupported::DIRECT_PAY_MAX,
            LoadMethod::PAYNAME_COINFLOW_CRYPTO,
            LoadMethod::PAYNAME_COINFLOW_PIX,
            LoadMethod::PAYNAME_COINFLOW_UK_FASTER_PAYMENTS,
            LoadMethod::PAYNAME_COINFLOW_SEPA,
            LoadMethod::PAYNAME_COINFLOW_CARD,
        ];
        $mapped = Util::keyArrayBy($result);
        $_result = [];
        foreach ($orders as $order) {
            if (isset($mapped[$order])) {
                $_result[$order] = $mapped[$order];
            }
        }
        foreach ($result as $item) {
            $name = $item['name'];
            if (!isset($_result[$name])) {
                $_result[$name] = $item;
            }
        }
        $result = $_result;

        // Get payment type for Rapyd pay method
        $payTypeList = Util::em()->getRepository(\CoreBundle\Entity\RapydCountryPaymentType::class)->paymemtList($this->user->getCountry());//->findBy(['country'=>$this->user->getCountry()->getId()]);

       foreach ($result as $key => &$item) {
            $item['icon'] = Util::fixFilePath($item['icon'], true);
            $item['partner_id'] = $item['partner']['id'];
            $item['partner_name'] = $item['partner']['name'];
            unset($item['partner'], $item['pay_amount']);
            // if Rapyd pay method get pay type
            if (count($payTypeList)) {
              if (strtolower($item['name']) == 'rapyd bank transfer') {
                $item['typeList'] = array_values(array_filter($payTypeList,function($t) { return  $t['category']=== 'bank_transfer';} ));
              }
              if (strtolower($item['name']) == 'rapyd bank redirect') {
                $item['typeList'] =  array_values(array_filter($payTypeList,function($t) { return  $t['category']=== 'bank_redirect';} ));
              }
              if (strtolower($item['name']) == 'rapyd ewallet') {
                $item['typeList'] =  array_values(array_filter($payTypeList,function($t) { return  $t['category']=== 'ewallet';} ));
              }
              if (strtolower($item['name']) == 'rapyd cash') {
                $item['typeList'] =  array_values(array_filter($payTypeList,function($t) { return  $t['category']=== 'cash';} ));
              }
            }
            if (empty($item['typeList']) && in_array(strtolower($item['name']),['rapyd bank transfer','rapyd bank redirect','rapyd ewallet','rapyd cash'])) {
                unset($result[$key]);
            }

        }

        unset($item);

        Log::debug('Create load record: ' .  $load->getId());
        return new SuccessResponse([
            'methods' => array_values($result),
            'canBeReferred' => ReferService::canBeReferred($this->user),
        ]);
    }

    /**
     * @Route("/p/load/fee")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function fee(Request $request)
    {
        $this->checkLoadingAvailable();

        $uc = UserService::getDummyCard($this->user);
        $request->request->set('userCardId', $uc->getId());

        $amount = Money::normalizeAmount($request->get('loadAmount'), 'USD');
        $request->request->set('amount', $amount);

        $method = $request->get('method');
        if (empty($method['partner_id'])) {
            return new FailedResponse('Please select a load method and try again.');
        }

        $lp = $this->em->getRepository(LoadPartner::class)->find($method['partner_id']);
        if (!$lp) {
            return new FailedResponse('Invalid load partner. Please try again from the beginning.');
        }

        if ($lp->is(LoadPartner::COINFLOW) && !$this->user->getBirthday()) {
            return new FailedResponse('Please fill your Date of Birth on the "My Settings" page and try again.', [
                'url' => '/p/profile',
            ]);
        }

        $request->request->set('partner', $lp->getId());

        $lm = $this->em->getRepository(LoadMethod::class)->find($method['id']);
        if (!$lm) {
            return new FailedResponse('Invalid load method. Please try again from the beginning.');
        }

        $request->request->set('method', $lm->getId());
        $request->request->set('currency', $method['currency']);

        $ctrl = $this->get('app.api_load_controller');
        $ctrl->request = $request;
        $summary = $this->translateResponse($ctrl->quoteAction());

        /** @var UserCardLoad $load */
        $load = $summary['load'];

        LoadService::checkLimit($load);

        $initialCurrency = $load->getInitialCurrency();
        $fee = sum([
            $summary['loadFee'],
            $summary['membershipFee'],
            $summary['replacementFee'],
        ]);
//        $pendingFees = $uc->getPendingFeeApiArray();
//        foreach ($pendingFees as $pendingFee) {
//            $fee += $pendingFee['coin']['amount'];
//        }
        if (!empty($summary['discount']['totalFee'])) {
            $fee -= $summary['discount']['totalFee'];
        }
        if ($fee < 0) {
            $fee = 0;
        }

        $result = [
            'id' => $load->getId(),
            'loadAmount' => $load->getInitialCoin()->toApiArray(),
            'fee' => Coin::create($fee, $initialCurrency)->toApiArray(),
            'payAmount' => Coin::create($load->getPayAmount(), $load->getPayCurrency())->toApiArray(),
            'membershipFee' => $summary['membershipFee'],
        ];

        if (isset($summary['issuers'])) {
            $result['issuers'] = $summary['issuers'];
        }

        return new SuccessResponse($result);
    }

    /**
     * @Route("/p/load/ach-sms")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function achSms(Request $request)
    {
        UserPinService::create($this->user);
        return new SuccessResponse();
    }

    /**
     * @Route("/p/load/confirm")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function confirm(Request $request)
    {
        $this->checkLoadingAvailable();

        $load = $request->get('load');
        Log::debug('Confirm load record: ' .  $load['id']);
        $ucl = $this->em->getRepository(UserCardLoad::class)->find($load['id']);
        $ach = $ucl->getMethodName() === GlobalCollectSupported::PAYNAME_ACH;
        $uk = $ucl->getMethodName() === GlobalCollectSupported::PAYNAME_DIRECT_DEBIT_UK;
        $sepa = $ucl->getMethodName() === GlobalCollectSupported::PAYNAME_SEPA;
        if ($ach || $uk || $sepa) {
            if (!UserPinService::verify($this->user, $load['achSms'] ?? '')) {
                return new FailedResponse('Invalid Security PIN!');
            }
        }

        $deviceId = $request->get('nSureDeviceId');
        if ($deviceId && $ucl->getPartner()->is(LoadPartner::COINFLOW)) {
            Util::updateMeta($ucl, [
                'nSureDeviceId' => $deviceId,
            ]);
        }

        $request->request->set('loadId', $load['id']);

        foreach([
            'issuerId', 'state', 'documentId', 'fiscalNumber',
            'accountHolderName', 'accountNumber', 'bankCode', 'iban',
            'CreditCardType', 'CreditCardNumber', 'CVV2', 'BIC','rapydType'
        ] as $key) {
            $request->request->set($key, $load[$key] ?? '');
        }

        if (!empty($load['Expiration'])) {
            $expiry = explode('/', $load['Expiration']);
            if (count($expiry) !== 2) {
                return new FailedResponse('Invalid Expiration Month/Year!');
            }
            $request->request->set('ExpirationMonth', (int)$expiry[0]);
            $request->request->set('ExpirationYear', (int)(20 . $expiry[1]));
        }

        $ctrl = $this->get('app.api_load_controller');
        $ctrl->request = $request;
        $result = $this->translateResponse($ctrl->createAction());

        $result['status'] = ucwords($ucl->getLoadStatus());

        if (($ach || $uk || $sepa) && !empty($result['result'])) {
            $items = ($ach || $uk) ? [
                [
                    'key' => 'Account Holder',
                    'value' => $load['accountHolderName'] ?? '',
                ],
                [
                    'key' => 'Account Number',
                    'value' => $load['accountNumber'] ?? '',
                ],
                [
                    'key' => 'Routing Number',
                    'value' => $load['bankCode'] ?? '',
                ],
            ] : [
                [
                    'key' => 'IBAN',
                    'value' => $load['iban'] ?? '',
                ],
            ];
            foreach ($result['result'] as $item) {
                if ($item->key === 'Payment Reference' && $item->value === '0') {
                    continue;
                }
                $items[] = [
                    'key' => $item->key,
                    'value' => $item->value,
                ];
            }
            $result['result'] = $items;
        }

        PrivacyService::clearTransactionsCache($this->user->getId());
        ComplianceService::checkRapidLoads($this->user, $ucl);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/p/load/status")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function status(Request $request)
    {
        $ctrl = $this->get('app.portal_register_load_card_controller');
        $request->query->set('step', 5);

        $load = $request->get('load');
        $request->request->set('load', $load['id']);

        $result = $this->translateResponse($ctrl->LoadCardPage($request));

        /** @var UserCardLoad $load */
        $load = $result['userCardLoad'];
        $result['date'] = $load->getInitializedAt()->format(Util::DATE_TIME_FORMAT);
        $result['method'] = $load->getMethod()->toApiArray();
        $result['load'] = [
            'loadAmount' => $load->getInitialCoin()->toApiArray(),
            'payAmount' => Coin::create($load->getPayAmount(), $load->getPayCurrency())->toApiArray(),
        ];
        if ($load->isLoaded()) {
            $result['load']['loadAmount'] = $load->getLoadCoin()->toApiArray();
        } else if ($load->isReceived()) {
            $result['load']['loadAmount'] = $load->getReceivedCoin()->toApiArray();
        }
        $result['description'] = Util::meta($load, 'systemLoadDescription');

        unset($result['userCard'], $result['userCardLoad'], $result['user']);

        return new SuccessResponse($result);
    }

    /**
     * @Route("/p/load/instruction")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function instruction(Request $request)
    {
        $loadId = $request->get('id');
        if (!$loadId) {
            return new FailedResponse('Unknown load transaction!');
        }

        $load = UserCardLoad::find($loadId);
        if (!$load || !Util::eq($load->getUserCard()->getUser(), $this->user)) {
            return new FailedResponse('Invalid load transaction!');
        }

        if (!$load->getTransaction()) {
            $error = 'The load transaction was failed to be created. ';
            $error .= Util::json($load, 'error', 'payment') ?? '';
            return new FailedResponse($error);
        }

        $ctrl = $this->get('app.portal_register_load_card_controller');
        $request->query->set('step', 4);
        $request->request->set('load', $loadId);

        /** @var SuccessResponse $result */
        $result = $this->translateResponse($ctrl->LoadCardPage($request));
        $result = $result->getResult();

        /** @var UserCardLoad $load */
        $load = $result['userCardLoad'];
        /** @var LoadMethod $method */
        $method = $result['method'];

        $methods = $method->toApiArray();
        $methods['currency'] = $load->getInitialCurrency();
        $methods['icon'] = $load->getTransaction()->getPayTypeIcon();
        if (empty($methods['icon'])) {
            $methods['icon'] = $method->getIconUrl();
        }

        $confirm = array_only($result, [
            'instruction', 'result', 'redirect', 'qrUrl', 'iframe', 'qrUrlCustom',
        ]);
        $confirm['status'] = ucwords($load->getLoadStatus());

        if ($method->is(AlternativePaymentSupported::DIRECT_PAY_MAX)) {
            if ($confirm['status'] === 'Error') {
                $confirm['redirect'] = null;
                $confirm['result'] = [
                    [
                        'key' => 'Result',
                        'value' => $confirm['status'],
                    ],
                    [
                        'key' => 'Status',
                        'value' => $load->getError() ?? Util::meta($load, '2000chargeDeclineReason'),
                    ]
                ];
            } else if ($confirm['redirect']) {
                $confirm['redirect'] = '/p/load/' . $load->getId() . '/redirect';
            }
        }

        return new SuccessResponse([
            'confirm' => $confirm,
            'method' => $methods,
            'load' => [
                'id' => $load->getId(),
                'canArchive' => $load->canArchive(),
                'payAmount' => $load->getPayCoin()->toApiArray(),
                'loadAmount' => $load->getInitialCoin()->toApiArray(),
            ],
        ]);
    }

    /**
     * @Route("/p/load/update-info")
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function updateInfo(Request $request)
    {
        $loadId = $request->get('id');
        if (!$loadId) {
            return new FailedResponse('Unknown load transaction!');
        }

        $load = UserCardLoad::find($loadId);
        if (!$load || !Util::eq($load->getUserCard()->getUser(), $this->user)) {
            return new FailedResponse('Invalid load transaction!');
        }

        if ($load->getPartner()->is(LoadPartner::COINFLOW)) {
            $info = $request->get('info') ?? [];
            if (!empty($info['paymentId'])) {
                CoinflowService::assignPaymentIdWithCheck($load, $info['paymentId']);
            }
        }

        return new SuccessResponse();
    }

    /**
     * @Route("/p/load/check-status")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function checkStatusSilently(Request $request)
    {
        $status = null;
        $completed = false;
        $loadId = $request->get('id');
        if ($loadId) {
            $load = UserCardLoad::find($loadId);
            if (Util::eq($load->getUserCard()->getUser(), $this->user)) {
                $status = $load->getLoadStatus();
                $completed = in_array($status, [
                    UserCardLoad::LOAD_STATUS_CONFIRMED,
                    UserCardLoad::LOAD_STATUS_RECEIVED,
                    UserCardLoad::LOAD_STATUS_LOADED,
                    UserCardLoad::LOAD_STATUS_ERROR,
                ]);
            }
        }
        return new SuccessResponse([
            'status' => $status,
            'completed' => $completed,
        ]);
    }

    /**
     * @Route("/p/load/{load}/redirect")
     * @param Request      $request
     * @param UserCardLoad $load
     *
     * @return RedirectResponse
     * @throws PortalException
     */
    public function redirectLoad(Request $request, UserCardLoad $load)
    {
        $this->own($load);
        $method = $load->getMethod();
        if ($method->is(AlternativePaymentSupported::DIRECT_PAY_MAX)) {
            try {
                $request->query->set('step', 5);
                $request->request->set('load', $load->getId());
                $ctrl         = $this->get('app.portal_register_load_card_controller');
                $result       = $this->translateResponse($ctrl->LoadCardPage($request));
                $nativeStatus = $result['nativeStatus'] ?? NULL;
            } catch (\Throwable $e) {
                $nativeStatus = null;
            }
            if ( ! $nativeStatus || in_array($nativeStatus, [
                AlternativePaymentService::STATUS_PENDING,
                AlternativePaymentService::STATUS_APPROVED,
            ])) {
                $transaction = $load->getTransaction();
                if ($transaction) {
                    $response = json_decode($transaction->getResponse());
                    if (!empty($response->redirectUrl)) {
                        return new RedirectResponse($response->redirectUrl);
                    }
                }
            }
        }
        return new RedirectResponse('/consumer-register/card-load?step=5&load=' . $load->getId());
    }

    /**
     * @Route("/p/load/{load}/archive")
     * @param Request      $request
     * @param UserCardLoad $load
     *
     * @return FailedResponse|SuccessResponse
     */
    public function archive(Request $request, UserCardLoad $load)
    {
        $this->own($load);

        if ( ! $load->canArchive()) {
            return new FailedResponse('The load transaction cannot be archived now. Please try again later.');
        }

        $load->archive();
        PrivacyService::clearTransactionsCache($load->getUserCard()->getUser()->getId());

        return new SuccessResponse();
    }
}
