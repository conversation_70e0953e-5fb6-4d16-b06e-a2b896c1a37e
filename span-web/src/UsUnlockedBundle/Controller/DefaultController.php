<?php

namespace UsUnlockedBundle\Controller;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\AlertService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\LegacyService;
use UsUnlockedBundle\Services\OneTimeCardService;
use UsUnlockedBundle\Services\PayPal\PayPalAPI;
use UsUnlockedBundle\Services\Rain\RainAPI;
use UsUnlockedBundle\Services\Rain\RainService;
use UsUnlockedBundle\Services\ReferService;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\SpendingService;
use UsUnlockedBundle\Services\Sumsub\SumsubService;
use UsUnlockedBundle\Services\UserRefererService;
use UsUnlockedBundle\Services\UserService;
use UsUnlockedBundle\UsUnlockedBundle;

class DefaultController extends BaseController
{
    /**
     * @Route("/p")
     * @param Request $request
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function indexAction(Request $request)
    {
//        if (!Util::isDev()) {
//            return new ErrorResponse('We are currently under maintenance. Please check back later and try again. We will get you shopping again in no time!');
//        }

        $this->checkOnboarding();
        $this->checkBrowser();

        UserRefererService::referForInviteFromRequest($this->user);
        $alerts = AlertService::getUserAlertsArray($this->user);

        return $this->render('@UsUnlocked/Default/index.html.twig', [
            'alerts' => $alerts
        ]);
    }

    /**
     * @Route("/p/abstract")
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function abstractAction(Request $request)
    {
        $pendingBalance = LegacyService::getPendingBalance($this->user);

        $force = $request->get('force', 'false') === 'true';
        if ($this->platform->isCashOnWeb()) {
          if ($force) {
              $oneTimeCard = OneTimeCardService::getLastForCowUserWithRefreshing($this->user);
          } else {
              $oneTimeCard = OneTimeCardService::getLastForCowUser($this->user);
          }
        } else {
          if ($force) {
              $oneTimeCard = OneTimeCardService::getLastForUserWithRefreshing($this->user);
          } else {
              $oneTimeCard = OneTimeCardService::getLastForUser($this->user);
          }
        }
        $dummyCard = UserService::getDummyCard($this->user);
        $spends = SpendingService::getData($this->user, $this->cardProgram, $request->get('period', ''));

        $user = $this->user->toFinalApiArray();
        $config = $this->user->ensureConfig();
        $user['referralCode'] = $config->ensureReferCode();

        $alerts = AlertService::getUserAlertsArray($this->user);
        $referAmount = ReferService::getRewardAmount($dummyCard);

        $systemMsg = Config::get('usu_system_message');
        if ($systemMsg) {
            $systemMsgKey = 'usu_system_message_' . md5($systemMsg);
            if (Data::hGet($systemMsgKey, $this->user->getId())) {
                $systemMsg = null;
            } else {
                Data::hSetNx($systemMsgKey, $this->user->getId(), time());
            }
        }
        $rainRegError = null;
        if (!$config->getRainUserId() && RainService::canCreateRainCard($this->user)) {
            $rainRegError = SumsubService::updateMember($this->user);
        }
        if ((bool)Util::meta($this->user, 'rainKycPending')) {
          RainService::updateRainAccount($this->user);
        }

        $needRainKyc = !$this->user->isUsuSumSubIdVerified();
        $rainKycUrl = Util::meta($this->user, 'rainKycUrl') ?? 'https://use.raincards.xyz/kyc';

        $canInvite = UserRefererService::canInvite($this->user);
        $showMaintenanceInfo = $canInvite ? false : 'waitlist';

        $oneTimeCardData = $oneTimeCard->toApiArrayForPrivacy();
        $oneTimeCardData['showCard'] = OneTimeCardService::isShowCard($this->user);

        // $needMoreBalance = $needRainKyc ? $this->checkNeedBalance() : false;
        return new SuccessResponse([
            'balance' => Money::formatAmount($dummyCard->getBalance(), $dummyCard->getCurrency()),
            'pendingBalance' => Money::format($pendingBalance, 'USD', false),
            'oneTimeCard' => $oneTimeCardData,
            'spends' => $spends,
            'user' => $user,
            'setFf' => $dummyCard->hasInitiatedLoading(),
            'ffId' => Util::meta($this->user, 'freightForwarder'),
            'cp' => $this->cardProgram->toMinApiArray(),
            'referAmount' => Money::usdFormat($referAmount),
            'canInvite' => $canInvite,
            'alerts' => $alerts,
            'kycOnAll' => UsUnlockedBundle::KYC_ON_ALL,
            'twoFactorSecret' => (bool)Util::meta($this->user, 'twoFactorSecret'),
            'KYC_notice' => $this->user->isUsuIdVerified() ||  Util::meta($this->user, 'ignore_KYC_notice'),
            'systemMessage' => $systemMsg,
            'rainAccountId' => $config->getRainUserId(),
            'needRainKYC' => $needRainKyc,
            'rainKycPending' => (bool)Util::meta($this->user, 'rainKycPending'),
            'rainRegError' => $rainRegError,
            'applicationReason' => Util::meta($this->user, 'applicationReason') ?? '',
            'legalsAccepted' => $this->user->isLegalAgreementsAccepted(),
            'rainKycUrl' => $rainKycUrl . '?userId=' . UserService::getRainUserId($this->user) . '&redirect=' . Util::host(),
            // return true when we disable paypal subscription
            'paypalSubscriptionFlag' => (int)Config::get('enable_paypal_subscription', 1) === 1 ? (UserService::getPayPalSubscription($this->user) ?? false) : true,
            'showMaintenanceInfo' => $showMaintenanceInfo,
            'subscriptionPlan' => Util::isDev() ? 'P-5FG845064D657635TM6S6XEY' :  Config::get('usu_monthly_paypal_plan', 'P-6NP30055P96950438M6RW3MI'),
            'subscriptionAnnuallyPlan' => Util::isDev() ? 'P-5FG845064D657635TM6S6XEY' : Config::get('usu_annually_paypal_plan', 'P-15A62697F69188548NANPXGA'),
            'subscriptionUpgradeAnnuallyPlan' =>  Util::isDev() ? 'P-9NN28240DU182515BNCGDJCY' : Config::get('usu_upgrade_annually_paypal_plan', 'P-4E177619DY673843PNCCDVMI'),
            'sumsubApplicantStatus' => $this->user->getSumsubApplicantStatus(),
            'sumsubApplicantReason' =>  Util::meta($this->user, 'sumsubApplicantReason'),
            'sumsubApplicantClientComment' => Util::meta($this->user, 'sumsubApplicantClientComment'),
            'accountStatus' => UserService::getAccountStatus($this->user)
        ]);
    }

     /**
     * @Route("/p/ignoreNotice")
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function ignoreNoticeAction(Request $request)
    {
      if ($request->get('ignore')) {
        Util::updateMeta($this->user, [
          'ignore_KYC_notice' => true,
        ]);
      }
      return new SuccessResponse();
    }

     /**
     * @Route("/p/store/paypal-subscribeId", methods={"POST"})
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function storePaypalSubscribeId(Request $request)
    {
      if ($request->get('subscriptionID')) {
        Util::updateMeta($this->user, [
          'paypalSubscriptionID' => $request->get('subscriptionID'),
        ]);
        $planType = $request->get('planType');
        $paypalSubscriptionData = $request->get('subscriptData');
        $subscription = new PayPalSubscription();
        $subscription->setUser($this->user)
                     ->setQuantity(1)
                     ->setSubscriptionId($request->get('subscriptionID'))
                     ->setPlanType($planType == 'upgrade' ? 'annually': $planType)
                     ->persist();
        Util::updateMeta($subscription, $paypalSubscriptionData);
        UserService::updatePayPalSubscriptInfo($this->user, $request->get('subscriptionID'));
      }
      return new SuccessResponse();
    }

    /**
     * @Route("/p/check-subscription", methods={"POST"})
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function checkSubscribe(Request $request)
    {
      $subscription = PayPalSubscription::findByUser($this->user);
      if ($subscription && $subscription->getCurrentPlanType() === PayPalSubscription::PLAN_TYPE_MONTHLY && $subscription->getStatus() === PayPalSubscription::STATUS_ACTIVE) {
        return new SuccessResponse();
      }
      return new FailedResponse('You can not switch the current monthly subscription to an annual subscription, your current subscription may have been cancelled or you may be in an annual subscription. Please refresh the page to get the latest subscription information.');
    }

    /**
     * @Route("/p/user/prelaunch", methods={"POST"})
     *
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function prelaunch(Request $request)
    {
        $action = $request->get('action');
        if (in_array($action, [
            'test', 'close',
        ])) {
            $key = 'usu_prelaunch_users_' . $action;
            Data::pushToArray($key, $this->user->getId());
        }
        $next = 'close';
        if (Util::isDev() || $this->user->isInternalTester()) {
            $next = 'keep';
        }
        $message = '';
        if ($action === 'refer') {
            $refererCode = $request->get('refererCode');
            $reason = null;
            $referer = null;
            $valid = UserRefererService::isCodeValid($refererCode, reason: $reason, referer: $referer);
            if (!$valid || !$referer) {
                $title = 'Invalid code';
                $msg = 'That referral code does not exist or is no longer active. Please double-check and try again.';
                if ($reason === 'used') {
                    $title = 'Code Limit Reached';
                    $msg = 'This referral code has already been used the maximum number of times. You will need a new code to gain access.';
                }
                return new FailedResponse($msg, [
                    'title' => $title,
                ]);
            }
            $next = 'keep';
            $message = 'Welcome!';
            UserRefererService::refer($referer, $this->user);
        }

        return new SuccessResponse([
            'next' => $next,
        ], $message);
    }

    /**
     * @Route("/p/user/invite", methods={"GET"})
     *
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function invite(Request $request)
    {
        if ( ! UserRefererService::canInvite($this->user)) {
            return new FailedResponse('You are not allowed to invite users at this time.', [
                'title' => 'Invite Not Allowed',
            ]);
        }
        $referer = UserRefererService::ensure($this->user);
        return new SuccessResponse([
            'referer' => $referer->toApiArray(),
            'invited' => UserRefererService::getReferralCount($referer),
        ]);
    }

    /**
     * @Route("/p/user/accept-legals", methods={"POST"})
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function acceptLegals(Request $request)
    {
        $fields = [];
        foreach (USUnlockedBundle::TERMS_FIELDS as $field) {
            if (Util::isTrue($request->get($field))) {
                $fields[] = $field;
            }
        }
        $this->user->acceptLegalAgreements($fields);
        return new SuccessResponse([
            'accepted' => $this->user->isLegalAgreementsAccepted(),
        ]);
    }

    /**
     * @Route("/p/kyc/status", methods={"POST"})
     *
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function kycStatus(Request $request)
    {
        return new SuccessResponse([
            'verified' => $this->user->isUsuSumSubIdVerified()
        ]);
    }
}
