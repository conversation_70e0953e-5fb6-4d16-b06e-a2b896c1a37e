<?php

namespace UsUnlockedBundle\Command;

use Carbon\Carbon;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\SlackService;

class UpdatePrivacySpendLimitCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:usu:privacy:update-spend-limit')
            ->setDescription('Check and update spending limit of all the consumers')
            ->addOption('update', null, InputOption::VALUE_NONE, 'Update the spending limit & status only when this is specified')
            ->addOption('sync', null, InputOption::VALUE_NONE, 'Query Privacy\'s List Cards API and compare local data')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Util::$platform = Platform::ternCommerce();
        $this->prepare($input, $output, true);
        $sync = $input->getOption('sync');
        $servers = [];
        if ($sync) {
            $servers = $this->query();
            $this->line('Total ' . count($servers) . ' cards issued so far!');

            Util::completeDoctrineBatch();
        }

        $update = $input->getOption('update');
        $em = Util::em();

        $q = $em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.cards', 'uc')
            ->join('uc.loads', 'ucl')
            ->where('uc.type = :type')
            ->andWhere('ucl.loadStatus = :loadStatus')
            ->setParameter('type', PrivacyAPI::CARD_TYPE_DUMMY)
            ->setParameter('loadStatus', UserCardLoad::LOAD_STATUS_LOADED);

        $count = (clone $q)->distinct()
            ->select('count(distinct u)')
            ->getQuery()
            ->getSingleScalarResult();
        $this->line('Found ' . $count . ' users');
        $users = $q->distinct()->getQuery()->toIterable();

        $fixed = [];
        $updatedStatus = [];
        /** @var User $user */
        foreach ($users  as $i => $user) {
            $uid = $user->getId();
            if ($i && $i % 100 === 0) {
                $this->line($i . '/' . $count);
                Util::completeDoctrineBatch();

                $user = User::find($uid);
            }

            if ($user->inTeams([Role::ROLE_CASH_ON_WEB_MEMBER])) {
                continue;
            }

            $ucs = $user->getOpenCardsInUsUnlocked();
            $dummy = $user->getUsuDummyCard();


            /** @var UserCard $uc */
            foreach ($ucs as $uc) {
                $token = $uc->getToken();
                if (!$token) {
                    continue;
                }
                $ucId = $uc->getId();
                $fix = $this->checkCard($uc, $dummy, $update, $sync, $servers,$updatedStatus);
                if ($update && $fix) {
                    try {
                        $temps = [
                            $token => PrivacyService::viewCard($uc),
                        ];
                    } catch (\Exception $exception) {
                        $this->line($exception->getMessage(), 'error');
                    }
                    $fix = $this->checkCard($uc, $dummy, $update, $sync, $temps,$updatedStatus);
                    if (!$fix) {
                        $this->line(sprintf(
                            'Skip: User %s card %s since it\'s already updated',
                            $uid, $ucId,
                        ));
                        continue;
                    }
                }

                if ($update && $fix) {
                    try {
                        PrivacyService::updateCard($uc);
                        $this->line('Fixed spending limit for ' . $uc->getId(), 'info');
                    } catch (\Exception $exception) {
                        $this->line($exception->getMessage(), 'error');
                    }

                    if (empty($fixed[$uid])) {
                        $fixed[$uid] = [];
                    }
                    $fixed[$uid][$ucId] = Money::format($fix[0], 'USD', false) . ' per ' . $fix[1];
                }
            }
        }
        Util::completeDoctrineBatch();

        if ($fixed) {
            SlackService::info('Fixed the card spending limits for below users!', $fixed);
        }

        if ($updatedStatus) {
            SlackService::info('Synced card status for below users!', $updatedStatus);
        }

        $this->done();
        return 0;
    }

    protected function checkCard(UserCard $uc, UserCard $dummy, $update, $sync, &$servers, &$updatedStatus)
    {
        $ucId = $uc->getId();
        $token = $uc->getToken();
        $user = $uc->getUser();
        $uid = $user->getId();

        [$min, $duration] = PrivacyService::calculateFinalSpendLimitForCard($uc, $dummy);

        $meta = Util::meta($uc);
        $_min = (int)($meta['privacySpendLimit'] ?? -1);
        $_duration = $meta['privacySpendLimitDuration'] ?? 'Unknown';
        if ($_duration === PrivacyAPI::CARD_SPEND_LIMIT_DURATION_TRANSACTION && $uc->getType() === PrivacyAPI::CARD_TYPE_SINGLE_USE) {
            $_duration = PrivacyAPI::CARD_SPEND_LIMIT_DURATION_FOREVER;
        }

        $server = $servers[$token] ?? null;
        $fix = false;
        if ($sync && $server) {
            $status = UserCard::convertPrivacyNativeStateToStatus($server['state']) ?: $uc->getStatus();
            if ($status !== $uc->getStatus()) {
                $this->line(sprintf(
                    'Different status: User %s card %s, privacy: %s, local: %s',
                    $uid, $ucId,
                    $status, $uc->getStatus()
                ));
            }

            if ($update) {
                $updated = $uc->updatePrivacyNativeStatus($server['state']);
                if ($updated) {
                    if (empty($updatedStatus[$uid])) {
                        $updatedStatus[$uid] = [];
                    }
                    $updatedStatus[$uid][$ucId] = $updated;
                }
            }

            if ($server['state'] !== PrivacyAPI::CARD_STATE_CLOSED) {
                $__min = (int)($server['spend_limit'] ?? -1);
                $__duration = $server['spend_limit_duration'] ?? 'Unknown';

                if ($_min !== $__min || $_duration !== $__duration) {
                    $uid  = $user->getId();
                    $ucId = $uc->getId();
                    $this->line(sprintf(
                        'Sync: User %s card %s, privacy: %s per %s, local: %s per %s',
                        $uid, $ucId,
                        $__min, $__duration,
                        $_min, $_duration
                    ), 'info');
                    $fix = [
                        $min,
                        $duration,
                    ];
                }
            } else {
                return $fix;
            }
        }

        if ($min !== $_min || $duration !== $_duration) {
            $this->line(sprintf(
                'User %s card %s: %s per %s, should be %s per %s',
                $uid, $ucId,
                $_min, $_duration,
                $min, $duration
            ));
            $fix = [
                $min,
                $duration,
            ];
        }

        return $fix;
    }

    protected function query()
    {
        $total = 0;
        $users = [];
        $unlimited = [];
        $all = [];

        $service = PrivacyAPI::get();
        $start = PrivacyAPI::getStartDate();
        $now = Carbon::now();
        while ($start->lt($now)) {
            $page = 1;
            $end = $start->copy()->addMonthNoOverflow();
            $this->line('Listing cards created between ' . $start->format(Util::DATE_FORMAT_SEARCH) . ' ~ ' . $end->format(Util::DATE_FORMAT_SEARCH));

            while(true) {
                [
                    ,
                    $data
                ] = $service->listCards(
                    $page,
                    1000,
                    $start,
                    $end,
                );
                $this->line('page ' . $page. '/' . ($data['total_pages'] ?? ''));

                if (empty($data['data'])) {
                    break;
                }

                foreach ($data['data'] as $item) {
                    $all[$item['token']] = $item;

                    if ($item['state'] === PrivacyAPI::CARD_STATE_CLOSED) {
                        continue;
                    }
                    if (!$item['spend_limit']) {
                        $unlimited[] = [
                            'pan' => $item['last_four'],
                            'token' => $item['token'],
                        ];
                    } else {
                        $userId = UserCard::parseUserIdFromPrivacyNickName($item['memo'] ?? '');
                        if ($userId) {
                            $users[$userId] = max($users[$userId] ?? 0, $item['spend_limit']);
                        } else {
                            // Not bound to any user
                            $total += $item['spend_limit'];
                        }
                    }
                }

                if ($data['total_pages'] <= $page) {
                    break;
                }
                $page++;
                Util::usleep(300, 1000);
            }
            $start = $end;
        }

        if ($unlimited) {
            SlackService::alert('*`Unlimited SPENDING LIMIT` cards*. Please fix ASAP in Privacy\'s portal.',
                $unlimited, [
                    SlackService::MENTION_HANS,
                ]);
        }

        // Calculate the total spending limit
        foreach ($users as $spendLimit) {
            $total += $spendLimit;
        }

        Data::set('privacy_total_spend_limit', $total);
        Data::set('privacy_total_issued_cards', count($all));

        return $all;
    }
}
