<?php

namespace UsUnlockedBundle\Command;

use CoreBundle\Command\BaseHostedCommand;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use CoreBundle\Entity\UserCard;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use PortalBundle\Util\RegisterStep;
use CoreBundle\Command\BaseCommand;
use CoreBundle\Entity\UserCardTransaction;
use Carbon\Carbon;
use CoreBundle\Entity\Email;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;

class ExportLoadsOfUserCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:usu:export:load-for-users')
            ->setDescription('Export loads for users');
    }

    protected function  execute(InputInterface $input, OutputInterface $output)
    {
        $this->prepare($input, $output, true);


        Util::$platform = Platform::ternCommerce();

        $now = Carbon::now()->subMonthNoOverflow()->endOfMonth();
  
        $query = $this->em->getRepository(UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->join('uc.user', 'u')
            ->join('uc.card', 'c')
            ->join('u.country', 'ct')
            ->join('c.cardProgram', 'cp')
            ->where($this->expr->in('ucl.loadStatus', ':loadStatuses'))
            ->andWhere('cp.platform = :platform')
            ->andWhere($this->expr->gt('ucl.receivedAt', ':receivedAt'))
            ->andWhere($this->expr->eq('ucl.type', ':type'))
            ->setParameter('platform', Platform::ternCommerce())
            ->setParameter('receivedAt',   $now->subMonths(12))
            ->setParameter('type', 'load_card')
            ->setParameter('loadStatuses', [
                // UserCardLoad::LOAD_STATUS_RECEIVED,
                UserCardLoad::LOAD_STATUS_LOADED,
            ]);
        $loads = (clone $query)->select('ct.id, ct.name, ucl.loadAmount, ucl.loadAt')
                      ->orderBy('ucl.loadAt', 'desc')
        //            ->setMaxResults(10000)
                      ->getQuery()
                      ->getArrayResult();
        $countryList = (clone $query)->select('ct.id, ct.name')
                              ->groupBy('ct.id')
                              ->getQuery()
                              ->getArrayResult();
        $this->line('Found ' . count($loads) . ' loads...');
        // Log::debug('=======', $countryList);
        $res = [];
        $dateList = [];
        foreach ($loads as $load) {
            $loadAt = $load['loadAt']->format('Y-m');
            $countryId = $load['id'];
            if (!in_array($loadAt, $dateList)) {
              $dateList[] = $loadAt;
            }
            if (!isset($res[$loadAt])) {
              $res[$loadAt] = [];
            }
            if (!isset($res[$loadAt][$countryId])) {
              $res[$loadAt][$countryId] = 0;
            }
            $res[$loadAt][$countryId] += $load['loadAmount'];
        }

        $fileName = 'Load_report_for_last_year';

        $path = Util::secureDir() . 'usu_users_' . $fileName . date('Y-m-d-H-i-s') . '.csv';

        $target = fopen($path, 'wb');
        $header = ['Month'];
        foreach ($dateList as $date) {
          $header[] = $date;
        }

        fputcsv($target, $header);
        foreach ($countryList as $country) {
          $row = [
            $country['name']
          ];
          foreach ($dateList as $date) {
           $row[] = isset($res[$date][$country['id']]) ? round(($res[$date][$country['id']]) * 0.01, 2) : 0;
          }
          fputcsv($target, $row);
        }

        fclose($target);
        $this->line($path);
        $cc = [];
        if (!Util::isDev()) {
          $cc = [
            '<EMAIL>',
            '<EMAIL>'
          ];
        }
        $body = '<p>Attached is a list of USUnlocked Countries and Loads Trailing over the past 12 months.</p>';
        Email::sendWithTemplate(Util::isDev() ? '<EMAIL>' : '<EMAIL>', Email::TEMPLATE_SIMPLE_LAYOUT, [
          'name' => 'USUnlocked Team',
          'body' => $body,
          'Product_Team' =>  Util::$platform->getName(),
          'subject' => 'USUnlocked Countries and Loads Trailing 12 Months',
          '_attachments' => [
              $path,
          ],
          '_cc' => $cc,
          '_bcc' => [
              '<EMAIL>',
              '<EMAIL>'
          ],
        ]);
        $this->done();
        return 0;
    }
}
