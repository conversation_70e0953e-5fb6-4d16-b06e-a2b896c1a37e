<?php

namespace UsUnlockedBundle\Command;

use CoreBundle\Command\BaseCommand;
use CoreBundle\Utils\Util;
use CoreBundle\Utils\Log;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use CoreBundle\Entity\Config;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\UserService;

class UpdatePayPalSubscriptionCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('span:dev:update-paypal-subscription')
            ->setDescription('Update Paypal subscription')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->prepare($input, $output);
        $list = Util::em()->getRepository(PayPalSubscription::class)
                          ->createQueryBuilder('ps')
                          ->where(Util::expr()->neq('ps.status',':status'))
                          ->orWhere(Util::expr()->isNull('ps.status'))
                          ->setParameter('status', 'CANCELLED')
                          ->getQuery()
                          ->getResult();
        $count = count($list);
        $this->line('Found ' . $count . ' subscriptions need to check');
        $successCount = 0;
        $failedCount = 0;
        /**
         * @var PayPalSubscription $item
         */
        foreach($list as $i => $item) {
          $id = $item->getSubscriptionId();
          if ($id) {
            $user = $item->getUser();
            if (!$user) {
              continue;
            }
            $data = [];
            $res = UserService::updatePayPalSubscriptInfo($user, $id, $data);
            if ($res) {
              $successCount++;
            } else {
              $failedCount++;
            }
            $this->line('Synced ' . ($i + 1) . '/' . $count . ' subscription on user ' . $user->getId() .
                        ' with subscription id ' . $id . ' to status ' . ($data['status'] ?? null));
            Util::usleep(300);
          }
        }
        $this->line('Update PayPal subscription, success ' . $successCount . ' and failed ' . $failedCount);
        $this->done();
        return 0;
    }

}
