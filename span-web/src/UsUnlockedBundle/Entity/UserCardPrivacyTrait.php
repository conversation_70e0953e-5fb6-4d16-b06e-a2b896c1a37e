<?php


namespace UsUnlockedBundle\Entity;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\EntitySignature;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\Merchant;
use CoreBundle\Entity\MerchantType;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardBalance;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use DateTime;
use Exception;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SpendrBundle\Services\BrazeService;
use SpendrBundle\Services\ChangeBalanceService;
use SpendrBundle\Services\SegmentService;
use SpendrBundle\SpendrBundle;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\Rain\RainAPI;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\UserService;

trait UserCardPrivacyTrait
{
    public function toApiArrayForPrivacy($extra = false)
    {
        /** @var User $user */
        $user = $this->getUser();

        /** @var Merchant $merchant */
        $merchant = $this->getMerchant();
        $all = [
            'type' => $this->getType(),
            'hash' => $this->getHash(),
            'token' => $this->getToken(),
            'nickName' => $this->getNickName(),
            'pan' => Util::formatPan($this->getPan()),
            'color' => $merchant ? $merchant->getCategoryColor() : MerchantType::DEFAULT_COLOR,
            'status' => $this->getStatus(),
            'phone' => $user->getMobilephone(),
            'sandbox' => $this->isSandbox(),
            'merchant' => $merchant ? [
                'category' => $merchant->getCategory(),
                'categoryIcon' => $merchant->getCategoryIcon(),
                'name' => $merchant->getSimplifiedName(),
                'icon' => $merchant->getIconUrl(),
            ] : null,
            'provider' => $this->getParsedCardProvider(),
        ];

        if ($extra) {
            $all['spendLimit'] = Money::formatAmountToNumber($this->getSpendLimit());
            $all['spendLimitDuration'] = $this->getSpendLimitDuration();

            if ($this->isIssued()) {
                $slAmount = Util::meta($this, 'privacySpendLimit') ?: $this->getSpendLimit() ?: 0;
                $sl = Money::format($slAmount, 'USD', false);
                $duration = Util::meta($this, 'privacySpendLimitDuration') ?: $this->getSpendLimitDuration();
                if ($duration) {
                    $sl .= ' ' . PrivacyAPI::getSpendLimitDurationName($duration);
                }

                if ($duration !== PrivacyAPI::CARD_SPEND_LIMIT_DURATION_TRANSACTION) {
                    $consumed = $this->getConsumedAmountInDuration($duration);
                    $remaining = Money::format($slAmount - $consumed, 'USD', false);
                    $sl .= ' (' . $remaining . ' Remaining)';
                }

                $all['actualSpendLimit'] = $sl;
            }
        }
        return $all;
    }

    /**
     * @param $token
     * @return null|UserCard
     */
    public static function findByPrivacyToken($token)
    {
        $cpct = CardProgramCardType::PrivacyCardProgram();
        $rs = Util::em()->getRepository(\CoreBundle\Entity\UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.accountNumber = :token')
            ->andWhere(Util::expr()->in('uc.card', ':card'))
            ->setParameter('token', $token)
            ->setParameter('card', $cpct)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if ($rs) {
            return $rs[0];
        }
        return null;
    }


    /**
     * @param string $cardId
     * @return null|UserCard
     */
    public static function findByRainCardId(string $cardId)
    {
        return self::findByPrivacyToken($cardId);
    }

    /**
     * @param $token
     *
     * @return UserCard
     */
    public function setToken($token) {
        return $this->setAccountNumber($token);
    }

    public function getToken() {
        return $this->getAccountNumber();
    }

    public function isOneTimeCard() {
        return $this->getType() === PrivacyAPI::CARD_TYPE_SINGLE_USE;
    }

    public function isSandbox() {
        return Util::json($this, 'meta', 'privacySandbox') ?: false;
    }

    public function isArchived() {
        return Util::meta($this, 'archived') ?: false;
    }

    public function isPausedByDev() {
        return Util::meta($this, 'pausedByDev') ?: false;
    }

    public function getPrivacyDummyCard()
    {
        if ($this->isDummy()) {
            return $this;
        }
        return UserService::getDummyCard($this->getUser(), $this->getCard()->getCardProgram());
    }

    public function getRainCardId ()
    {
        return $this->getAccountNumber();
    }

    public function setRainCardId(string $rainCardId)
    {
        return $this->setAccountNumber($rainCardId);
    }

    /**
     * TODO: Don't forget to update spending limit every time this method is called.
     *
     * @param       $delta
     * @param       $type
     * @param       $comment
     * @param bool  $skipIfNotEnough
     * @param mixed $meta
     * @param array $context
     *
     * @return int
     * @throws \Throwable
     */
    public function updatePrivacyBalanceBy($delta, $type, $comment, $skipIfNotEnough = false, $meta = null, $context = [])
    {
        PrivacyService::$usersApprovedTransactions[$this->getUser()->getId()] = $comment;

        if ($delta === 0) {
            return $delta;
        }

        $dummy = $this->getPrivacyDummyCard();
        $user = $dummy->getUser();
        $oldBalance = $dummy->getBalance() ?: 0;

        $platform = null;
        if ($dummy->isSpendr()) {
        	$platform = Platform::spendr();
        	SlackService::prepareForPlatform($platform);
		}

        if ($delta > 0 && $oldBalance + $delta > CardProgramCardType::MAX_BALANCE_RELOADABLE && Util::startsWith($type, 'privacy_')) {
            $new = new UserCardLoad();
            $new->setUserCard($dummy)
                ->setInitialCurrency('USD')
                ->setInitialAmount($delta)
                ->setStatus(UserCardLoad::STATUS_COMPLETED)
                ->setPartner(LoadPartner::find(LoadPartner::SYSTEM))
                ->setMethod(LoadMethod::find(LoadMethod::PAYNAME_SYSTEM))
                ->setPayCurrency('USD')
                ->setPayAmount($delta)
                ->setCompletedAt(new DateTime())
                ->setLoadAmount($delta)
                ->setType(UserCardLoad::TYPE_LOAD_CARD)
                ->setLoadStatus(UserCardLoad::LOAD_STATUS_RECEIVED)
                ->setMeta(Util::j2s([
                    'fromUpdatePrivacyBalanceBy' => [
                        'type' => $type,
                        'comment' => $comment,
                    ],
                ]))
                ->setTransactionNo(Util::unique_id())
                ->setReceivedCurrency('USD')
                ->setReceivedAmount($delta)
                ->setReceivedLocalCurrency('USD')
                ->setReceivedLocalAmount($delta)
                ->setReload(true)
                ->setInitializedAt(new DateTime());
            Util::em()->persist($new);

            SlackService::alert('Moved the auto balance adjustment to the load queue to avoid balance exceeding $3500', [
                'user' => $user->getId(),
                'type' => $type,
                'comment' => $comment,
                'old_balance' => Money::format($oldBalance, 'USD'),
                'adjustment' => Money::format($delta, 'USD'),
            ]);
            return 0;
        }

        try {
            $delta = $dummy->updateBalanceBy($delta, $type, $comment, $meta, $skipIfNotEnough);
        } catch (Exception $e) {
            SlackService::exception('Failed to update balance', $e, [
                'user' => $user->getId(),
                'delta' => $delta,
                'type' => $type,
                'comment' => $comment,
            ]);
            $delta = 0;
        }
        $newBalance = $dummy->getBalance();

        if ($newBalance < 0) {
            $msg = sprintf(
                'Negative Account Balance Alert: User `%s` has a `%s` balance. Type: `%s`, Comment: `%s`',
                $user->getId(),
                $dummy->getBalanceText(),
                $type,
                $comment
            );
            $mentions = [];
            if ($newBalance <= -5000) {
                $mentions = SlackService::getMentionGroup('neg_balance', $platform);
            }
            if ($dummy->isUsUnlocked()) {
                SlackService::prepareForCompliance();
            }
            SlackService::alert($msg, $context, $mentions);
            RegisterStep::updateUser($this->getUser(), false);
        } else if ($oldBalance < 0 && $newBalance >= 0) {
            $msg = sprintf(
                'Account balance becomes positive: User `%s` has a `%s` balance. Type: `%s`, Comment: `%s`',
                $user->getId(),
                $dummy->getBalanceText(),
                $type,
                $comment
            );
            $mentions = [];
            if ($oldBalance <= -5000) {
                $mentions = SlackService::getMentionGroup('neg_balance', $platform);
            }
            if ($dummy->isUsUnlocked()) {
                SlackService::prepareForCompliance();
            }
            SlackService::bell($msg, [
                'old_balance' => Money::format($oldBalance, $dummy->getCurrency() ?: 'USD'),
            ], $mentions);

            RegisterStep::updateUser($user, false);

            if ($dummy->isSpendr() && $user->inTeams(SpendrBundle::getConsumerRoles())) {
                ChangeBalanceService::afterBalanceBecomesPositive($user);
            }
        }

        return $delta;
    }

    public static function convertPrivacyNativeStateToStatus($state)
    {
        $new = null;
        if ($state === PrivacyAPI::CARD_STATE_OPEN) {
            $new = UserCard::STATUS_ACTIVE;
        } else if ($state === PrivacyAPI::CARD_STATE_PAUSED) {
            $new = UserCard::STATUS_INACTIVE;
        } else if ($state === PrivacyAPI::CARD_STATE_CLOSED) {
            $new = UserCard::STATUS_CLOSED;
        }
        return $new;
    }

    public function updatePrivacyNativeStatus($state)
    {
        $old = $this->getStatus();
        $new = self::convertPrivacyNativeStateToStatus($state) ?: $old;
        if ($old !== $new) {
            $this->setStatus($new, false, false);
            $this->setNativeStatus($state);
            Util::em()->persist($this);
            return $state;
        }
        return null;
    }

    public static function convertRainNativeStateToStatus($state)
    {
        $new = null;
        if ($state === RainAPI::CARD_STATE_ACTIVE) {
            $new = UserCard::STATUS_ACTIVE;
        } else if ($state === RainAPI::CARD_STATE_INACTIVE || $state === RainAPI::CARD_STATE_LOCKED ) {
            $new = UserCard::STATUS_INACTIVE;
        } else if ($state === RainAPI::CARD_STATE_CLOSED) {
            $new = UserCard::STATUS_CLOSED;
        }
        return $new;
    }

    public function updateRainNativeStatus($state)
    {
        $old = $this->getStatus();
        $new = self::convertRainNativeStateToStatus($state) ?: $old;
        if ($old !== $new) {
            $this->setStatus($new, false, false);
            $this->setNativeStatus($state);
            Util::em()->persist($this);
            return $state;
        }
        return null;
    }

    /**
     * Ever created a pending+ payments so that he can edit the freight forwarding settings
     */
    public function hasInitiatedLoading()
    {
        return $this->hasLoadWithStatus([
            UserCardLoad::LOAD_STATUS_INITIATED,
            UserCardLoad::LOAD_STATUS_PENDING,
            UserCardLoad::LOAD_STATUS_CONFIRMED,
            UserCardLoad::LOAD_STATUS_RECEIVED,
            UserCardLoad::LOAD_STATUS_LOADED,
        ]);
    }

    public function getPrivacyNickname()
    {
        $user = $this->getUser();
        $nickName = $this->getNickName() ?: '';

        if (!$nickName && $this->getType() === PrivacyAPI::CARD_TYPE_SINGLE_USE) {
            $nickName = PrivacyAPI::CARD_TYPE_SINGLE_USE;
        }

        return sprintf('%s - %s', $user->getId(), $nickName);
    }

    public static function parseUserIdFromPrivacyNickName($memo)
    {
        $matches = [];
        preg_match('|(\d+)( - .*)?|', $memo, $matches);
        if (!empty($matches[1])) {
            return $matches[1];
        }
        return null;
    }

    public function getConsumedAmountInDuration($duration)
    {
        $date = Carbon::now();
        if ($duration === PrivacyAPI::CARD_SPEND_LIMIT_DURATION_TRANSACTION) {
            return 0;
        }
        if ($duration === PrivacyAPI::CARD_SPEND_LIMIT_DURATION_MONTHLY) {
            $date->subMonthNoOverflow();
        } else if ($duration === PrivacyAPI::CARD_SPEND_LIMIT_DURATION_ANNUALLY) {
            $date->subYearNoOverflow();
        } else if ($duration === PrivacyAPI::CARD_SPEND_LIMIT_DURATION_FOREVER) {
            $date = Carbon::createFromTimestamp(0);
        }
        return Util::em()->getRepository(UserCardTransaction::class)
            ->createQueryBuilder('uct')
            ->where('uct.userCard = :uc')
            ->andWhere(Util::expr()->in('uct.accountStatus', ':statuses'))
            ->andWhere('uct.txnTime >= :txnTime')
            ->setParameter('uc', $this)
            ->setParameter('statuses', [
                PrivacyAPI::TRANSACTION_STATUS_PENDING,
                PrivacyAPI::TRANSACTION_STATUS_SETTLING,
                PrivacyAPI::TRANSACTION_STATUS_SETTLED,
            ])
            ->setParameter('txnTime', Util::toUTC($date))
            ->select('sum(uct.txnAmountUSD)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @return array 0: The log when it became negative
     *               1: All transactions happened when/causes negative
     */
    public function getLatestLogBecameNegative()
    {
        $repo = Util::em()->getRepository(UserCardBalance::class);
        $rs = $repo->createQueryBuilder('ucb')
            ->where('ucb.userCard = :userCard')
            ->setParameter('userCard', $this)
            ->orderBy('ucb.id', 'desc')
            ->select('ucb.currentBalance balance, ucb.id')
            ->getQuery()
            ->getArrayResult();
        $id = null;
        $trans = [];
        foreach ($rs as $r) {
            if ($r['balance'] < 0) {
                $id = $r['id'];
                $trans[] = $id;
            } else {
                break;
            }
        }
        if (!$id) {
            return [null, []];
        }
        $ucb = $repo->find($id);

        $all = [];
        foreach ($trans as $tran) {
            /** @var UserCardBalance $tran */
            $tran = $repo->find($tran);
            $data = $tran->parseUsuData();
            if (!empty($data['tranId'])) {
                $all[] = $data['tranId'];
            }
        }
        return [$ucb, $all];
    }
}
