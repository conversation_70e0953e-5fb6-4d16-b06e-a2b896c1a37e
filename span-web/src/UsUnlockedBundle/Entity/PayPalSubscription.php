<?php

namespace UsUnlockedBundle\Entity;

use Carbon\Carbon;
use CoreBundle\Entity\BaseEntity;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use SalexUserBundle\Entity\User;
use CoreBundle\Utils\Util;
use CoreBundle\Entity\Config;

/**
 * PayPalSubscription
 *
 * @ORM\Table(name="usu_paypal_subscription")
 * @ORM\Entity(repositoryClass="UsUnlockedBundle\Repository\PayPalSubscriptionRepository")
 */
class PayPalSubscription extends BaseEntity
{
    public const STATUS_ACTIVE = 'ACTIVE';
    public const STATUS_CANCELLED = 'CANCELLED';
    public const STATUS_SUSPENDED = 'SUSPENDED';

    public const PLAN_TYPE_MONTHLY = 'monthly';
    public const PLAN_TYPE_ANNUALLY = 'annually';
    public const PLAN_TYPE_WAIVED = 'waived';

    public const AMOUNT_MONTHLY = 495;
    public const AMOUNT_ANNUALLY = 3995;
    public const AMOUNT_WAIVED = 0;
    public const AMOUNT_MONTHLY_COST = 74;
    public const AMOUNT_ANNUALLY_COST = 248;


     /**
     * @param $subscriptionId
     *
     * @return static|null
     */
    public static function findBySubscriptionId($subscriptionId)
    {
        return Util::em()->getRepository(static::class)->findOneBy([
            'subscriptionId' => $subscriptionId,
        ]);
    }

     /**
     * @param $id
     *
     * @return static|null
     */
    public static function find($id)
    {
        return Util::em()->getRepository(static::class)->findOneBy([
            'id' => $id,
        ]);
    }

    public static function getPlanById($planId)
    {
        if ($planId == Config::get('usu_monthly_paypal_plan')) {
            return self::PLAN_TYPE_MONTHLY;
        } else if (in_array($planId, ['P-9NN28240DU182515BNCGDJCY', Config::get('usu_annually_paypal_plan'), Config::get('usu_upgrade_annually_paypal_plan')])) {
            return self::PLAN_TYPE_ANNUALLY;
        }
        return '';
    }

      /**
     * @param User $user
     *
     * @return static|null
     */
    public static function findByUser(User $user)
    {
        $res = Util::em()->getRepository(static::class)
                          ->createQueryBuilder('ps')
                          ->where(Util::expr()->eq('ps.user', ':user'))
                          ->setParameter('user', $user)
                          ->orderBy('ps.id', 'desc')
                          ->setMaxResults(1)
                          ->getQuery()
                          ->getResult();
        return $res ? $res[0] : null;
    }

      /**
     * @param User $user
     *
     * @return static|null
     */
    public static function findCurrentActiveMonthly(User $user)
    {
        $res = Util::em()->getRepository(static::class)
                          ->createQueryBuilder('ps')
                          ->where(Util::expr()->eq('ps.user', ':user'))
                          ->andWhere(Util::expr()->eq('ps.planType', ':planType'))
                          ->andWhere(Util::expr()->eq('ps.status', ':status'))
                          ->setParameter('planType', self::PLAN_TYPE_MONTHLY)
                          ->setParameter('status', self::STATUS_ACTIVE)
                          ->setParameter('user', $user)
                          ->orderBy('ps.id', 'desc')
                          ->setMaxResults(1)
                          ->getQuery()
                          ->getResult();
        return $res ? $res[0] : null;
    }

    public function getEndAt(): ?\DateTime
    {
        if ($this->getStatus() !== self::STATUS_ACTIVE) {
            $startedAt = Carbon::instance($this->getStartAt());
            $month = 1;
            if ($this->getCurrentPlanType() === self::PLAN_TYPE_ANNUALLY) {
                $month = 12;
            }
            return $startedAt->addMonths($month);
        }
        return null;
    }

    public function getAmount(): int
    {
        $type = $this->getCurrentPlanType();
        if ($type === self::PLAN_TYPE_MONTHLY) {
            return self::AMOUNT_MONTHLY;
        }
        if ($type === self::PLAN_TYPE_ANNUALLY) {
            return self::AMOUNT_ANNUALLY;
        }
        return 0;
    }

    /**
     * var integer $id
     *
     * @ORM\Id
     * @ORM\Column(type="integer")
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id", onDelete="cascade")
     * @Serializer\Exclude()
     */
    private $user;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true, options={"comment":"subscription status"})
     */
    private $status;


    /**
     * @var string
     *
     * @ORM\Column(name="subscription_id", type="string", length=255, options={"comment":"subscription id"})
     */
    private $subscriptionId;

     /**
     * @var integer
     * @ORM\Column(name="quantity", type="bigint",nullable=true, options={"comment":"subscription quantity"})
     */
    private $quantity;

     /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

      /**
     * @var string
     *
     * @ORM\Column(name="plan_type", type="string",  nullable=true, length=255, options={"comment":"subscription type monthly annually"})
     */
    private $planType;

    /**
     * @var \DateTime $manualAt
     * @ORM\Column(name="manual_at", type="datetime", nullable=true, options={"comment":"Automatically deduct the monthly fee time"})
     */
    private $manualAt;

    /**
     * @var string
     *
     * @ORM\Column(name="manual_plan_type", type="string",  nullable=true, length=255, options={"comment":"subscription type monthly annually by system"})
     */
    private $manualPlanType;

    public function getCurrentPlanType()
    {
        return $this->manualPlanType ?? $this->planType;
    }

    public function getStartAt()
    {
        return $this->manualAt ?? $this->createdAt;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getSubscriptionId(): ?string
    {
        return $this->subscriptionId;
    }

    public function setSubscriptionId(string $subscriptionId): static
    {
        $this->subscriptionId = $subscriptionId;

        return $this;
    }

    public function getQuantity(): ?string
    {
        return $this->quantity;
    }

    public function setQuantity(?string $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getMeta(): ?string
    {
        return $this->meta;
    }

    public function setMeta(?string $meta): static
    {
        $this->meta = $meta;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getPlanType(): ?string
    {
        return $this->planType;
    }

    public function setPlanType(?string $planType): static
    {
        $this->planType = $planType;

        return $this;
    }

    public function getManualAt(): ?\DateTimeInterface
    {
        return $this->manualAt;
    }

    public function setManualAt(?\DateTimeInterface $manualAt): static
    {
        $this->manualAt = $manualAt;

        return $this;
    }

    public function getManualPlanType(): ?string
    {
        return $this->manualPlanType;
    }

    public function setManualPlanType(?string $manualPlanType): static
    {
        $this->manualPlanType = $manualPlanType;

        return $this;
    }
}
