<?php


namespace UsUnlockedBundle\Entity;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\Platform;
use CoreBundle\Entity\UserBillingAddress;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use CoreBundle\Entity\Role;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use PortalBundle\Util\RegisterStep;
use SalexUserBundle\Entity\User;
use SalexUserBundle\Entity\UserConfig;
use Symfony\Component\Intl\Locales;
use TransferMexBundle\Services\MemberService;
use UsUnlockedBundle\Services\Sumsub\SumsubAPI;
use UsUnlockedBundle\Services\UserService;
use UsUnlockedBundle\UsUnlockedBundle;

trait UserTrait
{
    /**
     * @param $id
     *
     * @return User|object
     */
    public static function find($id)
    {
        if (!$id) {
            return null;
        }
        return Util::em()->getRepository(self::class)
            ->find($id);
    }

    /**
     * @param $id
     * @param string|null $column
     *
     * @return User|object
     */
    public static function findBy($id, $column = 'id')
    {
        if (!$id || !$column) {
            return null;
        }
        return Util::em()->getRepository(self::class)
            ->findOneBy([
                $column => $id
            ]);
    }

    /**
     * @param $email
     *
     * @return User|null
     */
    public static function findByEmail($email)
    {
        if (!$email) {
            return null;
        }
        return Util::em()->getRepository(self::class)
            ->findOneBy([
                'email' => $email,
            ]);
    }

    public function refresh()
    {
        return Util::refresh($this);
    }

    public function isEmailValid()
    {
        return Config::isEmailValid($this->getEmail());
    }

    public function setFreightForwarder(UserBillingAddress $uba)
    {
        $uba->setUser($this)
            ->setUserCard(null);
        Util::persist($uba);

        Util::updateMeta($this, [
            'freightForwarder' => $uba->getId(),
        ]);
    }

    /**
     * @return UserBillingAddress|null
     */
    public function getFreightForwarder()
    {
        $ffId = Util::meta($this, 'freightForwarder');
        if (!$ffId) {
            return null;
        }
        return Util::em()->getRepository(UserBillingAddress::class)
            ->find($ffId);
    }

    public function getLastActiveAt(): ?DateTime
    {
        $at = Data::get('session_at_' . $this->getId());
        if ($at) {
            return Carbon::createFromTimestamp($at);
        }
        return $this->getLastLogin() ?: $this->getCreatedAt();
    }

    public function getUsuShippingAddress(UserCard $_uc = null)
    {
        if ($_uc) {
            return $_uc->getBillingAddress();
        }

        /** @var ArrayCollection $ucs */
        $ucs = $this->getCardsInUsUnlocked(null, false);
        /** @var UserCard $uc */
        foreach ($ucs as $uc) {
            $ba = $uc->getBillingAddress();
            if ($ba) {
                return $ba;
            }
        }
        return null;
    }

    public function getUsuDummyCard()
    {
        return UserService::getDummyCard($this);
    }

    public function getUsuDummyCardOnly()
    {
        return UserService::getDummyCardOnly($this);
    }

    public function getReferralCodeUsageLimit()
    {
        $default = Util::isInternalTester($this) ? 1000 : 10;
        $limit = Util::meta($this, 'referralCodeUsageLimit');
        return $limit === null ? $default : (int)$limit;
    }

    public function getOpenCardLimit()
    {
        $limit = Util::meta($this, 'privacyOpenCardLimit');
        return $limit === null ? 99 : (int)$limit;
    }

    public function getUnusedOpenCardLimit()
    {
        $limit = Util::meta($this, 'privacyUnusedOpenCardLimit');
        return $limit === null ? 2 : (int)$limit;
    }

    public function getDailyIssuingOneTimeCardLimit()
    {
        $default = 10;
        $at = Util::meta($this, 'privacyIssuingOneTimeCardLimitAt');
        if ($at) {
            $at = Carbon::createFromTimestamp($at);
            if ($at->addHours(24)->gt(Carbon::now())) {
                $limit = Util::meta($this, 'privacyIssuingOneTimeCardLimit');
                return $limit === null ? $default : (int)$limit;
            }
        }
        return $default;
    }

    public function getDailyIssuingCardLimit()
    {
        $default = 30;
        $at = Util::meta($this, 'privacyIssuingCardLimitAt');
        if ($at) {
            $at = Carbon::createFromTimestamp($at);
            if ($at->addHours(24)->gt(Carbon::now())) {
                $limit = Util::meta($this, 'privacyIssuingCardLimit');
                return $limit === null ? $default : (int)$limit;
            }
        }
        return $default;
    }

    public function getCorrectedTimezone(): ?string
    {
        return Util::correctTimezone($this->getTimezone());
    }

    public function ensureConfig($persist = true)
    {
        $config = $this->getConfig();
        if (!$config) {
            $config = new UserConfig();
            $config->setUser($this);
            $this->setConfig($config);

            if ($persist) {
                Util::persist($config);
            }
        }
        return $config;
    }

    public function getLanguageCode(): string
    {
        if (Data::has('current-language-' . $this->getId())) {
            return MemberService::getCurrentLanguage($this) ?: 'en';
        }
        $lang = Util::meta($this, 'language') ?? [];
        $lang = mb_strtolower(trim((string)($lang['name'] ?? 'en')));
        if (str_contains($lang, 'english')) {
            $lang = 'en';
        } else if (str_contains($lang, 'spanish')) {
            $lang = 'es';
        }
        return $lang ?: 'en';
    }

    public function getCorrectedLanguageCode(): string
    {
        $all = Data::callback('locale_codes', [], static function () {
            return Locales::getNames();
        }, false);
        $code = $this->getLanguageCode();
        if (isset($all[$code])) {
            return $code;
        }
        return 'en';
    }

    public function getOnboardStatus()
    {
        return RegisterStep::text($this->getRegisterStep());
    }

    public function saveTotalMembershipFeeToPay($amount)
    {
        Util::updateMeta($this, [
            'totalMembershipFeeToPay' => $amount,
        ]);
    }

    private function getTotalMembershipFeeToPay()
    {
        // https://app.asana.com/0/1198894756203992/1201802042815418/f
        // Membership fee has been removed
        if (!$this->inTeam(Role::ROLE_CASH_ON_WEB_MEMBER)) {
          return 0;
        }


        $total = Util::meta($this, 'totalMembershipFeeToPay');
        if ($total === null) {
            $expr = Util::expr();
            $rs = Util::em()->getRepository(UserCardLoad::class)
                ->createQueryBuilder('ucl')
                ->join('ucl.userCard', 'uc')
                ->where('uc.user = :user')
                ->andWhere($expr->orX(
                    $expr->isNull('ucl.partner'),
                    $expr->neq('ucl.partner', ':partner')
                ))
                ->andWhere($expr->like('ucl.feeStructure', ':feeStructure'))
                ->setParameter('user', $this)
                ->setParameter('partner', LoadPartner::get(LoadPartner::SYSTEM))
                ->setParameter('feeStructure', '%"membershipFee"%')
                ->orderBy('ucl.id', 'asc')
                ->setMaxResults(1)
                ->getQuery()
                ->getResult()
            ;
            if ($rs) {
                /** @var UserCardLoad $ucl */
                $ucl = $rs[0];
                return $ucl->getMembershipFeeUSD();
            }
            return 1500; // All old users have $15 membership fee
        }
        return max($total, 0);
    }

    public function getUnpaidMembershipFee(UserCardLoad $except = null)
    {
        if (Util::meta($this, 'skipMembershipFee')) {
            return 0;
        }
        $paid = 0;
        $earliest = Carbon::create(2020, 11, 25, 9, 0, 0);
        $received = $this->everLoaded($except, true, true);
        /** @var UserCardLoad $ucl */
        foreach ($received as $ucl) {
            $uc = $ucl->getUserCard();
            if (Util::meta($uc, 'everLoaded')) { // imported users
                return 0;
            }
            // old paid users will be regarded as membership fee paid.
            $loadAt = $ucl->getLoadAt();
            if ($loadAt && $earliest->gt($loadAt)) {
                return 0;
            }
            $mf = $ucl->getMembershipFeeUSD();
            if ($mf !== null) {
                $paid += $mf;
            }
            $fs = Util::s2j($ucl->getFeeStructure());
            if (!empty($fs['discount']['membershipFee'])) {
                $paid += $fs['discount']['membershipFee'];
            }
        }

        $total = $this->getTotalMembershipFeeToPay();
        return max($total - $paid, 0);
    }

    public function getLegacyBalance()
    {
        $sum = 0;
        /** @var UserCard $uc */
        foreach($this->getCardsInPrograms([
            CardProgram::usunlockedLegacy(),
        ]) as $uc) {
            $b = $uc->getBalance();
            if ($b) {
                $sum += $b;
            }
            $b = $uc->getLocalBalance();
            if ($b) {
                $sum += $b;
            }
        }
        return $sum;
    }

    public function getUsuBalanceOnly()
    {
        $uc = $this->getUsuDummyCardOnly();
        if (!$uc) {
            return 0;
        }
        return $uc->getBalance();
    }

    public function getAccountBalance()
    {
        $sum = 0;
        /** @var UserCard $uc */
        foreach($this->getIssuedCards() as $uc) {
            $b = $uc->getBalance();
            if ($b) {
                $sum += $b;
            }
        }
        return $sum;
    }

    public function getLocalBalance()
    {
        $sum = 0;
        /** @var UserCard $uc */
        foreach($this->getIssuedCards() as $uc) {
            $b = $uc->getLocalBalance();
            if ($b) {
                $sum += $b;
            }
        }
        return $sum;
    }

    public function hasLegacyBalance()
    {
        /** @var UserCard $uc */
        foreach ($this->getIssuedCards() as $uc) {
            if ($uc->isUsUnlockedLegacy()) {
                if ($uc->getBalance() > 0 || $uc->getLocalBalance() > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public function isUsuIdVerified()
    {
        /** @var UserIdVerify $uiv */
        $uiv = $this->getIdVerify();
        if (!$uiv) {
            return false;
        }
        return $uiv->isUsuAccepted();
    }

    public function isUsuIdVerifiedAndValid(): bool
    {
        $uiv = $this->getIdVerify();
        if (!$uiv) {
            return false;
        }
        if (!$uiv->isUsuAccepted()) {
            return false;
        }
        $expiredAt = $uiv->getExpireAt();
        if ($expiredAt && Carbon::now()->gt($expiredAt)) {
            return false;
        }
        return true;
    }

    public function isRegisterRainUser() {
      return UserService::getRainUserId($this);
    }

    public function isUsuSumSubIdVerified () {
        return $this->getSumsubKycStatus() === 'Approved';
    }

    public function canSwitchPaypalPlan () {
        $subscription = PayPalSubscription::findByUser($this);
        if (!$subscription) {
            return false;
        }
        return $subscription->getCurrentPlanType() === PayPalSubscription::PLAN_TYPE_MONTHLY &&
               $subscription->getStatus() === PayPalSubscription::STATUS_ACTIVE;
    }

    /**
     * Raw Sumsub Applicant Review status
     * @return string|null
     */
    public function getSumsubApplicantStatus() {
        $config = $this->ensureConfig();    
        if ($config->getSumsubStatus()) {
            return $config->getSumsubStatus();
        }
        return Util::meta($this, 'sumsubApplicantStatus');
    }

    public function getSumsubApplicantId() {
        $config = $this->ensureConfig();
        if ($config->getSumsubApplicantId()) {
            return $config->getSumsubApplicantId();
        }
        return Util::meta($this, 'sumsubApplicantId');
    }

    /**
     * @return string Approved/Rejected/No KYC
     */
    public function getSumsubKycStatus()
    {
        /** @var UserIdVerify $uiv */
        $status = $this->getSumsubApplicantStatus();
        if (!$status) {
          return 'No KYC';
        }
        return $status === SumsubAPI::STATUS_GREEN ? 'Approved' : 'Rejected';
    }

    /**
     * @return bool|mixed|null true = past, string = fail error, null = unchecked yet
     */
    public function getOfacStatus()
    {
        $meta = Util::meta($this);
        if (!empty($meta['ofacSuccess']) || !empty($meta['ofacManual'])) {
            return true;
        }
        if (!empty($meta['ofacFailed'])) {
            return $meta['ofacFailed'];
        }
        return null;
    }

    public function getIdScanStatus()
    {
        $meta = Util::meta($this);
        if (!empty($meta['idScanManual'])) {
            return true;
        }
        /** @var UserIdVerify $uiv */
        $uiv = $this->getIdVerify();
        if (!$uiv) {
            return null;
        }
        if ($uiv->isAccepted()) {
            return true;
        }
        return $uiv->getReason() ?: null;
    }

    public function addClosureReason(string $reason)
    {
        $all = Util::s2je($this->getClosureReason() ?? '[]');
        if (!in_array($reason, $all)) {
            $all[] = $reason;
            $this->setClosureReason(Util::j2se($all));
        }
        return $this;
    }

    public function isLegalAgreementsAccepted(?string $field = null): bool
    {
        $meta = Util::meta($this);
        $fields = $field ? [$field] : UsUnlockedBundle::TERMS_FIELDS;
        foreach ($fields as $f) {
            $f = Util::ensurePrefix($f, 'terms_');
            if (empty($meta[$f])) {
                return false;
            }
        }
        return true;
    }

    public function acceptLegalAgreements(array $fields): static
    {
        if (empty($fields)) {
            return $this;
        }
        $changed = false;
        $when = date('c');
        $meta = Util::meta($this);
        foreach ($fields as $field) {
            $field = Util::ensurePrefix($field, 'terms_');
            if (empty($meta[$field])) {
                $meta[$field] = $when;
                $changed = true;
            }
        }
        if ($changed) {
            $this->setMeta(Util::j2s($meta))
                ->persist();
        }
        return $this;
    }
}
