<?php


namespace UsUnlockedBundle\Services\Sumsub;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserIdVerify;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\Rain\RainService;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\UserRefererService;
use UsUnlockedBundle\Services\UserService;
use PortalBundle\Util\RegisterStep;
class SumsubService
{
  public const RAIN_CLIENT_ID = 'raincards.xyz_43942';

  public const SUMSUB_COST = 300;

  public static function getShareToken(User $user): string
  {
      $service = SumsubAPI::get();
      $params = [
          'applicantId' => $user->getSumsubApplicantId(),
          'forClientId' => self::RAIN_CLIENT_ID
      ];
      /** @var ExternalInvoke $ei */
      [$ei, $result] = $service->getShareToken($params);
      if ($ei && $ei->isFailed()) {
          throw PortalException::createFromEi('Failed to create KYC share token (EI: ' . $ei->getId() . ')', $ei);
      }
      $token = $result['token'] ?? '';
      if (!$token) {
          throw PortalException::create('Failed to create KYC share token as the token is empty');
      }
      return $token;
  }

  public static function getLevelName(User $user)
  {
      if ($user->isInternalTester()) {
          return 'basic-kyc-level (US available)';
      }
      return Config::get('sumsub_level_name', 'basic-kyc-level');
  }

  public static function getAccessTokens(User $user) {
    try {
      $service = SumsubAPI::get();
      $params = [
        'userId' => $user->getId(),
        'levelName' => self::getLevelName($user),
      ];
      /** @var ExternalInvoke $ei */
      [$ei, $result] = $service->getAccessTokens($params);
      if ($ei && !$ei->isFailed()) {
        return $result['token'];
      }
    } catch (\Throwable $e) {
      Log::debug('Get sumsub share token error: ' . $e->getMessage());
    }
    return false;
  }

  public static function updateMember(User $user, bool $force = false): ?string
  {
    $sumsubApplicantStatus = $user->getSumsubApplicantStatus();
    if ($sumsubApplicantStatus === SumsubAPI::STATUS_GREEN) {
        $result = self::checkAndRegisterRainAccount($user);
        if (!$force) {
            return $result;
        }
    }
    try {
      $service = SumsubAPI::get();
      [$ei, $result] = $service->getApplicantData($user->getId());
        if ($ei && !$ei->isFailed()) {
            $reviewResult                 = $result['review']['reviewResult'] ?? [];
            $currentSumsubApplicantStatus = $reviewResult['reviewAnswer'] ?? '';
            $reason = str_replace(
                'contact the Company where you try to verify your profile',
                'contact us',
                $reviewResult['moderationComment'] ?? ''
            );
            Util::updateMeta($user, [
                'sumsubApplicantStatus'        => $currentSumsubApplicantStatus,
                'sumsubApplicantId'            => $result['id'],
                'sumsubApplicantReason'        => $reason,
                'sumsubApplicantClientComment' => $reviewResult['clientComment'] ?? '',
                'sumsubApplicantInfo'          => $result['info'] ?? [],
            ]);
            self::updateIdVerification($user, $result, $ei);
            if ($currentSumsubApplicantStatus !== SumsubAPI::STATUS_GREEN) {
                return $reason ?: null;
            }
        } else if ($ei instanceof ExternalInvoke) {
            $msg = $ei->getError();
            if (Util::containsSubString($msg, [
                'Applicant not found',
            ])) {
                return null;
            }
            return $msg;
        }
        return null;
    } catch (\Throwable $e) {
        Log::debug('Update sumsub info for the member error: ' . $e->getMessage());
        return $e->getMessage();
    }
  }

  private static function checkAndRegisterRainAccount(User $user): ?string
  {
      return Data::once('usu_register_rain_account_' . $user->getId(), function () use ($user) {
          return self::doCheckAndRegisterRainAccount($user);
      }, 60); // 1 minute cooldown
  }

  private static function doCheckAndRegisterRainAccount(User $user): ?string
  {
    // if kyc pass register Rain account
    if (!UserService::getRainUserId($user)) {
        try {
            $token = self::getShareToken($user);
            RainService::registerRainBySumsub($user, $token);
            UserRefererService::reward($user);
        } catch (\Throwable $e) {
            $alert = Util::isLive();
            $msg = $e->getMessage();

            if (Util::containsSubString($msg, [
                "body must have required property 'nationalId'"
            ])) {
                $msg = 'Failed to recognize the national ID from your document. Please try again (with a different document type).';

                Data::once('usu_sumsub_reset_alert_' . $user->getId(), function () use ($user) {
                    SlackService::warning('Please reset the applicant so that he/she can try again', [
                        'user' => $user->getSignature(),
                        'country' => $user->getCountryName(),
                        'sumsubApplicant' => $user->getSumsubApplicantId(),
                    ], SlackService::GROUP_SUMSUB_REVIEW);
                }, 86400);
            }

            /* Sumsub status has been invalid so needs to be updated. */
            if (str_contains($msg, 'When copying an applicant with preserving its status, it must be completed')) {
                Service::sendAsync('/t/cron/usu/sumsub/update-member/' . $user->getId());
                $alert = false;
            } else if (Util::containsSubString($msg, [
                'Token is invalid, make sure you generate a new one each time',
                'Please fill your phone number on the profile page and try again',
            ])) {
                $alert = false;
            }
            if ( ! $alert) {
                Log::debug('Failed to register Rain account for user ' . $user->getId() . ': ' .
                           $e->getMessage());
            } else {
                SlackService::exception('Failed to register Rain account by Sumsub', $e, [
                    'user' => $user->getId(),
                    'country' => $user->getCountryName(),
                    'path' => Util::request()->getPathInfo(),
                    'sumsubApplicant' => $user->getSumsubApplicantId(),
                ], SlackService::GROUP_DEV);
            }

            return $msg;
        }
    }
    return null;
  }

  /**
   * reset sumsub kyc
   */
   public static function resetApplicant(User $user) {
       $service = SumsubAPI::get();
       [$ei, $result] = $service->resetApplicant($user->getId());
       if ($ei && !$ei->isFailed()) {
           Util::updateMeta($user, [
               'sumsubApplicantStatus' => '',
               'sumsubApplicantId' => $result['id']
           ]);
           self::updateIdVerification($user, $result, $ei);
       }
       return [$ei, $result];
   }

   public static function updateIdVerification(User $user, array $result, ?ExternalInvoke $ei = null)
   {
       $oldUiv = $user->getIdVerify();

       $reviewResult = $result['review']['reviewResult'] ?? [];
       $status = $reviewResult['reviewAnswer'] ?? '';
       $reason = str_replace(
           'contact the Company where you try to verify your profile',
           'contact us',
           $reviewResult['moderationComment'] ?? ''
       );
       $comment = $reviewResult['clientComment'] ?? '';
       if ($comment) {
           $reason .= ' (' . $comment . ')';
       }

       $uiv = new UserIdVerify();
       $uiv->setUser($user)
           ->setType(UserIdVerify::TYPE_SUMSUB)
           ->setInvoke($ei)
           ->setStatus($status)
           ->setReason($reason)
           ->setNumber($result['id'] ?? null)
           ->setResult(Util::j2s($reviewResult))
           ->setCreatedAt(new Carbon($result['review']['reviewDate'] ?? 'now'))
           ->setMeta(Util::j2s($result))
           ->persist();

       $config = $user->ensureConfig();
       $config->setIdVerification($uiv)
           ->persist();

       if ($status === SumsubAPI::STATUS_GREEN && !empty($result['info']) && is_array($result['info'])) {
           $disabledFields = self::updateUserInfoBySumsub($user, $result['info']);
           if ($disabledFields) {
               $uiv->setDisabledFields(Util::j2s($disabledFields));
           }
           if (!empty($result['info']['validUntil'])) {
                $validUntil = Util::utc($result['info']['validUntil']);
                $uiv->setExpireAt($validUntil);
           }
           $uiv->persist();
       }

       if ($status === SumsubAPI::STATUS_RED && $oldUiv && $oldUiv->isAccepted()) {
           $rejectLabels = $reviewResult['rejectLabels'] ?? [];
           if (in_array('EXPIRATION_DATE', $rejectLabels)) {
               Email::sendWithTemplateToUser($user, Email::TEMPLATE_BASE_LAYOUT, [
                   'subject' => 'Your ID verification is expired',
                   'body'    => '<p>Please verify your account to continue using our service.</p>',
               ], CardProgram::usunlocked());
           }
       }
       // update register step from no kyc to kyc
       RegisterStep::updateUser($user);
       return $uiv;
   }

    public static function updateUserInfoBySumsub(User $user, array $info): array
    {
        $locked = [];

        $fn = $info['firstNameEn'] ?? '';
        $ln = $info['lastNameEn'] ?? '';
        if ($fn && !$ln) {
            $parts = explode(' ', $fn, 2);
            if (count($parts) === 2) {
                $fn = $parts[0];
                $ln = $parts[1];
            }
        } else if (!$fn && $ln) {
            $parts = explode(' ', $ln, 2);
            if (count($parts) === 2) {
                $fn = $parts[0];
                $ln = $parts[1];
            }
        }

        if ($fn && $ln) {
            $locked[] = 'user.firstName';
            $locked[] = 'user.lastName';

            $oldFn = $user->getFirstName();
            $oldLn = $user->getLastName();
            if(strcasecmp($fn, $oldFn) || strcasecmp($ln, $oldLn)) {
                Util::updateMeta($user, [
                    'previousFirstName' => $oldFn,
                    'previousLastName' => $oldLn,
                ], false);
                $fn = Util::title($fn);
                $ln = Util::title($ln);
                $user->setFirstName($fn)
                    ->setLastName($ln)
                    ->persist();
                $user->addNote('Updated user name from Sumsub: ' . $oldFn . ' ' . $oldLn .
                               ' to ' . $fn . ' ' . $ln);
            }
        }

        $dob = $info['dob'] ?? '';
        if ($dob) {
            $locked[] = 'user.birthday';

            $oldDob = $user->getBirthday();
            if (!$oldDob || $dob !== $oldDob->format('Y-m-d')) {
                $user->setBirthday(Util::utc($dob))
                    ->persist();
                $user->addNote('Updated user birthday from Sumsub: ' .
                               ($oldDob ? $oldDob->format('Y-m-d') : 'null') . ' to ' . $dob);
            }
        }

        return $locked;
    }
}
