<?php


namespace UsUnlockedBundle\Services\Rain;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\FeeGlobalName;
use CoreBundle\Entity\Merchant;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardDecline;
use CoreBundle\Entity\UserCardTransaction;
use CoreBundle\Entity\UserFeeHistory;
use CoreBundle\Services\MerchantService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Queue;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Util;
use DateTime;
use malkusch\lock\exception\LockReleaseException;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\CardProcessorHub;
use UsUnlockedBundle\Services\CardProcessorService;
use UsUnlockedBundle\Services\ComplianceService;
use UsUnlockedBundle\Services\Mautic\MauticEventService;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyService;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\UserService;

class RainService implements CardProcessorService
{
  public static $usersChangedBalance = [];
  public static $usersApprovedTransactions = [];
  public static $usersDeclinedTransactions = [];
  public static $closeSingleUseCards = [];

  public const FREE_CARD_COUNT = 10;
  public const CARD_CREATION_FEE = 200;
  public const CARD_CREATION_COST = 20;
  public const CHARGE_TRANSACTION_FEE = false;

  public static function canCreateRainCard(User $user): bool
  {
      if (Util::isDev() || $user->isInternalTester()) {
          return true;
      }

      return (int)Config::get('open_rain_card', 1) === 1;
  }

  public static function registerRainBySumsub(User $user, string $token)
  {
      $service = RainAPI::get();
      /** @var ExternalInvoke $ei */
      [$ei, $result] = $service->createRainUser($user, $token);
      if ($ei && $ei->isFailed()) {
          SlackService::alert('The US Unlocked user ' . $user->getId() . ' register Rain account failed, please check.', [
              'ei' => $ei->getId(),
              'error' => $ei->getError(),
          ], SlackService::DEVELOPER_GROUP);
          throw PortalException::createFromEi('Failed to register card account (EI: ' . $ei->getId() . ')', $ei);
      }

      Util::updateMeta($user, [
          'rainUserId' => $result['id'],
          'rainUserCreatedAt' => date('c'),
          'rainApplicationStatus' => $result['applicationStatus'],
          'rainKycUrl' => isset($result['applicationCompletionLink']) ?  $result['applicationCompletionLink']['url'] :  '',
      ]);
      $config = $user->ensureConfig();
      $config->setRainUserId($result['id'] ?? $config->getRainUserId())
             ->setRainStatus($result['applicationStatus'] ?? $config->getRainStatus())
             ->setRainRegisterDate(Carbon::now())
             ->persist();

      return $result;
  }

  public static function updateUser(User $user) {
    try {
      $service = RainAPI::get();
       /** @var ExternalInvoke $ei */
       [$ei, $result] = $service->updateUser($user);
       if ($ei && !$ei->isFailed()) {
         Log::debug("Updated the member's (" . $user->getId() . ") info of the rain user success!");
       } else {
         Log::debug('Failed to update rain user info');
       }
    }  catch (\Throwable $e) {
      Log::debug('Failed to update rain user info' . $e->getMessage());
    }
  }

  public static function createCard(UserCard $uc)
  {
      $type = $uc->getType();
      if (!$type) {
          return ExternalInvoke::createTempFailed('Unknown card type when creating card ' . $uc->getHash());
      }

      if ($type === PrivacyAPI::CARD_TYPE_DUMMY) {
          $uc->setAccountCreatedAt(new DateTime())
              ->setIssued(true)
              ->persist();
          return ExternalInvoke::createTempSuccess();
      }

      $chargeFee = false;
      if ($uc->isUsUnlocked()) {
          $user = $uc->getUser();
          $userStatus = $user->getStatus();
          if ($userStatus !== User::STATUS_ACTIVE) {
              return ExternalInvoke::createTempFailed('Failed to create cards since your account status is "' . $userStatus . '".');
          }

          $balance = $user->getUsuBalanceOnly();
          if (!$balance || $balance <= 0) {
              return ExternalInvoke::createTempFailed('Failed to create cards since your account balance is not positive.');
          }

          $chargeFee = $uc->getType() === PrivacyAPI::CARD_TYPE_SINGLE_USE;
          if (!$chargeFee) {
              $ucs = $uc->getUser()->getCardsInUsUnlocked(NULL);
              $openRainCardCount = 0;
              /** @var UserCard $_uc */
              foreach ($ucs as $_uc) {
                  if ($_uc->isRainCard() && !$_uc->isDummy() && $_uc->getType() !== PrivacyAPI::CARD_TYPE_SINGLE_USE) {
                      $openRainCardCount++;
                  }
              }
              if ($openRainCardCount >= self::FREE_CARD_COUNT) {
                  $chargeFee = true;
              }
          }

          if ($chargeFee && $balance < self::CARD_CREATION_FEE) {
              return ExternalInvoke::createTempFailed('Account balance is insufficient for the card creation fee.');
          }
      }

      [$spendLimit, $duration] = self::getFinalSpendLimitForCard($uc);
      $service = RainAPI::getForUserCard($uc);
      /** @var ExternalInvoke $ei */
      [$ei, $result] = $service->createCard($uc, $spendLimit, $duration);

      if ($ei && !$ei->isFailed()) {
          $pan = Util::maskPanWithBin($result['last4']);
          $uc->setAccountCreatedAt(Carbon::now())
              ->setAccountNumber($result['id'])
              ->setExpireAt(Carbon::create($result['expirationYear'], $result['expirationMonth'], 2))
              ->setPan($pan)
              ->setIssued(true)
              ->persist();
          // update user balance for creating card

          if ($chargeFee) {
              $comment = sprintf(
                  'Card creation fee %s at %s. Amount: %s',
                  $uc->getId(),
                  $uc->getAccountCreatedAt()
                      ? Util::formatDateTime($uc->getAccountCreatedAt())
                      : '',
                  Money::format(self::CARD_CREATION_FEE, 'USD')
              );
              $uc->updatePrivacyBalanceBy(-self::CARD_CREATION_FEE, 'create card', $comment, FALSE, $uc);
              self::updateSpendLimitForUser($uc->getUser());
              UserFeeHistory::create($uc->getUser(), self::CARD_CREATION_FEE, FeeGlobalName::CARD_CREATION_FEE, $uc, $comment);
          }
          SlackService::sendUsuEvents($uc->getUser(), 'Created Rain Card', 'Card Id: ' . $uc->getId());
      }

      return $ei;
  }

  public static function getFinalSpendLimitForCard(UserCard $uc, $dummy = null)
  {
      [$min, $duration] = self::calculateFinalSpendLimitForCard($uc, $dummy);

      $sandbox = $uc->isSandbox();
      $max = $sandbox ? RainAPI::MAX_SPEND_LIMIT_SANDBOX : RainAPI::MAX_SPEND_LIMIT;
      if ($min > $max) {
          if (!$sandbox) {
              SlackService::alert(sprintf(
                  'User `%s`\'s card `%s`(%s) wants a spending limit higher than the global setting `%s`!',
                  $uc->getUser()->getId(),
                  $uc->getNickName(),
                  $uc->getAccountNumber(),
                  Money::format($max, 'USD')
              ));
          }
          $min = $max;
      }

      Util::updateMeta($uc, [
          'rainSpendLimit' => $min,
          'rainSpendLimitDuration' => $duration,
      ], false);

      return [$min, $duration];
  }

  public static function calculateFinalSpendLimitForCard(UserCard $uc, $dummy = null)
  {
      $sl = $uc->getSpendLimit();
      $duration = $uc->getSpendLimitDuration();
      switch ($duration) {
        case PrivacyAPI::CARD_SPEND_LIMIT_DURATION_TRANSACTION:
          $duration = RainAPI::CARD_SPEND_LIMIT_FREQUENCY_AUTHORIZATION;
          break;
        case PrivacyAPI::CARD_SPEND_LIMIT_DURATION_MONTHLY:
          $duration = RainAPI::CARD_SPEND_LIMIT_FREQUENCY_MONTHLY;
          break;
        case PrivacyAPI::CARD_SPEND_LIMIT_DURATION_ANNUALLY:
          $duration = RainAPI::CARD_SPEND_LIMIT_FREQUENCY_YEARLY;
          break;
        case PrivacyAPI::CARD_SPEND_LIMIT_DURATION_FOREVER:
          $duration = RainAPI::CARD_SPEND_LIMIT_FREQUENCY_ALL_TIME;
          break;
        default:
          $duration = RainAPI::CARD_SPEND_LIMIT_FREQUENCY_ALL_TIME;
          break;
      }
      $dummy = $dummy ?: $uc->getPrivacyDummyCard();
      $balance = $dummy->getBalance() ?? 1;
      $min = $balance ?: 1;
      if ($sl !== null) {
          $min = min($min,  $sl);

          // https://github.com/terncommerce/span/issues/750
          // When the available spending balance is less than what the user set,
          // the spending limit duration will be `Per Charge`.
          if ($min < $sl) {
              $duration = RainAPI::CARD_SPEND_LIMIT_FREQUENCY_AUTHORIZATION;
          }
      }
      $min = (int)$min;

      // If set to 0, Rain wll regard it as `No Limit`...
      if ($min <= 0) {
          $min = 1;
      }

      return [$min, $duration];
  }

  public static function viewCardDetail (UserCard $uc)
  {
      if ($uc->getType() === PrivacyAPI::CARD_TYPE_DUMMY) {
        return [];
      }
      $service = RainAPI::getForUserCard($uc);
      [$ei, $data] = $service->viewCardDetail($uc->getRainCardId());
      if (!$ei->isFailed()) {
          return $data;
      }
      throw new PortalException('Unknown card!');
  }

  public static function getExpiration(array $cd): string
  {
      $month = str_pad($cd['expirationMonth'] ?? '00', 2, '0', STR_PAD_LEFT);
      $year = $cd['expirationYear'] ?? '0000';
      if (strlen($year) === 2) {
          $year = '20' . $year; // Convert to 4-digit year
      }
      return $month . '/' . $year;
  }

  public static function viewCard(UserCard $uc)
  {
      if ($uc->getType() === PrivacyAPI::CARD_TYPE_DUMMY) {
          return [];
      }
      if (Util::isLocal()) {
          return [
              'cvv' => '000',
              'exp' => '12/99',
              'pan' => '1234 5678 9012 3456',
          ];
      }
      $generateData = RainAPI::generateSessionId();
      $service = RainAPI::getForUserCard($uc);
      [$ei, $data] = $service->viewCard($uc->getRainCardId(), $generateData['sessionId']);
      if (!$ei->isFailed()) {
          Util::updateMeta($uc, [
            'sessionKey' => $generateData['secretKey'],
          ]);
          SlackService::sendUsuEvents($uc->getUser(), 'Viewed Rain Card', 'Card Id: ' . $uc->getId());
          return $data;
      }
      throw new PortalException('Unknown card!');
  }

  public static function updateCard(UserCard $uc)
  {
      if ($uc->getType() === PrivacyAPI::CARD_TYPE_DUMMY) {
          return ExternalInvoke::createTempSuccess();
      }

      [$spendLimit, $duration] = self::getFinalSpendLimitForCard($uc);
      $service = RainAPI::getForUserCard($uc);

      /** @var ExternalInvoke $ei */
      [$ei] = $service->updateCard($uc, $spendLimit, $duration);
      Util::em()->persist($uc);

      return $ei;
  }


  public static function isNotActiveCard($status) {
    if (!$status) {
      return false;
    }
    return in_array($status, [
        RainAPI::CARD_STATE_INACTIVE,
        RainAPI::CARD_STATE_LOCKED,
        RainAPI::CARD_STATE_CLOSED,
    ]);
  }

  public static function updateSpendLimitForUser(User $user = null)
  {
      if (!$user) {
          return;
      }

      $has = false;
      $cards = $user->getCardsInUsUnlocked(null);
      /** @var UserCard $uc */
      foreach ($cards as $uc) {
          if ($uc->isClosed() || !$uc->isRainCard()) {
              continue;
          }

          if ($uc->getType() === PrivacyAPI::CARD_TYPE_DUMMY) {
              continue;
          }

          $has = true;
          Queue::updatePrivacyCard($uc->getId());
      }
      if (!$has) {
          Log::debug('***** No active issued cards for user ' . $user->getId());
      }

      PrivacyService::clearTransactionsCache($user->getId());
  }

  public static function updateTransactionsListCache()
  {
      if (self::$usersApprovedTransactions) {
          Log::debug('***** Update approved transactions cache for users *****', self::$usersApprovedTransactions);
          foreach (self::$usersApprovedTransactions as $userId => $txnId) {
              PrivacyService::clearTransactionsCache($userId);
          }
          self::$usersApprovedTransactions = [];
      }

      if (self::$usersDeclinedTransactions) {
          Log::debug('***** Update declined transactions cache for users *****', self::$usersDeclinedTransactions);

          foreach (self::$usersDeclinedTransactions as $userId => $txnId) {
              $cacheKey = PrivacyService::getTransactionsListCacheKey($userId, PrivacyAPI::TRANSACTION_APPROVAL_STATUS_DECLINES);
              Data::del($cacheKey);
          }
          self::$usersDeclinedTransactions = [];
      }
  }

  public static function updateSpendLimitForBalanceChangedUsers()
  {
      if (self::$usersChangedBalance) {
          Log::debug('***** Update spending limit for balance changed users *****', self::$usersChangedBalance);

          foreach (self::$usersChangedBalance as $userId => $reason) {
              self::updateSpendLimitForUser(User::find($userId));
          }
          self::$usersChangedBalance = [];
      }
      self::closeSingleUseCard();
  }

  public static function updateRainAccount(User $user)
  {
    $rainUserId = $user->ensureConfig()->getRainUserId();
    if (!$rainUserId) {
        throw PortalException::create('Unknown Rain user ID on ' . $user->getId());
    }
    $service = RainAPI::get();
    [$ei, $data] = $service->viewUser($rainUserId);
    if ($ei->isFailed()) {
      Log::debug('Update Rain Account info failed');
      return false;
    }
    $rainAccountInfo = $data;

    if (isset($rainAccountInfo['applicationStatus'])) {
      $config = $user->ensureConfig();
      $config->setRainStatus($rainAccountInfo['applicationStatus'])
               ->persist();
      if (in_array($rainAccountInfo['applicationStatus'], ['approved', 'manualReview'])) {
        Util::updateMeta($user, [
          'rainApplicationStatus' => $rainAccountInfo['applicationStatus'],
          'rainKycPending' => 0,
        ]);
        SlackService::wave('The user has completed the registration of the Rain account and passed the KYC.',  [
          'Member' => $user->getSignature(),
        ]);
        SlackService::sendUsuEvents($user, 'Register Rain Account');
      } else if ($rainAccountInfo['applicationStatus'] !== 'needsVerification') {
        Util::updateMeta($user, [
          'rainKycPending' => 1,
          'applicationReason' => $rainAccountInfo['applicationReason']
        ]);
      }
    }
    return true;
  }

  public static function queryAllTransactions(Callable $callback = null,
    Carbon $startAt = null,
    Carbon $endAt = null,
    UserCard $uc = null)
  {
    $page = 1;
    $ids = [];

    $envs = [true];
    if (Util::isLive()) {
      $envs[] = false;
    }
    $cursor = null;
    foreach ($envs as $sandbox) {
      $service = new RainAPI($sandbox);
      while(true) {
        Util::usleep(300, 1000);
        try {
          [, $data] = $service->listTransactions(
          $uc,
          $page,
          100,
          $startAt,
          $endAt,
          $cursor
          );
        } catch (\Exception $exception) {
          if ( ! $sandbox) {
            throw $exception;
          }
          Log::warn('Failed to list the Privacy transactions: ' . $exception->getMessage());
        }
        if (empty($data)) {
          break;
        }

        if ($callback) {
          $callback($data, $sandbox, $page);
        }
        foreach ($data as $item) {
          if (!in_array($item['type'], RainAPI::VALID_TRANSACTIONS_TYPE)) {
            continue;
          }
          $ids[$item['id']] = $item;
          $cursor = $item['id'];
        }
        $page++;
      }
    }

    return $ids;
  }

  public static function refreshCardStatus(UserCard $uc)
  {
      if ($uc->getType() === PrivacyAPI::CARD_TYPE_DUMMY) {
          return false;
      }

      $service = RainAPI::getForUserCard($uc);
      [, $data] = $service->viewCardDetail($uc->getRainCardId());
      if ($data && is_array($data)) {
          $state = $data['status'];
          return $uc->updateRainNativeStatus($state);
      }
      return false;
  }

  public static function fillTransactions($items)
  {
      $errors = [];
      foreach ($items as $item) {
          if (!in_array($item['type'], RainAPI::VALID_TRANSACTIONS_TYPE)) {
            continue;
          }
          $mutex = Util::mutex('fill_transaction_' . $item['id'], 60);
          try {
              $mutex->synchronized(function () use ($item, &$errors) {
                 $status = null;
                 if ($item['type'] === RainAPI::TRANSACTION_TYPE_SPEND) {
                  $status = $item['spend']['status'];
                 } else if ($item['type'] === RainAPI::TRANSACTION_TYPE_PAYMENT) {
                  $status = $item['payment']['status'];
                 }
                 if (Util::isStaging()) {
                  Log::debug('The transaction status is ' . $status);
                 }
                  try {
                      if (in_array($status, [
                          RainAPI::TRANSACTION_STATUS_COMPLETED,
                          RainAPI::TRANSACTION_STATUS_PENDING,
                          // RainAPI::TRANSACTION_STATUS_REVERSED, // DO NOT refund in this status
                      ])) {
                          self::fillApprovedTransaction($item);
                      } else if ($status === RainAPI::TRANSACTION_STATUS_DECLINED) {
                          self::fillDeclinedTransaction($item);
                          self::deleteDeclinedApprovedTransaction($item);
                      }
                  } catch (\Exception $e) {
                      $errors[] = $item['token'] . ': ' . $e->getMessage();
                  }
              });
          } catch (LockReleaseException $unlockException) {
              $exception = $unlockException->getCodeException();
              if ($exception) {
                  SlackService::exception('Failed to fill transaction', $exception, [
                      'transaction' => $item,
                  ]);
              } else {
                  Log::error('Unlock error: ' . $unlockException->getMessage());
              }
          }
      }

      if ($errors) {
          $errors = array_slice($errors, 0, 5);
          SlackService::alert('Failed to fill transactions', $errors);
      }
  }

  protected static function fillApprovedTransaction($item)
  {
      $em = Util::em();
      $repo = $em->getRepository(UserCardTransaction::class);
      $rs = $repo->findBy([
          'tranId' => $item['id'],
      ], null, 1);
      if (Util::isStaging()) {
        Log::debug('Start check and fill the rain transaction!');
      }
      $newAmount = (int)$item['spend']['amount'];
      $newStatus = $item['spend']['status'];
      $merchantName = !empty($item['spend']['merchantName']) ? trim($item['spend']['merchantName']) : null;
      $newTxn = false;
      if ($rs) {
          /** @var UserCardTransaction $t */
          $t = $rs[0];
          MerchantService::updateFromRain($t, $item['spend']);
          if (!$t->needToUpdateByRain($item['spend'])) {
              return null;
          }

          $uc = $t->getUserCard();
          $user = $uc->getUser();
          // Update balance
          $oldAmount = (int)$t->getTxnAmount();
          $oldStatus = $t->getStatus();
          if ($oldAmount !== $newAmount) {
              $type = 'transaction';
              Log::debug('Different transaction amount in transaction ' . $item['id'], [
                  'type' => $type,
                  'old' => $oldAmount,
                  'new' => $newAmount,
              ]);
              $delta = $oldAmount - $newAmount;

              if ($newStatus === RainAPI::TRANSACTION_STATUS_PENDING) {
                // we need add more money for authorizations amount
                $comment = sprintf(
                    'Update transaction %s at %s. Amount: %s',
                    $item['id'],
                    $item['spend']['authorizedAt'] ?? '',
                    Money::format($newAmount, 'USD')
                );
                if ($delta < 0) {
                    $uc->updatePrivacyBalanceBy($delta, 'rain_' . $type, $comment, false, $t, [
                      'merchant' => $merchantName,
                      'type' => 'update'
                    ]);
                    self::$usersChangedBalance[$user->getId()] = $type . ' - ' . $comment;
                    SlackService::alert('The amount of the approved transaction was changed.', [
                        'user' => $user->getId(),
                        'transaction' => $t->getToken(),
                        'result' => $item['spend']['status'],
                        'authorized amount' => Money::format($oldAmount, 'USD'),
                        'final amount' => Money::format($newAmount, 'USD'),
                        'changed amount' => Money::format($delta, 'USD'),
                        'merchant' => $merchantName,
                    ], SlackService::GROUP_DEV);
                    SlackService::sendUsuEvents($user, 'Transaction', 'The amount of the approved transaction was changed', [
                        'Transaction Id' =>  $t->getId(),
                        'Merchant Name' => $merchantName,
                    ]);
                }
              }
            //   if ($newStatus === RainAPI::TRANSACTION_STATUS_REVERSED) {
            //     // The transaction was partially reversed or completely reversed
            //     SlackService::info('The amount of the approved transaction was reversed and adding to the return queue.', [
            //         'user' => $user->getId(),
            //         'transaction' => $t->getToken(),
            //         'result' => $item['spend']['status'],
            //         'authorized amount' => Money::format($oldAmount, 'USD'),
            //         'reversed amount' => Money::format($newAmount, 'USD'),
            //     ], SlackService::GROUP_DEV);
            //   }

              if ($newStatus === RainAPI::TRANSACTION_STATUS_COMPLETED) {
                // if the old amount < 0, it's a refund transaction, else is a spend transaction
                // check the refund transaction
                if ($oldAmount < 0) {
                    $comment = sprintf(
                        'Settle the refund transaction %s at %s. Amount: %s',
                        $item['id'],
                        $item['spend']['authorizedAt'] ?? '',
                        Money::format(-$newAmount, 'USD')
                    );
                    // check if there is a completed transaction in the same merchant with the same card
                    if (self::isRefundAmountHigherThanAuth($uc, $t, $newAmount)) {
                        SlackService::alert('Skip the refunded transaction. Reason: `there is no enough auth money to refund with the same card`', [
                            'user' => $user->getId(),
                            'transaction' => $t->getToken(),
                            'result' => $item['spend']['status'],
                            'authorized refunded amount' => Money::format(-$oldAmount, 'USD'),
                            'final refunded amount' => Money::format(-$newAmount, 'USD'),
                            'merchant' => $merchantName,
                        ], SlackService::DEVELOPER_GROUP);
                        SlackService::sendUsuEvents($user, 'Transaction', 'Skip the refunded transaction', [
                            'Transaction Id' =>  $t->getId(),
                            'Merchant Name' => $merchantName,
                            'Reason' => 'There is no enough auth money to refund with the same card.',
                        ]);
                    } else {
                        // need return the money to the member
                        $uc->updatePrivacyBalanceBy(-$newAmount, 'rain_' . $type, $comment, false, $t, [
                            'merchant' => $merchantName,
                            'type' => 'update'
                        ]);
                        self::$usersChangedBalance[$user->getId()] = $type . ' - ' . $comment;
                        SlackService::check('Settled refunded transaction success.', [
                            'user' => $user->getId(),
                            'transaction' => $t->getToken(),
                            'result' => $item['spend']['status'],
                            'authorized refunded amount' => Money::format(-$oldAmount, 'USD'),
                            'final refunded amount' => Money::format(-$newAmount, 'USD'),
                            'merchant' => $merchantName,
                        ]);
                        SlackService::sendUsuEvents($user, 'Transaction', 'Settled refunded transaction', [
                            'Transaction Id' =>  $t->getId(),
                            'Merchant Name' => $merchantName,
                        ]);
                    }
                } else if ($newAmount < 0 ) {
                    SlackService::alert('The approved transaction become refunded, please check.', [
                      'user' => $user->getId(),
                      'transaction' => $t->getToken(),
                      'result' => $item['spend']['status'],
                      'authorized amount' => Money::format($oldAmount, 'USD'),
                      'final amount' => Money::format($newAmount, 'USD'),
                      'merchant' => $merchantName,
                  ], SlackService::GROUP_DEV);
                  SlackService::sendUsuEvents($user, 'Transaction', 'The approved transaction become refunded', [
                      'Transaction Id' =>  $t->getId(),
                      'Merchant Name' => $merchantName,
                  ]);
                } else {
                    $comment = sprintf(
                        'Settle the transaction %s at %s. Amount: %s',
                        $item['id'],
                        $item['spend']['authorizedAt'] ?? '',
                        Money::format($newAmount, 'USD')
                    );
                    // need return the money to the member
                    $uc->updatePrivacyBalanceBy($delta, 'rain_' . $type, $comment, false, $t, [
                        'merchant' => $merchantName,
                        'type' => 'update'
                    ]);
                    self::$usersChangedBalance[$user->getId()] = $type . ' - ' . $comment;
                    SlackService::check('Settled the approved transaction.', [
                        'user' => $user->getId(),
                        'transaction' => $t->getToken(),
                        'result' => $item['spend']['status'],
                        'authorized amount' => Money::format($oldAmount, 'USD'),
                        'final amount' => Money::format($newAmount, 'USD'),
                        'merchant' => $merchantName,
                    ]);
                    SlackService::sendUsuEvents($user, 'Transaction', 'Settled the approved transaction', [
                        'Transaction Id' =>  $t->getId(),
                        'Merchant Name' => $merchantName,
                    ]);
                }
              }
          }
          // check the refund transaction
          if ($newAmount < 0 &&
                $oldStatus === RainAPI::TRANSACTION_STATUS_PENDING &&
                $newStatus === RainAPI::TRANSACTION_STATUS_COMPLETED) {
                $type = 'transaction';
                $context = [
                    'user' => $user->getSignature(),
                    'amount' => Money::formatUSD(-$newAmount),
                    'status' => $newStatus,
                    'transaction' => $t->getToken(),
                    'merchant' => trim($item['spend']['merchantName'])
                ];
                $comment = sprintf(
                    'Refund transaction %s at %s. Amount: %s',
                    $item['id'],
                    $item['spend']['authorizedAt'] ?? '',
                    Money::format(-$newAmount, 'USD')
                );
                // check if there is a completed transaction in the same merchant with the same card
                if (self::isRefundAmountHigherThanAuth($uc, $t, $newAmount)) {
                    $comment = sprintf(
                        'Skip refunding transaction %s at %s. Amount: %s. Reason: the refund amount is higher than authorization.',
                        $item['id'],
                        $item['spend']['authorizedAt'] ?? '',
                        Money::format(-$newAmount, 'USD')
                    );
                    SlackService::alert($comment, $context, SlackService::DEVELOPER_GROUP);
                    SlackService::sendUsuEvents($user, 'Transaction', 'Skip refunding negative authorization transaction as the amount is higher than authorization. Transaction Id: ' . $t->getId(), $context);
                } else {
                    SlackService::info('Pending negative authorization transaction becomes `' . $newStatus . '`', $context);
                    SlackService::sendUsuEvents($user, 'Transaction', 'Pending negative authorization transaction becomes `' . $newStatus . '`. Transaction Id: ' . $t->getId(), $context);
                    // need return the money to the member
                    $uc->updatePrivacyBalanceBy(-$newAmount, 'rain_' . $type, $comment, false, $t, [
                        'merchant' => $merchantName,
                        'type' => 'refund',
                        'network' => $item['network'] ?? '',
                    ]);
                    self::$usersChangedBalance[$user->getId()] = $type . ' - ' . $comment;
                }
          }
      } else {
          $uc = UserCard::findByRainCardId($item['spend']['cardId']);
          if (!$uc) {
              Log::debug('Find no card with the rain card id ' . $item['cardId']);
              return null;
          }
          $user = $uc->getUser();
          if (Util::isStaging()) {
            Log::debug('Rain Card Id ' . $item['spend']['cardId']);
            Log::debug('User card Id ' . $uc->getId());
          }
          $dummy = $uc->getPrivacyDummyCard();
          $newTxn = true;
          $merchantEntity = MerchantService::instanceFromRain($item['spend']);
          $t = new UserCardTransaction();
          $t->setUserCard($uc)
            ->setTranId($item['id'])
            ->setMerchant($merchantEntity)
            ->setPreBalance($dummy->getBalance())
          ;
          // Update balance
          $type = 'transaction';
          $balanceDelta = -$newAmount;

          $comment = sprintf(
              'In transaction %s at %s. Amount: %s',
              $item['id'],
              $item['spend']['authorizedAt'] ?? '',
              Money::format($newAmount, 'USD')
          );
          if ($balanceDelta < 0) {
              $uc->updatePrivacyBalanceBy($balanceDelta, 'rain_' . $type, $comment, false, $t, [
                  'merchant' => $merchantName,
                  'type' => 'update',
                  'network' => $item['network'] ?? '',
              ]);
          } else if ($balanceDelta > 0 && $newStatus === RainAPI::TRANSACTION_STATUS_COMPLETED) {
              $uc->updatePrivacyBalanceBy($balanceDelta, 'rain_' . $type, $comment, false, $t, [
                  'merchant' => $merchantName,
                  'type' => 'update',
                  'network' => $item['network'] ?? '',
              ]);
              SlackService::info('Credited for completed refund', [
                  'user' => $user->getSignature(),
                  'amount' => Money::formatUSD($balanceDelta),
                  'transaction' => $item['id'],
                  'merchant' => $merchantName,
              ]);
          }
          // store merchant
          $uc->setMerchant($merchantEntity)
                ->persist();
          self::$usersChangedBalance[$user->getId()] = $type . ' - ' . $comment;
          // Charge approved transaction fee
          if (self::CHARGE_TRANSACTION_FEE && $dummy->getBalance() > 0) {
              $feeType = 'txn';
              if ($feeType === 'txn') {
                  // Charge the approved transactions fee
                  $feeName = FeeGlobalName::APPROVED_TRANSACTION_FEE;
                  $fee = $uc->calculateFinalFee($feeName, $newAmount);
                  Log::debug('The transaction is ' . $fee);
                  if ($fee && (int)($newAmount) !== 0) {
                      $realFee = -$uc->updatePrivacyBalanceBy(-$fee, Util::feeName($feeName), $comment, true, $t);
                      $t->setFee($realFee);
                      UserFeeHistory::create($user, $realFee, $feeName, $t, $comment);
                  }
              }
          }
      }

      $txnTime = !empty($item['spend']['postedAt']) ? $item['spend']['postedAt'] : $item['spend']['authorizedAt'];
      $t->setTxnAmount($newAmount)
          ->setAfterBalance(null)
          ->setCurBalance(null)
          ->setSettledAmount($newAmount)
          ->setTxnTime(Carbon::parse($txnTime))
          ->setTranDesc(!empty($item['spend']['merchantName']) ? trim($item['spend']['merchantName']) : NULL)
          ->setStatus($newStatus)
          ->setResult($newStatus)
          ->setPartner('rain')
      ;

      self::saveTransactionHistoryInMeta($t, $item);
      $t->fillReturnType();
      Util::persist($t);

      self::$usersApprovedTransactions[$user->getId()] = $item['id'];

      if ($newTxn) {
          ComplianceService::checkRapidTransactions($user, $t);
          ComplianceService::checkMoneyInAndOutIn24Hours($uc->getUser());
          SlackService::sendUsuEvents($user, 'Transaction', 'Approved', [
              'Transaction ID' => $t->getTranId(),
              'Authorized At' => $item['spend']['authorizedAt'] ?? '',
              'Approved Merchant' => self::parseMerchantName($item['spend']),
              'Amount' => Money::formatUSD($t->getTxnAmount()),
              'Card Type' => $uc->getType(),
              'Card Nickname' => $uc->getNickName(),
              'Locked Merchant' => $uc->getMerchant()?->getSimplifiedName(),
          ]);
          MauticEventService::completedSpend($user, $t);
      }

      //
      // Close the single-use card once got an approved transaction
      //
      if ($uc->isOneTimeCard() && !$uc->isClosed() && in_array($t->getStatus(), [
          RainAPI::TRANSACTION_STATUS_PENDING,
          RainAPI::TRANSACTION_STATUS_COMPLETED,
      ])) {
        $uc->setStatus(UserCard::STATUS_CLOSED, false, false);
        Util::persist($uc);

        self::$closeSingleUseCards[$uc->getId()] = $uc;
      }


      Service::velocityCheck($t);

      return $t;
  }

  public static function parseMerchantName(array $spend): string
  {
      $enriched = trim($spend['enrichedMerchantName'] ?? '');
      if (!empty($enriched)) {
          return $enriched;
      }
      $name = $spend['merchantName'] ?? '';
      return trim(preg_replace('/[\d-]{8,}/', '', $name));
  }

  public static function parseMerchantCategory(array $spend): string
  {
      return trim($spend['enrichedMerchantCategory'] ?? $spend['merchantCategory'] ?? '');
  }

  public static function isSimilarText(string $a, string $b): bool
  {
      $a = mb_strtolower(trim($a));
      $b = mb_strtolower(trim($b));
      return Util::isSimilarText($a, $b, 40, 6);
  }

  public static function isTheSameMerchant(Merchant $merchant, array $spend): bool
  {
      $rainId = mb_strtolower(trim($merchant->getRainId()));
      $newRainId = mb_strtolower(trim($spend['merchantId'] ?? ''));
      if ($newRainId && $rainId === $newRainId) {
          return true;
      }

      $name = mb_strtolower(trim($merchant->getName()));
      $newName = mb_strtolower(self::parseMerchantName($spend));
      if ($newName && self::isSimilarText($name, $newName)) {
          return true;
      }

      $original = mb_strtolower(trim($merchant->getOriginalName()));
      if ($original && self::isSimilarText($original, $newName)) {
          return true;
      }

      $enriched = mb_strtolower(trim($merchant->getEnrichedName()));
      if ($enriched && self::isSimilarText($enriched, $newName)) {
          return true;
      }

      return false;
  }

  protected static function fillDeclinedTransaction($item)
  {
      $newTxn = false;
      $user = null;
      $repo = Util::em()->getRepository(UserCardDecline::class);
      $rs = $repo->findBy([
          'txnId' => $item['id'],
      ], null, 1);
      if ($rs) {
          /** @var UserCardDecline $t */
          $t = $rs[0];
          if (!$t->needToUpdateByRain($item['spend'])) {
              MerchantService::updateFromRain($t, $item['spend']);
              return null;
          }
      } else {
          $uc = UserCard::findByRainCardId($item['spend']['cardId']);
          if (!$uc) {
              return null;
          }
          $user = $uc->getUser();

          $newTxn = true;
          $t = new UserCardDecline();
          $t->setUserCard($uc)
              ->setCardProgram($uc->getCardProgram())
              ->setTxnId($item['id'])
          ;

          // Charge declined transaction fee
          if (Config::get('rain_declined_fee', 0)) {
              $feeName = FeeGlobalName::DECLINED_TRANSACTION_FEE;
              $fee = $uc->calculateFinalFee($feeName, $item['spend']['amount']);
              if ($fee) {
                  $dummy = $uc->getPrivacyDummyCard();
                  if ($dummy->getBalance() > 0) {
                      $comment = sprintf(
                          'In transaction %s at %s. Amount: %s',
                          $item['id'],
                          $item['spend']['authorizedAt'] ?? '',
                          Money::format($item['spend']['amount'], 'USD')
                      );
                      $fee = -$uc->updatePrivacyBalanceBy(-$fee, Util::feeName($feeName), $comment, true, $t);
                      $t->setFee($fee);
                      UserFeeHistory::create($uc->getUser(), $fee, $feeName, $t, $comment);
                      self::$usersChangedBalance[$uc->getUser()->getId()] = $feeName . ' - ' . $comment;
                  }
              }
          }
          self::$usersDeclinedTransactions[$user->getId()] = $item['id'];
      }
      $previousDeclineReason = $t->getDeclineReason();
      $errorKey = 'rain_transaction_' . $item['id'];
      $declinedReason = $item['spend']['declinedReason'] ?? 'unknown';
      if (Data::get($errorKey)) {
          $declinedReason = Data::get($errorKey);
          Data::del($errorKey);
      } else if ($previousDeclineReason && in_array(mb_strtolower($declinedReason), [
          null, '', 'unknown', 'webhook declined', 'webhook_declined', 'declined', 'decline',
      ])) {
          $declinedReason = $previousDeclineReason;
      }
      $t->setTxnAmount($item['spend']['amount'])
          ->setTxnTime(Carbon::parse($item['spend']['authorizedAt']))
          ->setMerchant(!empty($item['spend']['merchantName']) ? trim($item['spend']['merchantName']) : '')
          ->setDeclineReason($declinedReason)
          ->setAvsResult($item['spend']['status'])
          ->setMerchantEntity(MerchantService::instanceFromRain($item['spend']))
      ;
      self::saveTransactionHistoryInMeta($t, $item);
      Util::persist($t);

      Service::velocityCheckDecline($t);

      if ($newTxn && $user) {
          ComplianceService::checkRapidTransactions($user, $t);

          $uc = $t->getUserCard();
          SlackService::sendUsuEvents($user, 'Transaction', 'Declined', [
              'Transaction ID' => $t->getTranId(),
              'Declined Merchant' => self::parseMerchantName($item['spend']),
              'Declined Reason' => $declinedReason,
              'Amount' => Money::formatUSD($t->getTxnAmount()),
              'Card Type' => $uc->getType(),
              'Card Nickname' => $uc->getNickName(),
              'Locked Merchant' => $uc->getMerchant()?->getSimplifiedName(),
          ]);
      }

      return $t;
  }

  public static function saveTransactionHistoryInMeta($transaction, $data)
  {
      if(isset($data['spend'])) {
          Util::updateMeta($transaction, [
              'rainDetail' => $data,
          ], false);
      }

      $data['_inserted'] = Carbon::now(Util::tzUTC())->format('c');
      $history = Util::meta($transaction, 'rainHistory') ?: [];
      $history[] = $data;

      Util::updateMeta($transaction, [
          'rainHistory' => $history,
      ], false);
  }

  protected static function deleteDeclinedApprovedTransaction($item)
  {
      if ($item['spend']['status'] === RainAPI::TRANSACTION_STATUS_COMPLETED) {
          return;
      }
      $em = Util::em();
      $rs = $em->getRepository(UserCardTransaction::class)->findBy([
          'tranId' => $item['id'],
      ], null, 1);
      if (!$rs) {
          return;
      }
      /** @var UserCardTransaction $r */
      $transaction = $rs[0];

      // Already declined
      if (!$transaction || $transaction->isInvalid()) {
          return;
      }

      $key = 'refundedDeclinedApprovedTransactionAmount';
      if (Util::meta($transaction, $key)) {
          return;
      }

      /** @var integer $amount */
      $amount = $transaction->getTxnAmount();

      /** @var UserCard $uc */
      $uc = $transaction->getUserCard();

      $comment = sprintf(
          'Declined in the approved transaction %s. Amount: %s',
          $transaction->getToken(),
          Money::format($amount, 'USD')
      );
      $uc->updatePrivacyBalanceBy($amount, 'rain_declined_approved', $comment, false, $transaction);

      $user = $uc->getUser();
      self::$usersChangedBalance[$user->getId()] = $comment;

      SlackService::alert('The approved transaction was declined later. Refunding the transaction amount.', [
          'user' => $user->getId(),
          'transaction' => $transaction->getToken(),
          'result' => $item['spend']['status'],
          'amount' => Money::format($amount, 'USD'),
      ]);
      SlackService::sendUsuEvents($user, 'Transaction', 'The approved transaction was declined later. Refunding the transaction amount. Transaction Id: ' . $transaction->getId());
      $transaction->setStatus(RainAPI::TRANSACTION_STATUS_DECLINED);

      Util::updateMeta($transaction, [
          $key => true,
      ], false);
      self::saveTransactionHistoryInMeta($transaction, $item);

      Util::persist($transaction);
  }

  protected static function closeSingleUseCard()
  {
      if (self::$closeSingleUseCards) {
          $ucs = array_values(self::$closeSingleUseCards);
          self::$closeSingleUseCards = [];

          $ids = array_map(function (UserCard $uc) {
              return $uc->getId();
          }, $ucs);
          Log::debug('***** Close single-use cards after getting an approved transaction *****', $ids);

          /** @var UserCard $uc */
          foreach ($ucs as $uc) {
              CardProcessorHub::closingWithDelay($uc);
          }

          // Update the spending limit too
          /** @var UserCard $uc */
          foreach ($ucs as $uc) {
              $ei = self::updateCard($uc);
              if ($ei && $ei->isFailed()) {
                  SlackService::alert(sprintf(
                      'Failed to update user `%s`\'s single-use card `%s` because `%s`',
                      $uc->getUser()->getId(),
                      $uc->getToken(),
                      $ei->getError()
                  ));
              }
              Util::usleep(50, 200);
          }
      }
  }

  protected static function isRefundAmountHigherThanAuth(UserCard $uc, UserCardTransaction $uct, $amount) {
    $expr = Util::expr();
    $q = Util::em()->getRepository(UserCardTransaction::class)
        ->createQueryBuilder('uct')
        ->join('uct.userCard', 'uc')
        ->where($expr->eq('uc.id', ':ucId'))
        ->andWhere($expr->in('uct.accountStatus', ':statuses'))
        ->andWhere($expr->neq('uct.id', ':uctId'))
        ->setParameter('ucId', $uc->getId())
        ->setParameter('uctId', $uct->getId())
        ->setParameter('statuses', [
            RainAPI::TRANSACTION_STATUS_PENDING,
            RainAPI::TRANSACTION_STATUS_COMPLETED,
        ]);
    if ($uct->getMerchant()) {
        $q->join('uct.merchant', 'mc')
          ->andWhere($expr->eq('mc.id', ':mcId'))
          ->setParameter('mcId', $uct->getMerchant()->getId());
    }
    $sumTransactionAmount = $q->select('sum(uct.txnAmount)')
                              ->getQuery()->getSingleScalarResult();
    return $amount + $sumTransactionAmount < 0;
  }

  public static function sendMessageToSupport(string $subject, array $details = [], ?array $mentions = null)
  {
      $details['Note'] = "Please manually forward this message to Rain's Slack channel: #ext-us-unlocked-rain and tag "
                         . SlackService::MENTION_RAIN_SUPPORT;
      SlackService::wave($subject, $details, $mentions ?? [
          SlackService::MENTION_IAN,
          SlackService::MENTION_ISA,
      ]);
  }

  public static function sendMessageToSupportForKycReview(User $user)
  {
      if ( ! RainAPI::REVIEWED) {
          return 'Rain API is not reviewed by the compliance team';
      }
      $rainUserId = UserService::getRainUserId($user);
      if (empty($rainUserId)) {
          return 'Rain user ID is empty';
      }
      $sent = Data::once('rain_kyc_' . $user->getId(), function () use ($user, $rainUserId) {
          $subject = 'Please approve the following user manually.';
          self::sendMessageToSupport($subject, [
              'User' => $user->getSignature(),
              'Rain user ID' => $rainUserId,
              'Rain application status' => $user->getRainApplicationStatus(),
              'Sumsub applicant Status' => $user->getSumsubApplicantStatus(),
          ]);
          return 'Message sent';
      }, 86400 * 7); // 7 days
      return $sent ?? 'Message skipped';
  }
}
