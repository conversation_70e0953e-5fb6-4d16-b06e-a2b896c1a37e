<?php


namespace UsUnlockedBundle\Services\Rain;


use Carbon\Carbon;
use CoreB<PERSON>le\Entity\CardProgram;
use CoreB<PERSON>le\Entity\Email;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserCard;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib\Crypt\RSA;
use PortalBundle\Exception\PortalException;
use Ramsey\Uuid\Uuid;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\UserService;

final class RainAPI
{
    public const REVIEWED = true; // Rain Compliance Reviewed and Ready for Production

    public const URL_SANDBOX = 'https://api-dev.raincards.xyz/v1/issuing/';
    public const URL_PRODUCTION = 'https://api.raincards.xyz/v1/issuing/';

    public const CARD_SPEND_LIMIT_FREQUENCY_MONTHLY = 'per30DayPeriod';
    public const CARD_SPEND_LIMIT_FREQUENCY_WEEKLY = 'per7DayPeriod';
    public const CARD_SPEND_LIMIT_FREQUENCY_YEARLY = 'perYearPeriod';
    public const CARD_SPEND_LIMIT_FREQUENCY_ALL_TIME = 'allTime';
    public const CARD_SPEND_LIMIT_FREQUENCY_DAILY = 'per24HourPeriod';
    public const CARD_SPEND_LIMIT_FREQUENCY_AUTHORIZATION = 'perAuthorization';

    public const CARD_STATE_INACTIVE = 'notActivated';
    public const CARD_STATE_ACTIVE = 'active';
    public const CARD_STATE_LOCKED = 'locked';
    public const CARD_STATE_CLOSED = 'canceled';

    public const MAX_SPEND_LIMIT = 350000;
    public const MAX_SPEND_LIMIT_SANDBOX = 125000;

    // The approved application state is a terminal state, from which no further action can be taken.
    // The application has been approved, either automatically or through additional verification.
    // No further action is needed on your end.
    public const KYC_STATUS_APPROVED = 'approved';

    // This is the most common state for an application during processing.
    // There is likely some automated processing happening in the background.
    // Once the automated processing is completed, the application will transition to another state automatically.
    // We can send you a webhook (for companies or users) when this happens.
    // No further action is needed on your end at this point.
    public const KYC_STATUS_PENDING = 'pending';

    // This state occurs when more information is required to process the application.
    public const KYC_STATUS_NEED_INFO = 'needsInformation';

    // This state occurs when the automated processing is complete, but the consumer application is not verifiable.
    // This step generally occurs before manual review and allows the user to verify their identity and receive an automated adjudication.
    public const KYC_STATUS_NEED_VERIFY = 'needsVerification';

    // This state occurs when an application has finished automated processing but hasn't been automatically approved.
    // This means that an analyst is manually reviewing the submitted data, and will update the status when a decision is reached.
    // We can send you a webhook (for companies or users) when this happens.
    // No further action is needed on your end at this point.
    public const KYC_STATUS_MANUAL_REVIEW = 'manualReview';

    // This is generally a terminal state, from which no further action can be taken.
    // The application has been denied and cannot be appealed or resubmitted.
    // Applications are typically only denied after resubmissions and additional verification have failed.
    // No further action is possible on your end. However, we may change this status at our discretion.
    public const KYC_STATUS_DENIED = 'denied';

    // This is not a terminal state. The application has been locked due to compliance requirements and may be unlocked.
    // You should not expect to encounter this status during standard day-to-day operations.
    public const KYC_STATUS_LOCKED = 'locked';

    // This is a terminal state, from which no further action can be taken.
    // You canceled this application, and no further processing is possible.
    public const KYC_STATUS_CANCELED = 'canceled';

    public const TRANSACTION_TYPE_SPEND = 'spend';
    public const TRANSACTION_TYPE_PAYMENT = 'payment';

    public const TRANSACTION_STATUS_PENDING = 'pending';
    public const TRANSACTION_STATUS_COMPLETED = 'completed';
    public const TRANSACTION_STATUS_REVERSED = 'reversed';
    public const TRANSACTION_STATUS_DECLINED = 'declined';

    public const VALID_TRANSACTIONS_TYPE = [
      self::TRANSACTION_TYPE_SPEND
    ];

    public $apiKey;
    public $url;

    /**
     * @param UserCard $uc
     *
     * @return RainAPI
     */
    public static function getForUserCard(UserCard $uc)
    {
        $sandbox = Util::json($uc, 'meta', 'rainSandbox') || !Util::isLive();
        return new static($sandbox);
    }

    public static function get()
    {
        return new static(!Util::isLive());
    }

    public static function getParameter($key)
    {
        return Util::isLocal() ? Util::getConfigKey($key) : Util::getKmsParameter($key);
    }

    public static function getSpendFrequencyLimitName($frequency)
    {
        return [
            self::CARD_SPEND_LIMIT_FREQUENCY_AUTHORIZATION => 'Per Charge',
            self::CARD_SPEND_LIMIT_FREQUENCY_DAILY => 'Per Day',
            self::CARD_SPEND_LIMIT_FREQUENCY_WEEKLY => 'Per Week',
            self::CARD_SPEND_LIMIT_FREQUENCY_MONTHLY => 'Per Month',
            self::CARD_SPEND_LIMIT_FREQUENCY_YEARLY => 'Per Year',
            self::CARD_SPEND_LIMIT_FREQUENCY_ALL_TIME => 'Max Spend',
        ][$frequency] ?? $frequency;
    }

    public static function getStartDate()
    {
        return Carbon::create(2020, 3, 1, 0, 0, 0, Util::tzUTC());
    }

    /**
     * RainAPI constructor.
     *
     * @param bool $sandbox
     */
    public function __construct($sandbox)
    {
        if ($sandbox) {
            $this->apiKey = self::getParameter('rain_api_key_test');
            $this->url = self::URL_SANDBOX;
        } else {
            $this->apiKey = self::getParameter('rain_api_key');
            $this->url = self::URL_PRODUCTION;
        }
    }

    public static function isPaused()
    {
        return Data::get('rain_paused');
    }

    public static function pauseService()
    {
        Data::set('rain_paused', true);
    }

    public static function resumeService()
    {
        Data::del('rain_paused');
    }

    protected function request($method, $path, $params = [], $sessionId = null, $saveEi = true)
    {
        $method = strtoupper($method);
        $type = 'rain_' . $method . '_' . $path;
        $params['__sandbox'] = $this->url === self::URL_SANDBOX;

        $ei = ExternalInvoke::create($type, $params, null, $saveEi);


        if (!empty($params['__path_suffix'])) {
            $path .= '/' . $params['__path_suffix'];
            unset($params['__path_suffix']);
        }
        unset($params['__sandbox']);

        $client = new Client();
        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                'Api-Key' => $this->apiKey,
            ],
        ];
        if ($sessionId) {
          $options['headers']['SessionId'] = $sessionId;
        }
        if ($params) {
            if ($method === 'GET') {
                $options['query'] = $params;
            } else {
                $options['json'] = $params;
            }
        }
        try {
            $response = $client->request($method, $this->url . $path, $options);
        } catch (ClientException $exception) {
            $response = $exception->getResponse();
            $content = $response->getBody()->getContents() ?: '{}';
            $content = json_decode($content, true);
            $msg = $content['message'] ?? $exception->getMessage();
            $ei->fail($content, $msg)
                ->persist();

            throw PortalException::createFromEi('Card API error', $ei);
        } catch (\Exception $exception) {
            $response = null;
            if ($exception instanceof RequestException) {
                $resp = $exception->getResponse();
                if ($resp) {
                    $body = $resp->getBody();
                    if ($body) {
                        $response = [
                            'content' => $body->getContents()
                        ];
                    }
                }
            }
            $message = $exception->getMessage();
            $ei->fail($response, $message)
                ->persist();

            if ($message && mb_strpos($message, 'Server error') !== false) {
                $context = [
                    'method' => $method,
                    'path' => $path,
                    'params' => $params,
                ];
                if ($this->url === self::URL_PRODUCTION) {
                    $this->cacheErrorToPool($ei);
                    SlackService::alert($message, $context);

                    if ($exception instanceof RequestException) {
                        Log::requestException('Rain API call failure details', $exception, $options);
                    }
                } else {
                    Log::warn($message, $context);
                }
            }
            throw PortalException::createFromEi('Card processor error', $ei);
        }

        $content = $response->getBody()->getContents();

        if ($saveEi) {
            $ei->succeed($content)
                ->persist();
        } else {
            $ei = ExternalInvoke::createTempSuccess($type);
        }

        return [$ei, json_decode($content, true)];
    }

    public function createRainUser(User $user, string $token)
    {
      if (self::isPaused()) {
        throw new PortalException('Failed to create cards since the card service is not available now. Please retry a moment later.');
      }
      $country = $user->getCountry();
      $phoneNumber = Util::getNationalPhoneNumber(
          $user->getMobilephone(),
          $country,
      );
      if (!$phoneNumber) {
        throw new PortalException('Please fill your phone number on the profile page and try again.',
            persist: false, alert: false);
      }
      $params = [
        'firstName' =>$user->getFirstName(),
        'lastName'  => $user->getLastName(),
        // 'birthDate' => Util::formatDateTime($user->getBirthday(), Util::DATE_FORMAT_ISO_FULL_DATE),
        'email'     => $user->getEmail(),
        // 'countryOfIssue' => $country->getIsoCode(),
        // 'nationalId' => Util::meta($user, 'nationalId'),
        'address'   => [
          'country' => $country->getName(),
          'countryCode' => $country->getIsoCode(),
          'postalCode' => Util::cleanPostalCode($user->getZip(), $country) ?: '0',
          'region' => $user->getState() ? $user->getState()->getName() : '',
          'city'   => $user->getCity(),
          'line1'  => Util::maxLength($user->getAddress(), 99),
        ],
        'phoneNumber' => $phoneNumber,
        // 'phoneCountryCode' => $country->getPhoneCode(),
        'sumsubShareToken' => $token,
        'ipAddress' => Security::getClientIp() ?? Security::getServerIp(),
//        'occupation' => Util::meta($user, 'occupation') ?? 'Freelance work',
        'occupation' => Util::meta($user, 'occupation') ?? '45-2099', // Agricultural Workers, All Other,
        'annualSalary' =>  Util::meta($user, 'annualSalary') ?? '100000',
        'accountPurpose' => Util::meta($user, 'accountPurpose') ?? 'spend',
        'expectedMonthlyVolume' => '5000',
        'isTermsOfServiceAccepted' => true,
      ];

      if ($country->getIsoCode() === 'DO') { // Dominican Republic, see EI: ********
          $params['phoneCountryCode'] = (string)($country->getCallingCode());
      }

      $extra = Util::meta($user, 'rainExtraApplicantData') ?? [];
      if ($extra) {
          $params = array_merge($params, $extra);
          foreach ($params as $k => $v) {
              if ($v === null) {
                  unset($params[$k]);
              }
          }
      }

      return $this->request('post', 'applications/user', $params);
    }

    public function updateUser(User $user) {
      if (self::isPaused()) {
        throw new PortalException('Failed to create cards since the card service is not available now. Please retry a moment later.');
      }
      $rainUserId = UserService::getRainUserId($user);
      $params = [
        'birthDate' => Util::formatDateTime($user->getBirthday(), Util::DATE_FORMAT_ISO_FULL_DATE),
      ];
      return $this->request('post', 'applications/user/' . $rainUserId, $params);
    }

    public function viewUser($rainAccountId) {
      return $this->request('get', 'users/' . $rainAccountId);
    }

    public function createCard(UserCard $uc, $spendLimit = 1, $spendLimitDuration = null)
    {
        if (self::isPaused()) {
            throw new PortalException('Failed to create cards since the card service is not available now. Please retry a moment later.');
        }
        $rainUserId = UserService::getRainUserId($uc->getUser());
        if (!$rainUserId) {
            throw new PortalException('The card account owner was not created yet!');
        }

        $spendLimit = $spendLimit ?? $uc->getSpendLimit() ?: 1;

        $params = [
            'configuration' => [
              'displayName' => Util::maxLength(
                  Util::cleanAscii($uc->getPrivacyNickname()),
                  26
              ),
            ],
            'type' => 'virtual', // physical virtual
            'limit' => [
              'amount' => $spendLimit,
              'frequency' =>  $spendLimitDuration ?? $uc->getSpendLimitDuration() ?: self::CARD_SPEND_LIMIT_FREQUENCY_AUTHORIZATION,
            ]
        ];

        if ($spendLimit <= 1) {
            $uc->setStatus(UserCard::STATUS_INACTIVE, false, false)
                ->setNativeStatus(self::CARD_STATE_INACTIVE)
                ->persist();
        }

        try {
            return $this->request('post', 'users/' . $rainUserId . '/cards', $params);
        } catch (PortalException $e) {
//            $msg = $e->getMessage();
//            if (str_contains($msg, 'User exists, but is not approved')) {
//                $user = $uc->getUser();
//                RainService::sendMessageToSupportForKycReview($user);
//            }
            throw $e;
        }
    }

    public function updateCard(UserCard $uc, $spendLimit = 1, $spendLimitDuration = null)
    {
        $spendLimit = ($spendLimit ?? $uc->getSpendLimit()) ?: 1;
        $spendLimit = max($spendLimit, 1);
        $rainCardId = $uc->getRainCardId();
        $params = [
           'limit' => [
              'amount' => $spendLimit,
              'frequency' =>  $spendLimitDuration ?? $uc->getSpendLimitDuration() ?: self::CARD_SPEND_LIMIT_FREQUENCY_AUTHORIZATION,
            ]
        ];

        $manuSl = $uc->getSpendLimit() ?? 10;
        if ($spendLimit <= 1 && $manuSl > 1) {
            $wasActive = $uc->getStatus() === UserCard::STATUS_ACTIVE;
            $uc->setStatus(UserCard::STATUS_INACTIVE, false, false)
                ->setNativeStatus(self::CARD_STATE_INACTIVE);
            if ($wasActive) {
                Util::updateMeta($uc, [
                    'temporaryPausedBecauseOfLowBalance' => true,
                ], false);

                $dummy = $uc->getPrivacyDummyCard();
                $balance = $dummy ? $dummy->getBalanceOnly() : null;
                Log::warn('Disable user cards due to low spending limit', [
                    'uc' => $uc->getId(),
                    'spendLimit' => $spendLimit,
                    'duration' => $spendLimitDuration,
                    'userBalance' => $balance,
                ]);

                $user = $uc->getUser();
                if ($balance !== null && $balance <= 0 && !Util::meta($user, 'sentCardsPausedEmailDueToInsufficientBalance')) {
                    Email::sendWithTemplateToUser($user, Email::TEMPLATE_CARDS_PAUSED_DUE_TO_BALANCE, [
                        'pan' => $uc->getLast4(),
                    ], CardProgram::usunlocked());
                    Util::updateMeta($user, [
                        'sentCardsPausedEmailDueToInsufficientBalance' => true,
                    ]);
                }
            }
            $uc->persist();
        } else if (Util::meta($uc, 'temporaryPausedBecauseOfLowBalance')) {
            Util::updateMeta($uc, 'temporaryPausedBecauseOfLowBalance');
            Util::updateMeta($uc->getUser(), 'sentCardsPausedEmailDueToInsufficientBalance');
            $params['status'] = self::CARD_STATE_ACTIVE;

            $uc->setStatus(UserCard::STATUS_ACTIVE, false, false)
                ->setNativeStatus(self::CARD_STATE_ACTIVE)
                ->persist();
        }

        return $this->request('patch', 'cards' . '/' . $rainCardId, $params, $uc->getUser()->getId());
    }

    public function activateCard(UserCard $uc, $persistEi = true)
    {
        $rainCardId = $uc->getRainCardId();
        return $this->request('patch', 'cards' . '/' . $rainCardId, [
            'status' => self::CARD_STATE_ACTIVE,
        ], $uc->getUser()->getId(), $persistEi);
    }

    public function deactivateCard(UserCard $uc, $persistEi = true)
    {
        $rainCardId = $uc->getRainCardId();
        try {
            return $this->request('patch', 'cards' . '/' . $rainCardId, [
                'status' => self::CARD_STATE_LOCKED,
            ], $uc->getUser()->getId(), $persistEi);
        } catch (\Throwable $t) {
            if (Util::endsWith($t->getMessage(), 'Card not active')) {
                return [ExternalInvoke::createTempSuccess(), null];
            }
            throw $t;
        }
    }

    public function closeCard(UserCard $uc, $persistEi = true)
    {
        $rainCardId = $uc->getRainCardId();
        try {
            return $this->request('patch', 'cards' . '/' . $rainCardId, [
                'status' => self::CARD_STATE_CLOSED,
            ], $uc->getUser()->getId(), $persistEi);
        } catch (\Throwable $t) {
            if (Util::endsWith($t->getMessage(), 'Card not active')) {
                return [ExternalInvoke::createTempSuccess(), null];
            }
            throw $t;
        }
    }

    public function viewCard($rainCardId, $sessionId, $persistEi = true)
    {
        return $this->request('get', 'cards' . '/' . $rainCardId . '/secrets', [], $sessionId, $persistEi);
    }

    public function viewCardDetail($rainCardId, $persistEi = true)
    {
        return $this->request('get', 'cards' . '/' . $rainCardId, [], null, $persistEi);
    }

    private function cacheErrorToPool(ExternalInvoke $ei)
    {
        $error = $ei->getError();
        if (!$error) {
            return;
        }
        $key = 'rain_error_pool';
        $pool = Data::getArray($key);
        $pool[] = $ei->getId();
        Data::setArray($key, $pool);
    }

    public static function getCardProcessorService(): string
    {
        return RainService::class;
    }

    public static function generateSessionId() {
      $secret = str_replace('-', '', Uuid::uuid4());
      if (strlen($secret) % 2 !== 0) {
        $secret = '0' . $secret;
      }
      $binaryData = pack('H*', $secret);
      $base64Encoded = base64_encode($binaryData);

      $path = Util::getConfigKey('rain_rsa_key_path');
      $publicKeyStr = file_get_contents($path);
      $publicKey = PublicKeyLoader::loadPublicKey($publicKeyStr);
      $rsa = new RSA();
      $rsa->loadKey($publicKey);
      $encryptedMessage = $rsa->encrypt($base64Encoded);

      return [
        'secretKey' => $secret,
        'sessionId' => base64_encode($encryptedMessage)
      ];
  }

  public function listTransactions(UserCard $uc = null,
                                     $page = 1,
                                     $pageSize = 50,
                                     Carbon $begin = null,
                                     Carbon $end = null,
                                     $cursor = null)
  {
      $params = [
          'limit' => $pageSize,
          'type'  => [self::TRANSACTION_TYPE_SPEND]
      ];
      if ($page > 1) {
          $params['cursor'] = $cursor;
      }
      if ($uc) {
          if (!$uc->isRainCard()) {
              return [
                  ExternalInvoke::createTempSuccess(),
                  [
                      'data' => [],
                      'page' => $page,
                      'total_pages' => 0,
                      'total_entries' => 0,
                  ],
              ];
          }
          $params['cardId'] = $uc->getRainCardId();
      }
      if ($begin) {
          $params['authorizedAfter'] = $begin->format(Util::DATE_TIME_FORMAT_SEARCH_FULL);
      }
      if ($end) {
          $params['authorizedBefore'] = $end->format(Util::DATE_TIME_FORMAT_SEARCH_FULL);
      }

      return $this->request('get', 'transactions/', $params, null, false);
  }

  public function viewTransaction ($transactionId, $persistEi = false) {
    return $this->request('get', 'transactions' . '/' . $transactionId, [], null, $persistEi);
  }

  public function listCards(
                            User $user = null,
                            int $page = 1,
                            int $pageSize = 50,
                            String $status = null,
                            String $cursor = null
                            )
  {
    $params = [
        'limit' => $pageSize
    ];
    if ($page > 1) {
        $params['cursor'] = $cursor;
    }
    if ($user) {
        if (!$user->isRegisterRainUser()) {
            return [
                ExternalInvoke::createTempSuccess(),
                [
                    'data' => [],
                    'page' => $page,
                    'total_pages' => 0,
                    'total_entries' => 0,
                ],
            ];
        }
        $params['userId'] = UserService::getRainUserId($user);
    }
    if ($status) {
        $params['status'] = $status;
    }
    return $this->request('get', 'cards', $params, null);
  }

   public function listUser(
                            int $page = 1,
                            int $pageSize = 50,
                            String $cursor = null
                            )
  {
    $params = [
        'limit' => $pageSize
    ];
    if ($page > 1) {
        $params['cursor'] = $cursor;
    }
    return $this->request('get', 'users', $params, null);
  }
}

