<?php

namespace UsUnlockedBundle\Services\Coinflow;

use Carbon\Carbon;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use Namshi\Cuzzle\Formatter\CurlFormatter;
use PortalBundle\Exception\PortalException;

final class CoinflowAPI
{
    public const METHODS_ALL = [
        'card', 'ach', 'fasterPayments', 'sepa',
        'pix', 'usdc', 'googlePay', 'applePay',
        'credits', 'crypto',
    ];

    public const METHODS_CRYPTO = [
        /*'usdc', */'crypto',
    ];

    public const METHODS_CARD = [
        'card', 'googlePay', 'applePay',
    ];

    public const METHODS_SEPA = [
        'sepa',
    ];

    public const METHODS_FASTER_PAYMENTS = [
        'fasterPayments',
    ];

    public const PAYMENT_INFO_FIELDS = [
        'bankTransfer', // ACH
        'card', // Card, Google Pay, Apple Pay
        'crypto', // Crypto
        'iban', // SEPA, UK Faster Payments
        'pix', // PIX
    ];

    public const STATUS_INITIATED = 'initiated';
    public const STATUS_DEPOSITED = 'deposited';
    public const STATUS_SETTLED = 'settled';
    public const STATUS_EXPIRED = 'expired';
    public const STATUS_REFUNDED = 'refunded';

    public static function getNSurePartnerId()
    {
        return Util::isLive() ? 'USUNLKD' : 'COINFTEST';
    }

    public static function getNSureProductType()
    {
        // Util::isLive() ? 'moneyTopUpCartItem' : 'topUp';
        return Config::get('coinflow_nsure_product_type', 'topUp');
    }

    public static function getMerchantId()
    {
        return Util::isLive() ? 'USUnlocked' : 'Tern';
    }

    public static function request(string $endpoint, string $method = 'POST', array $params = [],
                                   bool $saveEi = false, string $requestType = NULL)
    {
        if (in_array($endpoint, ['checkout/pix'])) {
            $params['x-coinflow-auth-blockchain'] = Util::getConfigKey('coinflow_destination_chain') ?? 'base';
        }
        if (in_array($endpoint, [])) {
            $params['x-coinflow-auth-session-key'] = self::getCachedAuthSessionKey();
        }

        $requestType = $requestType ?? $endpoint;
        $context = $params;
        $ei = ExternalInvoke::create('coinflow_' . $method . '_' . $requestType, $context, null,
            $saveEi);

        $apiKey = Util::getKmsParameter('coinflow_api_key');
        if (!$apiKey) {
            return ExternalInvoke::tempFailedArray('Coinflow API access is not configured!');
        }

        $host = 'https://api-sandbox.coinflow.cash/api/';
        if (Util::isLive()) {
            $host = 'https://api.coinflow.cash/api/';
        }
        $url = $host . $endpoint;
        $request = new Request($method, $url);

        $client = new Client();
        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $apiKey,
            ],
        ];
        foreach ($params as $key => $value) {
            if (Util::startsWith($key, 'x-')) {
                $options['headers'][$key] = $value;
                unset($params[$key]);
            }
        }
        if ($method === 'GET') {
            $options['query'] = $params;
        } else {
            $options['json'] = $params;
        }

        $debug = false;
        if ($saveEi) {
            $debug = self::isDebugMode();
        }
        if ($debug) {
            $options['debug'] = $debug['resource'];
        }

        try {
            $response = $client->send($request, $options);
        } catch (\Throwable $exception) {
            $response = $exception instanceof RequestException ? $exception->getResponse() : null;
            $msg = $exception->getMessage();
            $rawContent = null;
            if ($response) {
                $body = $response->getBody();
                $rawContent = $body ? $body->getContents() : '{}';
                $content = Util::s2j($rawContent ?: '{}') ?? [];
                $msg = $content['message'] ??
                       $content['msg'] ??
                       $msg;
                if (!empty($content['details']) && is_string($content['details'])) {
                    $msg .= ' (' . $content['details'] . ').';
                }
                $ei->setStatusCode($response->getStatusCode());
            }
            $ei->fail($rawContent, $msg)
                ->persist();
            self::persistCurlToInvoke($debug, $apiKey, $request, $options, $ei);
            throw PortalException::create('Coinflow API error: ' . $msg . ' (EI: ' . $ei->getId() . ')');
        }

        $rawContent = $response->getBody()->getContents();
        $content = Util::s2j($rawContent) ?? [];

        if ($saveEi) {
            $ei->succeed($rawContent, $response->getStatusCode())
                ->persist();
            self::persistCurlToInvoke($debug, $apiKey, $request, $options, $ei);
        }

        return [$ei, $content];
    }

    protected static function isDebugMode()
    {
        if (Util::isStaging() && Util::isDev()) {
            $path = tempnam(sys_get_temp_dir(), 'CoinflowDebug');
            $resource = fopen($path, 'wb+');
            return compact('path', 'resource');
        }
        return false;
    }

    protected static function persistCurlToInvoke($debug, string $remove, Request $request, array $options,
                                                  ExternalInvoke $invoke)
    {
        if (!$debug) {
            return;
        }

        @fclose($debug['resource']);
        $console = file_get_contents($debug['path']);
        $console = str_replace($remove, '****', $console);
        @unlink($debug['path']);

        $curl = (new CurlFormatter())->format($request, $options);
        $curl = str_replace($remove, '****', $curl);

        Util::updateMeta($invoke, [
            'debug' => $console,
            'curl' => $curl,
        ]);
    }

    public static function getAuthSessionKey()
    {
        return self::request('auth/session-key', 'GET');
    }

    public static function getCachedAuthSessionKey(): string
    {
        return Data::callback('coinflow_auth_session_key', [], function () {
            /** @var ExternalInvoke $ei */
            [$ei, $data] = self::getAuthSessionKey();
            if ($ei->isFailed()) {
                return '';
            }
            return (string)$data;
        }, 3600 * 6); // 6 hours cache
    }

    public static function getDestinationAuthKey()
    {
        $params = [
            'blockchain' => Util::getConfigKey('coinflow_destination_chain') ?? 'base',
            'destination' => Util::getDecryptedParameter('coinflow_destination'),
        ];
        return self::request('checkout/destination-auth-key', params: $params);
    }

    public static function getCachedDestinationAuthKey(): string
    {
        return Data::callback('coinflow_destination_auth_key', [], function () {
            /** @var ExternalInvoke $ei */
            [$ei, $data] = self::getDestinationAuthKey();
            if ($ei->isFailed()) {
                return '';
            }
            return $data['destinationAuthKey'] ?? '';
        }, 3600 * 6); // 6 hours cache
    }

    public static function getCheckoutLink(array $params)
    {
        $params = array_merge([
            'blockchain' => Util::getConfigKey('coinflow_destination_chain') ?? 'base',
            'destination' => Util::getDecryptedParameter('coinflow_destination'),
            'settlementType' => 'USDC',
        ], $params);
        return self::request('checkout/link', params: $params, saveEi: true);
    }

    public static function createPixCheckout(array $params)
    {
        $params = array_merge([
            'settlementType' => 'USDC',
            'destinationAuthKey' => self::getCachedDestinationAuthKey(),
            'merchantId' => self::getMerchantId(),
        ], $params);
        return self::request('checkout/pix', 'POST', $params, saveEi: true);
    }

    public static function getPayment(string $paymentId)
    {
        return self::request('merchant/payments/' . $paymentId, 'GET',
            requestType: 'merchant/payments/x');
    }

    public static function sendEvent(array $params)
    {
        return self::request('events', 'POST', params: $params, saveEi: true);
    }

    public static function getAllPayments(Carbon $since = null, Carbon $until = null,
                                          string $status = null, string $method = null,
                                          int $page = 1, int $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
        ];
        if ($since) {
            $params['since'] = (int)($since->getTimestamp() . '000'); // $since->format('c');
        }
        if ($until) {
            $params['until'] = (int)($until->getTimestamp() . '999'); // $until->format('c');
        }
        if (
            (!empty($params['since']) && empty($params['until'])) ||
            (!empty($params['until']) && empty($params['since']))
        ) {
            throw new \InvalidArgumentException('Both `since` and `until` must be provided together.');
        }
        if ($status) {
            $params['status'] = $status;
        }
        if ($method) {
            $params['method'] = $method;
        }
        return self::request('merchant/payments', 'GET', $params);
    }

    public static function getPaymentWebhookActivity(string $eventType = null, string $responseCode = null,
                                                     int $page = 1, int $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
        ];
        if ($eventType) {
            $params['eventType'] = $eventType;
        }
        if ($responseCode) {
            $params['responseCode'] = $responseCode;
        }
        return self::request('webhook/webhooks', 'GET', $params);
    }

    public static function getCustomers(int $page = 1, int $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit,
        ];
        return self::request('merchant/customers', 'GET', $params);
    }

    public static function getCustomer()
    {
        return self::request('customer/v2', 'GET');
    }
}
