<?php

namespace UsUnlockedBundle\Services\Coinflow;

use Carbon\Carbon;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\LoadMethod;
use CoreBundle\Entity\LoadPartner;
use CoreBundle\Entity\Transaction;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Services\LoadCardService;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use Stringy\Stringy;
use UsUnlockedBundle\Services\SlackService;

class CoinflowService
{
    public const EXPIRY_PAYMENT_MINUTES = 180; // 3 hours

    public static function getChargebackProtectionData(UserCardLoad $ucl)
    {
        $cardProgram = $ucl->getUserCard()->getCardProgram();
        $type = CoinflowAPI::getNSureProductType();
        $all = [];
        if ($type === 'topUp') {
            $all[] = [
                'productType' => $type,
                'rawProductData' => [
                    'example' => Util::j2s([
                        'description' => $cardProgram->getName(),
                        'loadId' => $ucl->getId(),
                    ]),
                ],
                'productName' => $cardProgram->getName(),
                'quantity' => 1,
            ];
        } else if ($type === 'moneyTopUpCartItem') {
            $all[] = [
                'itemClass' => 'moneyTopUp',
                'id' => Stringy::create($cardProgram->getName())->dasherize() .
                        '-UCL-' . $ucl->getId(),
                'rawProductData' => [
                    'description' => $cardProgram->getName(),
                    'loadId' => $ucl->getId(),
                ],
                'sellingPrice' => [
                    'valueInCurrency' => $ucl->getInitialAmountUSD(),
                    'currency' => 'USD',
                ],
                'topUpAmount' => [
                    'valueInCurrency' => $ucl->getInitialAmountUSD(),
                    'currency' => 'USD',
                ],
                'quantity' => 1,
                'isPresetAmount' => $ucl->getInitialAmountUSD() === 5000,
            ];
        }
        return $all;
    }

    public static function getCheckoutLink(UserCardLoad $ucl, Transaction &$transaction = null,
                                           ExternalInvoke &$ei = null)
    {
        $reference = $ucl->getTransactionNo(true);
        $uc = $ucl->getUserCard();
        $cardProgram = $uc->getCard()->getCardProgram();
        $user = $uc->getUser();
        $params = [
            'x-coinflow-auth-user-id' => $user->getId(),
            'email' => $user->getEmail(),
            'customerInfo' => [
                'name' => $user->getFullName(),
                'address' => $user->getAddresses(),
                'city' => $user->getCity() ?? '',
                'state' => $user->getState()?->getAbbrOrName() ?? '',
                'zip' => $user->getZip() ?? '',
                'country' => $user->getCountry()?->getIsoCode() ?? '',
                'dob' => Util::formatDateTime($user->getBirthday(), Util::DATE_FORMAT_SEARCH),
            ],
            'webhookInfo' => [
                'example' => Util::j2s([
                    'ucl' => $ucl->getId(),
                    'txnNo' => $ucl->getTransactionNo(),
                ]),
            ],
            'subtotal' => [
                'cents' => $ucl->getPayAmount(),
                'currency' => $ucl->getPayCurrency(),
            ],
        ];

        $methodName = $ucl->getMethodName();
        if ($methodName === LoadMethod::PAYNAME_COINFLOW_PIX) {
            /** @var ExternalInvoke $ei */
            [$ei, $data] = CoinflowAPI::createPixCheckout($params);
        } else {
            $params = array_merge($params, [
                'idempotencyKey' => 'ucl_' . $ucl->getId(), // . '_' . Util::randString(4),
                'chargebackProtectionData' => self::getChargebackProtectionData($ucl),
                'supportEmail' => $cardProgram->getSupportEmailValue(),
            ]);

            if ($methodName === LoadMethod::PAYNAME_COINFLOW_CARD) {
                $params['allowedPaymentMethods'] = CoinflowAPI::METHODS_CARD;
            } else if ($methodName === LoadMethod::PAYNAME_COINFLOW_CRYPTO) {
                $params['allowedPaymentMethods'] = CoinflowAPI::METHODS_CRYPTO;
            } else if ($methodName === LoadMethod::PAYNAME_COINFLOW_SEPA) {
                $params['allowedPaymentMethods'] = CoinflowAPI::METHODS_SEPA;
            } else if ($methodName === LoadMethod::PAYNAME_COINFLOW_UK_FASTER_PAYMENTS) {
                $params['allowedPaymentMethods'] = CoinflowAPI::METHODS_FASTER_PAYMENTS;
            }

            $deviceId = Util::meta($ucl, 'nSureDeviceId');
            if ($deviceId) {
                $params['deviceId'] = $deviceId;
                $params['x-device-id'] = $deviceId;
            }

            /** @var ExternalInvoke $ei */
            [$ei, $data] = CoinflowAPI::getCheckoutLink($params);
        }

        if ($ei->isFailed()) {
            throw PortalException::createFromEi('Failed to get checkout link', $ei);
        }

        $transaction = Transaction::create(Transaction::TYPE_LOAD_CARD, $params,
            $ucl->getPayAmount(), $ucl->getPayCurrency());
        $transaction->setResponse(Util::j2s($data));
        $transaction->setRequestKey($reference);
        $transaction->setStatus(UserCardLoad::LOAD_STATUS_INITIATED);
        $transaction->persist();

        $ucl->setPaymentReference($reference)
            ->setInitializedAt(Carbon::now())
            ->setLoadInvoke($ei)
            ->persist();

        $ei->setRequestKey($reference)
            ->setEntity(Transaction::class)
            ->setForeignKey($transaction->getId());

        return $data;
    }

    public static function isLoadInitializationExpired(UserCardLoad $ucl)
    {
        if ($ucl->getPaymentId()) {
            return false;
        }
        if (!in_array($ucl->getLoadStatus(), [
            UserCardLoad::LOAD_STATUS_INITIATED,
            UserCardLoad::LOAD_STATUS_PENDING,
        ])) {
            return false;
        }
//        $earnType = $ucl->getEarnType();
//        if ($earnType) {
//            $meta = Util::meta($ucl, 'coinflowData') ?? [];
//            $expiration = $meta[$earnType . 'Info']['expiration'] ?? null;
//            if ($expiration && Carbon::now()->gt($expiration)) {
//                return true;
//            }
//        }
        return $ucl->getInitializedAt() &&
               Carbon::now()->subMinutes(self::EXPIRY_PAYMENT_MINUTES)->gt($ucl->getInitializedAt());
    }

    public static function expirePayment(UserCardLoad $ucl)
    {
        if ( ! self::isLoadInitializationExpired($ucl)) {
            return false;
        }

        $ucl->setLoadStatus(UserCardLoad::LOAD_STATUS_ERROR)
            ->setStatus(UserCardLoad::STATUS_EXPIRED)
            ->setError('No valid payment found')
            ->persist();
        return true;
    }

    public static function syncPaymentId(UserCardLoad $ucl)
    {
        $id = $ucl->getId();
        $params = [
            '--ucl' => $id,
            '--check' => true, // Do not queue it to update
        ];
        $initAt = $ucl->getInitializedAt();
        if ($initAt) {
            $params['--from'] = Carbon::instance($initAt)->addHour();
            $params['--hours'] = 2;
        } else {
            $params['--hours'] = 24;
        }
        Util::executeCommand('span:usu:coinflow:sync-payments', $params);
        Util::refresh($ucl);

        $ucl = UserCardLoad::find($id);
        return $ucl->getPaymentId();
    }

    public static function assignPaymentIdWithCheck(UserCardLoad $ucl, string $paymentId)
    {
        $old = $ucl->getPaymentId();
        if (!$old || $old === $paymentId) {
            $ucl->setPaymentId($paymentId)
                ->setPaymentReference($paymentId)
                ->persist();
            return true;
        }

        $loadStatus = $ucl->getLoadStatus();
        if (in_array($loadStatus, [
            UserCardLoad::LOAD_STATUS_CONFIRMED,
            UserCardLoad::LOAD_STATUS_RECEIVED,
            UserCardLoad::LOAD_STATUS_LOADED,
            UserCardLoad::LOAD_STATUS_REFUNDED,
        ])) {
            SlackService::alert('Skip assigning payment ID for load ' . $ucl->getTransactionNo(), [
                'load' => $ucl->getId(),
                'loadStatus' => $loadStatus,
                'oldPaymentId' => $old,
                'newPaymentId' => $paymentId,
            ], SlackService::GROUP_DEV);
            return false;
        }

        $ucl->setPaymentId($paymentId)
            ->setPaymentReference($paymentId)
            ->persist();
        return true;
    }

    public static function updatePayment(UserCardLoad $ucl)
    {
        return Data::singletonThrowable('coinflow_update_payment_' . $ucl->getId(), function () use (&$data, $ucl) {
            $paymentId = $ucl->getPaymentId();
            if (!$paymentId) {
                if (self::isLoadInitializationExpired($ucl)) {
                    self::expirePayment($ucl);
                    return null;
                }

                $pathInfo = Util::request()->getPathInfo();
                if (Util::startsWith($pathInfo, '/p/load/status')) {
                    return null;
                }

                throw PortalException::temp('The payment was not completed or updated to us yet. Please complete it or wait for a few minutes.');
            }
            if (!$ucl->getPartner()->is(LoadPartner::COINFLOW)) {
                throw PortalException::create($ucl->getMethodName() . ' - not a Coinflow load ' . $ucl->getTransactionNo());
            }
            /** @var ExternalInvoke $ei */
            [
                $ei,
                $data
            ] = CoinflowAPI::getPayment($paymentId);
            if ($ei->isFailed()) {
                throw PortalException::createFromEi('Failed to get payment info for load ' . $ucl->getTransactionNo(), $ei);
            }
            if (!$data) {
                throw PortalException::create('Invalid Payment ID for load ' . $ucl->getTransactionNo());
            }
            self::updatePaymentByData($data, $ucl);
            return $data;
        });
    }

    public static function updatePaymentByData(array $data, UserCardLoad $ucl = null)
    {
        Data::singletonThrowable('coinflow_update_payment_by_data_' . $data['paymentId'], function () use (&$data, $ucl) {
            $wi = Util::s2je($data['webhookInfo']['example'] ?? '{}');
            $uclId = $wi['ucl'] ?? 0;
            $ucl = $ucl ?? UserCardLoad::find($uclId);
            if (!$ucl) {
                throw PortalException::create('Unknown load for the payment ' . $data['paymentId']);
            }
            if ($ucl->getId() !== $uclId || $ucl->getPaymentId() !== $data['paymentId']) {
                throw PortalException::create('Invalid payment data for load ' . $ucl->getTransactionNo(), [
                    'uclIdInData' => $uclId,
                    'paymentIdInData' => $data['paymentId'],
                    'paymentIdInDB' => $ucl->getPaymentId(),
                ]);
            }
            Util::updateMeta($ucl, [
                'coinflowData' => $data,
            ]);
            $isNewStatus = UserCardLoad::isNewStatus(UserCardLoad::LOAD_STATUS_RECEIVED, $ucl->getLoadStatus());
            if (!$isNewStatus) {
                return;
            }

            $alertError = true;
            $error = null;
            $loadStatus = $ucl->getLoadStatus();
            $chargebackProtectionDecision = strtolower($data['chargebackProtectionDecision'] ?? 'unknown');
            if (!in_array($chargebackProtectionDecision, [
                'approved',
                'not enabled',
                'not reviewed',
                'pending review',
                'overridden',
            ])) {
                $error = 'Chargeback Protection - ' . $chargebackProtectionDecision;
                $loadStatus = UserCardLoad::LOAD_STATUS_ERROR;
            }

            $earnType = $ucl->getEarnType();
            $status = null;
            foreach (CoinflowAPI::PAYMENT_INFO_FIELDS as $mk) {
                $key = $mk . 'Info';
                $status = $data[$key]['status'] ?? $status;
                if ($status !== null) {
                    $earnType = $mk;
                    if (!empty($data[$key]['mobileWallet'])) {
                        $earnType .= '_' . $data[$key]['mobileWallet'];
                    }
                    $status = strtolower($status);
                    break;
                }
            }

            if (!$status || in_array($status, [
                'pending', // ACH, The initial state of the payment - it has been entered into the system
                'batched', // ACH, The bank has received the payment information
                'initiated', // Crypto
            ])) {
                $loadStatus = UserCardLoad::LOAD_STATUS_PENDING;
            } elseif (in_array($status, [
                'authorized', // Card, The transaction has been approved but not yet finalized.
                'deposited', // ACH, The bank has informed the system that the payment can be settled
            ])) {
                $loadStatus = UserCardLoad::LOAD_STATUS_CONFIRMED;
            } elseif (in_array($status, [
                'settled', // ACH, Card
            ])) {
                $loadStatus = UserCardLoad::LOAD_STATUS_RECEIVED;
            } elseif (in_array($status, [
                'refunded', // ACH, Card
                'chargeback', // ACH, Card, The cardholder disputes the transaction, and the amount is being investigated.
                'chargeback_lost', // Card, The dispute was resolved in favor of the cardholder, and the merchant loses the funds.
                'chargeback_win', // Card, The dispute was resolved in favor of the merchant, and the funds are retained.
                'refund review', // ACH, A payment on-chain was not able to be verified
            ])) {
                $loadStatus = UserCardLoad::LOAD_STATUS_REFUNDED;
                $error = 'Load Status - ' . $status;
            } elseif (in_array($status, [
                'failed', // ACH, The payment was rejected by the bank on submission
                'returned', // ACH, The bank has refused the payment (see ACH processing error codes)
                'voided', // Card, The transaction was canceled before it was completed.
                'expired', // Card, The payment window has expired.
                'error', // Fallback
                'invalid', // Fallback
            ])) {
                $loadStatus = UserCardLoad::LOAD_STATUS_ERROR;
                $error = $data['error'] ?? $data['reason'] ?? null;
                $alertError = false;
            } else {
                $error = 'Unknown Load Status - ' . $status;
            }

            if ($error && $alertError) {
                SlackService::warning('Coinflow load alert: ' . $error, [
                    'load' => $ucl->getId(),
                    'paymentId' => $ucl->getPaymentId(),
                    'user' => $ucl->getUserCard()->getUser()->getId(),
                ], SlackService::GROUP_DEV);
            }

            $uclStatus = $status;
            if (in_array($status, [
                'settled',
            ]) || in_array($loadStatus, UserCardLoad::RECEIVED_STATUS_ARRAY)) {
                $uclStatus = UserCardLoad::STATUS_COMPLETED;
            }

            $ucl->setStatus($uclStatus)
                ->setEarnType($earnType)
                ->setError($error);

            if ($loadStatus === UserCardLoad::LOAD_STATUS_RECEIVED) {
                $cost = 0;
                foreach (['merchantPaidCreditCardFees', 'merchantPaidChargebackProtectionFees', 'merchantPaidGasFees'] as $key) {
                    $cost += Money::convert(
                        $data['totals'][$key]['cents'] ?? 0,
                        $data['totals'][$key]['currency'] ?? 'USD',
                        'USD'
                    );
                }

                $receivedAmount = $data['totals']['total']['cents'] ?? 0;
                $receivedCurrency = $data['totals']['total']['currency'] ?? 'USD';

                $ucl->setCompletedAt(new \DateTime())
                    ->setCost($cost)
                    ->setReceivedCurrency($receivedCurrency)
                    ->setReceivedAmount($receivedAmount)
                    ->persist();
                $ucl->updateLoadAmountWhenReceived();
                $ucl->setLoadStatus($loadStatus, false)
                    ->persist();

                SlackService::tada('Received `Coinflow` payment. Added to the load queue.', [
                    'txn_no' => $ucl->getTransactionNo(),
                    'method' => $ucl->getMethodName(),
                    'amount' => $ucl->getReceivedAmountText(),
                    'user' => $ucl->getUserCard()->getUser()->getId(),
                ]);

                LoadCardService::executeLoad($ucl);
            } else {
                $ucl->setLoadStatus($loadStatus)
                    ->persist();
            }
        }, 3600);
    }
}
