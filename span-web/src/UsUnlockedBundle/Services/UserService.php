<?php


namespace UsUnlockedBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\UserCard;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\Mautic\MauticEventService;
use UsUnlockedBundle\Services\PayPal\PayPalAPI;
use UsUnlockedBundle\Services\Privacy\PrivacyAPI;
use UsUnlockedBundle\Services\Rain\RainAPI;

class UserService
{
    /**
     * @param User $user
     *
     * @return UserCard|null
     */
    public static function getDummyCardOnly(User $user)
    {
        return $user->getCardsInUsUnlocked(PrivacyAPI::CARD_TYPE_DUMMY)
            ->first();
    }

    /**
     * Create/get the dummy card for loading/unloading to hold the user balance info
     *
     * @param User             $user
     * @param CardProgram|null $cardProgram
     *
     * @return UserCard
     * @throws PortalException
     * @throws RedirectException
     */
    public static function getDummyCard(User $user, CardProgram $cardProgram = null)
    {
        $platform = Util::platform();
        if ($cardProgram) {
            $platform = $cardProgram->getPlatform();
        }

        if ($platform || $cardProgram) {
          if (($platform && $platform->isCashOnWeb()) || (is_object($cardProgram) && $cardProgram->isCashOnWeb())) {
            $cards = $user->getCardsInCashOnWeb(PrivacyAPI::CARD_TYPE_DUMMY);
          } else if (($platform && $platform->isSpendr()) || (is_object($cardProgram) && $cardProgram->isSpendr())) {
          	$cards = $user->getCardsInSpendr(PrivacyAPI::CARD_TYPE_DUMMY);
		  } else {
            $cards = $user->getCardsInUsUnlocked(PrivacyAPI::CARD_TYPE_DUMMY);
          }
        } else {
          $cardsUsu = $user->getCardsInUsUnlocked(PrivacyAPI::CARD_TYPE_DUMMY);
          $cardsCow = $user->getCardsInCashOnWeb(PrivacyAPI::CARD_TYPE_DUMMY);
          $cards = $cardsUsu->count() ? $cardsUsu : $cardsCow;
        }

        if ($cards->count()) {
            return $cards->first();
        }
        $cpct = CardProgramCardType::getForCardProgram($cardProgram ?? Util::cardProgram());
        $uc = CardService::create($user, $cpct);
        $uc->setType(PrivacyAPI::CARD_TYPE_DUMMY);

        if (($platform && $platform->isSpendr()) || (is_object($cardProgram) && $cardProgram->isSpendr())) {
        	$uc->setIssued(true)
				->setInitializedAt(new \DateTime())
				->persist();
		} else {
        	$uc->persist();
			$ei = $uc->createCard('/p/dummy_card', false);
			if ($ei && $ei->isFailed()) {
				throw new PortalException($ei->getError());
			}
		}

        $user->addCard($uc);
        return $uc;
    }

    public static function getBalance(User $user)
    {
        return self::getDummyCard($user)->getBalance();
    }

    public static function hasBalance(User $user)
    {
        $balance = self::getBalance($user);
        return $balance > 0;
    }

    public static function updatePayPalSubscriptInfo(User $user, $subscriptId, &$data = null) {
      $api = PayPalAPI::get();
      [$ei, $data] = $api->getSubscriptDetail($subscriptId, false);
      if (isset($data['plan_id']) && !$ei->isFailed()) {
        $subscription = PayPalSubscription::findBySubscriptionId($subscriptId);
        if (!$subscription) {
          $subscription = new PayPalSubscription();
          $subscription->setUser($user);
        }
        $plan = PayPalSubscription::getPlanById($data['plan_id']);
        $subscription->setSubscriptionId($subscriptId)
                      ->setQuantity($data['quantity'])
                      ->setStatus($data['status'])
                      ->setPlanType($plan)
                      ->persist();
        MauticEventService::submitPayPalSubscription($subscription);

        // if the plan is upgraded from monthly to annually, we need to cancel the monthly subscription
        if ($plan === PayPalSubscription::PLAN_TYPE_ANNUALLY
            && $data['status'] === PayPalSubscription::STATUS_ACTIVE
            && in_array($data['plan_id'], ['P-9NN28240DU182515BNCGDJCY', Config::get('usu_upgrade_annually_paypal_plan')]))
        {
          $monthlySubscription = PayPalSubscription::findCurrentActiveMonthly($user);
          if ($monthlySubscription) {
            try {
              $api = PayPalAPI::get();
              [$ei, $data] = $api->cancelSubscription($monthlySubscription->getSubscriptionId());
              if (!$ei->isFailed()) {
                $monthlySubscription->setStatus(PayPalSubscription::STATUS_CANCELLED)
                              ->persist();
              } else {
                SlackService::alert('Failed cancel the monthly paypal subscription when upgrade to annually.', [
                  'error' => $ei->getError(),
                  'subscriptionId' => $monthlySubscription->getSubscriptionId(),
                  'userId' => $user->getId(),
                ], SlackService::GROUP_DEV);
              }
            } catch (\Exception $e) {
              // do nothing
                SlackService::alert('Failed cancel the monthly paypal subscription when upgrade to annually.', [
                  'error' => $e->getMessage(),
                  'subscriptionId' => $monthlySubscription->getSubscriptionId(),
                  'userId' => $user->getId(),
                ], SlackService::GROUP_DEV);
            }
          }
        }
        return true;
      }
      return false;
    }

    public static function isPayPalSubscriptionWaived(User $user) {
        return Util::meta($user, 'paypalSubscriptionFree');
    }

    public static function getPreparedPayPalSubscription(User $user) {
        if (self::isPayPalSubscriptionWaived($user)) {
            $p = new PayPalSubscription();
            $p->setUser($user)
                ->setSubscriptionId('waived')
                ->setStatus(PayPalSubscription::STATUS_ACTIVE)
                ->setPlanType(PayPalSubscription::PLAN_TYPE_WAIVED)
                ->setCreatedAt(Carbon::create(2025, 7, tz: Util::tzUTC()))
                ->setQuantity(1);
            return $p;
        }
        return PayPalSubscription::findByUser($user);
    }

    /**
     * @param User $user
     *
     * @return bool|string|null false or null if no subscription found, true if the canceled subscription is
     * still active, or the ACTIVE status of the subscription
     */
    public static function getPayPalSubscription(User $user) {
      $subscription = self::getPreparedPayPalSubscription($user);
      if (!$subscription) {
        return false;
      }
      if ($subscription->getStatus() !== PayPalSubscription::STATUS_ACTIVE) {
        $subscriptionCreated = Carbon::instance($subscription->getStartAt());
        $now                 = Carbon::now();
        $month               = 1;
        if ($subscription->getCurrentPlanType() === PayPalSubscription::PLAN_TYPE_ANNUALLY) {
            $month = 12;
        }
        if ($subscription->getStatus() === 'CANCELLED' && $subscriptionCreated->addMonthsNoOverflow($month)->gte($now)) {
          return true;
        }
        return false;
      }
      return $subscription->getStatus();
    }

    public static function getPayPalSubscriptionExpiration(User $user): ?string
    {
        $subscription = self::getPreparedPayPalSubscription($user);
        if (!$subscription) {
            return null;
        }
        $startedAt = Carbon::instance($subscription->getStartAt());
        $month = 1;
        $plan = $subscription->getCurrentPlanType();
        if ($plan === PayPalSubscription::PLAN_TYPE_ANNUALLY) {
            $month = 12;
        } else if ($plan === PayPalSubscription::PLAN_TYPE_WAIVED) {
            $month = 9999;
        }
        return Util::formatDate($startedAt->addMonthsNoOverflow($month));
    }

    public static function getPayPalSubscriptionId(User $user) {
        $subscription = self::getPreparedPayPalSubscription($user);
        if (!$subscription) {
            return null;
        }
        return $subscription->getSubscriptionId();
    }

    public static function getPayPalSubscriptionType(User $user) {
        $subscription = self::getPreparedPayPalSubscription($user);
        if ($subscription) {
            return $subscription->getCurrentPlanType();
        }
        return '';
    }

    public static function getPayPalSubscriptionSummary(User $user): string
    {
        $sub = self::getPreparedPayPalSubscription($user);
        if (!$sub) {
            return 'None';
        }
        $subStatus = $sub->getStatus();
        if ($subStatus !== PayPalSubscription::STATUS_ACTIVE) {
            return Util::title($sub->getStatus());
        }
        return Util::title($sub->getCurrentPlanType());
    }

    public static function getAccountStatus(User $user) {
      $status = $user->getStatus();
      $sumsubKycStatus = $user->getSumsubApplicantStatus();
      if ($status === User::STATUS_ACTIVE) {
          // check KYC status
          if ($sumsubKycStatus !== 'GREEN') {
              $accountStatus = 'Pending';
          } else {
              // check rain status
              $rainApplicationStatus = $user->getRainApplicationStatus();
              $kycPending = (bool)Util::meta($user, 'rainKycPending');
              if ($rainApplicationStatus === RainAPI::KYC_STATUS_MANUAL_REVIEW) {
                  $accountStatus = 'Manual Review';
              } else if ($rainApplicationStatus === RainAPI::KYC_STATUS_DENIED) {
                  $accountStatus = 'Denied';
              } else if ($kycPending) {
                  $accountStatus = 'Pending Review';
              } else if (!($user->getConfig()->getRainUserId())) {
                  $accountStatus = 'Pending Activation';
              } else {
                  // check PayPal status
                  $subscription = self::getPreparedPayPalSubscription($user);
                  if (!$subscription) {
                      $accountStatus = 'Subscription Pending';
                  } else {
                      if ($subscription->getStatus() === PayPalSubscription::STATUS_CANCELLED) {
                          $accountStatus = 'Subscription Cancelled';
                      } else if ($subscription->getStatus() === PayPalSubscription::STATUS_SUSPENDED) {
                          $accountStatus = 'Subscription Expired';
                      } else {
                          $accountStatus = 'Active';
                      }
                  }
              }
          }
      } else {
          $accountStatus = Util::title($status);
      }
      return $accountStatus;
    }

    public static function getRainUserId(User $user) {
        return $user->ensureConfig()?->getRainUserId();
    }
}
