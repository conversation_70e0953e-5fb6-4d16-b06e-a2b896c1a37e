<?php


namespace UsUnlockedBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\Platform;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Traits\ConstantTrait;
use CoreBundle\Utils\Util;
use Exception;
use GuzzleHttp\Client;
use SalexUserBundle\Entity\User;
use Stringy\Stringy;
use Throwable;

// 	#usu-alerts
class SlackService
{
    use ConstantTrait;

    public const WEBHOOK_HANS   = '*******************************************************************************';
    public const WEBHOOK_URGENT = '*******************************************************************************'; // #urgent-alerts
    public const WEBHOOK_SYSTEM = '*******************************************************************************'; // #alerts
    public const WEBHOOK_DEVS   = '*******************************************************************************';

    public const CHANNEL_DEFAULT = null;
    public const CHANNEL_CLIENT = 'client'; // == external, public
    public const CHANNEL_COMPLIANCE = 'compliance';
    public const CHANNEL_EVENTS = 'events';
    public const CHANNEL_HANS = 'hans';

    // Tern employees
    public const MENTION_ABEL = '<@U01DM3794AV>';
    public const MENTION_BRION = '<@U0A40URT2>';
    public const MENTION_ELYSE = '<@U028U86FWLU>';
    public const MENTION_HARDIN = '<@U079E2JET41>';
    public const MENTION_GEORGE = '<@U02ADNMEQNM>';
    public const MENTION_HANS = '<@ULV4KLGCD>';
    public const MENTION_IAN = '<@U0SBV4VMK>';
    public const MENTION_ISA = '<@U04JEP79709>';
    public const MENTION_JERRY = '<@U05CCCSATBJ>'; // Jerry Jiang
    public const MENTION_JOOST = '<@U8QQAGDGA>';
    public const MENTION_TRACY = '<@U05C1AUGYBT>';

    // TransferMex employees
    public const MENTION_MEX_ROBERT = '<@U024ZKG0U2W>';
    public const MENTION_MEX_GERARDO = '<@U02VDJVE62E>';
    public const MENTION_MEX_DIANA = '<@U031NL9BY3X>';
    public const MENTION_MEX_ALAN = '<@U03SXJKBLF6>';

    // Spendr employees
    public const MENTION_SPENDR_LUCAS = '<@U027YAGNMQU>';
    public const MENTION_SPENDR_AUSTIN_SCHNEIDER = '<@U02QQS75ECB>';
    public const MENTION_SPENDR_RYAN = '<@U02QYRU1SHK>';
    public const MENTION_SPENDR_JOEL = '<@U07A8HTPZEJ>';

    // Rain
    public const MENTION_RAIN_SUPPORT = '<@U07RWBVR1T2>';

    // Groups
    public const GROUP_USU_BALANCE = [
        self::MENTION_HANS,
        self::MENTION_GEORGE,
    ];

    public const GROUP_USU_NEG_BALANCE = [
        self::MENTION_HANS,
        self::MENTION_GEORGE,
        self::MENTION_JOOST,
    ];

    public const GROUP_SPENDR_NEG_BALANCE = [
        self::MENTION_TRACY,
        self::MENTION_JERRY,
		self::MENTION_SPENDR_LUCAS,
        self::MENTION_SPENDR_JOEL
    ];

    public const GROUP_DEV = [
        self::MENTION_HANS,
        self::MENTION_ABEL,
    ];

    public const GROUP_DEV_SHINETECH = [
        self::MENTION_HANS,
        self::MENTION_ABEL,
        self::MENTION_TRACY,
        self::MENTION_JERRY,
    ];

    public const DEVELOPER_GROUP = self::GROUP_DEV;

    public const USU_LOAD_GROUP = [
      self::MENTION_IAN,
      self::MENTION_ISA,
    ];

    public const GROUP_COINFLOW_REVIEW = [
        self::MENTION_IAN,
        self::MENTION_ISA,
    ];

    public const GROUP_SUMSUB_REVIEW = [
        self::MENTION_IAN,
        self::MENTION_ISA,
    ];

    public static $channel;

    public static $webhookUrl;

    public static $platform;

    public static $disabled = false;

    protected static $forced = false;

    public static $frequencySeconds = false;
    public static $frequencyControl = 'message'; // Only message or all(including details)

    public static $mentions = [];

    public static function prepareForCompliance()
    {
        self::prepareForPlatform(null, self::CHANNEL_COMPLIANCE);
    }

    public static function prepareForPlatform(Platform $platform = null, $channel = null, $mentions = [])
    {
        if ($channel !== null) {
            static::$channel = $channel;
        }
        static::$platform = $platform;
        static::$webhookUrl = static::getWebhookUrlForPlatform($platform);
        static::$mentions = array_merge(
            (array)static::$mentions,
            (array)$mentions
        );
    }

    /**
     * @param Platform|null $platform
     *
     * @return string
     */
    public static function getWebhookUrlForPlatform(Platform $platform = null)
    {
        $platform = $platform ?? Util::platform() ?? Platform::ternCommerce();
        $custom = static::isCustomizedInPlatform($platform, 'getWebHookUrl');
        if ($custom) {
            return $custom($platform);
        }
        return static::getWebHookUrl();
    }

    public static function getWebHookUrl()
    {
        $platform = Util::platform();
        if ($platform && $platform->isCashOnWeb()) {
          // Cow live
          $url = Util::getParameter('slack_webhook_cow');
          if (!Util::isLive() || Util::isDev()) {
              $url = Util::getParameter('slack_webhook_cow_test');
          }
        } else {
          // USU live
          $url = Util::getParameter('slack_webhook_usu');
          if (static::$channel === self::CHANNEL_COMPLIANCE) {
              $url = Util::getParameter('slack_webhook_usu_compliance', true) ?? $url;
          }

          if (static::$channel === self::CHANNEL_EVENTS) {
              $url = Util::getParameter('slack_webhook_usu_events', true) ?? $url;
          }

          if (!Util::isLive() || Util::isDev()) {
              $url = Util::getParameter('slack_webhook_test');
          }
        }
        return $url;
    }

    public static function hans($text, $detail = null, $mentions = [])
    {
        Log::debug($text, $detail ?: []);
        static::$channel = 'hans';
        static::request(':wave:', $text, $detail, $mentions);
    }

    public static function tada($text, $detail = null, $mentions = [])
    {
        Log::debug($text, $detail ?: []);
        static::request(':tada:', $text, $detail, $mentions);
    }

    public static function info($text, $detail = null, $mentions = [])
    {
        Log::info($text, $detail ?: []);
        static::request(':information_source:', $text, $detail, $mentions);
    }

    public static function check($text, $detail = null, $mentions = [])
    {
        Log::debug($text, $detail ?: []);
        static::request(':white_check_mark:', $text, $detail, $mentions);
    }

    public static function wave($text, $detail = null, $mentions = [])
    {
        Log::debug($text, $detail ?: []);
        static::request(':wave:', $text, $detail, $mentions);
    }

    public static function urgent($text, $requiredAction, $detail = [], $mentions = [])
    {
        $oldPlatform = static::$platform;
        $oldWebhook = static::$webhookUrl;

        static::$platform = Platform::ternCommerce();

        if (Util::isLive()) {
            static::$webhookUrl = self::WEBHOOK_URGENT;
        }

        $detail['required_action'] = $requiredAction;
        static::alert($text, $detail, $mentions);

        static::$platform = $oldPlatform;
        static::$webhookUrl = $oldWebhook;
    }

    public static function alert($text, $detail = null, $mentions = [])
    {
        $sendMail = true;
        if (in_array($text, [
            'Other useless error messages',
            'Failed to request the card API: Card not active',
        ])) {
            $sendMail = false;
        } else if (Util::hasPrefix($text, [
            'An IDology API Internal Error for',
            'Failed to request the id verification API',
            'OFAC alert',
            'Duplicated payout amount on the same date',
            'Load failed, reason: ',
            'Unload failed, reason: ',
            'Skip updating all transactions since the last execution is not completed yet',
            'The location is not associated with your account',
            'Please activate this location first',
            'Transfer Payout alert: ',
            'Moved the auto balance adjustment to the load queue ',
            'This card will be deleted',
            '`Completed` payout becomes `Error`',
            'Failed to create/confirm Rapyd the payout',
            'Failed to create/confirm UniTeller the payout',
        ])) {
            $sendMail = false;
        }
        if ($sendMail) {
            Log::error($text, $detail ?: []);
        } else {
            Log::warn($text, $detail ?: []);
        }
        static::request(':exclamation:', $text, $detail, $mentions);
    }

    public static function bell($text, $detail = null, $mentions = [])
    {
        Log::info($text, $detail ?: []);
        static::request(':bell:', $text, $detail, $mentions);
    }

    public static function warning($text, $detail = null, $mentions = [])
    {
        Log::warn($text, $detail ?: []);
        static::request(':warning:', $text, $detail, $mentions);
    }

    public static function clap($text, $detail = null, $mentions = [])
    {
        Log::info($text, $detail ?: []);
        static::request(':clap:', $text, $detail, $mentions);
    }

    public static function eyes($text, $detail = null, $mentions = [])
    {
        Log::info($text, $detail ?: []);
        static::request(':eyes:', $text, $detail, $mentions);
    }

    public static function point($text, $detail = null, $mentions = [])
    {
        Log::info($text, $detail ?: []);
        static::request(':point_right:', $text, $detail, $mentions);
    }

    public static function clock($text, $detail = null, $mentions = [])
    {
        Log::info($text, $detail ?: []);

        $now = Carbon::now()->hour % 12;
        if ($now <= 0) {
            $now = 12;
        }
        static::request(':clock' . $now . ':', $text, $detail, $mentions);
    }

    public static function pure($text, $detail = null, $mentions = [])
    {
        self::request('', $text, $detail, $mentions);
    }

    public static function controlFrequency(int $seconds, bool $all = false)
    {
        static::$frequencySeconds = $seconds;
        static::$frequencyControl = $all ? 'all' : 'message';
    }

    public static function exception($message, Throwable $exception, $context = null, $traceOrMentions = false, $mentions = [])
    {
        $detail = [
            'exception' => (new \ReflectionClass($exception))->getShortName(),
            'file' => Util::getExceptionFileLine($exception),
        ];

        if ($exception->getCode()) {
            $detail['code'] = $exception->getCode();
        }

        if (method_exists($exception, 'getExternalInvokeId')) {
            $detail['ei'] = $exception->getExternalInvokeId();
        }

        if ($context) {
            $detail = array_merge($detail, $context);
        }
        if (is_array($traceOrMentions)) {
            $trace = false;
            $mentions = array_merge($traceOrMentions, $mentions);
        } else if (is_string($traceOrMentions) && Util::startsWith($traceOrMentions, '<@')) {
            $trace = false;
            $mentions = array_merge([
                $traceOrMentions,
            ], $mentions);
        } else {
            $trace = $traceOrMentions;
        }
        if ($trace) {
            $detail['trace'] = $exception->getTraceAsString();
        } else {
            Log::debug($message, array_merge($detail, [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]));
        }
        static::alert($message . ': ' . $exception->getMessage(), $detail, $mentions);
    }

    protected static function isCustomizedInPlatform(Platform $platform = null, $methodName = null)
    {
        if (!$platform) {
            return false;
        }
        $bundleName = $platform->getBundleName();
        $className = $bundleName . '\Services\SlackService';
        if (!$methodName) {
            if (class_exists($className)) {
                return [$className];
            }
        }
        if (class_exists($className) && method_exists($className, $methodName)) {
            return [$className, $methodName];
        }
        return false;
    }

    protected static function isExternalChannel($channel = null)
    {
        $channel = $channel ?? static::$channel;
        return in_array(strtolower($channel ?? ''), [
            'client',
            'external',
            'public',
        ]);
    }

    protected static function getFaasWebhookUrlForPlatform(Platform $platform = null)
    {
        $internal = 'slackInternalChannel';
        $external = 'slackExternalChannel';
        $field = $internal;
        if (static::isExternalChannel()) {
            $field = $external;
        }
        $url = Util::meta($platform, $field);
        if (!$url && $field === $external) {
            $url = Util::meta($platform, $internal);
        }
        return $url;
    }

    protected static function isFaasPlatformUsingCommonChannel(Platform $platform = null)
    {
        if (!$platform || !$platform->isFaasPlatforms()) {
            return false;
        }
        $url = static::getFaasWebhookUrlForPlatform($platform);
        return !$url;
    }

    protected static function request($prefix, $text, $detail = null, $mentions = [])
    {
        if (static::$channel === 'internal') {
            static::$channel = null;
        }

        $text = trim($prefix . ' ' . $text);
        if (Util::$console && Util::isCommand()) {
            try {
                Util::$console->line($text, null, $detail ?? []);
            } catch (\Exception $ex) {}
        }

        if (Util::isLocal()) {
            // Log::debug('Skip sending Slack message locally: ' . $text);
            return;
        }

        if (static::$frequencySeconds) {
            $keyParts = [$text];
            if (static::$frequencyControl === 'all') {
                $keyParts[] = $detail;
            }
            $cacheKey = 'slack_msg_' . md5(json_encode($keyParts));
            if (Data::has($cacheKey)) {
                Log::debug('Skip sending Slack message due to the frequency limit: ' . $text,
                    $detail ?: []);
                return;
            }
            Data::set($cacheKey, true, true, self::$frequencySeconds);
        }

        $mentions = array_merge(
            (array)(self::$mentions ?? []),
            (array)($mentions ?? [])
        );
        if ($mentions) {
            foreach ($mentions as $i => $me) {
                if ( ! Util::startsWith($me, '<@')) {
                    $me = static::class . '::MENTION_' . strtoupper($me);
                    if (defined($me)) {
                        $mentions[$i] = constant($me);
                    }
                }
            }

            $mentions = array_values(array_unique($mentions));
            if (self::$forced || Util::isLive()) {
                $text .= ' ' . implode(' ', $mentions);
            } else {
                $mentions = self::getMentionUsers($mentions);
                $text .= ' @' . implode(', ', $mentions);
            }
        }

        $blocks = [];
        $blocks[] = [
            'type' => 'section',
            'text' => [
                'type' => 'mrkdwn',
                'text' => $text,
            ],
        ];

        if ($detail) {
            $blocks[] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => '```' . Util::formatJSON($detail) . '```',
                ],
            ];
        }

        /** @var Platform $platform */
        $platform = static::$platform ?? Util::silent(fn () => Util::platform());
        if ($platform) {
            $live = Util::isLive();
            $customized = static::isCustomizedInPlatform($platform);
            $faasCommon = static::isFaasPlatformUsingCommonChannel($platform);
            $tern = $platform->isTernCommerce();
            if (!$live || (!$customized && !$tern) || $faasCommon) {
                $cmd = Util::isCommand();
                $name = $platform->getName(!$cmd) . ($live ? '' : ' - Test');

                $icon = $platform->getIconUrl();
                if ($cmd && $platform->isTernCommerce()) {
                    $icon = Util::host() . Platform::DEFAULT_ICON;
                }

                $blocks[] = [
                    'type' => 'context',
                    'elements' => [
                        [
                            'type' => 'image',
                            'image_url' => $icon,
                            'alt_text' => $name,
                        ],
                        [
                            'type' => 'mrkdwn',
                            'text' => '*' . $name . '*',
                        ],
                    ],
                ];
            }
        }
        $body = [
            'text' => $text,
            'blocks' => $blocks,
        ];

        $url = static::$webhookUrl;
        if (!$url && $platform && !in_array($platform->getName(), [
                Platform::NAME_TERN_COMMERCE,
                Platform::NAME_CASH_ON_WEB,
            ])) {
            $custom = static::isCustomizedInPlatform($platform, 'getWebHookUrl');
            if ($custom) {
                $url = $custom();
            }
        }
        if (!$url) {
            $url = static::getWebHookUrl();
        }
        if (static::$channel === 'hans') {
            $url = static::WEBHOOK_HANS;
        }

        try {
            self::doSend($url, $body);
        } catch (Exception $exception) {
            $time = Util::randTimeNumber();
            $msg = $exception->getMessage();
            Log::warn('Failed to send Slack msg (' . $time . '): ' . $msg, [
                'body' => $body,
                'url' => $url,
            ]);

            if (strpos($msg, 'invalid_blocks') !== false) {
                try {
                    $blocks[0]['text']['text'] = Util::maxLength($blocks[0]['text']['text'], 100);
                    $blocks[1] = [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => '```Check the service log for the details. (ID: ' . $time . ')```',
                        ],
                    ];
                    $body = [
                        'text' => $blocks[0]['text']['text'],
                        'blocks' => $blocks,
                    ];
                    self::doSend($url, $body);
                } catch (Exception $exception) {
                    Log::warn('Failed to send Slack msg again: ' . $exception->getMessage(), [
                        'body' => $body,
                    ]);
                }
            }
        }
        static::$channel = null;
        static::$webhookUrl = null;
        static::$platform = null;
        static::$mentions = [];
        static::$frequencySeconds = false;
        static::$frequencyControl = 'message';
    }

    private static function doSend($url, $body)
    {
        if (!static::$forced && (static::$disabled || Util::isTestEnv())) {
            $context = [];
            if (is_array($body)) {
                $context = $body;
            } else {
                $context['body'] = $body;
            }
            Log::debug('Muted sending Slack alert to ' . $url, $context);
            return;
        }

        $client = new Client();
        $client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => is_string($body) ? $body : Util::j2s($body),
        ]);
    }

    public static function force(callable $callback)
    {
        static::$forced = true;
        try {
            $callback();
        } catch (\Throwable) {}
        static::$forced = false;
    }

    public static function toChannel($channel, $action, $msg, $context = null)
    {
        static::$channel = $channel;
        static::$action($msg, $context);
        static::$channel = null;
    }

    public static function getMentionGroup($type, Platform $platform = null)
    {
        $type = strtoupper($type);
        $key = 'USU';
        if (!$platform) {
			$platform = Util::platform();
		}
        if ($platform && !$platform->isTernCommerce()) {
            $key = $platform->getKey();
        }
        $const = static::class . '::GROUP_' . $key . '_' . $type;
        $mentions = [];
        if (defined($const)) {
            $mentions = constant($const);
        }
        return $mentions;
    }

    public static function getMentionUsers($mentions)
    {
        $constants = static::getConstantsStartWith('MENTION_', true);
        $constants = array_flip($constants);
        $result = [];
        foreach ($mentions as $me) {
            $result[] = (string)Stringy::create($constants[$me] ?? $me)->toTitleCase();
        }
        return $result;
    }

    public static function prepareForEvents()
    {
        self::prepareForPlatform(null, self::CHANNEL_EVENTS);
    }


    public static function sendUsuEvents(User $user, $type, $details = null, array $others = []) {
      if (!Util::isLive()) {
        Log::debug('Send event to Usu Events Channel');
      }
      self::prepareForEvents();
      $context = [
        'Member' => $user->getSignature(),
        'Type'  => $type,
      ];
      if ($details) {
          $context['Details'] = $details;
      }
      if ($others) {
          $context = array_merge($context, $others);
      }
      $msg = 'User Events';

      SlackService::info($msg, $context);
    }
}
