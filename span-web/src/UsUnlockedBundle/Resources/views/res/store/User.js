import _ from 'lodash'

const state = {
  token: null,
  detail: null,
  setFf: false,
  ffId: null,
  loadLocator: false,
  KYC_notice: false,
  kycOnAll: true,
  twoFactorSecret: false,
  twoFactorFlag: false,
  cp: {
    id: 0,
    name: null,
    isCashOnWeb: false
  },
  referAmount: '$5.00',
  balance: '0.00',
  subscription: '',
  systemMessage: null,
  rainAccountId: null,
  needRainKYC: true,
  rainKycUrl: null,
  paypalSubscriptionFlag: false,
  paypalSubsExpiration: null,
  legalsAccepted: false,
  showMaintenanceInfo: false,
  rainKycPending: false,
  subscriptionPlan: null,
  subscriptionAnnuallyPlan: null,
  subscriptionUpgradeAnnuallyPlan: null,
  sumsubApplicantStatus: null,
  sumsubApplicantReason: null,
  sumsubApplicantClientComment: null,
  applicationReason: '',
  accountStatus: null,
  inviteReferer: null,
  hasFreeCards: true
}

const mutations = {
  update (state, o) {
    _.assignIn(state, o)
  },
  updateLoadLocator (state, val) {
    state.loadLocator = val
  },
  updateKYCNotice (state, val) {
    state.KYC_notice = val
  }
}

const getters = {
  balance (state) {
    let balance = ('' + (state.balance || '0')).replace(',', '')
    const b = parseFloat(balance)
    if (!b || Number.isNaN(b)) {
      return 0
    }
    return b
  },
  hasBalance (state, getters) {
    return getters.balance > 0
  },
  negativeBalance (state, getters) {
    return getters.balance < 0
  }
}

const actions = {
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
