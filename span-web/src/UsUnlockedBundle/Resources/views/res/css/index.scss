/** Bootstrap */
@import '_variable.scss';
@import '_usu.scss';

.btn {
  border-radius: 0.6rem;
}

.btn-success {
  background-color: $color_success;
  border-color: $color_success;
}

.btn-light {
  background-color: $color_light;
  border-color: $color_light;
}

.btn-green {
  background: $color_success;
  color: white;
  border-radius: 30px;

  &:hover {
    background: #1d8c21;
    color: white;
  }
}

.dropdown-item {
  &:focus,
  &:hover {
    background-color: #eee;
  }

  &.active,
  &:active {
    color: #333;
    background-color: #ddd;
  }
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-y-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tooltip-inner {
  max-width: 280px;
  text-align: left;
  padding: 0.6rem 0.8rem;

  > {
    h6 {
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.5);
      padding-bottom: 6px;

      &.sm {
        font-size: 13px;
        margin-bottom: 5px;
      }
    }

    p:last-of-type {
      margin-bottom: 0;
    }
  }
}

/** Global */

:root {
  font-size: 14px;
  color: #444;
}

body {
  font-family: 'Rubik', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f2f2f2;

  &.with-iframe {
    #root.container-xl {
      max-width: none;
      padding: 75px 0 0;
      overflow-y: hidden;
    }
  }
}

img {
  max-width: 100%;
  max-height: 100%;
}

a.link {
  color: $color_primary;
}

.main-iframe {
  width: 100%;
  height: 100%;
}

.page-loading {
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  justify-content: center;
  align-items: center;
  color: var(--blue);

  > .spinner-border {
    width: 4rem;
    height: 4rem;
  }
}

#header {
  height: 75px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

  .navbar-brand.logo {
    height: 62px;
  }

  .user-info {
    line-height: 44px;
    vertical-align: middle;
    cursor: pointer;

    &.user-alert-info {
      + .dropdown-menu:before {
        right: 22px;
      }
    }

    > .fa.fa-caret-down {
      line-height: 44px;
      margin-left: 8px;
      color: #888;
    }

    + .dropdown-menu {
      border-radius: 8px;
      min-width: 220px;

      .dropdown-header {
        text-align: center;
        font-size: 12px;
        border-bottom: 1px solid #ddd;

        img {
          height: 20px;
          margin-top: -10px;
        }
      }

      .email-info {
        margin: 10px 20px;
        color: $color_danger;
        font-weight: 300;
        font-size: 12px;
      }

      &:before {
        content: '';
        border: solid rgba(0, 0, 0, 0.15);
        border-width: 0 1px 1px 0;
        display: inline-block;
        padding: 5px;
        transform: rotate(-135deg);
        background: white;
        position: absolute;
        right: 32px;
        top: -6px;
      }

      .dropdown-item {
        padding: 0.7rem 1.5rem;

        .fa,
        .fas {
          margin-right: 10px;
        }
      }
    }
  }
}

.avatar-box {
  width: 44px;
  height: 44px;
  background: #eee;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.22);

  .img {
    overflow: hidden;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .default {
      width: 80%;
      height: auto;
    }
  }

  .status {
    border-radius: 50%;
    position: absolute;
    right: 0;
    bottom: 0;
    width: 14px;
    height: 14px;
    background: $color_success;
    border: 2px solid white;
    box-shadow: 0 0 2px #666;
  }
}

.notification-bell {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  position: relative;
  padding-right: 15px;
}

.badge {
  position: absolute;
  top: 5px;
  right: 10px;
  padding: 4px 5px;
  border-radius: 50%;
  background: red;
  color: white;
  font-size: 10px;
  text-decoration: none;
}

// @Nathanael: Be careful to change the global CSS like below:
.alert {
  //border-top: 1px solid lightgrey;
  //border-top: 1px solid rgba(0, 0, 0, 0.20);
  //padding-top: 0;
  //padding-right: 0;
}

.alert-title-container {
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin: 5px;
}

.alert-title {
  margin-top: 7px;
  font-weight: bold;
  padding-right: 10px;
  padding-left: 20px;
}

.alert-close {
  max-width: 14px;
  cursor: pointer;
  color: $color_danger;
  position: absolute;
  right: 10px;
  padding: 2px;
  transform: translate(0, 10px);
}

.dismiss-container {
  //border-top: 1px solid rgba(0, 0, 0, 0.20);
  text-align: center;
}

.dismiss-button {
  border: none;
  color: $color_danger;
  margin-top: 10px;
  background: white;
}

.alert-link {
  text-decoration: none !important;
  margin-bottom: 0;
  color: rgba(0, 0, 0, 0.5);
  font-style: normal;
  font-weight: normal;
}

.alert-link :hover {
  text-decoration: none !important;
  margin-bottom: 0;
  color: rgba(0, 0, 0, 0.5);
}

.alert-body {
  padding-bottom: 0;
  margin-bottom: 0;
  display: block;
  width: 350px;
}

.alert-message {
  text-decoration: none;
  padding-bottom: 0;
  margin-right: 40px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  max-width: 310px;
  min-width: 310px;
  max-height: 50%;
  //outline: 1px solid black;
  text-align: left;
  display: inline-block;

  white-space: normal;
}

.no-alerts-message {
  margin: 20px 10px 0 0;
  padding: 0;
}

.ff-box {
  margin: 16px auto 0;
  background: #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.5);
  padding: 4px 10px 2px;
  font-size: smaller;
  text-align: center;
  display: inline-block;
  transform: scale(0.9);
  cursor: pointer;

  img {
    width: 28px;
  }

  &:hover {
    box-shadow: 1px 3px 5px rgba(0, 0, 0, 0.6);
  }
}

.Oval {
  width: 25px;
  height: 25px;
  padding: 10px;
}

.viewed-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #0c55f2;
  position: absolute;
  transform: translate(390px, 45px);
}

#root {
  padding: 90px 15px 10px;
  height: 100vh;
  overflow-x: auto;
}

#footer {
  background: rgba(255, 255, 255, 0.7) !important;
  right: auto;
  width: 136px;
  height: 40px;
  border-top-right-radius: 20px;
  box-shadow: 1px 0 4px rgba(0, 0, 0, 0.12);

  .navbar-brand.logo {
    position: fixed;
    left: 13px;
    font-size: 14px;
    font-weight: 300;
    color: $color_text;

    span {
      margin-right: 5px;
      vertical-align: text-top;
      line-height: 40px;
    }

    img {
      // height: 40px;
      width: 108px;
    }
  }
}

//.sf-display-none {
//  display: none !important;
//}

#beacon-container .Beacon > {
  .BeaconFabButtonFrame {
    right: -2px;
    bottom: 2px;
    transform: scale(0.85) !important;
    opacity: 0.85;
  }

  .BeaconNotificationsFrame {
    bottom: 50px;
    right: 10px;
  }

  .BeaconContainer {
    right: 15px;
    bottom: 65px;
  }
}

@media (max-width: 450px) {
  #beacon-container > .Beacon > {
    .BeaconContainer {
      right: 10px;
      bottom: 50px;
      height: calc(100% - 50px);
    }
  }
}

@media (max-height: 667px), (max-width: 576px) {
  #beacon-container > .Beacon > {
    .BeaconFabButtonFrame {
      right: -78px;
      bottom: -7px;
      transform: scale(0.7) !important;
      opacity: 0.65;
    }

    .BeaconNotificationsFrame {
      bottom: 40px;
      right: 0;
    }

    .BeaconContainer {
      right: 10px;
      bottom: 50px;
    }
  }

  #root {
    padding-top: 68px;
    padding-bottom: 10px;
  }

  #header {
    height: 58px;

    .navbar-brand.logo {
      height: 40px;
      margin-top: -4px;
    }
  }

  #footer {
    box-shadow: none;
    right: auto;
    left: 0;
    bottom: 1px;
    height: 38px;
    width: 36px;
    padding: 0;

    .navbar-brand.logo {
      position: static;
      left: 0;
      font-size: 13px;
      padding: 0;

      span {
        line-height: 30px;
      }

      img {
        height: 30px;
      }
    }
  }

  body.with-iframe {
    #root.container-xl {
      padding-top: 58px;
      padding-bottom: 0;
    }
  }
}

@media (max-width: 1190px) {
  .title-icon-box > .title > .sub-title,
  .title-select-box > .title > .sub-title {
    font-size: 0.9rem !important;
  }
}

@media (max-width: 1100px) {
  .title-icon-box > .title > .sub-title,
  .title-select-box > .title > .sub-title {
    font-size: 0.85rem !important;
  }
}

@media (max-width: 1020px) {
  .title-icon-box > .title > .sub-title,
  .title-select-box > .title > .sub-title {
    font-size: 0.8rem !important;
  }
}

@media (max-width: 965px) {
  .title-icon-box > .title > h4,
  .title-select-box > .title > h4 {
    font-size: 1.2rem !important;
  }

  .title-icon-box > .title > .sub-title,
  .title-select-box > .title > .sub-title {
    font-size: 0.75rem !important;
  }
}

@media (max-width: 920px) {
  .title-icon-box > .title > .sub-title,
  .title-select-box > .title > .sub-title {
    font-size: 0.7rem !important;
  }
}

@mixin printCss {
  body * {
    visibility: hidden;
  }
  .section-to-print,
  .section-to-print * {
    visibility: visible;
  }
  .section-to-print {
    position: fixed;
    left: 0;
    top: 0;

    .section-to-print-exclude,
    .section-to-print-exclude * {
      visibility: hidden;
    }
  }
}

html.print {
  @include printCss;
}

@media print {
  @include printCss;
}

/** Index page */

.container-xl {
  max-width: 1200px;
}

.text-shadow {
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

img.shadow {
  box-shadow: none !important;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.5));
}

.btn-circle {
  width: 28px;
  height: 28px;
  background: #444;
  border-radius: 50%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  border: 1px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

a.btn-circle {
  &:hover,
  &:focus,
  &:active {
    color: white;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.7);
    text-decoration: none;
  }
}

.btn-circle {
  &.help {
    background: $color_primary;
  }

  &.status {
    &.status-active {
      background: $color_success;
      font-size: 12px;
      padding-left: 2px;
    }

    &.status-inactive {
      background: $color_danger;
      font-size: 12px;
    }
  }

  &.refresh {
    .fa {
      font-size: 13px;
    }
  }

  &.setting,
  &.refresh {
    background: #cbd1da;
    color: #7a869a;
    padding-top: 1px;
  }
}

a.btn-circle.setting,
a.btn-circle.refresh {
  &:hover,
  &:focus,
  &:active {
    color: #555;
  }
}

.card-body > .card-body-container {
  overflow-y: auto;
  overflow-x: visible;
  margin-left: -24px;
  margin-right: -24px;
  padding: 10px 30px 0;
}

@media (max-width: 1024px) {
  .card-body > .card-body-container {
    margin-left: -15px;
    margin-right: -15px;
    padding: 10px 20px 0;
  }
}

.add-card-box {
  text-transform: uppercase;
  font-size: 30px;
  font-weight: 300;
  cursor: pointer;
  box-shadow: none;
  margin-bottom: 20px;
  transition: background 0.3s;

  &:hover {
    background: #eee;
  }

  .icon {
    width: 60px;
    height: 60px;
    background: #38bf4e;
    border-radius: 50%;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40px;
    border: 4px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    margin: 0 6px;
  }

  .card-box-inner > span {
    flex-basis: calc((100% - 72px) / 2);

    &:first-of-type {
      text-align: right;
      letter-spacing: 0.3rem;
    }
  }
}

@media (max-width: 965px) {
  .add-card-box {
    font-size: 25px;
  }
}

.card-box {
  width: 100%;
  padding-top: 60%;
  background: #f5f5f5;
  border-radius: 10px;
  margin-bottom: 20px;
  position: relative;
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.3);
}

.card-box-name {
  text-align: center;
  font-weight: 500;
  margin-bottom: 3px;
}

.card-box-inner {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-box.virtual-card {
  background: #084d7e;
  &.cow-card {
    border-radius: 25px;
  }
  .btn-circle.status.cow-setting {
    right: 20px !important;
    top: 20px !important;
    left: auto;
    border-radius: 8px;
  }
  .btn-circle.setting.cow-setting {
    right: 20px !important;
    bottom: 20px !important;
    left: auto;
    border-radius: 8px;
  }

  .btn-circle {
    &.help {
      position: absolute;
      right: -10px;
      top: -10px;
    }

    &.status {
      position: absolute;
      left: -10px;
      top: -10px;
    }

    &.setting,
    &.refresh {
      position: absolute;
      right: -10px;
      bottom: -10px;
    }
  }

  .icon {
    position: absolute;
    left: 7%;
    top: 10%;
    max-height: 20%;
    max-width: 40%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .card-status {
    position: absolute;
    right: 10%;
    top: 15%;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 0 10px;
    color: white;
    text-transform: uppercase;
    font-size: 12px;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
  }

  .card-number,
  .expire,
  .holder {
    font-family: 'OCR A Extended', 'Rubik', 'Helvetica Neue', Helvetica, Arial,
      sans-serif;
    font-size: 21px;
    position: absolute;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    cursor: pointer;
  }

  .usu-card-icon {
    font-size: 30px;
    position: absolute;
    cursor: default;
  }

  .card-number:hover,
  .expire:hover,
  .holder:hover {
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8);
  }

  .card-number {
    width: 82%;
    text-align: justify;
    top: 50%;
    transform: translateY(-25%);

    &:after {
      content: '';
      display: inline-block;
      width: 100%;
    }
  }

  .mc-icon {
    position: absolute;
    width: 50px;
    height: 50px;
    right: 7%;
    bottom: 9%;
    cursor: pointer;

    &.mc-icon-img {
      border-radius: 12px;
      overflow: hidden;
    }
  }

  .expire {
    left: 9%;
    bottom: 12%;
  }

  .holder {
    left: 45%;
    bottom: 12%;
  }

  .usu-card-icon {
    right: 8%;
    bottom: 5%;
  }
}

@font-face {
  font-family: 'OCR A Extended';
  src: url('../img/ocraext.ttf') format('truetype');
}

@media (max-width: 980px) {
  .card-box.virtual-card {
    .card-number,
    .expire,
    .holder {
      font-size: 15px !important;
    }

    .mc-icon {
      width: 35px;
      height: 35px;

      &.mc-icon-img {
        border-radius: 8px;
      }
    }
  }
}

@media (max-width: 1092px) and (min-width: 981px) {
  .card-box.virtual-card {
    .card-number,
    .expire,
    .holder {
      font-size: 17px !important;
    }

    .mc-icon {
      width: 40px;
      height: 40px;
    }
  }
}

@media (max-width: 1192px) and (min-width: 1093px) {
  .card-box.virtual-card {
    .card-number,
    .expire,
    .holder {
      font-size: 18px !important;
    }

    .mc-icon {
      width: 45px;
      height: 45px;
    }
  }
}

@media (min-width: 1200px) {
  #header > .container-xl {
    padding: 0 15px;
  }
}

.bootstrap-select > .dropdown-toggle {
  border: 1px solid #939393;
  height: 40px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &.bs-placeholder .filter-option-inner-inner {
    color: #aaa;
    font-weight: 300;
  }

  &:after {
    color: #495f80;
    margin-left: 6px;
  }
}

.title-select-box {
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
  justify-content: flex-end;
  margin-bottom: 5px;

  > {
    .title {
      flex: 1 0 200px;
      margin-bottom: 6px;
    }

    .bootstrap-select {
      width: auto !important;
      min-width: 90px;
      flex-grow: 1;
      margin-bottom: 10px;
    }
  }
}

.title-icon-box {
  display: flex;
  min-height: 42px;
  margin-bottom: 5px;

  > {
    .title {
      margin-right: 10px;
    }

    .btn-circle {
      margin-top: 4px;
      flex-basis: 28px;
      flex-shrink: 0;
    }

    .title > .sub-title {
      font-size: 1rem;
      white-space: nowrap;
      margin-top: 2px;
    }
  }
}

.title-select-box > .title > .sub-title {
  font-size: 1rem;
  white-space: nowrap;
  margin-top: 2px;
}

.progress {
  height: 0.7rem;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background-color: #ecf1f5;
}

.modal {
  overflow-y: auto;
}

.modal-content {
  border-radius: 1.6rem;
  border-color: transparent;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
}

.modal-header {
  padding: 1.8rem 2.5rem 0.5rem;
  border-bottom-color: transparent;

  &.text-center .modal-title {
    width: 100%;
    transform: translateX(20px);
    text-align: center;
    padding: 0 20px;

    &.no-close {
      transform: translateX(0);
    }
  }

  .close {
    font-size: 40px;
    font-weight: 100;
    color: $color_danger;
    margin-top: -5px;
    padding: 0 14px;
  }

  .btn {
    color: $color_primary;

    &:hover {
      background-color: #eee;
    }
  }
}

@media (max-width: 520px) {
  .modal-header {
    padding: 1.5rem 1.5rem 0.5rem !important;

    &.text-center .modal-title {
      padding: 0 10px;
      font-size: 1.4rem;
    }
  }

  .modal-body {
    padding: 0.5rem 1.5rem !important;
  }

  .modal-footer {
    padding: 0.5em 1.5rem 1.5rem !important;
  }
}

.modal-title {
  font-size: 1.6rem;
  color: $color_text;
}

.modal-body {
  padding: 0.5rem 2.5rem;
  color: $color_text;

  strong {
    font-weight: 500;
  }

  .italic {
    font-style: italic;
  }

  blockquote {
    border-left: 4px solid #ddd;
    padding-left: 10px;
  }
}

.modal-footer {
  padding: 0.5em 2.5rem 1.5rem;
  justify-content: center;
  border-top-color: transparent;

  > .btn {
    padding: 0.375rem 1.75rem;
  }
}

.btn-group-toggle {
  flex-wrap: wrap;

  .btn {
    border-radius: 2rem !important;
    padding: 8px 16px;

    &:hover {
      background: #eee;
    }

    &.active {
      background: #d8d8d8;
    }
  }
}

body .irs--round {
  .irs-line {
    top: 34px;
    height: 8px;
  }

  .irs-bar {
    background-color: $color_success;
    height: 8px;
    top: 34px;
  }

  .irs-handle {
    background-color: $color_primary;
    border-color: white;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);

    &.state_hover,
    &:hover {
      background-color: #009ee9;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.7);
    }
  }
}

.single_use_color {
  color: #CF514D !important;
}

.merchant_locked_color {
  color: #7B7B7B !important;
}

.unlocked_color {
  color: #379AD3 !important;
}

.card-box.virtual-card {
  &.single_use {
    background-color: #CF514D !important;
  }
  &.merchant_locked {
    background-color: #7B7B7B !important;
  }
  &.unlocked {
    background-color: #379AD3 !important;
  }
}

.swal-modal {
  width: 400px;
  max-width: 95%;
  box-shadow: 5px 5px 50px rgba(0,0,0,0.2);

  .swal-footer {
    text-align: center;
  }

  &.swal-left, &.swal-left-lg {
    .swal-content {
      text-align: left;
    }
    .swal-button-container {
      margin: 5px 10px;
    }
  }

  &.swal-left-lg {
    width: 530px;
    font-weight: 300;

    strong {
      font-weight: 500;
    }

    .italic {
      font-style: italic;
    }
  }

  .swal-button {
    background-color: #00b836;

    &.swal-button--cancel {
      background-color: #eee;
    }

    &.swal-button--warning {
      background-color: #ffc107;
    }

    &.swal-button--destructive {
      background-color: red;
    }
  }

  .swal-button:not([disabled]):hover {
    background-color: #218838;
  }

  .swal-button.swal-button--cancel:not([disabled]):hover {
    background-color: #ddd;
  }

  .swal-button.swal-button--warning:not([disabled]):hover {
    background-color: #ca9b00;
  }

  .swal-button.swal-button--destructive:not([disabled]):hover {
    background-color: #c30000;
  }
}

.swal-content {
  margin-top: 25px;
}

.swal-title {
  font-size: 20px;
}

.swal-text {
  line-height: 1.6em;
  text-align: center;
}

@media (max-width: 422px) {
  .swal-modal {
    width: calc(100% - 20px);
  }
}

.swal-toast {
  pointer-events: none;
  background: transparent;

  .swal-modal {
    min-width: 280px;
    width: auto;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.8);
    border: 1px solid $color_success;
    background: #f4faf4;
  }

  .swal-text {
    &:first-child {
      margin-top: 20px;
    }

    &:last-child {
      margin-bottom: 20px;
    }
  }
}

.tern-sm-dialog .modal-dialog {
  max-width: 500px;

  .modal-content {
    overflow: hidden;
  }

  .modal-header {
    padding: 0 !important;
    width: 498px;
    height: 230px;
    background-size: contain;
    background-color: transparent;
    background-repeat: no-repeat;

    .modal-title-x {
      color: white;
      width: 50%;
      margin: 30px 0 0 30px;

      > p {
        margin-top: 15px;
        font-weight: 300;
      }
    }

    .modal-title {
      color: white;
      font-size: 36px;
      line-height: 1.1em;
    }

    > img {
      position: absolute;
    }

    > .close {
      margin: 0.6rem;
      color: white;
    }
  }

  .modal-body {
    padding: 20px 35px;

    > h4 {
      font-weight: normal;
      font-size: 15px;
      margin-bottom: 0;
    }

    .form-group {
      margin-bottom: 0;
      width: 250px;
      max-width: 60%;

      .form-control {
        background-color: white !important;
        border-radius: 10px;
      }
    }

    .btn-green {
      margin-left: 15px;
    }
  }
}

.form-fieldset {
  border: 1px solid $color_primary;
  border-radius: 8px;
  padding: 4px;
  position: relative;
  max-width: 100%;
  width: 200px;
  margin: 0 auto 20px;

  label {
    position: absolute;
    font-size: 12px;
    color: $color_primary;
    top: -10px;
    left: 50%;
    background: white;
    transform: translateX(-50%) scale(0.9);
    margin: 0;
    white-space: nowrap;

    &:before,
    &:after {
      content: '';
      height: 1rem;
      width: 0.5rem;
      display: inline-block;
      vertical-align: text-bottom;
    }

    &.lg {
      font-size: 16px;
      top: -13px;
    }
  }

  .form-control {
    border-color: transparent;
    text-align: center;
    text-align-last: center;

    &:focus {
      outline: none;
      box-shadow: none;
    }

    &::placeholder {
      color: #aaa;
      opacity: 1;
      font-weight: 300;
    }

    &[readonly] {
      background: white;
    }
  }

  .bootstrap-select {
    .dropdown-toggle {
      background: transparent;
      border: none;
      box-shadow: none;
      padding-top: 0;
      padding-bottom: 0;
      height: 34px;
      outline: none !important;
    }

    .dropdown-item {
      text-align: left;
      text-align-last: left;
    }

    .bs-searchbox .form-control {
      border-color: #ccc;
      text-align: left;
      text-align-last: left;
    }
  }

  .input-group.input-group-number {
    .input-group-text {
      width: 20px;
      padding: 0;
      text-align: right;
      justify-content: flex-end;
      font-size: 22px;
    }

    input[type='number'] {
      font-size: 24px;
    }
  }

  .input-group-text {
    border-color: transparent;
    background: transparent;
  }
}

.form-help-block {
  text-align: center;
  color: #555;
  margin-top: -18px;
  font-size: 12px;
  transform: scale(0.9);
  font-weight: 300;
}

.list-style-none {
  list-style: none;
}

.bootstrap-select .dropdown-toggle .filter-option {
  display: flex;
  align-items: center;
  color: #333;
}

.bootstrap-select .dropdown-menu li a {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.mc-icon-computer {
  background-position: 0 0;
  background-size: 100%;
}
.mc-icon-entertainment {
  background-position: 0 14.285714%;
  background-size: 100%;
}
.mc-icon-finance {
  background-position: 0 28.571429%;
  background-size: 100%;
}
.mc-icon-food {
  background-position: 0 42.857143%;
  background-size: 100%;
}
.mc-icon-health {
  background-position: 0 57.142857%;
  background-size: 100%;
}
.mc-icon-house {
  background-position: 0 71.428571%;
  background-size: 100%;
}
.mc-icon-other {
  background-position: 0 85.714286%;
  background-size: 100%;
}
.mc-icon-transportation {
  background-position: 0 100%;
  background-size: 100%;
}

@media (max-width: 475px) {
  #header #notification_dropdown .dropdown-menu {
    right: -60px;
    .alert-body {
      width: 200px;
      .alert-message {
        min-width: 50%!important;
        margin-right: 0;
      }
    }
  }
  #header #notification_dropdown .user-alert-info+.dropdown-menu:before {
    right: 82px;
  }
}
