<template>
  <div class="card col"
       id="index-card-abstract">
    <div class="card-body">
      <div class="balance">
        <sup>$</sup>
        <span>{{ balance }}</span>
      </div>
      <div class="pending-balance"
           v-if="pendingBalance !== '$0.00'">
        <div class="text-shadow">{{ pendingBalance }}</div>
        <div class="tip">Pending Deposits</div>
        <a href="javascript:"
           class="btn-circle help"
           data-toggle="modal"
           data-target="#helpLegacyBalance">
          <i class="fa fa-question"></i>
        </a>
      </div>
      <div class="text-center">
        <a href="javascript:"
           v-if="user.cp.is_cashOnWeb && !user.cp.is_forwarding"
           @click="loadLocatorShow()"
           class="btn btn-success w-90 mx-1">LOAD</a>
        <a href="javascript:"
           v-else
           @click="$root.$emit('show-load-card-dialog')"
           class="btn btn-success w-90 mx-1">LOAD</a>
        <a href="javascript:"
           v-if="$store.getters['User/hasBalance'] && false"
           @click="$root.$emit('show-unload-card-dialog')"
           class="btn btn-danger w-90 mx-1">UNLOAD</a>
      </div>
      <div class="text-center pt-15">
        <a href="javascript:"
           @click="showLowBalanceDialog"
           class="font-12 underline">
          Set low balance notification
        </a>
        <div class="ff-box pl-20 pr-20"
             @click="showFfDialog">
          Your <span class="emoji">🇺🇸</span> Shipping Address
        </div>
      </div>
      <div class="kyc-item">
        Account Status:
        <span :class="getClassName()">
          {{ user.accountStatus }}
          <i class="fa fa-info-circle"
             v-if="(![true, 'GREEN', 'Active', 'Approved'].includes(user.sumsubApplicantStatus)) && user.sumsubApplicantReason"
             data-toggle="tooltip"
             :title="user.sumsubApplicantReason + '.(' + user.sumsubApplicantClientComment + ')'"></i>
        </span>
        <p>
          <span @click="paypalSubscribe" class="btn btn-success btn-sm" v-if="user.accountStatus == 'Subscription Cancelled' || user.accountStatus == 'Subscription Expired'">
            Re-Subscribe
          </span>
        </p>
      </div>
      <div class="title-icon-box mt-20">
        <div class="title text-shadow">
          <h4>
            <span><span class="emoji">⚡</span> One-Time Use Card</span>

            <a href="javascript:"
               class="silver ml-5 font-12"
               data-toggle="tooltip"
               data-placement="top"
               title="Sandbox card"
               v-if="oneTimeCard.sandbox">
              <i class="fas fa-code"></i>
            </a>
          </h4>
          <div class="sub-title">For secure one-time online payments</div>
        </div>
        <a href="javascript:"
           data-toggle="modal"
           data-target="#helpOneTimeCard"
           class="btn-circle help"><i class="fa fa-question"></i></a>
      </div>
      <div class="px-2 mt-10">
        <div class="card-box virtual-card single_use"
             v-show="oneTimeCard.showCard || oneTimeCard.adding"
             :class="user.cp.is_cashOnWeb ? 'cow-card': '' "
             :style="user.cp.is_cashOnWeb  ? {background: user.cp.cuscolor + '!important'} : ''">
          <div class="card-box-inner">
            <a href="javascript:"
               v-if="oneTimeCard.pan && oneTimeCard.pan !== '**** **** **** ****' && oneTimeCard.status !== 'active'"
               @click="toggleStatus(oneTimeCard)"
               class="btn-circle status"
               :class="'status-' + oneTimeCard.status">
              <i class="fa"
                 :class="oneTimeCard.status === 'active' ? 'fa-play' : 'fa-pause'"></i>
            </a>
            <a href="javascript:"
               class="btn-circle refresh"
               data-toggle="tooltip"
               :title="user.rainAccountId ? 'Refresh the card status': 'Refresh the card status and create a new card if this one has been used or declined because of unauthorized merchant.'"
               @click="init(true)">
              <i class="fa fa-sync"></i>
            </a>

            <div class="icon">
              <img :src="user.cp.oneCardArt">
            </div>
            <div v-if="!user.cp.is_cashOnWeb"
                 class="card-status"
                 :class="{red: oneTimeCard.status === 'inactive'}"
                 v-text="oneTimeCard.status === 'active' ? 'Active' : 'Paused'"></div>

            <div class="card-number touch-tip-less"
                 v-text="oneTimeCard.detail.pan || oneTimeCard.pan || '**** **** **** ****'"
                 data-toggle="tooltip"
                 title="Click to show/hide"
                 data-placement="top"
                 @click="useOneTimeCard"></div>
            <div class="expire touch-tip-less"
                 v-text="oneTimeCard.detail.exp || '**/**'"
                 data-toggle="tooltip"
                 title="Click to show/hide"
                 @click="useOneTimeCard"></div>
            <div class="holder touch-tip-less"
                 v-text="oneTimeCard.detail.cvv || '***'"
                 data-toggle="tooltip"
                 title="Click to show/hide"
                 @click="useOneTimeCard"></div>
            <div class="usu-card-icon"
                 data-toggle="tooltip"
                 :title="getCardTypeName(oneTimeCard)"
                 data-placement="top"><span class="emoji">⚡</span></div>

            <SpanTimer :float="true"
                       v-if="!oneTimeCard.hostedUI && oneTimeCard.hash === $store.state.Pin.card"></SpanTimer>
          </div>

          <CheckSmsPopup :card="oneTimeCard"
                         @view="viewCard(oneTimeCard, $event)"
                         @cancel="smsPopup[oneTimeCard.hash] = false; oneTimeCard.adding = false"
                         v-if="smsPopup[oneTimeCard.hash]"></CheckSmsPopup>
        </div>
        <div class="card-box add-card-box"
             v-show="!oneTimeCard.showCard && !oneTimeCard.adding"
             @click="useOneTimeCard">
          <div class="card-box-inner">
            <span>add</span>
            <div class="icon"><i class="fa fa-plus"></i></div>
            <span>card</span>
          </div>
        </div>
      </div>
      <Spends :data="spends"></Spends>
      <div class="my-4"></div>

      <Loading v-if="loading"></Loading>

      <SubscribePaypalDialog></SubscribePaypalDialog>
      <LoadCardDialog></LoadCardDialog>
      <UnloadCardDialog></UnloadCardDialog>
      <HelpOneTimeCard></HelpOneTimeCard>
      <KycDialog></KycDialog>
      <KycInstructionDialog></KycInstructionDialog>
      <LowBalanceDialog></LowBalanceDialog>
      <CoinflowDialog></CoinflowDialog>
      <LegalAgreementDialog></LegalAgreementDialog>
      <SumsubKycDialog></SumsubKycDialog>
      <HelpTutorial></HelpTutorial>
      <SystemMessageDialog></SystemMessageDialog>
    </div>

    <ReloadRow @reload="init"
               :loading="loading"></ReloadRow>
  </div>
</template>

<script>
  import Spends from './spends'
  import HelpTutorial from './help-tutorial'
  import HelpOneTimeCard from './help-one-time-card'
  import LoadCardDialog from './load-card-dialog'
  import UnloadCardDialog from './unload-card-dialog'
  import KycDialog from './../kyc/kyc-dialog'
  import KycInstructionDialog from './../kyc/kyc-instruction-dialog'
  import LowBalanceDialog from './low-balance-dialog'
  import CardMixin from '../../mixin/CardMixin'
  import { eventHandlerMixin } from '../../js/common'
  import SystemMessageDialog from './../common/system-message-dialog'
  import SubscribePaypalDialog from './../common/subscribe-paypal-dialog'
  import CoinflowDialog from './coinflow-dialog.vue'
  import SumsubKycDialog from './../kyc/sumsub-kyc-dialog'
  import LegalAgreementDialog from '../kyc/legal-agreement-dialog.vue'

  export default {
    components: {
      Spends,
      HelpTutorial,
      HelpOneTimeCard,
      LoadCardDialog,
      UnloadCardDialog,
      KycDialog,
      KycInstructionDialog,
      LowBalanceDialog,
      SystemMessageDialog,
      CoinflowDialog,
      SubscribePaypalDialog,
      LegalAgreementDialog,
      SumsubKycDialog
    },
    mixins: [
      CardMixin,
      eventHandlerMixin('reload-abstract', 'init'),
      eventHandlerMixin('use-one-time-card', 'useOneTimeCard')
    ],
    data () {
      return {
        balance: '0.00',
        pendingBalance: '$0.00',
        oneTimeCard: {
          detail: {}
        },
        spends: [],
        pinTarget: 'oneTimeCard',
        loading: false,
        initialized: false
      }
    },
    computed: {
      cards () {
        return [
          this.oneTimeCard
        ]
      },
      user () {
        return this.$store.state.User
      }
    },
    methods: {
      init (force = false, period = '') {
        this.loading = true

        $.get('/p/abstract', ts.queryParams({
          force,
          period
        }), resp => {
          this.loading = false
          if (ts.resp(resp)) {
            this.balance = resp.data.balance
            this.pendingBalance = resp.data.pendingBalance
            this.oneTimeCard = resp.data.oneTimeCard
            this.oneTimeCard.detail = {}
            this.oneTimeCard.adding = false
            this.spends = resp.data.spends

            this.$set(this.smsPopup, this.oneTimeCard.hash, false)

            this.$store.commit('User/update', {
              balance: resp.data.balance,
              detail: resp.data.user,
              cp: resp.data.cp,
              referAmount: resp.data.referAmount,
              setFf: resp.data.setFf,
              ffId: resp.data.ffId,
              KYC_notice: resp.data.KYC_notice,
              kycOnAll: resp.data.kycOnAll,
              twoFactorSecret: resp.data.twoFactorSecret,
              systemMessage: resp.data.systemMessage,
              rainAccountId: resp.data.rainAccountId,
              needRainKYC: resp.data.needRainKYC,
              rainKycUrl: resp.data.rainKycUrl,
              rainKycPending: resp.data.rainKycPending,
              applicationReason: resp.data.applicationReason,
              paypalSubscriptionFlag: resp.data.paypalSubscriptionFlag,
              legalsAccepted: resp.data.legalsAccepted,
              showMaintenanceInfo: resp.data.showMaintenanceInfo,
              subscriptionPlan: resp.data.subscriptionPlan,
              subscriptionAnnuallyPlan: resp.data.subscriptionAnnuallyPlan,
              subscriptionUpgradeAnnuallyPlan: resp.data.subscriptionUpgradeAnnuallyPlan,
              sumsubApplicantStatus: resp.data.sumsubApplicantStatus,
              sumsubApplicantReason: resp.data.sumsubApplicantReason,
              sumsubApplicantClientComment: resp.data.sumsubApplicantClientComment,
              accountStatus: resp.data.accountStatus
            })

            if (!this.initialized) {
              ts.initCallback = () => {
                const showOtherDialogs = () => {
                  if (!resp.data.paypalSubscriptionFlag) {
                    this.$root.$emit('show-help-tutorial', {
                      callback: () => {
                        this.$root.$emit('show-subscribe-paypal-dialog')
                      }
                    })
                  } else if (resp.data.rainRegError) {
                    ts.error(resp.data.rainRegError, () => {
                      if (resp.data.needRainKYC) {
                        ts.showKycStepDialog()
                      }
                    }, 'KYC error');
                  } else if (resp.data.needRainKYC) {
                    ts.showKycStepDialog()
                  }
                }

                if (resp.data.showMaintenanceInfo || resp.data.systemMessage) {
                  this.$root.$emit('show-system-message-dialog', {
                    callback: showOtherDialogs
                  })
                } else {
                  showOtherDialogs()
                }
              };
            }

            this.initialized = true

            this.$nextTick(() => {
              ts.installBootstrap()
              ts.invokeInitCallback('abstract');
            })
          }
        }).fail(() => (this.loading = false))
      },

      useOneTimeCard () {
        if (!this.$store.state.User.paypalSubscriptionFlag) {
          this.$root.$emit('show-subscribe-paypal-dialog')
          return
        }

        if (!this.oneTimeCard.detail.pan) {
          if (this.oneTimeCard.hash !== 'one_time_card') {
            this.oneTimeCard.adding = true
            this.toggleSmsPopup(this.oneTimeCard)
          } else {
            ts.confirm([
              '<span class="emoji">⚡</span> Create a One-Time Use Card',
              '<p class="text-center mt--15"><img src="/static/usu/img/one-time-card.jpg" /></p>' +
              '<p><strong>One-Time Use Only</strong></p>' +
              '<p>This card is valid for a single online purchase at a U.S. merchant and will automatically disable after one successful charge.</p>' +
              '<p><strong>Cost:</strong> $2 will be deducted from your available balance for each One-Time Use Card issued.</p>' +
              '<p>By continuing, you agree to this charge and understand that <strong>One-Time Use Cards may not support pre-authorization holds</strong> (some merchants may attempt a $0.00 verification. In those cases, use an <strong><span class="emoji">💸</span> Everyday Spending Card</strong> instead).</p>'
            ], () => {
              this.oneTimeCard.adding = true
              this.toggleSmsPopup(this.oneTimeCard)
            }, {
              buttons: ['<span class="emoji">❌</span> Close', '<span class="emoji">⚡</span> Yes, Create My One-Time Use Card for $2'],
              className: 'swal-left-lg',
              icon: ''
            })
          }
        } else {
          this.oneTimeCard.adding = true
          this.toggleSmsPopup(this.oneTimeCard)
        }
      },

      showFfDialog () {
        return this.$root.$emit('show-ff-dialog')
      },

      showLowBalanceDialog () {
        return this.$root.$emit('show-low-balance-dialog')
      },

      loadLocatorShow () {
        if (!this.$store.state.User.cp.is_locator) {
          window.open('https://' + this.$store.state.User.cp.locatorUrl)
        } else {
          this.$store.commit('User/updateLoadLocator',true)
        }
      },
      getClassName() {
        if (this.$store.state.User.accountStatus === 'Active') {
          return 'approved'
        } else if (this.$store.state.User.accountStatus === 'Pending') {
          return 'pending'
        } else {
          return 'reject'
        }
      },
      paypalSubscribe () {
        return this.$root.$emit('show-subscribe-paypal-dialog')
      }
    },
    mounted () {
      this.init()
    }
  }
</script>

<style lang="scss">
@import "../../css/variable";

#index-card-abstract {
  .card-body > .balance {
    text-align: center;
    font-size: 34px;
    line-height: 1em;
    margin-bottom: 7px;
    margin-left: -16px;

    sup {
      font-size: 19px;
      margin-right: -5px;
    }
  }

  .card-body > .pending-balance {
    color: $color_danger;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;

    .text-shadow {
      font-size: 18px;
    }

    .tip {
      flex-basis: 60px;
      font-size: 12px;
      line-height: 1.2em;
      text-transform: uppercase;
      font-weight: 300;
      transform: scale(0.75);
    }

    .btn-circle {
      background: $color_danger;
    }
  }
  .card-box.cash-card {
  }
  .kyc-item {
    text-align: center;
    font-weight: bold;
    margin-top: 12px;
    .approved {
      color: green;
    }
    .reject {
      color: red;
    }
    .pending {
      color: #007bff;
    }
  }
}
</style>
