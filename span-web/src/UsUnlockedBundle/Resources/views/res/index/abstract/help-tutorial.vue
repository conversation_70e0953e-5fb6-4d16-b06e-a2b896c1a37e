<template>
  <div class="modal fade"
       id="helpTutorial"
       tabindex="-1"
       role="dialog">
    <div class="modal-dialog modal-dialog-centered"
         role="document">
      <div v-if="$store.state.User.cp.is_cashOnWeb && $store.state.User.KYC_notice"
           class="modal-content">
        <div class="modal-header text-center">
          <h5 class="modal-title font-weight-light"
              v-text="headerTitle"></h5>
          <button type="button"
                  class="close"
                  data-dismiss="modal"
                  aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body cow-body">
          <div class="cow-step-title"
               v-text="title"></div>
          <div class="row light"
               v-show="step === 0">
            <div class="step-card">
              <div class="card-box virtual-card cow-card"
                   :style="{background: $store.state.User.cp.cuscolor + '!important'}">
                <div class="card-box-inner">
                  <div class="icon">
                    <img :src="$store.state.User.cp.oneCardArt">
                  </div>
                  <div class="card-number touch-tip-less"
                       v-text="'**** **** **** ****'"></div>
                  <div class="expire touch-tip-less"
                       v-text="'**/**'"></div>
                  <div class="holder touch-tip-less"
                       v-text="'***'"></div>

                </div>
              </div>
            </div>
          </div>
          <!-- <div class="row light"
               v-show="step === 1">
            <div class="load-step">
              Balance
              <p>$1000</p>
              <p class="btn-item">Load Account</p>
            </div>
          </div> -->
          <div class="row light"
               v-show="step === 1">
            <div class="col text-center">
              <img v-if="$store.state.User.cp.stepOne"
                   :src="$store.state.User.cp.stepOne"
                   alt="">
              <img v-else
                   :src="'/static/usu/img/brand-matrix.svg'"
                   alt="">
            </div>
          </div>
          <div class="row light"
               v-show="step === 2">
            <div class="text-center billing-item">
              <div class="ff-box">
                <img :src="'/static/usu/img/ff-box.png'"
                     alt="">
                Billing/Shipping Information
              </div>
            </div>
          </div>
          <div class="row light"
               v-show="step === 3">
            <div class="col text-center">
              <img v-if="$store.state.User.cp.stepThree"
                   :src="$store.state.User.cp.stepThree"
                   alt="">
              <img v-else
                   :src="'/static/usu/img/brand-matrix.svg'"
                   alt="">
            </div>
          </div>
          <div class="row">
            <button class="btn load-btn"
                    v-if="step == 3"
                    @click="loadLocatorShow()"
                    type="button">
              Load My Account
            </button>
            <button v-if="step < 3"
                    class="btn next-btn"
                    type="button"
                    v-text="next"
                    @click="nextStep()">
            </button>
            <button class="btn back-btn"
                    v-if="step == 2 "
                    type="button"
                    @click="backStep()">
              Back
            </button>
            <button class="btn back-btn"
                    v-if="step == 3 "
                    type="button"
                    @click="backStep()">
              Close
            </button>
          </div>
        </div>
      </div>
      <div v-else-if="$store.state.User.cp.is_cashOnWeb && !$store.state.User.KYC_notice"
           class="modal-content">
        <div class="modal-header text-center">
          <h5 class="modal-title font-weight-light">Notice</h5>
          <button type="button"
                  class="close"
                  data-dismiss="modal"
                  aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body cow-body">
          <div class="cow-step-title">In order to have higher load limits and and increase your spending power online, please proceed with ID verification. You can load up to $500 at an agent location without completing this step.</div>
          <p class="text-center"><input type="checkbox"
                   name="ignore"
                   v-model="ignore" /> Permanently turn off this prompt</p>
          <div class="row">
            <button class="btn load-btn"
                    @click="startScan()"
                    type="button">
              Start ID verification
            </button>
            <button class="btn back-btn"
                    type="button"
                    @click="latterScan()">
              I will do ID verification later
            </button>
          </div>
        </div>
      </div>
      <div v-else
           class="modal-content">
        <div class="modal-header text-center">
          <h5 class="modal-title font-weight-light">Getting started with US Unlocked</h5>
          <button type="button"
                  class="close"
                  data-dismiss="modal"
                  aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="step-title"
               v-text="title"></div>
          <div class="row light"
               v-show="step === 0">
            <div class="col d-flex align-items-center">
              <img :src="'/static/usu/img/paypal.png'"
                   alt="">
            </div>
            <div class="col">
              <p>We use a PayPal subscription to collect the monthly maintenance fee. Your subscription must remain active to continue using your US Unlocked cards.</p>
              <p>You can also choose an <strong>annual subscription</strong> and save over 30%.</p>
            </div>
          </div>
          <div class="row light"
               v-show="step === 1">
            <div class="col d-flex align-items-center">
              <img :src="'/static/usu/img/kyc.jpg'"
                   style="height: 250px"
                   alt="">
            </div>
            <div class="col">
              <p>To use US Unlocked, you must successfully complete identity verification with our compliance partner. This is a strict regulatory requirement - we can only support verified users who reside outside the United States.</p>
              <p>If your last attempt was unsuccessful, no worries - you can retry verification at any time.</p>
            </div>
          </div>
          <div class="row light"
               v-show="step === 2">
            <div class="col d-flex align-items-center">
              <img :src="'/static/usu/img/abstract_side2.jpg'"
                   style="height: 250px"
                   alt="">
            </div>
            <div class="col d-flex align-items-center">
              <p>To issue your US Unlocked virtual payment cards, you will need to fund your account using one of our supported payment methods.</p>
            </div>
          </div>
          <div class="row light"
               v-show="step === 3">
            <div class="col d-flex align-items-center">
              <img :src="'/static/usu/img/shipping2.jpg'"
                   alt="">
            </div>
            <div class="col">
              <p>Once your first account load clears, you can set up a <span class="emoji">🇺🇸</span> shipping address if you plan to ship your purchases internationally.</p>
              <p>We partner with trusted freight forwarders to provide this service. The address they assign will be used at checkout when shopping online.</p>
            </div>
          </div>
          <div class="row light"
               v-show="step === 4">
            <div class="col d-flex align-items-center">
              <img :src="'/static/usu/img/abstract_side_loaded2.jpg'"
                   alt="">
            </div>
            <div class="col">
              <p>You are all set to shop online. Just click on any virtual payment card and enter your SMS/OTP code to view the card details.</p>
              <p>We offer three types of cards to suit different needs:</p>
              <ul style="list-style: none; padding-left: 0;">
                <li><strong><span class="emoji">⚡</span> One-Time Use Card</strong> – Ideal for single purchases</li>
                <li><strong><span class="emoji">🔒</span> Store Locked Card</strong> – Locks to one merchant for added security</li>
                <li><strong><span class="emoji">🇺🇸</span> US Unlocked Card</strong> – No merchant or usage limits</li>
              </ul>
              <p>If you have any questions, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            </div>
          </div>

          <Pagination :pagination="{data: [], count: steps.length}"
                      :pageSize="1"
                      class="mt-30 mb-10"
                      @reload="step = $event - 1"
                      :page="step + 1"></Pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import DialogMixin from '../../mixin/DialogMixin'
  import { eventHandlerMixin } from '../../js/common'

  export default {
    name: 'help-tutorial',
    mixins: [
      DialogMixin,
      eventHandlerMixin('show-help-tutorial', 'show')
    ],
    data () {
      return {
        step: 0,
        ignore: false,
        callback: null,
      }
    },
    methods: {
      show (args) {
        if (args && args.callback && (typeof args.callback === 'function')) {
          this.callback = args.callback
        } else {
          this.callback = null
        }
        this.step = 0
        $(this.$el).modal('show')
      },
      nextStep () {
        if (this.step < 3) {
          this.step += 1
        }
      },
      backStep () {
        if (this.step == 3) {
           this.hide()
        }
        if (this.step > 1) {
          this.step -= 1
        }
      },
      loadLocatorShow () {
        if (!this.$store.state.User.cp.is_locator) {
          window.open('https://' + this.$store.state.User.cp.locatorUrl)
        } else {
          this.$store.commit('User/updateLoadLocator',true)
        }
      },
      startScan () {
        this.hide()
        this.$root.$emit('show-kyc-dialog')
      },
      latterScan () {
        if (this.ignore) {
          ts.loading()
          $.post('/p/ignoreNotice', {
            ignore: true
          }, resp => {
            if (ts.resp(resp)) {
              this.$store.commit('User/updateKYCNotice',true)
            }
          })
        } else {
          this.$store.commit('User/updateKYCNotice',true)
        }
      }
    },
    computed: {
      steps () {
        if (this.$store.state.User.cp.is_cashOnWeb) {
          return [
            'Getting started with your Cash on Web account is easy just follow the following steps!',
            'Use our Cash Load locator to find the nearest load partner to make a deposit on your account.',
            'Once your deposit is completed, please fill out your address by clicking the billing/shipping information button on the left side of your dashboard so you can start receiving your online orders.',
            'Once you have completed these steps, you’re ready to shop online freely using your Cash on Web Virtual Payment Cards.'
          ]
        }
        return [
          'STEP 1: Choose a Subscription Plan',
          'STEP 2: Complete Identity Verification',
          'STEP 3: Load Your US Unlocked Account',
          'STEP 4: Connect a Free Freight Forwarder (Optional)',
          'STEP 5: Time to Start Shopping!'
        ]
      },
      title () {
        return this.steps[this.step]
      },
      next () {
        if (this.step === 0) {
          return 'Get Started'
        }
        return 'Next'
      },
      headerTitle () {
        return [
            'Getting started',
            'Step 1',
            'Step 2',
            'You Are Ready to Shop!'
           ][this.step]
      }
    },
    mounted () {
      $('#helpTutorial').on('hidden.bs.modal', () => {
        this.step = 0
        if (typeof this.callback === 'function') {
          this.callback()
        }
      })
    }
  }
</script>

<style lang="scss">
#helpTutorial .modal-dialog {
  max-width: 540px;

  .step-title {
    font-weight: 500;
    font-size: 15px;
    margin: 0 0 25px;
    text-align: center;
  }
  .cow-step-title {
    font-size: 16px;
    color: rgba($color: #000000, $alpha: 0.5);
    line-height: 1.88;
    text-align: center;
    margin-bottom: 20px;
  }
  .btn {
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    line-height: 50px;
    color: #ffffff;
    padding: 0;
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 320px;
  }
  .load-btn {
    margin-top: 20px;
    background: #7ac142;
  }
  .next-btn {
    background: #9c8bff;
    margin-top: 20px;
  }
  .back-btn {
    background: #b5b5be;
  }
  .step-card {
    width: 325px;
    // height: 205px;
    margin: 0 auto;
    .card-box {
      margin-bottom: 0;
    }
  }
  .load-step {
    width: 410px;
    border-radius: 20px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
    background-color: #ffffff;
    color: #1c2d4c;
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    padding-top: 24px;
    padding-bottom: 28px;
    margin: 0 auto;
    margin-top: 34px;
    margin-bottom: 30px;
    p {
      margin: 0;
      font-size: 40px;
      font-weight: 900;
      color: #1c2d4c;
    }
    .btn-item {
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      border-radius: 29px;
      background-color: #01da33;
      width: 360px;
      margin: 0 auto;
      line-height: 50px;
      margin-top: 19px;
    }
  }
  .billing-item {
    margin: 0 auto;
  }
  .cow-body {
    padding-bottom: 20px;
  }
}
</style>
