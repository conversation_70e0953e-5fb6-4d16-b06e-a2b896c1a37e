<template>
  <div class="row"
       id="index-cards"
       v-if="!$store.state.User.loadLocator">
    <abstract v-show="!isMobile || menuIndex === 0"></abstract>
    <cards v-show="!isMobile || menuIndex === 1"></cards>
    <history v-show="!isMobile || menuIndex === 2"></history>
    <div class="menu-footer"
         v-show="isMobile">
      <div class="menu-item"
           :class="{ active: menuIndex === 0 }"
           @click="menuIndex = 0">
        <span class="emoji">🏠</span>
      </div>
      <div class="menu-item"
           :class="{ active: menuIndex === 1 }"
           @click="menuIndex = 1">
        <span class="emoji">💸</span>
      </div>
      <div class="menu-item"
           :class="{ active: menuIndex === 2 }"
           @click="menuIndex = 2">
        <span class="emoji"> 📒</span>
      </div>
    </div>
    <HelpOneTimeCard></HelpOneTimeCard>
    <SubscribePaypalDialog></SubscribePaypalDialog>
    <HelpTutorial></HelpTutorial>
    <SumsubKycDialog></SumsubKycDialog>
    <HelpLegacyBalance></HelpLegacyBalance>
    <CardDetailPopup></CardDetailPopup>
    <SetupTwoFactorPopup></SetupTwoFactorPopup>
    <VerifyOwnerPopup></VerifyOwnerPopup>
    <InviteFriendDialog></InviteFriendDialog>
    <ChangeSubscriptionDialog></ChangeSubscriptionDialog>
    <FfDialog></FfDialog>
    <SwitchPaypalDialog></SwitchPaypalDialog>
    <HistoryDetailPopup></HistoryDetailPopup>
    <LegalAgreementDialog></LegalAgreementDialog>
  </div>
  <div v-else>
    <loadLocator></loadLocator>
  </div>
</template>

<script>
  import abstract from './abstract'
  import cards from './cards'
  import history from './history'
  import FfDialog from './abstract/ff-dialog.vue'
  import HelpLegacyBalance from './common/help-legacy-balance'
  import CardDetailPopup from './common/card-detail-popup'
  import SetupTwoFactorPopup from './common/setup-two-factor-popup'
  import VerifyOwnerPopup from './common/verify-owner-popup'
  import HistoryDetailPopup from './history/history-detail-popup'
  import InviteFriendDialog from './common/invite-friend-dialog'
  import SwitchPaypalDialog from './common/switch-paypal-dialog'
  import SumsubKycDialog from './kyc/sumsub-kyc-dialog'
  import HelpTutorial from './abstract/help-tutorial'
  import SubscribePaypalDialog from './common/subscribe-paypal-dialog'
  import HelpOneTimeCard from './abstract/help-one-time-card'
  import LegalAgreementDialog from './kyc/legal-agreement-dialog.vue'

  import loadLocator from './loadLocator'
  import ChangeSubscriptionDialog from './common/change-subscription-dialog'

  export default {
    components: {
      FfDialog,
      InviteFriendDialog,
      abstract,
      cards,
      history,
      HelpLegacyBalance,
      CardDetailPopup,
      loadLocator,
      SetupTwoFactorPopup,
      VerifyOwnerPopup,
      HistoryDetailPopup,
      ChangeSubscriptionDialog,
      SwitchPaypalDialog,
      SumsubKycDialog,
      HelpTutorial,
      SubscribePaypalDialog,
      HelpOneTimeCard,
      LegalAgreementDialog
    },
    mounted () {
      this.$nextTick(() => {
        setTimeout(() => {
          const u = new URL(location.href)
          const evt = u.searchParams.get('evt');
          if (evt) {
            this.$root.$emit(evt)
          }
        }, 1000)
      })
      this.handleResize()
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy () {
      window.removeEventListener('resize', this.handleResize)
    },
    data () {
      return {
        menuIndex: 0,
        isMobile: false
      }
    },
    methods: {
      handleResize () {
        this.isMobile = window.innerWidth <= 475
      }
    }
  }
</script>

<style lang="scss">
#index-cards {
  height: 100%;
  min-width: 900px;

  > .col.card {
    background: white;
    border-radius: 10px;
    padding: 0;
    margin: 0 7px;
    border-color: #e7e8f3;
    height: 100%;
    overflow: auto;

    .card-body {
      height: 100%;
      overflow: auto;
      padding: 20px 24px 0;
    }

    .title > {
      h4 {
        font-size: 1.3rem;
        margin-bottom: 0;
        color: #0a2251;
      }

      .sub-title {
        color: #888;
        font-weight: 300;
      }
    }
  }
  .menu-footer {
    display: none;
  }
}

#index-card-abstract {
  margin-left: 14px !important;
}

#index-card-history {
  margin-right: 14px !important;
}

#index-card-cards,
#index-card-history {
  > .card-body {
    display: flex;
    flex-direction: column;
  }
}

@media (max-width: 1034px) {
  #index-cards {
    padding: 0 4px;

    > .col.card {
      margin: 0 5px;

      .card-body {
        padding: 15px 15px 0;
      }
    }
  }

  #index-card-abstract {
    margin-left: 8px !important;
  }

  #index-card-history {
    margin-right: 8px !important;
  }
}
@media (max-width: 475px) {
  #index-cards {
    flex-flow: column;
    min-width: 90%;
    margin: 0 auto;
    padding-bottom: 50px;
    > .col.card {
      margin: 0;
    }
    .menu-footer {
      position: fixed;
      bottom: 10px;
      width: 90%;
      height: 40px;
      border-radius: 15px;
      padding: 0 10%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: #ffff;
      color: #000;
      font-size: 24px;
      margin-top: 10px;
      .menu-item {
        text-align: center;
        margin: 0 5px;
        opacity: 0.5;
      }
      .active {
        color: #00b836;
        opacity: 1;
      }
    }
  }
  #index-card-abstract {
    margin: 0 !important;
  }
}
</style>
