<template>
  <div class="card col"
       id="index-card-history">
    <div class="card-body">
      <div class="title-select-box">
        <div class="title text-shadow">
          <h4>Transaction History</h4>
          <div class="sub-title">Activity includes all cards</div>
        </div>
        <select class="bs-select-picker"
                v-model="status">
          <option value="approvals"
                  selected>Approved</option>
          <option value="declines">Declined</option>
        </select>
      </div>
      <div class="card-body-container">
        <div class="history-item-box"
             v-for="(s, i) in items"
             :key="i"
             :data-id="s.id"
             :data-token="s.token"
             :class="[s.type || '', i % 2 ? 'even' : '']"
             @click="showItem(s)">
          <div class="icon"
               :class="iconClass(s)"
               :style="iconStyle(s)">
            <img v-if="s.icon"
                 :src="`${s.icon}`"
                 alt="">
            <div class="mc-icon"
                 v-else-if="s.iconClass">
              <img :src="mcImg(s)"
                   alt="">
            </div>
            <i class="fa fa-exclamation-triangle"
               v-else-if="s.type === 'alert'"></i>
          </div>
          <div class="body">
            <div class="title">{{ s.title }}</div>
            <div class="sub-title"
                 :class="s.subClass">
              {{ s.subtitle }}

              <a href="javascript:"
                 class="text-muted"
                 data-toggle="tooltip"
                 data-placement="top"
                 v-if="s.subTip"
                 :title="s.subTip">
                <i class="far fa-question-circle"></i>
              </a>

              <a href="javascript:"
                 class="text-danger ml-5"
                 v-if="getLoadErrorTip(s)"
                 data-toggle="tooltip"
                 data-placement="top"
                 @click="showLoadErrorTip(s)"
                 :title="getLoadErrorTip(s)">
                <i class="far fa-question-circle"></i>
              </a>
            </div>
            <div class="time"
                 v-if="s.timestamp">{{ ts.time(s.timestamp) }}</div>
          </div>
          <div class="right">
            <div class="amount"
                 :class="amountClass(s)">{{ s.amount }}</div>
            <div class="balance"
                 :class="balanceClass(s)"
                 v-if="s.balance">{{ s.balance }}</div>
          </div>
          <i class="fa fa-chevron-right"
             v-if="s.type === 'transaction' || s.type === 'decline' || s.action && s.type !== 'alert'"></i>
        </div>

        <div class="font-12 text-danger"
             v-if="items.length <= 0">
          There is no activity to show at this time.
        </div>
        <div class="font-12 text-center mv-10"
             v-else>
          <a href="javascript:"
             @click="init('more')"
             v-if="items.length < total">
            <i class="fa fa-chevron-down mr-3"></i>
            Load more
          </a>
          <div class="text-faded"
               v-else>No more data</div>
        </div>

        <div class="my-3"></div>
      </div>

      <Loading v-if="loading"></Loading>
    </div>

    <ReloadRow @reload="init('force')"
               :left="true"
               :loading="loading"></ReloadRow>
    <TransactionDetail></TransactionDetail>
  </div>
</template>

<script>
  import { eventHandlerMixin } from '../../js/common'
  import TransactionDetail from './transaction-detail'

  export default {
    components: {
      TransactionDetail
    },
    mixins: [
      eventHandlerMixin('reload-transactions', 'init')
    ],
    data () {
      return {
        status: 'approvals',
        items: [],
        total: 0,
        page: 1,
        loading: false
      }
    },
    watch: {
      status () {
        this.init()
      }
    },
    methods: {
      init (mode) {
        if (mode !== 'more') {
          this.page = 1
        } else {
          this.page++
        }
        this.loading = true
        $.get('/p/history', ts.queryParams({
          status: this.status,
          mode: mode,
          page: this.page
        }), resp => {
          this.loading = false
          if (ts.resp(resp)) {
            if (mode === 'more') {
              this.items = this.items.concat(resp.data.items)
            } else {
              this.items = resp.data.items
            }
            this.total = resp.data.total

            this.$nextTick(() => {
              ts.installBootstrap()
              ts.invokeInitCallback('history');
            })
          }
        }).fail(() => {
          this.loading = false
        })
      },
      showItem (item) {
        if (item.type === 'fee' || item.type === 'transaction' || item.type === 'decline') {
          if (item.token) {
            this.$root.$emit('show-history-detail-dialog', item)
          }
        } else {
          if (!item.action) {
            return
          }
          if (item.action.modal) {
            return $(item.action.modal).modal('show')
          }
          if (item.action.event) {
            return this.$root.$emit(item.action.event, item)
          }
          ts.loading()
          $.get('/p/history/detail', ts.queryParams({
            type: item.type,
            id: item.id
          }), resp => {
            if (ts.resp(resp)) {
            }
          })
        }
      },
      iconClass (s) {
        const cls = []
        if (s.icon || s.iconClass) {
          cls.push('img')
        }
        if (s.iconBgColor) {
          cls.push('self-shadow')
        }
        if (s.iconBoxClass) {
          cls.push(s.iconBoxClass)
        }
        if (s.icon && (s.icon.indexOf('.svg') != -1)) {
          cls.push('svg-item')
        }
        return cls.join(' ')
      },
      iconStyle (s) {
        const style = {}
        if (s.iconBgColor) {
          style.backgroundColor = s.iconBgColor
        }
        if (s.icon && s.icon.startsWith('https://storage.googleapis.com/heron-merchant-assets/')) {
          delete style.backgroundColor
        }
        return style
      },
      mcImg (s) {
        return `/static/usu/img/category3d/${s.iconClass}.jpg`
      },
      amountClass (s) {
        if (!s.amountValue) {
          return 'lightgray'
        }
        if (s.amountValue > 0) {
          return 'text-success'
        }
        if (s.amountValue < 0) {
          return 'text-danger'
        }
      },
      balanceClass (s) {
        if (!s.amountValue) {
          return 'lightgray'
        }
        return ''
      },
      showHistoryDetailPopup () {
        this.$root.$emit('show-history-detail-dialog')
      },
      getLoadErrorTip (s) {
        if (s.subTip) {
          return s.subTip
        }
        if (s.title === 'Credit Card Load' && ['Error', 'Failed'].includes(s.subtitle)) {
          return 'Payment was declined?'
        }
        return null
      },
      showLoadErrorTip (s) {
        if (s.title === 'Credit Card Load') {
          ts.showCardLoadTipDialog()
        }
      }
    },
    mounted () {
      this.init()
    }
  }
</script>

<style lang="scss">
@import "../../css/variable";

#index-card-history {
  .card-body > .card-body-container {
    padding: 0 20px;
  }

  .history-item-box {
    display: flex;
    padding: 0.5rem 0.75rem;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;
    transition: background 0.3s;

    &.even {
      background: #f9f9f9;
    }

    &:hover {
      background: #eee;
    }

    &.alert {
      background: #fffdf9;
      border: 1px solid #ffb6b8;
      text-transform: uppercase;

      &:hover {
        background: rgba(255, 214, 216, 0.25);
        border-color: #f3bec0;
      }

      > .icon {
        border-color: $color_danger;
        color: $color_danger;
      }

      .sub-title {
        color: #f9434f;
        font-weight: 400;
      }

      .right .amount {
        color: #ff9a9d;
        font-size: 22px;
      }
    }

    > .icon {
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      border-radius: 12px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
      color: silver;
      background-color: white;
      overflow: hidden;

      &.load_method {
        img {
          max-width: 115%;
          width: 115%;
        }
      }

      .mc-icon {
        width: 100%;
        height: 100%;
      }
    }

    > .fa-chevron-right {
      margin-left: 7px;
      margin-right: -7px;
      color: #bbb;
    }

    .title {
      color: $color_text;
      font-weight: 500;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      max-height: 42px;
      word-break: break-word;
    }

    .sub-title {
      font-size: 13px;
      font-weight: 300;
    }

    .time {
      font-size: xx-small;
      font-style: italic;
      font-weight: 100;
      color: #999;
      margin-top: 2px;
    }

    .right {
      margin-left: auto;

      .amount {
        font-size: 16px;
      }

      .balance {
        margin-top: 6px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 1024px) {
  #index-card-history {
    .card-body > .card-body-container {
      padding: 5px 12px 0;
    }
  }
}

@media (max-width: 965px) {
  #index-card-history {
    .history-item-box {
      padding: 0.5rem 0.7rem;

      .sub-title {
        font-size: 12px;
      }
    }
  }
}
</style>
