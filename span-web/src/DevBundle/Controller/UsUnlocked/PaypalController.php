<?php


namespace DevB<PERSON>le\Controller\UsUnlocked;


use Carbon\Carbon;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use DevBundle\Controller\BaseController;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\PayPal\PayPalAPI;
use UsUnlockedBundle\Services\UserService;

class PaypalController extends BaseController
{
    /**
     * @Route("/dev/us/paypal/view-user/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function viewUser(user $user)
    {
        $data = [];
        if (Util::meta($user, 'paypalSubscriptionID')) {
          $api = PayPalAPI::get();
          /** @var ExternalInvoke $ei */
          [$ei, $data] = $api->getSubscriptDetail(Util::meta($user, 'paypalSubscriptionID'), false);
        }
        return new SuccessResponse([
            'data' => $data
        ]);
    }

    /**
     * @Route("/dev/us/paypal/view-user/{user}/transactions")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function viewUserTransactions(user $user)
    {
        $data = [];
        $paypalSubscriptionId = UserService::getPayPalSubscriptionId($user);
        if ($paypalSubscriptionId) {
          $api = PayPalAPI::get();
          $params = [
            'start_time' => Carbon::now()->subYears(2)->toIso8601String(),
            'end_time' => Carbon::now()->toIso8601String()
          ];
          /** @var ExternalInvoke $ei */
          [$ei, $data] = $api->getSubscriptTransactions($paypalSubscriptionId, $params, true);
        }
        return new SuccessResponse([
            'data' => $data
        ]);
    }

    /**
     * @Route("/dev/us/paypal/webhooks")
     *
     * @return SuccessResponse
     */
    public function getWebhook()
    {
        $api = PayPalAPI::get();
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->getWebHook();
        
        return new SuccessResponse($data);
    }

     /**
     * @Route("/dev/us/paypal/{id}/delWebhook")
     *
     * @return SuccessResponse
     */
    public function delWebhook(String $id)
    {
        $api = PayPalAPI::get();
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->delWebHook($id, true);
        
        return new SuccessResponse($data);
    }

     /**
     * @Route("/dev/us/paypal/createWebhook")
     *
     * @return SuccessResponse
     */
    public function createWebhook()
    {
        $api = PayPalAPI::get();
        $params =[
                    "url" => Util::isLive() ? "https://www.virtualcards.us/webhook/paypal" : "https://staging.virtualcards.us/webhook/paypal",
                    "event_types" => [
                        [
                            "name"=> 'BILLING.SUBSCRIPTION.CREATED'
                        ], 
                        [
                            "name"=> 'BILLING.SUBSCRIPTION.CANCELLED'
                        ],
                        [
                            "name"=> 'BILLING.SUBSCRIPTION.ACTIVATED'
                        ],
                        [
                            "name"=> 'BILLING.SUBSCRIPTION.SUSPENDED'
                        ],
                        [
                            "name"=> 'BILLING.SUBSCRIPTION.PAYMENT.FAILED'
                        ]
                    ]
                ];
        [$ei, $data] = $api->createWebHook($params, true);

        return new SuccessResponse([
            'data' => $data
        ]);
    }


     /**
     * @Route("/dev/us/paypal/updateWebhook")
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function updateWebhook(Request $request)
    {
        $api = PayPalAPI::get();
        $id = $request->get('id');
        if (!$id) {
            return new FailedResponse('Invalid id!');
        }

        $params = [
            [
                "op" => "replace",
                "path" => "/event_types",
                "value" => [
                    ["name" => "BILLING.SUBSCRIPTION.CREATED"],
                    ["name" => "BILLING.SUBSCRIPTION.CANCELLED"],
                    ["name" => "BILLING.SUBSCRIPTION.ACTIVATED"],
                    ["name" => "BILLING.SUBSCRIPTION.SUSPENDED"],
                    ["name" => "BILLING.SUBSCRIPTION.PAYMENT.FAILED"],
                    ["name" => "PAYMENT.SALE.COMPLETED"]
                ]
            ]
        ];

        $newUrl = $request->get('url');
        if ($newUrl) {
            $params[] = [
                "op" => "replace",
                "path" => "/url",
                "value" => $newUrl
            ];
        }

        Log::debug('PayPal webhook update params:', $params);
        /** @var ExternalInvoke $ei */
        [$ei, $data] = $api->updateWebHook($id, $params, true);

        if (!$data) {
            return new FailedResponse('Failed to update webhook. Check logs for details.');
        }

        return new SuccessResponse([
            'data' => $data,
            'external_invoke' => $ei ? $ei->getId() : null
        ]);
    }
}
