<?php


namespace DevB<PERSON>le\Controller\UsUnlocked;


use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Util;
use DevB<PERSON>le\Controller\BaseController;
use SalexUserBundle\Entity\User;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Sumsub\SumsubAPI;
use UsUnlockedBundle\Services\Sumsub\SumsubService;
use UsUnlockedBundle\Services\UserRefererService;

class SumsubController extends BaseController
{
    /**
     * @Route("/dev/us/sumsub/get-access-token/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function getAccessToken(User $user)
    {
        return new SuccessResponse([
            'data' => SumsubService::getAccessTokens($user)
        ]);
    }

    /**
     * @Route("/dev/us/sumsub/get-share-token/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function getShareToken(User $user)
    {
        return new SuccessResponse([
            'data' => SumsubService::getShareToken($user)
        ]);
    }

    /**
     * @Route("/dev/us/sumsub/meta/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function getSumsubApplicantMeta(User $user)
    {
        $meta = Util::meta($user);
        $result = array_filter($meta, function ($key) {
            return str_starts_with($key, 'sumsub');
        }, ARRAY_FILTER_USE_KEY);
        return new SuccessResponse($result);
    }

    /**
     * @Route("/dev/us/sumsub/reward/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function reward(User $user)
    {
        UserRefererService::reward($user);
        return new SuccessResponse();
    }

    /**
     * @Route("/dev/us/sumsub/get-applicant-data/{user}")
     * @param User $user
     *
     * @return SuccessResponse
     */
    public function getApplicantData(User $user)
    {
        $api = SumsubAPI::get();
        return new SuccessResponse([
            'data' => $api->getApplicantData($user->getId()),
        ]);
    }
}
