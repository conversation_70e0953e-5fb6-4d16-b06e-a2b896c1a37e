<?php

namespace DevBundle\Command;

use CoreBundle\Command\BaseHostedCommand;
use CoreBundle\Utils\Util;
use DevBundle\Services\SlackService;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Yaml\Yaml;

class ScanCodeCommand extends BaseHostedCommand
{
    protected ?bool $alertForDuplicated = false;

    protected function configure()
    {
        $this
            ->setName('span:dev:scan-code')
            ->setDescription('Do code static scan and report errors in Slack')
            ->addOption('revision', null, InputOption::VALUE_REQUIRED, 'The revision to save as the file name instead of random number')
            ->addOption('test', null, InputOption::VALUE_NONE, 'Test without sending Slack messages')
        ;
    }

    protected function hostedExecute()
    {
        try {
            $dir = __DIR__ . '/../../..';
            $command = "php $dir/vendor/bin/phpstan --error-format=prettyJson --no-progress --configuration=$dir/phpstan.neon";
            $output = Util::executeShell($command, separator: '');
            $this->line('Static code scan completed', 'info',
                compact('command', 'output'));

            $output = Util::s2j($output) ?? [];
            $totals = $output['totals'] ?? [];
            if (empty($totals)) {
                throw new \RuntimeException('Invalid scan result!');
            }

            if (!empty($totals['errors']) || !empty($totals['file_errors'])) {
                $revision = $this->input->getOption('revision') ?? Util::randTimeNumber();
                $report = 'phpstan_' . $revision . '.json';
                $path = $dir . '/tests/_output/' . $report;
                file_put_contents($path, Util::formatJSON($output));

                $host = Util::silent(fn () => Util::host(true), 'https://staging.virtualcards.us');
                $url = $host . '/dev/test/' . $report;

                $files = $output['files'] ?? [];
                $onlyIgnored = count($files) > 0;
                $patterns = [
                    '|Ignored error pattern .* is expected to occur \d+ times, but occurred only \d+ time|',
                    '|Ignored error pattern .* was not matched in reported errors|',
                ];
                foreach ($files as $fe) {
                    foreach ($fe['messages'] ?? [] as $fem) {
                        $mm = $fem['message'] ?? '';
                        $matched = false;
                        foreach ($patterns as $pattern) {
                            if (preg_match($pattern, $mm)) {
                                $matched = true;
                                break;
                            }
                        }
                        if (!$matched) {
                            $onlyIgnored = false;
                        }
                    }
                }

                $success = false;
                if ($onlyIgnored) {
                    $success = true;
                    $this->generateBaseline();
                }
                $this->sendSlackAlert($success, $totals, $url);
            } else {
                $this->sendSlackAlert(true);
            }
        } catch (\Throwable $e) {
            $this->sendSlackAlert(false, [
                'error' => $e->getMessage(),
            ]);
        }
    }

    protected function generateBaseline()
    {
        $dir = __DIR__ . '/../../..';
        $parsed = Yaml::parseFile($dir . '/phpstan.neon');
        $baseline = $parsed['includes'][0] ?? null;
        if ($baseline) {
            $command = "php $dir/vendor/bin/phpstan analyse --error-format=prettyJson --no-progress --configuration=$dir/phpstan.neon --generate-baseline $baseline";
            $output = Util::executeShell($command, separator: '');
            $this->line('Regenerated baseline file', 'info',
                compact('command', 'output'));
            if ( ! $this->input->getOption('test')) {
                Util::$platform = null;
                SlackService::$platform = null;
                SlackService::force(function () use ($output) {
                    SlackService::check('Regenerated baseline file', [
                        'output' => Util::maxLength($output, 255),
                    ], SlackService::MENTION_HANS);
                });
            }
        }
    }

    protected function sendSlackAlert(bool $success, array $summary = null, $url = null)
    {
        if ($this->input->getOption('test')) {
            $this->lineNoLog('Skip sending Slack message', 'debug', func_get_args());
            return;
        }

        Util::$platform = null;
        SlackService::$platform = null;
        SlackService::force(function () use ($success, $summary, $url) {
            $suffix = '';
            if ($url) {
                $suffix = ': <' . $url . '|read the report>';
            }
            $summary = $summary ?? [];
            $summary['host'] = Util::host(true);
            if ($success) {
                SlackService::$webhookUrl = SlackService::WEBHOOK_SYSTEM;
                SlackService::check('Passed static code scan' . $suffix, $summary);
            } else {
                SlackService::$webhookUrl = SlackService::WEBHOOK_DEVS;
                SlackService::warning('Static code scan failed' . $suffix, $summary,
                    SlackService::GROUP_DEV_SHINETECH);
            }
        });
    }
}
