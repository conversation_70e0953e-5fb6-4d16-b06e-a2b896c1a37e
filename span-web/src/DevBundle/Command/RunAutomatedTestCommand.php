<?php

namespace DevBundle\Command;

use CoreBundle\Command\BaseHostedCommand;
use CoreBundle\Utils\Util;
use DevBundle\Services\SlackService;
use Symfony\Component\Console\Input\InputOption;

class RunAutomatedTestCommand extends BaseHostedCommand
{
    protected ?bool $alertForDuplicated = false;

    protected function configure()
    {
        $this
            ->setName('span:dev:run-test')
            ->setDescription('Run Codecept automated test cases')
            ->addOption('revision', null, InputOption::VALUE_REQUIRED, 'The revision as the report name', 'report')
        ;
    }

    protected function hostedExecute()
    {
        if (Util::isLive()) {
            $this->line('Skip running the automated test in production!', 'comment');
            return;
        }

        $env = 'dev';
        if (Util::isStaging()) {
            $env = 'staging';
            Util::executeCommand('span:dev:mex:transfer:cancel-created');
        }

        $commit = $this->input->getOption('revision');
        $cmd = "vendor/bin/codecept run --env=$env --html=$commit.html 2>&1";
        $this->line($cmd);

        $output = Util::executeShell($cmd);
        $this->line($output);

        if ($commit !== 'report') {
            $dir = __DIR__ . '/../../../tests/_output/';
            unlink($dir . 'report.html');
            copy($dir . $commit . '.html', $dir . 'report.html');
        }
    }
}
