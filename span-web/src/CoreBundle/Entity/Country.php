<?php

namespace CoreBundle\Entity;

use Api<PERSON>undle\Entity\ApiEntityInterface;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use Doctrine\ORM\Mapping\ManyToMany;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Country
 *
 * @ORM\Table(name="country", indexes={
 *     @Index(name="iso_code_index", columns={"iso_code"}),
 *     @Index(name="iso3_code_index", columns={"iso3_code"}),
 *     @Index(name="phone_code_index", columns={"phone_code"}),
 *     @Index(name="numeric_code_index", columns={"numeric_code"}),
 * })
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\CountryRepository")
 */
class Country implements ApiEntityInterface
{
    public const CONTINENT_EUROPE = 'Europe';

    /**
     * @param $id
     *
     * @return Country|null
     */
    public static function find($id)
    {
        return Util::em()->getRepository(static::class)->find($id);
    }

    public static function usa()
    {
        return self::findByCode('USA');
    }

    public static function mex()
    {
        return self::findByCode('MEX');
    }

    public function isUSA() {
        return $this->getIso3Code() === 'USA' || $this->getIsoCode() === 'US';
    }

    public function isMX() {
      return $this->getIso3Code() === 'MEX' || $this->getIsoCode() === 'MX';
  }

    /**
     * @param $code
     * @return null|Country
     */
    public static function findByCode($code)
    {
        if (!$code) {
            return null;
        }
        if (is_numeric($code)) {
            return Util::em()->getRepository(static::class)
                ->find($code);
        }
        $code = strtoupper($code);
        $fallback = [
            'UK' => 'GB',
        ];
        $code = $fallback[$code] ?? $code;
        $query = Util::em()->getRepository(\CoreBundle\Entity\Country::class)
            ->createQueryBuilder('c');
        $expr = $query->expr();
        $rs = $query->where($expr->orX(
                $expr->eq('c.isoCode', ':isoCode'),
                $expr->eq('c.iso3Code', ':iso3Code')
            ))
            ->setParameter('isoCode', $code)
            ->setParameter('iso3Code', $code)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        return $rs[0] ?? null;
    }

    /**
     * @param integer $code
     *
     * @return Country|null
     */
    public static function findByNumericCode($code)
    {
        $rs = Util::em()->getRepository(self::class)
            ->createQueryBuilder('c')
            ->where('c.numericCode = :numericCode')
            ->setParameter('numericCode', (int)$code)
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        return $rs ? $rs[0] : null;
    }

    /**
     * @param integer $name
     *
     * @return Country|null
     */
    public static function findByName($name)
    {
        $rs = Util::em()->getRepository(self::class)
            ->createQueryBuilder('c')
            ->where('c.name = :name')
            ->setParameter('name', ucwords($name))
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        return $rs ? $rs[0] : null;
    }

    public function toApiArray(bool $extra = false): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'iso' => $this->getIsoCode(),
            'iso3' => $this->getIso3Code(),
            'phoneCode' => $this->getPhoneCode(),
            'numericCode' => $this->getNumericCode(),
            'region' => $this->getRegion(),
            'currency' => $this->getCurrency(),
        ];
    }

    public function toApiMinArray()
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'iso' => $this->getIsoCode(),
            'iso3' => $this->getIso3Code(),
            'numeric' => $this->getNumericCode(),
            'currency' => $this->getCurrency(),
        ];
    }

    public function findStateByName($name)
    {
        $query = Util::em()->getRepository(BaseState::getSpecificClass())
            ->createQueryBuilder('c');
        $expr = $query->expr();
        $rs = $query->where($expr->orX(
                $expr->eq('c.name', ':name'),
                $expr->eq('c.abbr', ':name')
            ))
            ->andWhere('c.country = ' . $this->getId())
            ->setParameter('name', $name)
            ->getQuery()
            ->getResult();
        return $rs[0] ?? null;
    }

    public function hasStates()
    {
        $count = Util::em()->getRepository(BaseState::getSpecificClass())
            ->createQueryBuilder('s')
            ->where('s.country = :country')
            ->setParameter('country', $this)
            ->select('count(s)')
            ->getQuery()
            ->getSingleScalarResult();
        return $count > 0;
    }

    /**
     * @return BaseState[]
     */
    public function getSortedStates()
    {
        return Util::em()->getRepository(BaseState::getSpecificClass())
            ->createQueryBuilder('s')
            ->where('s.country = :country')
            ->setParameter('country', $this)
            ->orderBy('s.name', 'asc')
            ->getQuery()
            ->getResult();
    }

    public function getSpecificStates()
    {
        if (Bundle::isEsSolo()) {
            return $this->getEsSoloStates();
        }
        return $this->getStates();
    }

    public function canSignupInUsu()
    {
        return true;
//        return !in_array($this->getName(), [
//            'Romania',
//            'Albania',
//            'Germany',
//            'Austria',
//        ]);
    }

    public function isWatchingInUsu()
    {
        return false;
//        return $this->getContinent() === self::CONTINENT_EUROPE;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=225)
     * @Assert\NotBlank()
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="iso_code", type="string", length=10, nullable=true, options={"comment":"2 characters, like US"})
     * @Assert\Length(max=10)
     */
    private $isoCode;

    /**
     * @var string
     *
     * @ORM\Column(name="iso3_code", type="string", length=3, options={"comment":"3 characters, like USA"})
     * @Assert\NotBlank()
     * @Assert\Length(max=3)
     */
    private $iso3Code;

    /**
     * @var integer
     *
     * @ORM\Column(name="numeric_code", type="integer", nullable=true, options={"comment":"ISO numeric code digits, like 840 for USA"})
     */
    private $numericCode;

    /**
     * @var string
     *
     * @ORM\Column(name="phone_code", type="string", length=6)
     * @Assert\NotBlank()
     * @Assert\Length(max=6)
     */
    private $phoneCode;

    /**
     * @var string
     *
     * @ORM\Column(name="in_country_phone_prefix", type="string", length=6)
     * @Assert\NotBlank()
     * @Assert\Length(max=6)
     */
    private $inCountryPhonePrefix;

    /**
     * @var boolean
     *
     * @ORM\Column(name="twilio_call", type="boolean")
     * @Assert\NotNull()
     */
    private $twilioCall;

    /**
     * @var boolean
     *
     * @ORM\Column(name="status", type="boolean")
     * @Assert\NotNull()
     */
    private $status;

    /**
     * @var boolean
     *
     * @ORM\Column(name="id_status", type="boolean")
     * @Assert\NotNull()
     */
    private $idStatus;

    /**
     * @var string
     *
     * @ORM\Column(name="continent", type="string", length=255)
     * @Assert\NotBlank()
     */
    private $continent;

    /**
     * @var string
     *
     * @ORM\Column(name="region", type="string", length=255)
     * @Assert\NotBlank()
     */
    private $region;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=3)
     * @Assert\NotBlank()
     * @Assert\Length(max=3)
     */
    private $currency;

    /**
     * @ManyToMany(targetEntity="CoreBundle\Entity\CardProgram", mappedBy="countries")
     * @Exclude
     */
    private $cardPrograms;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\State", mappedBy="country")
     * @Exclude()
     */
    protected $states;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="EsSoloBundle\Entity\State", mappedBy="country")
     * @Exclude()
     */
    protected $esSoloStates;

    /**
     * @var \Doctrine\Common\Collections\Collection
     *
     * @ORM\OneToMany(targetEntity="SalexUserBundle\Entity\User", mappedBy="country")
     * @Exclude()
     */
    protected $users;


     /**
     * @var string
     *
     * @ORM\Column(name="intermex_country_id", type="string", length=255, nullable=true)
     */
    private $intermexCountryId;

     /**
     * @var boolean
     *
     * @ORM\Column(name="transfer_enable", type="boolean", nullable=true,  options={"default: 0"})
     */
    private $transferEnable;
    
    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Country
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set isoCode
     *
     * @param string $isoCode
     *
     * @return Country
     */
    public function setIsoCode($isoCode)
    {
        $this->isoCode = $isoCode;

        return $this;
    }

    /**
     * Get isoCode
     *
     * @return string
     */
    public function getIsoCode()
    {
        return $this->isoCode;
    }

    /**
     * Set iso3Code
     *
     * @param string $iso3Code
     *
     * @return Country
     */
    public function setIso3Code($iso3Code)
    {
        $this->iso3Code = $iso3Code;

        return $this;
    }

    /**
     * Get iso3Code
     *
     * @return string
     */
    public function getIso3Code()
    {
        return $this->iso3Code;
    }

    /**
     * Set phoneCode
     *
     * @param string $phoneCode
     *
     * @return Country
     */
    public function setPhoneCode($phoneCode)
    {
        $this->phoneCode = $phoneCode;

        return $this;
    }

    /**
     * Get phoneCode
     *
     * @return string
     */
    public function getPhoneCode()
    {
        return $this->phoneCode;
    }

    /**
     * Set inCountryPhonePrefix
     *
     * @param string $inCountryPhonePrefix
     *
     * @return Country
     */
    public function setInCountryPhonePrefix($inCountryPhonePrefix)
    {
        $this->inCountryPhonePrefix = $inCountryPhonePrefix;

        return $this;
    }

    /**
     * Get inCountryPhonePrefix
     *
     * @return string
     */
    public function getInCountryPhonePrefix()
    {
        return $this->inCountryPhonePrefix;
    }

    /**
     * Set twilioCall
     *
     * @param integer $twilioCall
     *
     * @return Country
     */
    public function setTwilioCall($twilioCall)
    {
        $this->twilioCall = $twilioCall;

        return $this;
    }

    /**
     * Get twilioCall
     *
     * @return int
     */
    public function getTwilioCall()
    {
        return $this->twilioCall;
    }

    /**
     * Set status
     *
     * @param integer $status
     *
     * @return Country
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set idStatus
     *
     * @param integer $idStatus
     *
     * @return Country
     */
    public function setIdStatus($idStatus)
    {
        $this->idStatus = $idStatus;

        return $this;
    }

    /**
     * Get idStatus
     *
     * @return int
     */
    public function getIdStatus()
    {
        return $this->idStatus;
    }


    /**
     * Set continent
     *
     * @param string $continent
     *
     * @return Country
     */
    public function setContinent($continent)
    {
        $this->continent = $continent;

        return $this;
    }

    /**
     * Get continent
     *
     * @return string
     */
    public function getContinent()
    {
        return $this->continent;
    }

    /**
     * Set region
     *
     * @param string $region
     *
     * @return Country
     */
    public function setRegion($region)
    {
        $this->region = $region;

        return $this;
    }

    /**
     * Get region
     *
     * @return string
     */
    public function getRegion()
    {
        return $this->region;
    }

    /**
     * Set currency
     *
     * @param string $currency
     *
     * @return Country
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * Get currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->cardPrograms = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add cardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $cardProgram
     *
     * @return Country
     */
    public function addCardProgram(\CoreBundle\Entity\CardProgram $cardProgram)
    {
        $this->cardPrograms[] = $cardProgram;

        return $this;
    }

    /**
     * Remove cardProgram
     *
     * @param \CoreBundle\Entity\CardProgram $cardProgram
     */
    public function removeCardProgram(\CoreBundle\Entity\CardProgram $cardProgram)
    {
        $this->cardPrograms->removeElement($cardProgram);
    }

    /**
     * Get cardPrograms
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getCardPrograms()
    {
        return $this->cardPrograms;
    }

    /**
     * Add state
     *
     * @param \CoreBundle\Entity\State $state
     *
     * @return Country
     */
    public function addState(\CoreBundle\Entity\State $state)
    {
        $this->states[] = $state;

        return $this;
    }

    /**
     * Remove state
     *
     * @param \CoreBundle\Entity\State $state
     */
    public function removeState(\CoreBundle\Entity\State $state)
    {
        $this->states->removeElement($state);
    }

    /**
     * Get states
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getStates()
    {
        return $this->states;
    }

    /**
     * Add esSoloState
     *
     * @param \EsSoloBundle\Entity\State $esSoloState
     *
     * @return Country
     */
    public function addEsSoloState(\EsSoloBundle\Entity\State $esSoloState)
    {
        $this->esSoloStates[] = $esSoloState;

        return $this;
    }

    /**
     * Remove esSoloState
     *
     * @param \EsSoloBundle\Entity\State $esSoloState
     */
    public function removeEsSoloState(\EsSoloBundle\Entity\State $esSoloState)
    {
        $this->esSoloStates->removeElement($esSoloState);
    }

    /**
     * Get esSoloStates
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getEsSoloStates()
    {
        return $this->esSoloStates;
    }

    /**
     * Add user
     *
     * @param \SalexUserBundle\Entity\User $user
     *
     * @return Country
     */
    public function addUser(\SalexUserBundle\Entity\User $user)
    {
        $this->users[] = $user;

        return $this;
    }

    /**
     * Remove user
     *
     * @param \SalexUserBundle\Entity\User $user
     */
    public function removeUser(\SalexUserBundle\Entity\User $user)
    {
        $this->users->removeElement($user);
    }

    /**
     * Get users
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * Set numericCode
     *
     * @param integer $numericCode
     *
     * @return Country
     */
    public function setNumericCode($numericCode)
    {
        $this->numericCode = $numericCode;

        return $this;
    }

    /**
     * Get numericCode
     *
     * @return integer
     */
    public function getNumericCode()
    {
        return $this->numericCode;
    }

    public function getIntermexCountryId(): ?string
    {
        return (int)$this->intermexCountryId;
    }

    public function setIntermexCountryId(?string $intermexCountryId): static
    {
        $this->intermexCountryId = $intermexCountryId;
        return $this;
    }

    public function getTransferEnable(): ?bool
    {
        return $this->transferEnable;
    }

    public function setTransferEnable(?bool $transferEnable): static
    {
        $this->transferEnable = $transferEnable;

        return $this;
    }
}
