<?php

namespace CoreBundle\Entity;

use Carbon\Carbon;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Service;
use CoreBundle\Utils\Traits\ConstantTrait;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;
use Exception;
use FaasBundle\Services\ClientService;
use Postmark\Models\DynamicResponseModel;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use UsUnlockedBundle\UsUnlockedBundle;

/**
 * Email history
 *
 * @ORM\Table(name="email", indexes={
 *     @Index(name="status_idx", columns={"status"}),
 *     @Index(name="error_idx", columns={"error"}),
 *     @Index(name="subject_idx", columns={"subject"}),
 *     @Index(name="time_idx", columns={"time"}),
 *     @Index(name="message_id_idx", columns={"message_id"}),
 *     @Index(name="content_idx", columns={"status", "subject", "time"}),
 * }, options={"comment":"Email history, also the email queue."})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\EmailRepository")
 */
class Email
{
    use ConstantTrait;

    const CONTENT_TYPE_HTML = 'text/html';
    const CONTENT_TYPE_TEXT = 'text/plain';

    const PURPOSE_REGULAR = 'regular';
    const PURPOSE_REGISTER_CONFIRM = 'register_confirm';
    const PURPOSE_RESET_PASSWORD = 'reset_password';
    const PURPOSE_LOAD_CARD_PAYMENT = 'load_card_payment';
    const PURPOSE_ID_VERIFICATION = 'id_verification';
    const PURPOSE_DEBUG = 'debug';
    const PURPOSE_PROMOTION = 'promotion';

    const TEMPLATE_EMPTY_TEMPLATE = '32350275'; // Empty template
    const TEMPLATE_SIMPLE_LAYOUT = '24179115'; // Empty layout
    const TEMPLATE_SIMPLE_LAYOUT_SPANISH = '32304824'; // Empty layout (Spanish)
    const TEMPLATE_BASE_LAYOUT = '25435510'; // With the user login link and username

    const TEMPLATE_LOAD_CARD_PAYMENT_INITIATED = 1494984;
    const TEMPLATE_LOAD_CARD_PAYMENT_PENDING = 1810481;
    const TEMPLATE_LOAD_CARD_PAYMENT_CONFIRMED = null;
    const TEMPLATE_LOAD_CARD_PAYMENT_RECEIVED = 1500022;
    const TEMPLATE_LOAD_CARD_PAYMENT_LOADED = 1810482;

    const TEMPLATE_RESET_PASSWORD = 1456381;
    const TEMPLATE_RESET_PASSWORD_BY_SYSTEM = 28693625;
    const TEMPLATE_EMAIL_ADDRESS_VERIFICATION = 1456501;
    const TEMPLATE_EMAIL_ADDRESS_VERIFICATION_FOR_TESTING = 5323401;
    const TEMPLATE_REGISTERED = 1500606;
    const TEMPLATE_PROFILE_UPDATED = 2087803;

    const TEMPLATE_ID_VERIFICATION_ACCEPTED = 1421361;
    const TEMPLATE_ID_VERIFICATION_MANUAL_ACCEPTED = 17340266;
    const TEMPLATE_ID_VERIFICATION_EXPIRED = 1456326;
    const TEMPLATE_ID_VERIFICATION_INCOMPLETE = 1456323;
    const TEMPLATE_ID_VERIFICATION_INVALID = 1456384;
    const TEMPLATE_ID_VERIFICATION_NONE = 1456325;

    const TEMPLATE_USER_BANNED = 1667923;
    const TEMPLATE_USER_CLOSED = 1668041;
    const TEMPLATE_USER_UNDER_REVIEW = 4394921;

    const TEMPLATE_CARD_ISSUED = 1500481;
    const TEMPLATE_CARD_CLOSED = 1729905;
    const TEMPLATE_CARD_EXPIRING_REMINDER = '1729403';
    const TEMPLATE_CARD_BALANCE_REMINDER = '1676421';
    const TEMPLATE_CARD_REPLACEMENT = 1807781;
    const TEMPLATE_CARD_UPGRADED = 1868622;

    const TEMPLATE_LOAD_FAILED_BECAUSE_OF_MAX_BALANCE = 17340119;
    const TEMPLATE_EXTENDED_INITIAL_LOAD_TIME = 17374791;
    const TEMPLATE_PENDING_LOAD_REMINDER_NO_PROMOTION = '17475549';
    const TEMPLATE_PENDING_LOAD_REMINDER_PROMOTION = '17476282';
    const TEMPLATE_OPEN_USERS_IN_KYC_EXCEPTION_POOL = 17565390;
    const TEMPLATE_KYC_REMIND_WHEN_BALANCE_NEARING = 17564713;
    const TEMPLATE_KYC_REMIND_WHEN_LOAD_AMOUNT_EXCEED = 17578237;
    const TEMPLATE_REMIND_HOME_STEP_USERS = 17594731;

    const TEMPLATE_DECLINED_TRANSACTIONS_AVS = '1749143';
    const TEMPLATE_DECLINED_TRANSACTIONS_INSUFFICIENT_FUNDS = '1749142';
    const TEMPLATE_DECLINED_TRANSACTIONS_NON_US_MERCHANT = '1749141';
    const TEMPLATE_FV_DECLINED_UPDATED = '1757084';
    const TEMPLATE_FV_TRANSACTION_UPDATED = '1757322';
    const TEMPLATE_SUMMERY_RECENT_TRANSACTION = '1748603';
    const TEMPLATE_WX_PARSED = '2937141';

    const TEMPLATE_CARD_CLOSED_DECLINE = '********';
    const TEMPLATE_FRAUD_ADVICE_DECLINE = '********';
    const TEMPLATE_INSUFFICIENT_FUNDS_DECLINE = '********';
    const TEMPLATE_INVALID_CARD_DETAILS_DECLINE = '********';
    const TEMPLATE_UNKNOWN_HOST_TIMEOUT_DECLINE = '********';
    const TEMPLATE_UNAUTHORIZED_MERCHANT_DECLINE = '********';
    const TEMPLATE_USER_TRANSACTION_LIMIT_DECLINE = '********';
    const TEMPLATE_PENDING_MEMBER_DRIP = '********';
    const TEMPLATE_ACCOUNT_CREATION_FOLLOWUP = '********';
    const TEMPLATE_CARDS_PAUSED_DUE_TO_BALANCE = '********';

    const TEMPLATE_AFFILIATE_APPLY_APPROVED = '5167182';
    const TEMPLATE_AFFILIATE_APPLY_DECLINED = '5167181';
    const TEMPLATE_AFFILIATE_APPLY = '********';

    const TEMPLATE_SCHEDULED_REPORTING = '6214364';
    const TEMPLATE_WELCOME_NEW_DEVELOPER = '********';
    const TEMPLATE_WELCOME = '********';

    // LeafLink
    const TEMPLATE_LEAFLINK_ONBOARDING_LINK = '********';
    const TEMPLATE_LEAFLINK_ONBOARDING_LINK_TEST = '********';
    const TEMPLATE_LEAFLINK_ONBOARDING_LINK_STAGE = '********';
    const B2B_BATCH_REPORT = '********';
    const BATCH_REPORT = '********';

    // FIS
    const TEMPLATE_FIS_VELOCITY_ALERT = '********';
    const TEMPLATE_FIS_CARDS_REPORT = '********';
    const TEMPLATE_FIS_WELCOME = '********';

    // TransferMex
    const TEMPLATE_TRANSFER_MEX_INVITE_AGENT = '********';
    const TEMPLATE_TRANSFER_MEX_INVITE_AGENT_BY_URL = '********';
    const TEMPLATE_TRANSFER_MEX_PAYROLL_FILE = '********';
    const TEMPLATE_TRANSFER_MEX_TRANSFER_BANK = '********';
    const TEMPLATE_TRANSFER_MEX_TRANSFER_CASH = '********';
    const TEMPLATE_TRANSFER_MEX_CREDIT_MEMBER_SPANISH = '********';
    const TEMPLATE_TRANSFER_MEX_CREDIT_MEMBER = '********';
    const TEMPLATE_TRANSFER_MEX_RAPYD_SETTLEMENT = '********';

    const STATUS_DISABLED = 'disabled';
    const STATUS_PENDING = 'pending';
    const STATUS_SENT = 'sent';
    const STATUS_ERROR = 'error';

    //Cow
    const CASH_ON_WEB_INVITE_AGENT = '********';
    const COW_TEMPLATE_REMIND_HOME_STEP_USERS = '********';
    const COW_TEMPLATE_LOAD_FAILED_BECAUSE_OF_MAX_BALANCE = '********';
    const COW_TEMPLATE_EMAIL_ADDRESS_VERIFICATION = '********';
    const COW_TEMPLATE_REGISTERED = ********;
    const COW_TEMPLATE_KYC_DENY = '********';
    const COW_TEMPLATE_KYC_MANUAL = '********';
    const COW_TEMPLATE_KYC_FLOW = '********';
    const COW_REFUND_EMAIL = '********';


    // wilen
    const WILEN_INVITE_AGENT = '********';

    // Faas
    const FAAS_TEMPLATE_FAAS_FLOW = '24791073';

    const VERIFY_CODE = '24944476';

    // spendr mobile
	const SPENDR_MOBILE_TEMPLATE_EMAIL_ADDRESS_VERIFICATION = '25317894';

    public static bool $disabled = false;
    public static array $errorsCache = [];

    public function getTemplateName()
    {
        $id = $this->getTemplate();
        if (!$id) {
            return '';
        }
        $templates = self::getTemplates();
        return $templates[$id] ?? '';
    }

    public static function getTemplates()
    {
        $result = [];
        $templates = Email::getConstantsStartWith('TEMPLATE');
        foreach ($templates as $name => $id) {
            if ($id) {
                $name = str_replace('TEMPLATE_', '', $name);
                $name = strtolower($name);
                $result[$id] = Util::humanize($name);
            }
        }
        return $result;
    }

    public static function send($recipients, $subject, $content, $purpose = self::PURPOSE_REGULAR, $contentType = self::CONTENT_TYPE_HTML)
    {
        if (self::$disabled) {
            return;
        }

        $container = Util::$container;
        $sender = $container->getParameter('mail_sender_address');

        $email = new self();
        $email->setSender($sender);
        $email->setRecipients(json_encode($recipients));
        $email->setPurpose($purpose);
        $email->setSubject($subject);
        $email->setBody($content);
        $email->setContentType($contentType);
        $email->setTime(new Carbon());
        $email->setStatus(self::STATUS_PENDING);
        Util::persist($email);

        Service::email($email);
    }

    public static function isValid($email)
    {
        return Config::isEmailValid($email);
    }

    public static function debug($title, array $body = [])
    {
        if (self::$disabled) {
            return '';
        }
        $param = Util::jsonRequest();
        $body = array_merge($param, $body);
        return Service::debugEmail($title, $body);
    }

    public static function sendWithTemplate($recipients, $template, array $templateData = [],
                                            User $consumerUser = null, CardProgram $cardProgram = null)
    {
        if (!$template) {
            Log::debug('Unknown email template');
            return null;
        }

        if (self::$disabled) {
            Log::debug('Email service is disabled in this script.');
            return null;
        }

        if (!$cardProgram) {
            $cardProgram = Util::cardProgram();
        }

        if (!$cardProgram) {
            $cardProgram = CardProgram::usunlocked();
        }

        if ($template === self::TEMPLATE_SIMPLE_LAYOUT && !empty($templateData['_lang'])) {
            if ($templateData['_lang'] === 'es') {
                $template = self::TEMPLATE_SIMPLE_LAYOUT_SPANISH;
            }
        }

        if (!Util::isLive() && !in_array($template, [
                self::TEMPLATE_FV_TRANSACTION_UPDATED,
                self::TEMPLATE_FV_DECLINED_UPDATED,
            ], false))
        {
            // Prevent from sending same errors again and again in short time(1 hr).
            $now = new Carbon();
            if (isset(self::$errorsCache[$template])) {
                /** @var Carbon $last */
                $last = self::$errorsCache[$template];
                if ($last->addHour()->gte($now)) {
                    Log::debug('Skip sending duplicated emails in a single script');
                    return null;
                }
            }
            self::$errorsCache[$template] = $now;
        }

        $container = Util::$container;

        $host = $cardProgram->getPlatform()->host();
        $user = $consumerUser ?? Util::user();

        if ($user && self::isUserSkipped($user)) {
            Log::debug('Skip sending emails to the skipped user ' . $user->getId() . ' with template ' . $template);
            return null;
        }

        $client = null;
        if ($user) {
            if ($user->inTeam(Role::ROLE_FAAS_CLIENT)) {
                $client = $user;
            } else if ($user->inTeam(Role::ROLE_FAAS_MEMBER)) {
                $client = $user->getPrimaryGroupAdmin();
            }
        }
        if (!$client) {
            $client = ClientService::getClientByDomainOrReferer();
        }
        $host = ClientService::getPreferredHost($client, $host);
        $rootHost = Util::host();

        if (!empty($templateData['action_url'])) {
            $actionUrl = $templateData['action_url'];
            if (Util::startsWith($actionUrl, '/')) {
                $templateData['action_url'] = $host . $actionUrl;
            } else {
                $map = [
                    $host . '/account-service' => $host . '/p/profile',
                    $host . '/card-management' => $host . '/p',
                    $host . '/consumer-register/card-load' => $host . '/p',

                    // root
                    $rootHost . '/account-service' => $host . '/p/profile',
                    $rootHost . '/card-management' => $host . '/p',
                    $rootHost . '/consumer-register/card-load' => $host . '/p',
                ];
                foreach ($map as $old => $new) {
                    if (Util::startsWith($actionUrl, $old)) {
                        $actionUrl = $new;
                        break;
                    }
                }
                $templateData['action_url'] = $actionUrl;
            }
        }

        $productSuffix = $client ? (' ' . $client->getAdminGroupName()) : '';

        $logo = $cardProgram->getEmailValue('htmlLogo', '');
        if ($client) {
            $logoImage = ClientService::getLogoByDomainOrReferer($client);
            if ($logoImage) {
                $logo = Platform::createHtmlLogo($logoImage, $host);
            }
        }

        if ($cardProgram->getPlatform()->isFaas() && $client && $productSuffix && ($template == self::TEMPLATE_RESET_PASSWORD || $template == self::TEMPLATE_SIMPLE_LAYOUT) ) {
          $templateData = array_merge([
              'name'           => $user ? $user->getFullName() : '',
              'username'       => $user ? $user->getEmail() : '',
              'login_url'      => $host . '/login',
              'support_email'  => $cardProgram->getEmailValue('supportEmailValue', Util::DEFAULT_HELP_DESK_MAIL),
              'live_chat_url'  => $cardProgram->getEmailValue('liveChatUrlValue', ''),
              'help_url'       => $cardProgram->getEmailValue('helpUrl', ''),
              'website_url'    => $host,
              'Product_Team'   => $productSuffix,
              'company_name'   => $productSuffix,
              'product_name'   => $productSuffix,
              'sender_name'    => $productSuffix,
              'logo'           => $logo,
              'copyright_year'  => date('Y'),
              'company_address' => $cardProgram->getEmailValue('htmlAddress', ''),
              'company_address_text' => $cardProgram->getEmailValue('textAddress', ''),
              'payment_detail' => '',
              'Company_Name'   => $productSuffix,
          ], $templateData);
        } else {
          $templateData = array_merge([
              'name'           => $user ? $user->getFullName() : '',
              'username'       => $user ? $user->getEmail() : '',
              'login_url'      => $host . '/login',
              'support_email'  => $cardProgram->getEmailValue('supportEmailValue', Util::DEFAULT_HELP_DESK_MAIL),
              'live_chat_url'  => $cardProgram->getEmailValue('liveChatUrlValue', ''),
              'help_url'       => $cardProgram->getEmailValue('helpUrl', ''),
              'website_url'    => $host,
              'Product_Team'   => $cardProgram->getEmailValue('productTeam', '') . $productSuffix,
              'company_name'   => $cardProgram->getEmailValue('companyName', Util::SYSTEM_NAME) . $productSuffix,
              'product_name'   => $cardProgram->getEmailValue('productName', Util::SYSTEM_NAME) . $productSuffix,
              'sender_name'    => $cardProgram->getEmailValue('senderName', Util::SYSTEM_NAME) . $productSuffix,
              'logo'           => $logo,
              'copyright_year'  => date('Y'),
              'company_address' => $cardProgram->getEmailValue('htmlAddress', ''),
              'company_address_text' => $cardProgram->getEmailValue('textAddress', ''),
              'payment_detail' => '',
              'Company_Name'   => $cardProgram->getEmailValue('companyName', Util::SYSTEM_NAME) . $productSuffix,
          ], $templateData);
        }
        if (empty($templateData['action_url'])) {
            $templateData['action_url'] = $templateData['website_url'];
        }
        if (empty($templateData['name'])) {
            $templateData['name'] = 'User';
        }
        // Log::debug('Template data', $templateData);
        $email = new self();

        $sender = $container->getParameter('mail_sender_address');
        $cardProgramSender = $cardProgram->getEmailValue('supportEmailValue');
        if ($cardProgramSender) {
            $sender = $cardProgramSender;
        }

        // use tern info for account limit error email
        if (
			isset($templateData['subject'])
			&& $templateData['subject']
			&& strpos($templateData['subject'], 'Please fix the account limit error') !== false
			&& (strpos($templateData['subject'], 'Transfer to') !== false || strpos($templateData['subject'], 'settlement to') !== false)
		) {
          $sender = '<EMAIL>';
          $templateData['action_url'] = '';
          $templateData['logo'] = Platform::createHtmlLogo('https://www.virtualcards.us' . CardProgram::DEFAULT_ICON, '');
          $templateData['Product_Team'] = 'Tern';
          $templateData['product_name'] = 'Tern';
          $templateData['Company_Name'] = 'Tern';
          $templateData['company_Name'] = 'Tern';
        }

        if (
        	$cardProgram->isSpendr()
			&& isset($templateData['to_spendr'])
			&& $templateData['to_spendr']
		) {
			$sender = '<EMAIL>';
			$templateData['name'] = 'Support Admin';
		}

        $email->setSender($sender);
        $email->setRecipients(json_encode($recipients));
        $email->setPurpose($templateData['_purpose'] ?? self::PURPOSE_REGULAR);
        $email->setSubject('Postmark Template');
        $email->setTemplate($template);
        $email->setTemplateData(Util::j2s($templateData));
        $email->setContentType('text/html');
        $email->setTime(new Carbon());

        if ($template && $cardProgram->isEsSolo() && in_array($template, [
                static::TEMPLATE_LOAD_CARD_PAYMENT_INITIATED,
                static::TEMPLATE_LOAD_CARD_PAYMENT_PENDING,
                static::TEMPLATE_LOAD_CARD_PAYMENT_RECEIVED,
                static::TEMPLATE_LOAD_CARD_PAYMENT_LOADED,

                static::TEMPLATE_EMAIL_ADDRESS_VERIFICATION,
                static::TEMPLATE_EMAIL_ADDRESS_VERIFICATION_FOR_TESTING,
                static::TEMPLATE_REGISTERED,
                static::TEMPLATE_PROFILE_UPDATED,

                static::TEMPLATE_ID_VERIFICATION_ACCEPTED,
                static::TEMPLATE_ID_VERIFICATION_MANUAL_ACCEPTED,
                static::TEMPLATE_ID_VERIFICATION_EXPIRED,
                static::TEMPLATE_ID_VERIFICATION_INCOMPLETE,
                static::TEMPLATE_ID_VERIFICATION_INVALID,
                static::TEMPLATE_ID_VERIFICATION_NONE,

                static::TEMPLATE_USER_BANNED,
                static::TEMPLATE_USER_CLOSED,
                static::TEMPLATE_USER_UNDER_REVIEW,

                static::TEMPLATE_CARD_ISSUED,
                static::TEMPLATE_CARD_CLOSED,
                static::TEMPLATE_CARD_EXPIRING_REMINDER,
                static::TEMPLATE_CARD_BALANCE_REMINDER,
                static::TEMPLATE_CARD_REPLACEMENT,
                static::TEMPLATE_CARD_UPGRADED,

                static::TEMPLATE_LOAD_FAILED_BECAUSE_OF_MAX_BALANCE,

                static::TEMPLATE_DECLINED_TRANSACTIONS_AVS,
                static::TEMPLATE_DECLINED_TRANSACTIONS_INSUFFICIENT_FUNDS,
                static::TEMPLATE_DECLINED_TRANSACTIONS_NON_US_MERCHANT,
                static::TEMPLATE_FV_DECLINED_UPDATED,
                static::TEMPLATE_FV_TRANSACTION_UPDATED,
                static::TEMPLATE_SUMMERY_RECENT_TRANSACTION,
                static::TEMPLATE_WX_PARSED,
            ], true)) {
//            Service::log('Will not send email for some templates in Es Solo.', [
//                'subject' => $email->getSubject(),
//                'recipients' => $email->getRecipients(),
//            ]);
            $email->setStatus(self::STATUS_DISABLED);
        } else {
            if ($cardProgram->isServiceEnabled(ServiceManager::POSTMARK)) {
                $email->setStatus(self::STATUS_PENDING);
            } else {
//                Service::log('Will not send email as postmark is not enabled in the card program.', [
//                    'subject' => $email->getSubject(),
//                    'recipients' => $email->getRecipients(),
//                ]);
                $email->setStatus(self::STATUS_DISABLED);
            }
        }

        Util::persist($email);

        if ($recipients) {
            Service::email($email);
        } else {
            $email->setStatus(self::STATUS_ERROR);
            Util::persist($email);
        }

        return $email;
    }

    public static function sendWithTemplateToUser(User $user, $template, array $templateData = [],
                                                  CardProgram $cardProgram = null)
    {
        if ($user->isClosed()) {
            if (!in_array($template, [
                self::TEMPLATE_CARD_CLOSED,
                self::TEMPLATE_USER_CLOSED,
            ])) {
                Log::debug('Disabled sending emails to the closed account ' . $user->getId() . ' with template ' . $template);
                return null;
            }
        }
        if (self::isUserSkipped($user)) {
            Log::debug('Disabled sending emails to the skipped user ' . $user->getId() . ' with template ' . $template);
            return null;
        }

        $name = $user->getName();
        $recipients = [
            $user->getEmail() => $name,
        ];
        if (!$cardProgram) {
            $cardProgram = Util::$cardProgram;
        }
        if (!$cardProgram) {
            $uc = $user->getOneCardInPlatform();
            if ($uc) {
                $cardProgram = $uc->getCard()->getCardProgram();
            }
        }
        if (!$cardProgram) {
            $cps = $user->getOpenCardPrograms();
            if (!$cps->isEmpty()) {
                $cardProgram = $cps->first();
            }
        }

        $lang = self::getEmailLanguageCode($user);
        $templateData[$lang] = true;

        return self::sendWithTemplate($recipients, $template, $templateData, $user, $cardProgram);
    }

    public static function isUserSkipped(User $user): bool
    {
        $ids = self::getSkippedUserIds();
        if (in_array($user->getId(), $ids)) {
            return true;
        }
        if (Bundle::isUsUnlocked() && !$user->hasNonConsumerRole()) {
            $country = $user->getCountryCode();
            if ($country && in_array($country, UsUnlockedBundle::RESTRICTED_COUNTRIES)) {
                return true;
            }
        }
        return false;
    }

    public static function getSkippedUserIds(): array
    {
        if ( ! Util::isLive()) {
            return [0];
        }
        // See https://docs.google.com/spreadsheets/d/1b59aD2LWj25mWtse_NAX6sqn2mfIubWJDATVUp_9nUs/edit?gid=0#gid=0
        return [
            // Blocked
            500194739, 500135817, 500199211, 500199499, 500197547, 500197535, 500197581, 500199212, 500253455, 500250581,
            500266031, 500313927, 500199240, 500199234, 500199519, 500199198, 500197541, 500197540, 500197545, 500197538,
            500197568, 500197577, 500199707, 500308603, 500133325, 500236644, 500292395, 500302846, 500087534, 500058534,
            500178488, 500186270, 500258915, 500304138, 500174382, 500382493, 500310940,

            // Under review
            500102659, 500108994, 500103362, 500157492, 500199633, 500295707, 23082308, 17036183, 500442416, 500277917,
            500271008, 500263600, 500092078, 500060838, 500190961, 500087966, 500068877, 500226411, 22822941, 28515,
            500113901, 500154813, 60542080, 500166331, 500148437, 500220187, 500272726, 23803, 500152577, 500165161,
            500295305, 500148638, 500322494, 18391206, 500119462, 48960721, 14266158, 500104852, 500183737, 500091947,
            500123894, 500361197, 500141412, 500132362, 500124058, 500061493, 500094964, 500128896, 500168426, 500237778,
            500155000, 500279453, 500080338, 500129045, 90194630, 500134802, 500072358, 500075379, 500071501, 500179968,
            500063883, 40271952, 46578120, 500146767, 31033, 500298106, 500080890, 500086695, 500125953, 500172352, 500196357,
            500312395, 500053377, 500046911, 500323917, 500239114, 500278426, 500310274, 500218772, 40276, 500197322, 500164569,
            500338453, 500259761, 500266704, 500056853, 500116466, 500064487, 31152, 500092362, 500075481, 500083771, 500285574,
            500055445, 500312062, 500216585, 500147445, 500282898, 500083593, 500189001, 500146255, 500146256, 500256136,
            500354909, 84853859, 500088101, 500308206, 500133295, 500032111, 500131027, 500306777, 500327276, 500121124, 39384,
            500181718, 500000601, 500100102, 500074412, 500251223, 57823327, 500291473, 500112424, 500106475, 500061280,
            500106350, 500334063, 500366974,
        ];
    }

    private static function getEmailLanguageCode(User $user)
    {
        $lang = $user->getLanguageCode();
        if (!in_array($lang, ['en', 'es'])) {
            $lang = 'en';
        }
        return $lang;
    }

    public static function sendWithTemplateToAdmins($template, array $templateData = [], CardProgram $cardProgram = null, $otherRecipients = [])
    {
        $templateData = array_merge([
            'name' => 'Tern Admin',
        ], $templateData);
        Service::log('** Send email to admins', [
            'template'    => $template,
            'data'        => $templateData,
            'cardProgram' => $cardProgram ? $cardProgram->getId() : null,
        ], 'warn');
        $adminsEmails = Util::em()->getRepository(\CoreBundle\Entity\Config::class)
            ->getArray(Config::CONFIG_ADMINS_MAIL);
        $adminsEmails = array_merge($adminsEmails, $otherRecipients);
        if (!$adminsEmails) {
            return;
        }
        self::sendWithTemplate($adminsEmails, $template, $templateData, null, $cardProgram);
    }

    public static function sendToAdmins($subject, $content, $otherRecipients = [])
    {
        Service::log('** Send email to admins', func_get_args(), 'warn');
        $adminsEmails = Util::em()->getRepository(\CoreBundle\Entity\Config::class)
            ->getArray(Config::CONFIG_ADMINS_MAIL);
        $adminsEmails = array_merge($adminsEmails, $otherRecipients);
        if (!$adminsEmails) {
            return;
        }
        self::send($adminsEmails, $subject, $content);
    }

    public static function sendToDevelopers($subject, $content = null)
    {
        Service::log('** Send email to developers', func_get_args(), 'warn');
        self::send([
            '<EMAIL>' => 'Hans Zhang',
            '<EMAIL>' => 'Abel Zhang',
            '<EMAIL>' => 'Hans Zhang',
        ], $subject, $content ?: $subject);
    }

    // Prevent from sending same errors again and again in short time.
    // TCSPAN-787: Merge them in one email.
    public static function sendToAdminsAvoidBatch($subject, $content)
    {
        $prefix = __FUNCTION__ . '_';
        $key = $prefix . Carbon::yesterday()->format(Util::DATE_FORMAT);
        if (Data::has($key)) {
            $yesterday = Data::getArray($key);
            $html = '<table><thead><tr><th>Subject</th><th>Content</th></tr></thead><tbody>';
            foreach ($yesterday as $item) {
                $html .= "<tr><td>{$item['subject']}</td><td>{$item['content']}</td></tr>";
            }
            $html .= '</tbody></table>';
            self::sendToAdmins('Summary of error reports', $html);
            Data::delAllWith($prefix);
        }
        $todayKey = $prefix . Carbon::now()->format(Util::DATE_FORMAT);
        if (!Data::has($todayKey)) {
            $today = [];
        } else {
            $today = Data::getArray($todayKey);
        }
        $today[] = [
            'subject' => $subject,
            'content' => $content,
        ];
        Data::setArray($todayKey, $today);
    }

    /**
     * @param User     $user
     * @param          $subject
     * @param          $content
     * @param string   $purpose
     */
    public static function sendToUser(User $user, $subject, $content, $purpose = self::PURPOSE_REGULAR)
    {
        if ($user->isClosed()) {
            Log::debug('Disabled sending emails to the closed account ' . $user->getId());
            return;
        }
        if (self::isUserSkipped($user)) {
            Log::debug('Disabled sending emails to the skipped user ' . $user->getId());
            return;
        }

        $name = $user->getName();
        $recipients = [
            $user->getEmail() => $name,
        ];
        self::send($recipients, $subject, $content, $purpose);
    }

    public static function sendOnBehalfOf(User $recipient, $subject, $content, User $sender, $purpose = self::PURPOSE_REGULAR)
    {
        $subject .= ' - on behalf of ' . $sender->getName() . ' (' . $sender->getEmail() . ')';
        self::sendToUser($recipient, $subject, $content, $purpose);
    }

    public function getRecipientsArray()
    {
        $r = $this->recipients;
        $items = json_decode($r, true);
        if (!$items) {
            return [$r];
        }
        if (is_string($items)) {
            return [$items];
        }
        return $items;
    }

    public static function parsePostmarkResponse(DynamicResponseModel $model)
    {
        $result = [];
        foreach ($model as $k => $v) {
            if ($v instanceof DynamicResponseModel) {
                $v = self::parsePostmarkResponse($v);
            }
            $result[$k] = $v;
        }
        return $result;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="subject", type="string", length=255, nullable=true)
     */
    private $subject;

    /**
     * @var string
     *
     * @ORM\Column(name="body", type="text", nullable=true)
     */
    private $body;

    /**
     * @var string
     *
     * @ORM\Column(name="content_type", type="string", length=255)
     */
    private $contentType;

    /**
     * @var string
     *
     * @ORM\Column(name="sender", type="string", length=255)
     */
    private $sender;

    /**
     * @var string
     *
     * @ORM\Column(name="recipients", type="text", options={"comment":"Also saved in email_recipient table"})
     */
    private $recipients;

    /**
     * @var ArrayCollection
     *
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\EmailRecipient", mappedBy="email")
     */
    private $emailRecipients;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="time", type="datetime")
     */
    private $time;

    /**
     * @var string
     *
     * @ORM\Column(name="template", type="string", length=255, nullable=true, options={"comment":"Email template id in postmarkapp.com"})
     */
    private $template;

    /**
     * @var string
     *
     * @ORM\Column(name="template_data", type="text", nullable=true, options={"comment":"Data sent to postmark used to replace placeholders in template"})
     */
    private $templateData;

    /**
     * @var string Postmark message id
     *
     * @ORM\Column(name="message_id", type="string", length=128, nullable=true, options={"comment":"Message ID returned from postmark, used to fetch email content."})
     */
    private $messageId;

    /**
     * @var string
     *
     * @ORM\Column(name="purpose", type="string", length=255)
     */
    private $purpose;

    /**
     * @var string
     *
     * @ORM\Column(name="error", type="string", length=1024, nullable=true, options={"comment":"The system will retry sending if failed for some errors."})
     */
    private $error;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", options={"default"="sent", "comment":"disabled, pending, send or error"})
     */
    private $status;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set subject
     *
     * @param string $subject
     *
     * @return Email
     */
    public function setSubject($subject)
    {
        $this->subject = $subject;

        return $this;
    }

    /**
     * Get subject
     *
     * @return string
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * Set body
     *
     * @param string $body
     *
     * @return Email
     */
    public function setBody($body)
    {
        $this->body = $body;

        return $this;
    }

    /**
     * Get body
     *
     * @return string
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * Set contentType
     *
     * @param string $contentType
     *
     * @return Email
     */
    public function setContentType($contentType)
    {
        $this->contentType = $contentType;

        return $this;
    }

    /**
     * Get contentType
     *
     * @return string
     */
    public function getContentType()
    {
        return $this->contentType;
    }

    /**
     * Set sender
     *
     * @param string $sender
     *
     * @return Email
     */
    public function setSender($sender)
    {
        $this->sender = $sender;

        return $this;
    }

    /**
     * Get sender
     *
     * @return string
     */
    public function getSender()
    {
        $data = json_decode($this->sender, true);
        if (empty($data) || is_string($data)){
            return $this->sender;
        }
        $senderStr = '';
        foreach ((array)$data as $name => $email) {
            $senderStr .= $email . ' <' .$name.'>' ;
        }
        return $senderStr;

    }

    /**
     * Set recipients
     *
     * @param string $recipients
     *
     * @return Email
     * @throws \Doctrine\ORM\ORMInvalidArgumentException
     */
    public function setRecipients($recipients)
    {
        $this->recipients = $recipients;

        $es = $this->getEmailRecipients();
        if ($es->isEmpty()) {
            $rs = json_decode($recipients, true);
            $em = Util::em();
            if (is_string($rs)) {
                $e = new EmailRecipient();
                $e->setEmail($this);
                $e->setAddress($rs);
                $this->addEmailRecipient($e);
                $em->persist($e);
            } else if (is_array($rs)) {
                foreach ($rs as $a => $b) {
                    $e = new EmailRecipient();
                    $e->setEmail($this);
                    if (is_numeric($a)) {
                        $e->setAddress($b);
                    } else {
                        $e->setAddress($a);
                        $e->setName($b);
                    }
                    $this->addEmailRecipient($e);
                    $em->persist($e);
                }
            }
        }

        return $this;
    }

    /**
     * Get recipients
     *
     * @param bool $original
     * @return string
     */
    public function getRecipients($original = false)
    {
        if ($original) {
            return $this->recipients;
        }
        $data = json_decode($this->recipients, true);
        if (empty($data) || is_string($data)){
            return $this->recipients;
        }
        $recipients = '';
        foreach ((array)$data as $email => $name) {
            $recipients .= $name . ' <' .$email.'>' ;
        }
        return $recipients;
    }

    /**
     * Set time
     *
     * @param \DateTime $time
     *
     * @return Email
     */
    public function setTime($time)
    {
        $this->time = $time;

        return $this;
    }

    /**
     * Get time
     *
     * @return \DateTime
     */
    public function getTime()
    {
        return $this->time;
    }

    /**
     * Set purpose
     *
     * @param string $purpose
     *
     * @return Email
     */
    public function setPurpose($purpose)
    {
        $this->purpose = $purpose;

        return $this;
    }

    /**
     * Get purpose
     *
     * @return string
     */
    public function getPurpose()
    {
        return $this->purpose;
    }

    /**
     * Set error
     *
     * @param string $error
     *
     * @return Email
     */
    public function setError($error)
    {
        $this->error = $error;

        return $this;
    }

    /**
     * Get error
     *
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * Set template
     *
     * @param string $template
     *
     * @return Email
     */
    public function setTemplate($template)
    {
        $this->template = $template;

        return $this;
    }

    /**
     * Get template
     *
     * @return string
     */
    public function getTemplate()
    {
        return $this->template;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return Email
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set templateData
     *
     * @param string $templateData
     *
     * @return Email
     */
    public function setTemplateData($templateData)
    {
        $this->templateData = $templateData;

        return $this;
    }

    /**
     * Get templateData
     *
     * @return string
     */
    public function getTemplateData()
    {
        return $this->templateData;
    }

    /**
     * Set messageId
     *
     * @param string $messageId
     *
     * @return Email
     */
    public function setMessageId($messageId)
    {
        $this->messageId = $messageId;

        return $this;
    }

    /**
     * Get messageId
     *
     * @return string
     */
    public function getMessageId()
    {
        return $this->messageId;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->emailRecipients = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add emailRecipient
     *
     * @param \CoreBundle\Entity\EmailRecipient $emailRecipient
     *
     * @return Email
     */
    public function addEmailRecipient(\CoreBundle\Entity\EmailRecipient $emailRecipient)
    {
        $this->emailRecipients[] = $emailRecipient;

        return $this;
    }

    /**
     * Remove emailRecipient
     *
     * @param \CoreBundle\Entity\EmailRecipient $emailRecipient
     */
    public function removeEmailRecipient(\CoreBundle\Entity\EmailRecipient $emailRecipient)
    {
        $this->emailRecipients->removeElement($emailRecipient);
    }

    /**
     * Get emailRecipients
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getEmailRecipients()
    {
        return $this->emailRecipients;
    }
}
