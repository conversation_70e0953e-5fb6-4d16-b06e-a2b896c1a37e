<?php
/**
 * User: Bob
 * Date: 2017/5/5
 * Time: 11:52
 * This is used to handle the information for affiliate
 */

namespace CoreBundle\Entity;

use CoreBundle\Utils\Money;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\Mapping as ORM;
use J<PERSON>\Serializer\Annotation as Serializer;
use SalexUserBundle\Entity\User;
use function Stringy\create as s;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class Affiliate
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\AffiliateRepository")
 * @ORM\Table(name="affiliate", options={"comment":"Affiliate details"})
 * @package CoreBundle\Entity
 * @ORM\HasLifecycleCallbacks()
 */
class Affiliate extends BaseEntity
{
    const NAME_REFER_A_FRIEND = 'Refer a Friend';

    const TYPE_LOAD_PARTNER = Tenant::TYPE_LOAD_PARTNER;
    const TYPE_KYC_PROVIDER = Tenant::TYPE_KYC_PROVIDER;
    const TYPE_MARKETING_PARTNER = Tenant::TYPE_MARKETING_PARTNER;
    const TYPE_BRAND_PARTNER = Tenant::TYPE_BRAND_PARTNER;
    const TYPE_RESHIPPER = Tenant::TYPE_RESHIPPER;
    const TYPE_USER = 'user';

    const PAYOUT_METHOD_CUSTOM = 'custom';
    const PAYOUT_METHOD_CONTACT = 'contact';

    public static function getAffIdFromRequest(Request $request)
    {
        $affId = $request->get('aff_id') ?: $request->get('tid');
        if (!$affId) {
            $affId = $request->cookies->get('usu_aff_id');
        }
        if ($affId) {
            if (Util::startsWith($affId, 'aff_id')) {
                $affId = str_replace('aff_id', '', $affId);
            }
        }
        return $affId;
    }

    public static function ensureAffIdCookie(Request $request, Response $response, $affId = null)
    {
        $affId = $affId ?: self::getAffIdFromRequest($request);
        if ($affId && !$request->cookies->has('usu_aff_id')) {
            // 2592000 = 30 * 24 * 3600 = 30 days
            $cookie = new Cookie('usu_aff_id', $affId, time() + 2592000);
            $response->headers->setCookie($cookie);
        }
        return $response;
    }

    public static function getTypes() {
        return [
            self::TYPE_LOAD_PARTNER,
            self::TYPE_KYC_PROVIDER,
            self::TYPE_MARKETING_PARTNER,
            self::TYPE_BRAND_PARTNER,
            self::TYPE_RESHIPPER,
        ];
    }

    public static function findAll() {
        $types = self::getTypes();
        $result = [];
        foreach ($types as $type) {
            $result[$type] = [];
        }

        $rs = Util::em()->getRepository(\CoreBundle\Entity\Affiliate::class)->findAll();
        foreach ($rs as $r) {
            $type = $r->getAffType();
            if (!isset($result[$type])) {
                $result[$type] = [];
            }
            $result[$type][] = $r;
        }
        return $result;
    }

    /**
     * @return static
     */
    public static function referFriend() {
        $all = Util::em()->getRepository(static::class)->findBy([
            'affName' => self::NAME_REFER_A_FRIEND,
        ], [], 1);
        return $all ? $all[0] : null;
    }

    /**
     * @param $code
     * @return static
     */
    public static function findByAffId($code)
    {
        $rs = Util::em()->getRepository(static::class)->findBy([
            'affId' => $code,
        ], [], 1);
        return $rs ? $rs[0] : null;
    }

    public static function isAffIdUsed($code, Affiliate $except = null)
    {
        $query = Util::em()->getRepository(static::class)
            ->createQueryBuilder('a')
            ->where('a.affId = :affId')
            ->setParameter('affId', $code);

        if ($except) {
            $query->andWhere('a <> :except')
                ->setParameter('except', $except);
        }

        $rs = $query->getQuery()
            ->setMaxResults(1)
            ->getResult();
        return count($rs) > 0;
    }

    public static function getClassOfType($type)
    {
        if (self::TYPE_USER === $type) {
            $class = User::class;
        } else {
            $class = Tenant::getClassOfType($type);
        }
        return $class;
    }

    public function isReferFriend()
    {
        return $this->getName() === self::NAME_REFER_A_FRIEND;
    }

    public function getName()
    {
        return $this->getAffName();
    }

    public function getType()
    {
        return $this->getAffType();
    }

    public function getClass()
    {
        $type = $this->getAffType();
        return self::getClassOfType($type);
    }

    public function getUnpaidAmountUSD()
    {
        $expr = Util::expr();
        $partner = LoadPartner::system();
        $types = ['membership', 'load'];
        $sum = 0;
        foreach ($types as $type) {
            $sum += Util::em()->getRepository(\CoreBundle\Entity\UserCardLoad::class)
                ->createQueryBuilder('ucl')
                ->join('ucl.userCard', 'uc')
                ->join('uc.user', 'u')
                ->where('u.affiliate = :affiliate')
                ->andWhere('ucl.affiliatePayment is null')
                ->andWhere($expr->gt('ucl.' . $type . 'CommissionUSD', ':min'))
                ->andWhere($expr->in('ucl.loadStatus', ':loadStatus'))
                ->andWhere('ucl.partner <> :partner')
                ->setParameter('affiliate', $this->getId())
                ->setParameter('min', 0)
                ->setParameter('partner', $partner)
                ->setParameter('loadStatus', UserCardLoad::RECEIVED_STATUS_ARRAY)
                ->select('sum(ucl.' . $type . 'CommissionUSD)')
                ->getQuery()
                ->getSingleScalarResult();
        }
        $prepaid = $this->getPrepaid() ?: 0;
        if ($sum <= $prepaid) {
            return 0;
        }
        return $sum - $prepaid;
    }

    public function getDomains()
    {
        $domains = Util::json($this, 'meta', 'domains') ?: [];
        $admin = $this->getAdmin();
        if ($admin) {
            $domains[] = Util::json($admin, 'meta', 'applyWebsite');
        }
        $tenant = $this->getTenant();
        if ($tenant) {
            $others = $tenant->getContacts()->map(function (TenantUser $tu) {
                $user = $tu->getUser();
                return Util::json($user, 'meta', 'applyWebsite');
            });
            $domains = array_merge($domains, $others->toArray());
        }
        return Util::unique(Util::filterArray($domains));
    }

    public function getRevenueSharesKeyedByType()
    {
        $shares = $this->getAffiliateRevenueShare();
        $result = [];
        /** @var AffiliateRevenueShare $share */
        foreach ($shares as $share) {
            $type = $share->getType();
            $result[$type] = $share;
        }
        return $result;
    }

    public function getAccessibleMaterials()
    {
        $cpIds = [];
        /** @var User $contact */
        foreach ($this->getContacts() as $contact) {
            /** @var CardProgram $cp */
            foreach ($contact->getOpenCardPrograms() as $cp) {
                $cpIds[] = $cp->getId();
            }
        }
        $expr = Util::expr();
        return Util::em()->getRepository(\CoreBundle\Entity\AffiliateMaterial::class)
            ->createQueryBuilder('am')
            ->where($expr->orX(
                $expr->isNull('am.owner'),
                $expr->in('am.owner', ':cpIds')
            ))
            ->setParameter('cpIds', array_unique($cpIds))
            ->orderBy('am.createdAt', 'desc')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get affName
     *
     * @return string
     */
    public function getAffName()
    {
        $name = $this->affName;
        if (null === $name) {
            $class = $this->getClass();
            $key = $this->getForeignKey();
            if ($class && $key) {
                $entity = Util::em()->getRepository($class)->find($key);
                if ($entity) {
                    return $entity->getName();
                }
            }
        }
        return $name;
    }

    /**
     * Get affType
     *
     * @param bool $human
     * @return string
     */
    public function getAffType($human = false)
    {
        $type = $this->affType;
        if ($human) {
            $type = s($type)->humanize();
        }
        return $type;
    }

    public function hit()
    {
        $request = Util::request();
        $ua = $request->headers->get('User-Agent', '');
        if (!$ua) {
            return;
        }

        if (Util::user()) {
            return;
        }

        // Don't save robots' hits
        foreach ([
            'robot', 'crawler', 'spider', 'AdsBot', 'bingbot',
            'SemrushBot', 'Googlebot',
            'Applebot', 'YandexBot', 'MJ12bot', 'AhrefsBot',
            'DotBot', 'SeznamBot', 'RU_Bot', 'MauiBot',
            'BLEXBot', 'AspiegelBot', 'TelegramBot', 'Go-http-client',
            'bitlybot', 'Bleriot', 'TwitterBot', 'CCBot', 'Exabot',
            'TurnitinBot', 'Qwantify',
        ] as $skip) {
            if (mb_stripos($ua, $skip) !== false) {
                return;
            }
        }

        $hit = new AffiliateHit();
        $hit->setAffiliate($this);
        $hit->setUrl($request->getUri());
        $hit->setTime(new \DateTime());
        $hit->setIp(Security::getClientIp());
        $hit->setUserAgent($ua);
        $hit->setReferer($request->headers->get('Referer', ''));
        Util::persist($hit);
    }

    public function pendingConsumerCount()
    {
        return Util::em()->getRepository(\SalexUserBundle\Entity\User::class)
            ->createQueryBuilder('user')
            ->where('user.affiliate = :affiliate')
            ->setParameter('affiliate', $this)
            ->select('count(distinct user)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function linkedConsumerCount()
    {
        return Util::em()->getRepository(\SalexUserBundle\Entity\User::class)
            ->createQueryBuilder('user')
            ->join('user.cards', 'uc')
            ->join('uc.loads', 'ucl')
            ->where('user.affiliate = :affiliate')
            ->andWhere(Util::expr()->in('ucl.loadStatus', ':loadStatuses'))
            ->setParameter('affiliate', $this)
            ->setParameter('loadStatuses', UserCardLoad::RECEIVED_STATUS_ARRAY)
            ->select('count(distinct user)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get language
     *
     * @return string
     */
    public function getLanguage()
    {
        return $this->language ?: 'en';
    }

    /**
     * @ORM\PrePersist()
     */
    public function onPersist()
    {
        $this->affId = Util::guid();
    }

    /**
     * Set tenant
     *
     * @param \CoreBundle\Entity\Tenant $tenant
     *
     * @return Affiliate
     */
    public function setTenant(\CoreBundle\Entity\Tenant $tenant = null)
    {
        $this->tenant = $tenant;

        if ($tenant) {
            $this->setForeignKey($tenant->getId())
                ->setAffType($tenant->getType());
        }

        return $this;
    }

    /**
     * Set admin
     *
     * @param \SalexUserBundle\Entity\User $admin
     *
     * @return Affiliate
     */
    public function setAdmin(\SalexUserBundle\Entity\User $admin = null)
    {
        $this->admin = $admin;

        if ($admin) {
            $this->setForeignKey($admin->getId())
                ->setAffType(self::TYPE_USER);
        }

        return $this;
    }

    public function getContacts()
    {
        $tenant = $this->getTenant();
        if ($tenant) {
            return $tenant->getContacts()->map(function (TenantUser $tu) {
                $u = $tu->getUser();
                $u->main = $tu->getMain();
                return $u;
            });
        }
        $items = new ArrayCollection();
        $admin = $this->getAdmin();
        if ($admin) {
            $admin->main = true;
            $items->add($admin);
        }
        return $items;
    }

    public function getPayoutMethodText()
    {
        $method = $this->getPayoutMethod();
        if ($method === self::PAYOUT_METHOD_CUSTOM) {
            return 'Custom field: ' . $this->getPayoutCustom();
        }
        if ($method === self::PAYOUT_METHOD_CONTACT) {
            $s = 'Contact: ';
            $user = $this->getPayoutContact();
            if ($user) {
                $s .= $user . ' (' . $user->getId() . ') - ';

                $ac = $user->getActiveCard();
                if ($ac ) {
                    $s .= $ac->getName();
                } else {
                    $s .= 'No card';
                }
            } else {
                $s .= 'Unset';
            }
            return $s;
        }
        return ucfirst($method);
    }

    public function getRevenueShareSummary() {
        $revenueInfo = $this->getAffiliateRevenueShare();
        $arr = array();
        /** @var AffiliateRevenueShare $revenue */
        foreach ($revenueInfo as $revenue)
        {
            $currency = $revenue->getCurrency();
            if (!$currency) {
                continue;
            }

            $a = Util::humanize($revenue->getType()) . ': ';
            $base = $a;
            if ($revenue->getCost()) {
                $a .= ' Cost ' . Money::format($revenue->getCost(), $currency);
            }
            if ($revenue->getFixed()) {
                $a .= ' Fixed ' . Money::format($revenue->getFixed(), $currency);
            }
            if ($revenue->getRatio()) {
                $a .= ' Ratio ' . $revenue->getRatio() . '%';
            }
            if ($revenue->getStart()) {
                $a .= ' Start from ' . $revenue->getStart() . ' cards';
            }
            if ($a === $base) {
                continue;
            }

            $arr[] = $a;
        }
        return implode('<br/>', $arr);
    }

    /**
     * @var string $affType
     * @ORM\Column(name="aff_type", type="string")
     */
    private $affType;

    /**
     * @var string
     *
     * @ORM\Column(name="foreign_key", type="string", length=255, nullable=true, options={"comment":"ID of `talent`, `user` or maybe other tables, according to `aff_type`."})
     */
    private $foreignKey;

    /**
     * @var string $affId
     * @ORM\Column(name="aff_id", type="string", length=36)
     */
    private $affId;

    /**
     * @var string $affName
     * @ORM\Column(name="aff_name", type="string", nullable=true)
     */
    private $affName;

    /**
     * Duplicated with foreign_key if the aff_type is a tenant.
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Tenant")
     */
    private $tenant;

    /**
     * @var User $admin
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User", inversedBy="managingAffiliates")
     * @Serializer\Exclude()
     */
    private $admin;

    /**
     * @ORM\OneToMany(targetEntity="AffiliateRevenueShare" , mappedBy="affiliate" , cascade={"all"} , orphanRemoval=true)
     */
    private $affiliateRevenueShare;

    /**
     * @var ArrayCollection $users
     * @ORM\OneToMany(targetEntity="SalexUserBundle\Entity\User" , mappedBy="affiliate")
     * @Serializer\Exclude()
     */
    private $users;

    /**
     * @var ArrayCollection $hits
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\AffiliateHit" , mappedBy="affiliate")
     * @Serializer\Exclude()
     */
    private $hits;

    /**
     * @var ArrayCollection $reshippers
     * @ORM\OneToMany(targetEntity="CoreBundle\Entity\AffiliateReshipper" , mappedBy="affiliate")
     * @Serializer\Exclude()
     */
    private $reshippers;

    /**
     * @var string $language
     * @ORM\Column(name="language", type="string", nullable=true)
     */
    private $language;

    /**
     * @var string $payoutMethod
     * @ORM\Column(name="payout_method", type="string", nullable=true, options={"comment":"contact or custom"})
     */
    private $payoutMethod;

    /**
     * @var string $payoutCustom
     * @ORM\Column(name="payout_custom", type="text", nullable=true, options={"comment":"Custom way via which to pay commission."})
     */
    private $payoutCustom;

    /**
     * @var User $payoutContact
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $payoutContact;

    /**
     * @var Collection $payments
     * @ORM\OneToMany(targetEntity="AffiliatePayment", mappedBy="affiliate")
     */
    private $payments;

    /**
     * @var int
     *
     * @ORM\Column(name="prepaid", type="bigint", nullable=true, options={"comment":"Prepaid money to affiliate. Used to subtract when calculating commission."})
     */
    private $prepaid;

    /**
     * @var string
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * Set affType
     *
     * @param string $affType
     *
     * @return Affiliate
     */
    private function setAffType($affType)
    {
        $this->affType = $affType;

        return $this;
    }

    /**
     * Set affId
     *
     * @param integer $affId
     *
     * @return Affiliate
     */
    public function setAffId($affId)
    {
        $this->affId = $affId;

        return $this;
    }

    /**
     * Get affId
     *
     * @return integer
     */
    public function getAffId()
    {
        return $this->affId;
    }

    /**
     * Set affName
     *
     * @param string $affName
     *
     * @return Affiliate
     */
    public function setAffName($affName)
    {
        $this->affName = $affName;

        return $this;
    }

    /**
     * Set foreignKey
     *
     * @param string $foreignKey
     *
     * @return Affiliate
     */
    private function setForeignKey($foreignKey)
    {
        $this->foreignKey = $foreignKey;

        return $this;
    }

    /**
     * Get foreignKey
     *
     * @return string
     */
    public function getForeignKey()
    {
        return $this->foreignKey;
    }
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->affiliateRevenueShare = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add affiliateRevenueShare
     *
     * @param \CoreBundle\Entity\AffiliateRevenueShare $affiliateRevenueShare
     *
     * @return Affiliate
     */
    public function addAffiliateRevenueShare(\CoreBundle\Entity\AffiliateRevenueShare $affiliateRevenueShare)
    {
        $this->affiliateRevenueShare[] = $affiliateRevenueShare;

        return $this;
    }

    /**
     * Remove affiliateRevenueShare
     *
     * @param \CoreBundle\Entity\AffiliateRevenueShare $affiliateRevenueShare
     */
    public function removeAffiliateRevenueShare(\CoreBundle\Entity\AffiliateRevenueShare $affiliateRevenueShare)
    {
        $this->affiliateRevenueShare->removeElement($affiliateRevenueShare);
    }

    /**
     * Get affiliateRevenueShare
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getAffiliateRevenueShare()
    {
        return $this->affiliateRevenueShare;
    }

    /**
     * Add user
     *
     * @param \SalexUserBundle\Entity\User $user
     *
     * @return Affiliate
     */
    public function addUser(\SalexUserBundle\Entity\User $user)
    {
        $this->users[] = $user;

        return $this;
    }

    /**
     * Remove user
     *
     * @param \SalexUserBundle\Entity\User $user
     */
    public function removeUser(\SalexUserBundle\Entity\User $user)
    {
        $this->users->removeElement($user);
    }

    /**
     * Get users
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * Add hit
     *
     * @param \CoreBundle\Entity\AffiliateHit $hit
     *
     * @return Affiliate
     */
    public function addHit(\CoreBundle\Entity\AffiliateHit $hit)
    {
        $this->hits[] = $hit;

        return $this;
    }

    /**
     * Remove hit
     *
     * @param \CoreBundle\Entity\AffiliateHit $hit
     */
    public function removeHit(\CoreBundle\Entity\AffiliateHit $hit)
    {
        $this->hits->removeElement($hit);
    }

    /**
     * Get hits
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getHits()
    {
        return $this->hits;
    }

    /**
     * Add reshipper
     *
     * @param \CoreBundle\Entity\AffiliateReshipper $reshipper
     *
     * @return Affiliate
     */
    public function addReshipper(\CoreBundle\Entity\AffiliateReshipper $reshipper)
    {
        $this->reshippers[] = $reshipper;

        return $this;
    }

    /**
     * Remove reshipper
     *
     * @param \CoreBundle\Entity\AffiliateReshipper $reshipper
     */
    public function removeReshipper(\CoreBundle\Entity\AffiliateReshipper $reshipper)
    {
        $this->reshippers->removeElement($reshipper);
    }

    /**
     * Get reshippers
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getReshippers()
    {
        return $this->reshippers;
    }

    /**
     * Get tenant
     *
     * @return \CoreBundle\Entity\Tenant
     */
    public function getTenant()
    {
        return $this->tenant;
    }

    /**
     * Set payoutMethod
     *
     * @param string $payoutMethod
     *
     * @return Affiliate
     */
    public function setPayoutMethod($payoutMethod)
    {
        $this->payoutMethod = $payoutMethod;

        return $this;
    }

    /**
     * Get payoutMethod
     *
     * @return string
     */
    public function getPayoutMethod()
    {
        return $this->payoutMethod;
    }

    /**
     * Set payoutCustom
     *
     * @param string $payoutCustom
     *
     * @return Affiliate
     */
    public function setPayoutCustom($payoutCustom)
    {
        $this->payoutCustom = $payoutCustom;

        return $this;
    }

    /**
     * Get payoutCustom
     *
     * @return string
     */
    public function getPayoutCustom()
    {
        return $this->payoutCustom;
    }

    /**
     * Add payment
     *
     * @param \CoreBundle\Entity\AffiliatePayment $payment
     *
     * @return Affiliate
     */
    public function addPayment(\CoreBundle\Entity\AffiliatePayment $payment)
    {
        $this->payments[] = $payment;

        return $this;
    }

    /**
     * Remove payment
     *
     * @param \CoreBundle\Entity\AffiliatePayment $payment
     */
    public function removePayment(\CoreBundle\Entity\AffiliatePayment $payment)
    {
        $this->payments->removeElement($payment);
    }

    /**
     * Get payments
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getPayments()
    {
        return $this->payments;
    }

    /**
     * Get admin
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getAdmin()
    {
        return $this->admin;
    }

    /**
     * Set payoutContact
     *
     * @param \SalexUserBundle\Entity\User $payoutContact
     *
     * @return Affiliate
     */
    public function setPayoutContact(\SalexUserBundle\Entity\User $payoutContact = null)
    {
        $this->payoutContact = $payoutContact;

        return $this;
    }

    /**
     * Get payoutContact
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getPayoutContact()
    {
        return $this->payoutContact;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return Affiliate
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set prepaid
     *
     * @param integer $prepaid
     *
     * @return Affiliate
     */
    public function setPrepaid($prepaid)
    {
        $this->prepaid = $prepaid;

        return $this;
    }

    /**
     * Get prepaid
     *
     * @return integer
     */
    public function getPrepaid()
    {
        return $this->prepaid;
    }

    /**
     * Set language
     *
     * @param string $language
     *
     * @return Affiliate
     */
    public function setLanguage($language)
    {
        $this->language = $language;

        return $this;
    }
}
