<?php

namespace CoreBundle\Entity;

use CoreBundle\Utils\Util;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use SalexUserBundle\Entity\User;

/**
 * AffiliateApply
 *
 * @ORM\Table(name="affiliate_apply", options={"comment":"Used to save/handle request from https://www.usunlocked.com/become-referral-partner/"})
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\AffiliateApplyRepository")
 * @Gedmo\SoftDeleteable()
 */
class AffiliateApply
{
    use SoftDeleteableEntity;

    const STATUS_PENDING = null;
    const STATUS_DECLINED = 'declined';
    const STATUS_APPROVED = 'approved';

    public static function createFromUser(User $user)
    {
        $a = new self();
        $a->setFirstName($user->getFirstName());
        $a->setLastName($user->getLastName());
        $a->setEmail($user->getEmail());
        $a->setPhone($user->getPhone());
        $a->setStreetAddress($user->getAddress());
        $a->setAddressLine2($user->getAddressline());
        $a->setCity($user->getCity());
        $a->setState($user->getStateName());
        $a->setZip($user->getZip());
        $a->setCountry($user->getCountryName());

        foreach ([
            'CompanyName', 'Skype', 'Website',
            'Audience', 'MonthlyVisitors', 'Facebook',
            'LinkedIn', 'Twitter', 'Instagram'
        ] as $item) {
            $method = "set{$item}";
            $a->$method(Util::json($user, 'meta', 'apply' . $item));
        }

        $applyId = Util::json($user, 'meta', 'affiliateApply');
        $repo = Util::em()->getRepository(\CoreBundle\Entity\AffiliateApply::class);
        $apply = null;
        if ($applyId) {
            $apply = $repo->find($applyId);
        }

        if (!$apply) {
            $rs = $repo->findBy(['user' => $user->getId()]);
            if ($rs) {
                $apply = $rs[0];
            }
        }

        if ($apply) {
            $a->setAcceptAt($apply->getAcceptAt());
        }

        return $a;
    }

    public static function updateUser(User $a, array $data)
    {
        $a->setFirstName($data['firstName']);
        $a->setLastName($data['lastName']);
        $a->setUsername($data['email']);
        $a->setEmail($data['email']);
        $a->setPhone($data['phone']);
        $a->setAddress($data['streetAddress']);
        $a->setAddressline($data['addressLine2']);
        $a->setCity($data['city']);

        $country = $data['country'] ? Util::em()->getRepository(\CoreBundle\Entity\Country::class)->findOneBy([
            'name' => $data['country'],
        ]) : null;
        $a->setCountry($country);

        $state = $country && $data['state'] ? $country->findStateByName($data['state']) : null;
        $a->setState($state);

        $a->setZip($data['zip']);

        foreach ([
            'CompanyName', 'Skype', 'Website',
            'Audience', 'MonthlyVisitors', 'Facebook',
            'LinkedIn', 'Twitter', 'Instagram'
        ] as $item) {
            $d = $data[lcfirst($item)];
            Util::updateJson($a, 'meta', [
                'apply' . $item => $d,
            ], false);
        }
    }

    public function connectToUser(User $a)
    {
        Util::updateJson($a, 'meta', [
            'affiliateApply' => $this->getId(),
        ], false);

        foreach ([
                     'CompanyName', 'Skype', 'Website',
                     'Audience', 'MonthlyVisitors', 'Facebook',
                     'LinkedIn', 'Twitter', 'Instagram'
                 ] as $item) {
            $method = "get{$item}";
            Util::updateJson($a, 'meta', [
                'apply' . $item => $this->$method(),
            ], false);
        }

        Util::persist($a);
    }

    public function createUser()
    {
        $a = new User();
        $a->setEnabled(true);
        $a->setFirstName($this->getFirstName());
        $a->setLastName($this->getLastName());
        $a->setUsername($this->getEmail());
        $a->setPassword(Util::encodePassword($a, 'Span1234'));
        $a->setEmail($this->getEmail());
        $a->setPhone($this->getPhone());
        $a->setAddress($this->getStreetAddress());
        $a->setAddressline($this->getAddressLine2());
        $a->setCity($this->getCity());
        $a->setSource('usu_affiliate_apply');

        $country = Util::em()->getRepository(\CoreBundle\Entity\Country::class)
            ->findOneBy([
                'name' => $this->getCountry(),
            ]);
        $a->setCountry($country);

        if ($country) {
            $state = $country->findStateByName($this->getState());
            $a->setState($state);
        }

        $a->setZip($this->getZip());

        $this->connectToUser($a);

        return $a;
    }

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="firstName", type="string", length=255)
     */
    private $firstName;

    /**
     * @var string
     *
     * @ORM\Column(name="lastName", type="string", length=255)
     */
    private $lastName;

    /**
     * @var string
     *
     * @ORM\Column(name="email", type="string", length=255)
     */
    private $email;

    /**
     * @var string
     *
     * @ORM\Column(name="companyName", type="string", length=255)
     */
    private $companyName;

    /**
     * @var string
     *
     * @ORM\Column(name="skype", type="string", length=255, nullable=true)
     */
    private $skype;

    /**
     * @var string
     *
     * @ORM\Column(name="phone", type="string", length=255, nullable=true)
     */
    private $phone;

    /**
     * @var string
     *
     * @ORM\Column(name="website", type="string", length=255, nullable=true)
     */
    private $website;

    /**
     * @var string
     *
     * @ORM\Column(name="street_address", type="string", length=255, nullable=true)
     */
    private $streetAddress;

    /**
     * @var string
     *
     * @ORM\Column(name="address_line2", type="string", length=255, nullable=true)
     */
    private $addressLine2;

    /**
     * @var string
     *
     * @ORM\Column(name="city", type="string", length=255, nullable=true)
     */
    private $city;

    /**
     * @var string
     *
     * @ORM\Column(name="state", type="string", length=255, nullable=true)
     */
    private $state;

    /**
     * @var string
     *
     * @ORM\Column(name="zip", type="string", length=255, nullable=true)
     */
    private $zip;

    /**
     * @var string
     *
     * @ORM\Column(name="country", type="string", length=255, nullable=true)
     */
    private $country;

    /**
     * @var string
     *
     * @ORM\Column(name="audience", type="string", length=1023, nullable=true)
     */
    private $audience;

    /**
     * @var string
     *
     * @ORM\Column(name="monthly_visitors", type="string", length=255, nullable=true)
     */
    private $monthlyVisitors;

    /**
     * @var string
     *
     * @ORM\Column(name="facebook", type="string", length=255, nullable=true)
     */
    private $facebook;

    /**
     * @var string
     *
     * @ORM\Column(name="linked_in", type="string", length=255, nullable=true)
     */
    private $linkedIn;

    /**
     * @var string
     *
     * @ORM\Column(name="twitter", type="string", length=255, nullable=true)
     */
    private $twitter;

    /**
     * @var string
     *
     * @ORM\Column(name="instagram", type="string", length=255, nullable=true)
     */
    private $instagram;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="time", type="datetime")
     */
    private $time;

    /**
     * @var string
     *
     * @ORM\Column(name="ip", type="string", length=255, nullable=true)
     */
    private $ip;

    /**
     * @var string
     *
     * @ORM\Column(name="user_agent", type="string", length=1023, nullable=true)
     */
    private $userAgent;

    /**
     * The time when applier accept the terms and conditions
     *
     * @var \DateTime
     *
     * @ORM\Column(name="accept_at", type="datetime", nullable=true)
     */
    private $acceptAt;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=255, nullable=true, options={"comment":"declined, approved or null"})
     */
    private $status;

    /**
     * Created or connected affiliate user when the request is approved
     *
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="SalexUserBundle\Entity\User")
     */
    private $user;

    /**
     * Created or connected affiliate when the request is approved
     *
     * @var Affiliate
     *
     * @ORM\ManyToOne(targetEntity="CoreBundle\Entity\Affiliate")
     */
    private $affiliate;

    /**
     * @var string
     *
     * @ORM\Column(name="meta", type="text", nullable=true)
     */
    private $meta;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set firstName
     *
     * @param string $firstName
     *
     * @return AffiliateApply
     */
    public function setFirstName($firstName)
    {
        $this->firstName = $firstName;

        return $this;
    }

    /**
     * Get firstName
     *
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * Set lastName
     *
     * @param string $lastName
     *
     * @return AffiliateApply
     */
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;

        return $this;
    }

    /**
     * Get lastName
     *
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * Set email
     *
     * @param string $email
     *
     * @return AffiliateApply
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set companyName
     *
     * @param string $companyName
     *
     * @return AffiliateApply
     */
    public function setCompanyName($companyName)
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * Get companyName
     *
     * @return string
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * Set skype
     *
     * @param string $skype
     *
     * @return AffiliateApply
     */
    public function setSkype($skype)
    {
        $this->skype = $skype;

        return $this;
    }

    /**
     * Get skype
     *
     * @return string
     */
    public function getSkype()
    {
        return $this->skype;
    }

    /**
     * Set phone
     *
     * @param string $phone
     *
     * @return AffiliateApply
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Get phone
     *
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * Set website
     *
     * @param string $website
     *
     * @return AffiliateApply
     */
    public function setWebsite($website)
    {
        $this->website = $website;

        return $this;
    }

    /**
     * Get website
     *
     * @return string
     */
    public function getWebsite()
    {
        return $this->website;
    }

    /**
     * Set streetAddress
     *
     * @param string $streetAddress
     *
     * @return AffiliateApply
     */
    public function setStreetAddress($streetAddress)
    {
        $this->streetAddress = $streetAddress;

        return $this;
    }

    /**
     * Get streetAddress
     *
     * @return string
     */
    public function getStreetAddress()
    {
        return $this->streetAddress;
    }

    /**
     * Set addressLine2
     *
     * @param string $addressLine2
     *
     * @return AffiliateApply
     */
    public function setAddressLine2($addressLine2)
    {
        $this->addressLine2 = $addressLine2;

        return $this;
    }

    /**
     * Get addressLine2
     *
     * @return string
     */
    public function getAddressLine2()
    {
        return $this->addressLine2;
    }

    /**
     * Set city
     *
     * @param string $city
     *
     * @return AffiliateApply
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get city
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set state
     *
     * @param string $state
     *
     * @return AffiliateApply
     */
    public function setState($state)
    {
        $this->state = $state;

        return $this;
    }

    /**
     * Get state
     *
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * Set zip
     *
     * @param string $zip
     *
     * @return AffiliateApply
     */
    public function setZip($zip)
    {
        $this->zip = $zip;

        return $this;
    }

    /**
     * Get zip
     *
     * @return string
     */
    public function getZip()
    {
        return $this->zip;
    }

    /**
     * Set country
     *
     * @param string $country
     *
     * @return AffiliateApply
     */
    public function setCountry($country)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Get country
     *
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set audience
     *
     * @param string $audience
     *
     * @return AffiliateApply
     */
    public function setAudience($audience)
    {
        $this->audience = $audience;

        return $this;
    }

    /**
     * Get audience
     *
     * @return string
     */
    public function getAudience()
    {
        return $this->audience;
    }

    /**
     * Set monthlyVisitors
     *
     * @param string $monthlyVisitors
     *
     * @return AffiliateApply
     */
    public function setMonthlyVisitors($monthlyVisitors)
    {
        $this->monthlyVisitors = $monthlyVisitors;

        return $this;
    }

    /**
     * Get monthlyVisitors
     *
     * @return string
     */
    public function getMonthlyVisitors()
    {
        return $this->monthlyVisitors;
    }

    /**
     * Set facebook
     *
     * @param string $facebook
     *
     * @return AffiliateApply
     */
    public function setFacebook($facebook)
    {
        $this->facebook = $facebook;

        return $this;
    }

    /**
     * Get facebook
     *
     * @return string
     */
    public function getFacebook()
    {
        return $this->facebook;
    }

    /**
     * Set linkedIn
     *
     * @param string $linkedIn
     *
     * @return AffiliateApply
     */
    public function setLinkedIn($linkedIn)
    {
        $this->linkedIn = $linkedIn;

        return $this;
    }

    /**
     * Get linkedIn
     *
     * @return string
     */
    public function getLinkedIn()
    {
        return $this->linkedIn;
    }

    /**
     * Set twitter
     *
     * @param string $twitter
     *
     * @return AffiliateApply
     */
    public function setTwitter($twitter)
    {
        $this->twitter = $twitter;

        return $this;
    }

    /**
     * Get twitter
     *
     * @return string
     */
    public function getTwitter()
    {
        return $this->twitter;
    }

    /**
     * Set instagram
     *
     * @param string $instagram
     *
     * @return AffiliateApply
     */
    public function setInstagram($instagram)
    {
        $this->instagram = $instagram;

        return $this;
    }

    /**
     * Get instagram
     *
     * @return string
     */
    public function getInstagram()
    {
        return $this->instagram;
    }

    /**
     * Set time
     *
     * @param \DateTime $time
     *
     * @return AffiliateApply
     */
    public function setTime($time)
    {
        $this->time = $time;

        return $this;
    }

    /**
     * Get time
     *
     * @return \DateTime
     */
    public function getTime()
    {
        return $this->time;
    }

    /**
     * Set ip
     *
     * @param string $ip
     *
     * @return AffiliateApply
     */
    public function setIp($ip)
    {
        $this->ip = $ip;

        return $this;
    }

    /**
     * Get ip
     *
     * @return string
     */
    public function getIp()
    {
        return $this->ip;
    }

    /**
     * Set userAgent
     *
     * @param string $userAgent
     *
     * @return AffiliateApply
     */
    public function setUserAgent($userAgent)
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    /**
     * Get userAgent
     *
     * @return string
     */
    public function getUserAgent()
    {
        return $this->userAgent;
    }

    /**
     * Set meta
     *
     * @param string $meta
     *
     * @return AffiliateApply
     */
    public function setMeta($meta)
    {
        $this->meta = $meta;

        return $this;
    }

    /**
     * Get meta
     *
     * @return string
     */
    public function getMeta()
    {
        return $this->meta;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return AffiliateApply
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set user
     *
     * @param \SalexUserBundle\Entity\User $user
     *
     * @return AffiliateApply
     */
    public function setUser(\SalexUserBundle\Entity\User $user = null)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return \SalexUserBundle\Entity\User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set affiliate
     *
     * @param \CoreBundle\Entity\Affiliate $affiliate
     *
     * @return AffiliateApply
     */
    public function setAffiliate(\CoreBundle\Entity\Affiliate $affiliate = null)
    {
        $this->affiliate = $affiliate;

        return $this;
    }

    /**
     * Get affiliate
     *
     * @return \CoreBundle\Entity\Affiliate
     */
    public function getAffiliate()
    {
        return $this->affiliate;
    }

    /**
     * Set acceptAt
     *
     * @param \DateTime $acceptAt
     *
     * @return AffiliateApply
     */
    public function setAcceptAt($acceptAt)
    {
        $this->acceptAt = $acceptAt;

        return $this;
    }

    /**
     * Get acceptAt
     *
     * @return \DateTime
     */
    public function getAcceptAt()
    {
        return $this->acceptAt;
    }
}
