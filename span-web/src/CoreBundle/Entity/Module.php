<?php

namespace CoreBundle\Entity;

use CoreBundle\Utils\Traits\ConstantTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * Module
 *
 * @ORM\Table(name="module")
 * @ORM\Entity(repositoryClass="CoreBundle\Repository\ModuleRepository")
 */
class Module
{
    use ConstantTrait;

    const ID_DASHBOARD = 'dashboard';
    const ID_USER_MANAGEMENT = 'user_managment';
    const ID_TENANT_MANAGEMENT = 'tenant_management';
    const ID_AFFILIATE_MANAGEMENT = 'affiliate_management';
    const ID_MARKETING_MANAGEMENT = 'marketing_management';
    const ID_FEE_MANAGEMENT = 'fee_management';
    const ID_CARD_MANAGEMENT = 'card_management';
    const ID_MERCHANT_MANAGEMENT = 'merchant_management';
    const ID_CARD_PROGRAM_WIZARD = 'card_program_wizard';
    const ID_TRANSACTIONS = 'transactions';
    const ID_REPORTS = 'reports';
    const ID_MONITOR = 'monitor';
    const ID_SYSTEM_SETTING = 'system_setting';
    const ID_DEVELOPER = 'developer';

    const ID_USER = 'user';
    const ID_USER_LOGIN_AS_A = 'user_managment__user__login_at';
    const ID_USER_ADD_USER = 'user_managment__user__add_user';
    const ID_USER_SMS_PIN_A = 'sms_pin';
    const ID_USER_UNDER_REVIEW = 'user_under_review';
    const ID_PROMOTION = 'promotion';
    const ID_ROLE = 'role';
    const ID_ID_DOCUMENT = 'id_document';
    const ID_KYC_EXCEPTION_POOL = 'kyc_exception_pool';

    const ID_PROCESSOR_MANAGEMENT = 'processor_management';
    const ID_PROGRAM_MANAGEMENT = 'program_management';
    const ID_LOAD_PARTNER_MANAGEMENT = 'load_partner_management';
    const ID_LOAD_METHOD_MANAGEMENT = 'load_method_management';
    const ID_KYC_PROVIDER_MANAGEMENT = 'kyc_provider_management';
    const ID_COUNTRY_MANAGEMENT = 'country_management';
    const ID_CURRENCY_MANAGEMENT = 'currency_management';
    const ID_ISSUING_BANK_MANAGEMENT = 'issuing_bank_management';
    const ID_BRAND_PARTNER_MANAGEMENT = 'brand_partner_management';
    const ID_MARKETING_PARTNER_MANAGEMENT = 'marketing_partner_management';
    const ID_RESHIPPER_MANAGEMENT = 'reshipper_management';
    const ID_SERVICE_MANAGEMENT = 'service_management';
    const ID_PLATFORM_MANAGEMENT = 'platform_management';

    const ID_AFFILIATE = 'affiliate';
    const ID_AFFILIATE_APPLICATIONS = 'affiliate_applications';
    const ID_AFFILIATE_PAYMENTS = 'affiliate_payments';
    const ID_AFFILIATE_MATERIALS = 'affiliate_materials';

    const ID_FEE_CATEGORY = 'fee_category';
    const ID_FEE_GLOBAL_NAME = 'fee_global_name';
    const ID_FEE_OPTION = 'fee_option';
    const ID_FEE_SCHEDULE = 'fee_schedule';
    const ID_FEE_HISTORY = 'fee_history';

    const ID_CARD_TYPE = 'card_type';
    const ID_CARD_LIST = 'card_list';
    const ID_CARD_INVENTORY = 'card_inventory';
    const ID_CARD_LOAD_UNLOAD = 'card_load_unload';
    const ID_CARD_TRANSFER = 'card_transfer';
    const ID_CARD_TRANSACTIONS = 'card_transactions';

    const ID_CARD_HOLDER_MANAGEMENT = 'cardholder_management';
    const ID_INSTANT_REGISTRATION = 'instant_registration';

    const ID_DEVELOPER_RESOURCES = 'developer_resources';
    const ID_DEV_DOCUMENTATION = 'dev_documentation';

    const ID_MERCHANT_LIST = 'merchant_list';
    const ID_MERCHANT_LIST_TYPE = 'merchant_list_type';
    const ID_MERCHANT_MCC = 'merchant_mcc';

    const ID_SET_UP_WIZARD = 'set_up_wizard';
    const ID_CARD_PROGRAM_LIST = 'card_program_list';
    const ID_CARD_PROGRAM_OWNERS = 'card_program_owners';

    const ID_LOAD_TRANSACTION_LIST = 'load_transaction_list';
    const ID_LOAD_QUEUE = 'load_queue';
    const ID_VOIDING_QUEUE = 'voiding_queue';
    const ID_RETURN_QUEUE = 'return_queue';
    const ID_REFUND_LIST = 'refund_list';
    const ID_CARD_TRANSACTION_LIST = 'card_transaction_list';
    const ID_CARD_DECLINE_LIST = 'card_decline_list';
    const ID_ALERT_LIST = 'alert_list';
    const ID_ALERT_JOB_LIST = 'alert_job_list';

    const ID_PARTNER = 'partner';

    const ID_ERROR_MONITOR = 'errors_report';

    const ID_KEYMETRICS_REPORT = 'keymetrics_report';
    const ID_AFFILIATE_REPORT = 'affiliate_report';
    const ID_VELOCITY_REPORT = 'velocity_report';
    const ID_CARD_ACTIVITY_REPORT = 'card_activity_report';
    const ID_REVENUE_REPORT = 'card_revenue_report';
    const ID_REPORT_SCHEDULE = 'report_schedule';
    const ID_FEE_REPORT = 'fee_report';
    const ID_NEGATIVE_BALANCE_REPORT = 'negative_balance_report';
    const ID_PRIVACY_CARD_CREATION_REPORT = 'privacy_card_creation_report';
    const ID_LOADS_PER_COUNTRY_REPORT = 'members_load_per_country_report';
    const ID_PROMOTION_REPORT = 'promotion_report';

    const ID_ANALYTIC_MANAGEMENT = 'analytic_management';
    const ID_ACTIVE_SESSIONS = 'active_sessions';

    const ID_EMAIL_HISTORY = 'email_history';
    const ID_EXTERNAL_INVOKE = 'external_invoke';
    const ID_IP_WHITE_LIST = 'ip_white_list';
    const ID_USER_STATUS_LIST = 'user_status_list';
    const ID_BANNED_REASON = 'banned_reason';
    const ID_CLOSURE_REASON = 'closure_reason';
    const ID_VELOCITY_SETTING = 'velocity_setting';
    const ID_LANGUAGE_LIST = 'language_list';
    const ID_GLOBAL_SETTING = 'global_settings';
    const ID_GLOBAL_CHART_SETTING = 'global_chart_settings';
    const ID_PLATFORM_BRANDING = 'platform_branding';
    const ID_KYC_CHECK = 'kyc_check';

    /*
     * CLF
     */
    const ID_CLF_BANKING  = 'clf_banking';
    const ID_CLF_PATIENTS = 'clf_patients';
    const ID_CLF_DISPENSARIES = 'clf_dispensaries';
    const ID_CLF_BILL_PAY = 'clf_bill_pay';
    const ID_CLF_EMPLOYEE = 'clf_employee';
    const ID_CLF_SETTINGS = 'clf_settings';
    const ID_CLF_TRANSACTIONS = 'clf_transactions';
    const ID_CLF_TRANSACTIONS_REPORT = 'clf_transactions_report';
    const ID_CLF_TRANSACTIONS_NEW = 'clf_transactions_new';
    const ID_CLF_REPORTS = 'clf_reports';
    const ID_CLF_REPORTS_BANK_DISPENSARY_VOLUMES = 'clf_reports_bank_dispensary_volumes';

    /*
     * FIS
     */
    const ID_FIS_PORTFOLIO_REPORTING = 'fis_portfolio_reporting';
    const ID_FIS_BALANCE_REPORTING = 'fis_balance_reporting';
    const ID_FIS_USAGE_REPORTING = 'fis_usage_reporting';
    const ID_FIS_TRANSACTIONS_REPORTING = 'fis_transactions_reporting';
    const ID_FIS_LOADS_REPORTING = 'fis_loads_reporting';
    const ID_FIS_FEES_REPORTING = 'fis_fees_reporting';
    const ID_FIS_MONETARY_REPORTING = 'fis_monetary_reporting';
    const ID_FIS_AUTH_REPORTING = 'fis_auth_reporting';
    const ID_FIS_SPEND_REPORTING = 'fis_spend_reporting';
    const ID_FIS_DISPUTE_REPORTING = 'fis_dispute_reporting';
    const ID_FIS_NON_MONETARY_REPORTING = 'fis_none_monetary_reporting';
    const ID_FIS_NEGATIVE_BALANCE_REPORTING = 'fis_negative_balance_reporting';

    const ID_FIS_CASHBACK_REWARDS = 'Cashback Rewards';
    const ID_FIS_CASHBACK_CARDS = 'Opt-in Cards';
    const ID_FIS_CASHBACK_TRANSACTIONS = 'Cashback Transactions';

    const ID_FIS_MONITORING = 'fis_monitoring';

    const ID_FIS_SETTINGS = 'fis_settings';
    const ID_FIS_SETTINGS_PROGRAM_TYPES = 'fis_settings_program_types';
    const ID_FIS_SETTINGS_VELOCITY = 'fis_settings_velocity';
    const ID_FIS_SETTINGS_CHART = 'fis_settings_chart';

    const ID_FIS_CASHBACK_PROGRAMS = 'Cashback Programs';
    const ID_FIS_TRANSACTIONS = 'Transactions';

    /*
     * TransferMex
     */
    const ID_MEX_PROGRAM = 'mex_program';
    const ID_MEX_PROMO = 'mex_promo';
    const ID_MEX_AGENTS = 'mex_agents';
    const ID_MEX_EMPLOYERS = 'mex_employers';
    const ID_MEX_MEMBERS = 'mex_members';
    const ID_MEX_TRANSFERS = 'mex_transfers';
    const ID_MEX_TRANSFERS_MENU = 'mex_transfers_menu';
    const ID_MEX_TRANSFER_FUNDINGS = 'mex_transfer_fundings';
    const ID_MEX_UNITELLER_FUNDINGS = 'mex_uniteller_fundings';
    const ID_MEX_INTERMEX_FUNDINGS = 'mex_intermex_fundings';
    const ID_MEX_REPORT_REVENUE = 'mex_report_revenue';
    const ID_MEX_MESSAGE_CENTER = 'mex_message_center';
    const ID_MEX_MESSAGE_CENTER_BATCH = 'mex_message_center_batch';
    const ID_MEX_MESSAGE_CENTER_RECORD = 'mex_message_center_record';
    const ID_MEX_PLATFORM_REVENUE = 'mex_platform_revenue';
    const ID_MEX_PAYROLL_EXCEPTIONS = 'mex_payroll_exceptions';
    const ID_MEX_SETTINGS = 'mex_settings';
    const ID_MEX_SETTINGS_PAYERS = 'mex_settings_uniteller_payer';
    const ID_MEX_SETTINGS_LOCATION = 'mex_settings_location';
    const ID_MEX_SETTINGS_LOCATION_REQUEST = 'mex_settings_location_requests';
    const ID_MEX_SETTINGS_WEBVIEW_URLS = 'mex_settings_webview_urls';
    const ID_MEX_NOTIFIY_EMAIL = 'mex_settings_notify_email';
    const ID_MEX_SPLASH_PAGE = 'mex_settings_splash_page';
    const ID_MEX_TRANSACTION_GROUP = 'mex_settings_transaction_group';
    const ID_MEX_INTERMEX_PAYER = 'mex_settings_intermex_payer';
    const ID_MEX_ADMIN = 'mex_admin';
    const ID_MEX_ADMIN_RAPID_HISTORY = 'mex_admin_rapid_history';
    const ID_MEX_ADMIN_RAPYD_HISTORY = 'mex_admin_rapyd_history';
    const ID_MEX_ADMIN_UNITELLER_HISTORY = 'mex_admin_uniteller_histroy';
    const ID_MEX_MONTHLY_REPORT = 'mex_admin_monthly_report';
    const ID_MEX_MONTHLY_REPORT_PARTNER = 'mex_admin_monthly_report_partner';
    const ID_MEX_WORKERS_REPORT = 'mex_admin_workers_report';
    const ID_MEX_PAYROLL_REPORT = 'mex_admin_payroll_details_report';
    const ID_MEX_PAYROLL_BATCH_REPORT = 'mex_admin_payroll_batch_report';
    const ID_MEX_LOAD_TRANSFER_REPORT = 'mex_admin_load_transfer_report';
    const ID_MEX_PROGRAM_SUMMARY = 'mex_program_summary';
    const ID_MEX_PROGRAM_SUMMARY_PREFUND_ACH = 'mex_program_summary_prefund_ach';
    const ID_MEX_PROGRAM_SUMMARY_PREFUND_LOAD = 'mex_program_summary_prefund_load';
    const ID_MEX_PROGRAM_SUMMARY_EMPLOYER_ACH = 'mex_program_summary_employer_ach';
    const ID_MEX_PROGRAM_SUMMARY_TRANSFER = 'mex_program_summary_transfer';
    const ID_MEX_KYC_EXCEPTIONS = 'mex_kyc_exceptions';
    const ID_MEX_ADMIN_DEPOSIT_HISTORY = 'mex_employer_deposit';
    const ID_MEX_INACTIVE_MEMBER_FEE_REPORT = 'mex_inactive_member_fee';

    /*
     * TransferMex Employer dashboard
     */
    const ID_MEX_EMPLOYER_EMPLOYEE = 'mex_employer_employee';
    const ID_MEX_EMPLOYER_PAYMENTS = 'mex_employer_payments';
    const ID_MEX_EMPLOYER_DEPOSITS = 'mex-bank-icon';
    const ID_MEX_EMPLOYER_AGENT = 'mex_employer_agent';

    /*
    * Cash On web
    */
    const ID_CASH_AGENTS = 'cow_agents';
    const ID_CASH_DASHBOARD = 'cow_dashboard';
    const ID_CASH_SETTINGS = 'cow_settings';
    const ID_CASH_MEMBER = 'cow_members';
    const ID_CASH_FUNDING = 'cow_partner';
    const ID_CASH_PROGRAM = 'cow_program';
    const ID_CASH_REPORT = 'cow_reports';
    const ID_CASH_APPROVED = 'cash_report_approved';
    const ID_CASH_DECLINED = 'cash_report_declined';
    const ID_CASH_ACCOUNTS = 'cash_report_account';
    const ID_CASH_TRANSACTION = 'cash_report_transaction';
    const ID_CASH_LOADS = 'cash_report_loads';
    const ID_CASH_COMMISSIONS = 'cash_report_commissions';
    const ID_CASH_FEE = 'cash_report_fee';
    /*
     * Leaflink
     */
    const ID_LEAFLINK_DASHBOARD = 'leaflink_dashboard';
    const ID_LEAFLINK_ACCOUNTS = 'leaflink_accounts';
    const ID_LEAFLINK_PAYMENTS = 'leaflink_payments';
    const ID_LEAFLINK_PENDING = 'leaflink_pending';
    const ID_LEAFLINK_SYSTEM_MONITOR = 'leaflink_system_monitor';
    const ID_LEAFLINK_SETTINGS = 'leaflink_settings';
    /*
     * Wilen
     */
    const ID_WILEN_DASHBOARD = 'wilen_dashboard';
    const ID_WILEN_AGENTS = 'wilen_agents';
    const ID_WILEN_PROGRAMS = 'wilen_programs';
    const ID_WILEN_CAMPAIGNS = 'wilen_campaigns';
    const ID_WILEN_SETTING = 'wilen_settings';

    /*
    * FASS America voice
    */
    const ID_FAAS_PARTNER ='faas_partner';
    const ID_FAAS_PROGRAM ='faas_program';
    const ID_FAAS_AGENT = 'faas_agents';
    const ID_FAAS_CLIENT = 'faas_clients';
    const ID_FAAS_USERS = 'faas_users';
    const ID_FAAS_TRANSFERS = 'faas_transfers';
    const ID_FAAS_SETTING = 'faas_settings';
	/*
	 * FAAS Client dashboard
	 */
	const ID_FAAS_CLIENT_MEMBER = 'faas_client_member';
	const ID_FAAS_CLIENT_PAYMENTS = 'faas_client_payments';
	/*
	 * FAAS Member dashboard
	 */
	const ID_FAAS_MEMBER_TRANSACTIONS = 'faas_member_transactions';
	const ID_FAAS_MEMBER_SETTINGS= 'faas_member_settings';

    /*
     * Spendr
     */
    const ID_MERCHANT_ONBOARD_QUEUE = 'merchant_onboard_queue';
    const ID_BANK_LEDGER = 'bank_ledger';
    const ID_BALANCE_HISTORY = 'balance_history';
    const ID_ACH_FILES = 'ach_files';
    const ID_LOADS = 'loads';
    const ID_RECON_ACTIVITY = 'recon_activity';
    const ID_RECON_LEDGER = 'recon_ledger';
    const ID_SPENDR_SETTING = 'spendr_setting';
    const ID_SPENDR_SETTING_PLAID = 'spendr_setting_plaid';
    const ID_SPENDR_SETTING_FUZZY_MATCH = 'spendr_setting_fuzzy_match';
    const ID_SPENDR_SETTING_VERSION = 'spendr_setting_version';
    const ID_SPENDR_SETTING_TIPPING = 'spendr_setting_tipping';
    const ID_SPENDR_SETTING_REWARDS = 'spendr_setting_rewards';
    const ID_SPENDR_SETTING_REFERRAL = 'spendr_setting_referral';
    const ID_SPENDR_SETTING_RESTRICT = 'spendr_setting_restrict';
    const ID_SPENDR_SETTING_BANKCARD_BLACKLIST = 'spendr_setting_bankcard_blacklist';
    const ID_SPENDR_SETTING_USERNAME_BLACKLIST = 'spendr_setting_username_blacklist';
    const ID_SPENDR_MERCHANT_SETTING_AUTO_WITHDRAWAL = 'spendr_merchant_setting_auto_withdrawal';
    const ID_SPENDR_GROUP_LIST = 'spendr_group';

    const ID_LOGOUT = 'logout';

    const TREE = [
        [
            'id' => self::ID_DASHBOARD,
            'name' => 'Dashboard',
            'route' => 'admin_dashboard',
            'mdRoute' => 'dashboard',
            'icon' => 'fa fa-fw fa-dashboard',
            'mdIcon' => 'mdi-view-dashboard',
            'children' => [],
        ],
        [
            'id' => self::ID_USER_MANAGEMENT,
            'name' => 'User Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-users',
            'mdIcon' => 'mdi-account-multiple',
            'children' => [
                [
                    'id' => self::ID_USER,
                    'name' => 'User',
                    'route' => '',
                    'mdRoute' => 'user-management/user',
                ],
                [
                    'id' => self::ID_USER_UNDER_REVIEW,
                    'name' => 'Under review/watch list',
                    'route' => 'admin_user_under_review_index',
                ],
                [
                    'id' => self::ID_PROMOTION,
                    'name' => 'Promotion',
                    'route' => 'admin_promotion',
                ],
                [
                    'id' => self::ID_ROLE,
                    'name' => 'Role',
                    'route' => 'admin_role',
                ],
                [
                    'id' => self::ID_ID_DOCUMENT,
                    'name' => 'ID Document',
                    'route' => 'admin_id_document',
                ],
                [
                    'id' => self::ID_KYC_EXCEPTION_POOL,
                    'name' => 'KYC Exception Pool',
                    'route' => '',
                    'mdRoute' => 'user-management/kyc-exceptions'
                ],
            ],
        ],
        [
            'id' => self::ID_TENANT_MANAGEMENT,
            'name' => 'Tenant Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-institution',
            'mdIcon' => 'mdi-bank',
            'children' => [
                [
                    'id' => self::ID_PROCESSOR_MANAGEMENT,
                    'name' => 'Processor Management',
                    'route' => 'admin_processor',
                ],
                [
                    'id' => self::ID_PROGRAM_MANAGEMENT,
                    'name' => 'Program Manager',
                    'route' => 'admin_program',
                ],
                [
                    'id' => self::ID_LOAD_PARTNER_MANAGEMENT,
                    'name' => 'Load Partner',
                    'route' => 'admin_load_partner',
                ],
                [
                    'id' => self::ID_LOAD_METHOD_MANAGEMENT,
                    'name' => 'Load Method',
                    'route' => 'admin_load_method',
                ],
                [
                    'id' => self::ID_KYC_PROVIDER_MANAGEMENT,
                    'name' => 'KYC Provider',
                    'route' => 'admin_kyc_provider',
                ],
                [
                    'id' => self::ID_COUNTRY_MANAGEMENT,
                    'name' => 'Country & Currency',
                    'route' => 'admin_country',
                ],
                [
                    'id' => self::ID_CURRENCY_MANAGEMENT,
                    'name' => 'Currency Rate',
                    'route' => 'admin_currency',
                ],
                [
                    'id' => self::ID_ISSUING_BANK_MANAGEMENT,
                    'name' => 'Issuing Bank Management',
                    'route' => 'admin_issuing_bank',
                ],
                [
                    'id' => self::ID_BRAND_PARTNER_MANAGEMENT,
                    'name' => 'Brand Partner',
                    'route' => 'admin_brand_partner',
                ],
                [
                    'id' => self::ID_MARKETING_PARTNER_MANAGEMENT,
                    'name' => 'Marketing Partner',
                    'route' => 'admin_marketing_partner',
                ],
                [
                    'id' => self::ID_RESHIPPER_MANAGEMENT,
                    'name' => 'Reshipper Management',
                    'route' => 'admin_reshipper',
                ],
                [
                    'id' => self::ID_SERVICE_MANAGEMENT,
                    'name' => 'Service Management',
                    'route' => 'admin_service',
                ],
                [
                    'id' => self::ID_PLATFORM_MANAGEMENT,
                    'name' => 'Platform Management',
                    'route' => 'admin_platform',
                ],
            ],
        ],
        [
            'id' => self::ID_AFFILIATE_MANAGEMENT,
            'name' => 'Affiliate Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-link',
            'mdIcon' => 'mdi-link-variant',
            'children' => [
                [
                    'id' => self::ID_AFFILIATE,
                    'name' => 'Affiliates',
                    'route' => 'admin_affiliate',
                ],
                [
                    'id' => self::ID_AFFILIATE_APPLICATIONS,
                    'name' => 'Applications',
                    'route' => 'admin_affiliate_apply',
                ],
                [
                    'id' => self::ID_AFFILIATE_PAYMENTS,
                    'name' => 'Payment Queue / History',
                    'route' => 'admin_affiliate_payment',
                ],
                [
                    'id' => self::ID_AFFILIATE_MATERIALS,
                    'name' => 'Marketing Materials',
                    'route' => 'admin_affiliate_materials',
                ],
            ],
        ],
        [
            'id' => self::ID_MARKETING_MANAGEMENT,
            'name' => 'Marketing Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-gift',
            'mdIcon' => 'mdi-gift-outline',
            'children' => [
                [
                    'id' => self::ID_ALERT_LIST,
                    'name' => 'Alert Management',
                    'route' => '',
                    'mdRoute' => 'alert/search'
                ],
                [
                    'id' => self::ID_ALERT_JOB_LIST,
                    'name' => 'Alert Jobs',
                    'route' => '',
                    'mdRoute' => 'alert_job/search'
                ]
            ]
        ],
        [
            'id' => self::ID_FEE_MANAGEMENT,
            'name' => 'Fee Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-money',
            'mdIcon' => 'mdi-cash-usd',
            'children' => [
                [
                    'id' => self::ID_FEE_CATEGORY,
                    'name' => 'Category',
                    'route' => 'admin_fee_category',
                ],
                [
                    'id' => self::ID_FEE_GLOBAL_NAME,
                    'name' => 'Global Name',
                    'route' => 'admin_fee_global_name',
                ],
                [
                    'id' => self::ID_FEE_OPTION,
                    'name' => 'Tenant Fee Options',
                    'route' => 'admin_tenant_fee_options',
                ],
                [
                    'id' => self::ID_FEE_SCHEDULE,
                    'name' => 'Fee Engine',
                    'route' => 'admin_fee_engine_all',
                ],
                [
                    'id' => self::ID_FEE_SCHEDULE,
                    'name' => 'Fee Items',
                    'route' => 'admin_fee_item_all',
                ],
                [
                    'id' => self::ID_FEE_HISTORY,
                    'name' => 'Fee History',
                    'route' => '',
                    'mdRoute' => 'fee/history',
                ],
            ],
        ],
        [
            'id' => self::ID_CARD_MANAGEMENT,
            'name' => 'Card Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-credit-card',
            'mdIcon' => 'mdi-credit-card',
            'children' => [
                [
                    'id' => self::ID_CARD_TYPE,
                    'name' => 'Card Type Management',
                    'route' => 'admin_card_type',
                ],
                [
                    'id' => self::ID_CARD_LIST,
                    'name' => 'Cards Management',
                    'route' => '',
                    'mdRoute' => 'card-management/card'
                ],
                [
                    'id' => self::ID_CARD_INVENTORY,
                    'name' => 'Card Inventory',
                    'route' => '',
                    'mdRoute' => 'card-management/inventory'
                ],
            ],
        ],
        [
            'id' => self::ID_MERCHANT_MANAGEMENT,
            'name' => 'Merchant Management',
            'route' => '',
            'icon' => 'fa fa-fw fa-shopping-cart',
            'mdIcon' => 'mdi-cart',
            'children' => [
                [
                    'id' => self::ID_MERCHANT_LIST,
                    'name' => 'Merchant List',
                    'route' => 'admin_merchant',
                ],
                [
                    'id' => self::ID_MERCHANT_LIST_TYPE,
                    'name' => 'List Type',
                    'route' => 'admin_merchant_list_type',
                ],
                [
                    'id' => self::ID_MERCHANT_MCC,
                    'name' => 'MCC',
                    'route' => 'admin_merchant_mcc',
                ],
            ],
        ],
        [
            'id' => self::ID_CARD_PROGRAM_WIZARD,
            'name' => 'Card Program',
            'route' => '',
            'icon' => 'fa fa-fw fa-cubes',
            'mdIcon' => 'mdi-cube',
            'children' => [
                [
                    'id' => self::ID_SET_UP_WIZARD,
                    'name' => 'Set Up Wizard',
                    'route' => 'admin_card_program_wizard',
                ],
                [
                    'id' => self::ID_CARD_PROGRAM_LIST,
                    'name' => 'Card Program List',
                    'route' => 'admin_card_program',
                ],
            ],
        ],
        [
            'id' => self::ID_TRANSACTIONS,
            'name' => 'Transactions',
            'route' => '',
            'icon' => 'fa fa-fw fa-list',
            'mdIcon' => 'mdi-format-list-bulleted',
            'children' => [
                [
                    'id' => self::ID_LOAD_TRANSACTION_LIST,
                    'name' => 'Card Load Transactions',
                    'route' => '',
                    'mdRoute' => 'transactions/load',
                ],
                [
                    'id' => self::ID_LOAD_QUEUE,
                    'name' => 'Card Load Queue',
                    'route' => '',
                    'mdRoute' => 'transactions/load-queue',
                ],
                [
                    'id' => self::ID_REFUND_LIST,
                    'name' => 'Refund List / Queue',
                    'route' => '',
                    'mdRoute' => 'transactions/refund',
                ],
                [
                    'id' => self::ID_VOIDING_QUEUE,
                    'name' => 'Voiding Queue',
                    'route' => '',
                    'mdRoute' => 'transactions/voiding',
                ],
                [
                    'id' => self::ID_CARD_TRANSACTION_LIST,
                    'name' => 'Card Transaction List',
                    'route' => '',
                    'mdRoute' => 'transactions/list'
                ],
                [
                    'id' => self::ID_RETURN_QUEUE,
                    'name' => 'Return Queue',
                    'route' => '',
                    'mdRoute' => 'transactions/returning',
                ],
                [
                    'id' => self::ID_CARD_DECLINE_LIST,
                    'name' => 'Card Decline List',
                    'route' => '',
                    'mdRoute' => 'transactions/decline'
                ],
                [
                    'id' => 'force_posts',
                    'name' => 'Force Posts',
                    'route' => '',
                    'mdRoute' => 'transactions/force-posts'
                ],
            ],
        ],
        [
          'id' => self::ID_PARTNER,
          'name' => 'Partners',
          'route' => 'admin_partners',
          'mdRoute' => 'partners',
          'icon' => 'fa fa-fw fa-dashboard',
          'mdIcon' => 'mdi-view-dashboard',
          'children' => [],
        ],
        [
            'id' => self::ID_REPORTS,
            'name' => 'Reporting',
            'route' => '',
            'icon' => 'fa fa-fw fa-file-text',
            'mdIcon' => 'mdi-file-chart',
            'children' => [
                [
                    'id' => self::ID_KEYMETRICS_REPORT,
                    'name' => 'Keymetrics',
                    'route' => 'admin_report_keymetrics',
                ],
                [
                    'id' => self::ID_AFFILIATE_REPORT,
                    'name' => 'Affiliate',
                    'route' => '',
                    'mdRoute' => 'report/affiliate',
                ],
                [
                    'id' => self::ID_VELOCITY_REPORT,
                    'name' => 'Velocity/Fraud',
                    'route' => 'admin_report_velocity',
                ],
                [
                    'id' => self::ID_CARD_ACTIVITY_REPORT,
                    'name' => 'Card account activity',
                    'route' => '',
                    'mdRoute' => 'report/card-account-activity'
                ],
                [
                    'id' => self::ID_REVENUE_REPORT,
                    'name' => 'Net Revenue Report(Legacy)',
                    'route' => '',
                    'mdRoute' => 'report/revenue',
                ],
                [
                  'id' => 'revenup_report',
                  'name' => 'Net Revenue Report(New)',
                  'route' => '',
                  'mdRoute' => 'report/revenue-report',
                ],
                [
                    'id' => self::ID_REPORT_SCHEDULE,
                    'name' => 'Report schedules',
                    'route' => 'admin_report_schedule',
                ],
                [
                    'id' => self::ID_FEE_REPORT,
                    'name' => 'Fee Report',
                    'route' => '',
                    'mdRoute' => 'report/fee',
                ],
                [
                    'id' => self::ID_NEGATIVE_BALANCE_REPORT,
                    'name' => 'Negative Balance Report',
                    'route' => '',
                    'mdRoute' => 'report/negative-balance',
                ],
                [
                    'id' => self::ID_PRIVACY_CARD_CREATION_REPORT,
                    'name' => 'Card Creation Report',
                    'route' => '',
                    'mdRoute' => 'report/privacy-card-creation',
                ],
                [
                    'id' => self::ID_LOADS_PER_COUNTRY_REPORT,
                    'name' => 'Loads Per Country Report',
                    'route' => '',
                    'mdRoute' => 'report/loads_per_country',
                ],
                [
                    'id' => 'load_methods',
                    'name' => 'Load Method Report',
                    'route' => '',
                    'mdRoute' => 'report/load-methods',
                ],
                [
                  'id' => 'total_account_balance',
                  'name' => 'Daily Data Report',
                  'route' => '',
                  'mdRoute' => 'report/total_account_balance',
                ],
                [
                  'id' => 'IDology_usage',
                  'name' => 'IDology usage Report',
                  'route' => 'admin_idology_usage_report',
                ],
                [
                  'id' => 'Twilio_usage',
                  'name' => 'Twilio usage Report',
                  'route' => '',
                  'mdRoute' => 'report/twilio-report',
                ],
                [
                  'id' => 'Rain_usage',
                  'name' => 'Rain Migration Report',
                  'route' => '',
                  'mdRoute' => 'report/rain-report',
                ]
            ],
        ],
        [
            'id' => self::ID_MONITOR,
            'name' => 'System Monitor',
            'route' => '',
            'icon' => 'fa fa-fw fa-bar-chart',
            'mdIcon' => 'mdi-finance',
            'children' => [
                [
                    'id' => self::ID_ANALYTIC_MANAGEMENT,
                    'name' => 'Analytics',
                    'route' => 'admin_analytics',
                ],
                [
                    'id' => self::ID_ACTIVE_SESSIONS,
                    'name' => 'Active Sessions',
                    'route' => '',
                    'mdRoute' => 'monitor/active-sessions',
                ],
                [
                  'id' => self::ID_ERROR_MONITOR,
                  'name' => 'System Error',
                  'route' => '',
                  'mdRoute' => 'monitor/system-error',
                ]
            ],
        ],
        [
            'id' => self::ID_SYSTEM_SETTING,
            'name' => 'System Setting',
            'route' => '',
            'icon' => 'fa fa-fw fa-cogs',
            'mdIcon' => 'mdi-settings',
            'children' => [
                [
                    'id' => self::ID_EMAIL_HISTORY,
                    'name' => 'Email Sent History',
                    'route' => 'admin_email_history',
                ],
                [
                    'id' => self::ID_EXTERNAL_INVOKE,
                    'name' => 'External Invoke logs',
                    'route' => 'admin_external_invoke',
                ],
                [
                    'id' => self::ID_IP_WHITE_LIST,
                    'name' => 'IP White List',
                    'route' => 'admin_ip_white_list',
                ],
                [
                    'id' => self::ID_USER_STATUS_LIST,
                    'name' => 'User Status List',
                    'route' => 'admin_user_status_list',
                ],
                [
                    'id' => self::ID_BANNED_REASON,
                    'name' => 'Banned Reason',
                    'route' => 'admin_user_banned_reason',
                ],
                [
                    'id' => self::ID_CLOSURE_REASON,
                    'name' => 'Closure Reason',
                    'route' => 'admin_user_closure_reason',
                ],
                [
                    'id' => self::ID_VELOCITY_SETTING,
                    'name' => 'Velocity/Fraud Settings',
                    'route' => 'admin_velocity_setting',
                ],
                [
                    'id' => self::ID_LANGUAGE_LIST,
                    'name' => 'Languages',
                    'route' => 'admin_languages',
                ],
                [
                    'id' => self::ID_GLOBAL_SETTING,
                    'name' => 'Global Settings',
                    'route' => 'admin_global_settings',
                ],
                [
                    'id' => self::ID_GLOBAL_CHART_SETTING,
                    'name' => 'Global Chart Settings',
                    'route' => 'admin_chart_settings',
                ],
                [
                    'id' => self::ID_PLATFORM_BRANDING,
                    'name' => 'Platform Branding',
                    'route' => '',
                    'mdRoute' => 'system/platform-branding',
                ],
                [
                    'id' => self::ID_KYC_CHECK,
                    'name' => 'KYC Check',
                    'route' => '',
                    'mdRoute' => 'system/kyc-check',
                ]
            ],
        ],
        [
            'id' => self::ID_DEVELOPER,
            'name' => 'Developer Tools',
            'route' => '',
            'icon' => 'fa fa-fw fa-code',
            'mdIcon' => 'mdi-code-tags',
            'children' => [
                [
                    'id' => Module::ID_DEV_DOCUMENTATION,
                    'name' => 'Documentation',
                    'route' => '',
                    'mdRoute' => 'i/developer__'
                ],
                [
                    'id' => Module::ID_DEVELOPER_RESOURCES,
                    'name' => 'API Config',
                    'route' => '',
                    'mdRoute' => 'i/admin__developer'
                ]
            ],
        ],
        [
            'id' => self::ID_CLF_PATIENTS,
            'name' => 'Patients',
            'route' => 'noop',
            'icon' => '',
            'mdRoute' => 'clf/patients',
            'mdIcon' => 'flaticon-health',
            'children' => [],
        ],
        [
            'id' => self::ID_CLF_EMPLOYEE,
            'name' => 'Employees',
            'route' => 'noop',
            'icon' => '',
            'mdRoute' => 'clf/employees',
            'mdIcon' => 'flaticon-employees',
            'children' => [],
        ],
        [
            'id' => self::ID_CLF_DISPENSARIES,
            'name' => 'Dispensaries',
            'route' => 'noop',
            'icon' => '',
            'mdRoute' => 'clf/merchants/dispensary',
            'mdIcon' => 'flaticon-pharmacy',
            'children' => [],
        ],
        [
            'id' => self::ID_CLF_TRANSACTIONS,
            'name' => 'Transactions',
            'route' => '',
            'icon' => '',
            'mdIcon' => 'flaticon-bill',
            'children' => [
                [
                    'id' => self::ID_CLF_TRANSACTIONS_REPORT,
                    'name' => 'Transactions Report',
                    'route' => '',
                    'mdRoute' => 'clf/report/transactions',
                ],
                [
                    'id' => self::ID_CLF_TRANSACTIONS_NEW,
                    'name' => 'New Payment',
                    'route' => '',
                    'mdRoute' => 'clf/transactions/new',
                ],
            ],
        ],
        [
            'id' => self::ID_CLF_BILL_PAY,
            'name' => 'Bill Pay',
            'route' => 'noop',
            'icon' => '',
            'mdRoute' => 'clf/bills',
            'mdIcon' => 'flaticon-pay',
            'children' => [],
        ],
        [
            'id' => self::ID_CLF_BANKING,
            'name' => 'Banking',
            'route' => 'clf_bank',
            'icon' => '',
            'mdIcon' => 'flaticon-bank',
            'children' => [],
        ],
        [
            'id' => self::ID_CLF_REPORTS,
            'name' => 'Reports',
            'route' => '',
            'icon' => '',
            'mdIcon' => 'flaticon-growth',
            'children' => [
                [
                    'id' => self::ID_CLF_REPORTS_BANK_DISPENSARY_VOLUMES,
                    'name' => 'Dispensary Volume Report',
                    'route' => '',
                    'mdRoute' => 'clf/report/bank/dispensary-volume',
                ],
            ],
        ],
        [
            'id' => self::ID_CLF_SETTINGS,
            'name' => 'Settings',
            'route' => 'noop',
            'icon' => '',
            'mdRoute' => 'clf/settings',
            'mdIcon' => 'flaticon-settings',
            'children' => [],
        ],
    ];

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="string", length=255)
     * @ORM\Id
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="parent", type="string", length=255, nullable=true)
     */
    private $parent;

    /**
     * @var string
     *
     * @ORM\Column(name="route", type="text")
     */
    private $route;

    /**
     * @var string
     *
     * @ORM\Column(name="icon", type="string", length=255, nullable=true)
     */
    private $icon;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return Module
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set parent
     *
     * @param string $parent
     *
     * @return Module
     */
    public function setParent($parent)
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * Get parent
     *
     * @return string
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * Set route
     *
     * @param string $route
     *
     * @return Module
     */
    public function setRoute($route)
    {
        $this->route = $route;

        return $this;
    }

    /**
     * Get route
     *
     * @return string
     */
    public function getRoute()
    {
        return $this->route;
    }

    /**
     * Set icon
     *
     * @param string $icon
     *
     * @return Module
     */
    public function setIcon($icon)
    {
        $this->icon = $icon;

        return $this;
    }

    /**
     * Get icon
     *
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }
}

