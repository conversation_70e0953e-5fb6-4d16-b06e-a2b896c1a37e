<?php

namespace CoreBundle\Command;

require_once __DIR__ . '/../../../web/secure.php';

use CoreBundle\Entity\Platform;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Query\Expr;
use PHPStan\Cache\Cache;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use UsUnlockedBundle\Services\SlackService;
use Carbon\Carbon;

abstract class BaseCommand extends Command
{
    protected InputInterface $input;

    protected OutputInterface $output;

    protected SymfonyStyle $io;

    protected EntityManager $em;

    protected Expr $expr;

    protected string $execKey;

    /**
     * Ensure that this command can only have one instance in how many minutes
     * Set to null to allow multiple concurrent executions
     */
    protected $singletonMinutes = 24 * 60; // 24 hours

    protected $estimatedMinutes = 3 * 60; // 3 hours

    protected ?bool $alertForDuplicated = true;

    protected array $mentionsForDuplicated = [
        SlackService::MENTION_HANS,
        SlackService::MENTION_ABEL,
    ];

    public function getSingletonKey()
    {
        return $this->getName();
    }

    public static function getSingletonKeyFor(string $key)
    {
        return 'singleton_command_' . $key;
    }

    public static function hasSingletonRunning(string $key)
    {
        return Data::lockedHasOnly(self::getSingletonKeyFor($key));
    }

    public function setSingleToMinutes($time) {
        $this->singletonMinutes = $time;
    }

    public function closeAlertForDuplicated() {
      $this->alertForDuplicated = false;
   }

    public function alertForDuplicatedCall()
    {
        return $this->alertForDuplicated;
    }

    public function getMentionsForDuplicatedCall()
    {
        return $this->mentionsForDuplicated;
    }

    /**
     * @param InputInterface  $input
     * @param OutputInterface $output
     * @param bool            $log
     */
    protected function prepare(InputInterface $input, OutputInterface $output, $log = false)
    {
        $this->input = $input;
        $this->output = $output;
        $this->em = Util::em();
        $this->expr = Util::expr();
        $this->execKey = Util::generate_key(3);
        $this->io = new SymfonyStyle($input, $output);

        if (!Util::$platform) {
            Util::$platform = Platform::ternCommerce();
        }

        SlackService::prepareForPlatform(Util::$platform);

        Util::$console = $this;
        Util::request();
        Util::longRequest(0, Util::isLive() ? '8192M' : '4096M');

        $pid = getmypid();
        $from = Util::isCommand() ? 'cli' : 'fpm';
        $this->line('Command ' . $this->execKey . " is starting in process $pid from $from", 'info');

        if ($this->singletonMinutes !== null) {
            $singleKey = $this->getSingletonKey();
            $cacheKey = 'singleton_command_' . $singleKey;
            $value = Util::j2s([
                'when' => date('c'),
                'from' => $from,
                'pid' => $pid,
            ]);
            if (Data::lockedHas($cacheKey, $this->singletonMinutes * 60, $value)) {
                $bypass = false;
                $local = Util::isLocal() || Util::isDevDevice();
                $lock = Data::createLock($cacheKey);
                if ($lock->acquire()) {
                    $last = Data::get($cacheKey);
                    if ($last && str_starts_with($last, '{"')) {
                        $last = Util::s2j($last) ?? [];
                        $live = Util::isLive();
                        if ( ! $live) {
                            $this->estimatedMinutes = 1;
                        }
//                        if ($local) {
//                            $this->line(Util::j2s($last));
//                        }
                        if (
                            isset($last['when'], $last['from'], $last['pid']) &&
                            time() - strtotime($last['when']) > ($this->estimatedMinutes * 60 - 10))
                        {
                            if ( ! Util::isPidRunning($last['pid'])) {
                                $bypass = 'no_pid_' . $last['pid'];
                            } else if ($last['from'] === 'cli' && ! Util::hasProcessMatches($this->getName(), $pid)) {
                                $bypass = 'no_cli_' . $this->getName();
                            }
//                            else if ($local) {
//                                $this->line('Now: ' . date('c'));
//                            }
                        }
//                        else if ($local) {
//                            $this->line('Now: ' . date('c'));
//                            $this->line('Has pid: ' . Util::isPidRunning($last['pid']));
//                            $this->line('Has matched process: ' . implode("\n", Util::hasProcessMatches($this->getName(), $pid)));
//                        }
                    }
                    if ($bypass) {
                        Data::set($cacheKey, $value, true, $this->singletonMinutes * 60);
                    }
                    $lock->release();
                }

                if ( ! $bypass) {
                    $msg = 'Skip calling singleton command `' . $singleKey . '`';
                    if ( ! $local && $this->alertForDuplicatedCall()) {
                        SlackService::controlFrequency(5);
                        SlackService::warning($msg,
                            null,
                            $this->getMentionsForDuplicatedCall());
                    } else {
                        $this->line($msg, 'error');
                    }
                    if ( ! $local || Util::isTestEnv()) {
                        throw new \RuntimeException($msg);
                    }
                } else {
                    SlackService::warning('Bypass the singleton lock for command `' . $singleKey . '`',
                        [
                            'reason' => $bypass,
                        ],
                        $this->getMentionsForDuplicatedCall());
                }
            }
        }

        if ($log) {
            $this->line('start...', 'info');
        }
    }

    public function longestRequest()
    {
        Util::longRequest(0, Util::isLive() ? '20480M' : '2048M');
    }

    /**
     * @param string $message
     * @param null|string $type can be `info`, `error`, `question`, `comment` or null
     */
    public function line($message, $type = null, $context = [], $log = true)
    {
        if (is_array($type) && empty($context)) {
            $context = $type;
            $type = null;
        }
        if ($type && $type !== 'debug') {
            $message = "<$type>$message</$type>";
        }
        $env = Util::env();
        $micro = str_pad((int)(microtime(true) * 1000) % 1000, 3, '0');
        $message =  date('Y-m-d H:i:s') . '.' . $micro. ' (' . $env . ') ' . $message;
        if ($type === 'debug' && $context) {
            $this->output->writeln($message . ' ----- ' . Util::j2s($context));
        } else {
            $this->output->writeln($message);
        }

        if ($log) {
            Log::debug($this->getName() . ' -' . $this->execKey . '- ' . $message, $context ?? []);
        }
    }

    public function lineNoLog($message, $type = null, array $context = [])
    {
        $this->line($message, $type, $context, false);
    }

    public function lines(array $lines, $type = null)
    {
        $this->line(implode("\n", $lines), $type);
        return null;
    }

    protected function execOutput($command, $mask = null)
    {
        $output = [];

        $line = $command;
        if ($mask) {
            $line = str_replace($mask, 'xxxxxx', $command);
        }
        $this->line($line, 'info');

        exec($command, $output);
        $this->lines($output, 'comment');
        return $output;
    }

    public function getInput()
    {
        return $this->input;
    }

    public function getOutput()
    {
        return $this->output;
    }

    public function getExecKey()
    {
        return $this->execKey;
    }

    public function setInput(InputInterface $input)
    {
        $this->input = $input;
        return $this;
    }

    public function setOutput(OutputInterface $output)
    {
        $this->output = $output;
        return $this;
    }

    public function setExecKey(string $execKey)
    {
        $this->execKey = $execKey;
        return $this;
    }

    public function initFromCommand(BaseCommand $command)
    {
        $this->setInput($command->getInput())
            ->setOutput($command->getOutput())
            ->setExecKey($command->getExecKey() ?? Util::generate_key(3));
        $this->em = Util::em();
        $this->expr = Util::expr();
        $this->io = new SymfonyStyle($this->input, $this->output);
        return $this;
    }

    protected function done($msg = 'done...', $type = null)
    {
        if ($this->singletonMinutes !== null) {
            $cacheKey = 'singleton_command_' . $this->getSingletonKey();
            Data::del($cacheKey, true);
        }

        $this->line($msg ?? 'done...', $type ?? 'info');
        return 0;
    }

    protected function lineMemoryUsage(string $message, $type = null, $context = [], $log = true)
    {
        $used = number_format(round(memory_get_usage() / 1048576, 3), 3) . ' MB';
        $this->line(
            'memory_usage: ' . $used . '. ' . $message,
            $type, $context, $log
        );
    }

    public function isVerbosePlus()
    {
        return $this->output->getVerbosity() >= OutputInterface::VERBOSITY_VERBOSE;
    }
}
