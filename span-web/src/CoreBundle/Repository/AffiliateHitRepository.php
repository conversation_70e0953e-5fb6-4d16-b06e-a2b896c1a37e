<?php

namespace CoreBundle\Repository;

use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use Doctrine\ORM\QueryBuilder;

/**
 * AffiliateHitRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class AffiliateHitRepository extends \Doctrine\ORM\EntityRepository
{
    use DbTrait;

    public function queryTotal()
    {
        $user = Util::user();
        $affIds = $user->getAccessibleAffiliates()->map(function (Affiliate $affiliate) {
            return $affiliate->getId();
        });

        $request = clone Util::request();
        $range = $request->get('range');
        if ($range) {
            $range['ah.time'] = $range['ucl.receivedAt'];
            unset($range['ucl.receivedAt']);
            $request->request->set('range', $range);
        }

        $query = Util::em()->getRepository(\CoreBundle\Entity\AffiliateHit::class)
            ->createQueryBuilder('ah');
        $query->join('ah.affiliate', 'aff')
            ->distinct();
        $expr = $query->expr();
        $query->where($expr->in('aff.id', ':affIds'))
            ->setParameters([
                'affIds' => $affIds,
            ]);

        $params = new QueryListParams($query, $request, 'ah');
        $query = $this->queryListForPaginator($params);
        return $this->queryCount($query, 'count(ah)');
    }
}
