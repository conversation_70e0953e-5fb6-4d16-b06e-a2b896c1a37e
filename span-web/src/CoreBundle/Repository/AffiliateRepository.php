<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/11/17
 * Time: 16:47
 */

namespace CoreBundle\Repository;

use CoreBundle\Entity\Affiliate;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\QueryListParams;
use CoreBundle\Utils\Traits\DbTrait;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;

class AffiliateRepository extends BaseRepository
{
    use DbTrait {
        queryCount as traitQueryCount;
    }

    public function querySignUpsCount($user = null)
    {
        if (null === $user) {
            $user = Util::user();
        }
        $em = Util::em();
        $affIds = $user->getAccessibleAffiliates()->map(function (Affiliate $affiliate) {
            return $affiliate->getId();
        });

        $request = clone Util::request();
        $range = $request->get('range');
        if ($range) {
            $range['u.createdAt'] = $range['ucl.receivedAt'];
            unset($range['ucl.receivedAt']);
            $request->request->set('range', $range);
        }

        $query = $em->getRepository(\SalexUserBundle\Entity\User::class)->createQueryBuilder('u');
        $query->join('u.affiliate', 'aff')
            ->distinct();
        if ($affIds) {
            $expr = $query->expr();
            $query->where($expr->in('aff.id', ':affIds'))
                ->setParameters([
                    'affIds' => $affIds,
                ]);
        }

        $params = new QueryListParams($query, $request, 'u');
        $query = $this->queryListForPaginator($params);
        return $this->traitQueryCount($query, 'count(distinct u)');
    }

    public function querySignUpsLoadedCount($user = null)
    {
        if (null === $user) {
            $user = Util::user();
        }
        $em = Util::em();
        $expr = Util::expr();
        $request = Util::request();
        $query = $em->getRepository(User::class)
            ->createQueryBuilder('u')
            ->join('u.cards', 'uc')
            ->join('uc.loads', 'ucl')
            ->join('u.affiliate', 'aff')
            ->where($expr->in('ucl.loadStatus', ':loadStatuses'))
            ->setParameter('loadStatuses', UserCardLoad::RECEIVED_STATUS_ARRAY)
            ->distinct();

        $affIds = $user->getAccessibleAffiliates()->map(function (Affiliate $affiliate) {
            return $affiliate->getId();
        });
        if ($affIds) {
            $query->andWhere($expr->in('aff.id', ':affIds'))
                ->setParameter('affIds', $affIds);
        }

        $params = new QueryListParams($query, $request, 'u');
        $query = $this->queryListForPaginator($params);
        return $this->traitQueryCount($query, 'count(distinct u)');
    }
}
