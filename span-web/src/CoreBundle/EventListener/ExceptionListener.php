<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 2017/5/31
 * Time: 下午3:17
 */

namespace CoreBundle\EventListener;

use CoreBundle\Exception\RenderViewException;
use CoreBundle\Entity\Email;
use CoreBundle\Exception\FailedException;
use CoreB<PERSON>le\Exception\InvalidAdminTokenException;
use CoreBundle\Exception\MessageException;
use CoreBundle\Exception\NotLoggedInException;
use CoreBundle\Exception\OverwriteBundleException;
use CoreBundle\Exception\RedirectException;
use CoreBundle\Response\ErrorResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use DevBundle\Services\SlackService;
use PortalBundle\Exception\PortalException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use UsUnlockedBundle\Exception\UnsupportedBrowserException;

class ExceptionListener
{
    public function onKernelException(ExceptionEvent $event)
    {
        $ex = $event->getThrowable();
        if ($ex instanceof RedirectException) {
            return;
        }

        if ($ex instanceof InvalidAdminTokenException) {
            $this->logout($event, $ex);
            return;
        }

        $message = $ex->getMessage();
        $skips = [
            'Disable loading from inactive email',
            'No route found for ',
            'Session is timeout',
            'The phone number has been registered before.',
            'Your account is under review.',
            'Invalid username or password',
            'Exceed max balance limit',
            'No valid providers found. Please contact support.',
            'Unknown redirect!',
            'Unmatched full card number',
            'Invalid full card number',
            'Only members are supported to login the mobile app.',
            'The app version you are using has been deprecated.',
            'Your login has expired',
            'Live Mode not Active',
            'Expired Token.',
            'Could not find original Token object', // Cross-domain login as is not working, and won't be supported
            'Invalid BIC (EI: ',
            'Invalid IBAN (EI: ',
            'Invalid parameter City (EI: ',
            'PSU ID was not provided (EI: ',
            'Requested resource could not be found', // Invalid 2000charge payment
        ];
        foreach ($skips as $skip) {
            if (str_starts_with($message, $skip)) {
                return;
            }
        }

        $skips = [
            'Too many attempts. Please try again',
            'the login details of this item have changed',
            'Unable to send SMS verification code',
            'Please contact Spendr support',
            'is part of a batch that contains a pre-fed error',
            'Only consumers are supported to login the mobile app',
            'The string supplied did not seem to be a phone number.',
            'Update required.',
        ];
        foreach ($skips as $skip) {
            if (str_contains($message, $skip)) {
                return;
            }
        }

        if (in_array($message, [
            'Invalid BIC',
            'Invalid IBAN',
        ])) {
            return;
        }

        $alert = true;
        $persist = true;
        if ($ex instanceof PortalException) {
            if (!$ex->persist) {
                $persist = false;
                $alert = $ex->alert ?? false;
            }
            if ($ex->alert === false) {
                $alert = false;
            }
        } else if (
            $ex instanceof FailedException
            || $ex instanceof MessageException
            || $ex instanceof NotFoundHttpException
            || $ex instanceof NotLoggedInException
            || $ex instanceof OverwriteBundleException
            || $ex instanceof ResourceNotFoundException
            || $ex instanceof UnsupportedBrowserException
            || $ex instanceof RenderViewException
        ) {
            $persist = false;
            $alert = false;
        }

        $request = Util::request();
        if (in_array($message, [
            'Access denied!',
            'Permission denied!',
            'Permission denied',
        ])) {
            $message .= ' - ' . $request->getUri();
            $alert = false;
        }

        $mutes = [
            'Input validation error: ',
        ];
        foreach ($mutes as $mute) {
            if (str_starts_with($message, $mute)) {
                $persist = false;
                $alert = false;
            }
        }

        $mutes = [
            'or more accounts with same ID number already exist',
            'The load method is temporarily unavailable. Please try again with other methods for now.',
        ];
        foreach ($mutes as $mute) {
            if (str_contains($message, $mute)) {
                $persist = false;
                $alert = false;
            }
        }

        $mutes = [
            'Sorry that the operation failed due to an error (code: ', // UniTeller token error
            'Oops! We experienced a system error while trying to send your information', // UniTeller known error
            'The email address had been registered by others!',
            'Unknown request source',
            'You cannot pick the same password that was in the last 10 changes',
            'Your account is not active anymore. Please contact support or admin.',
            'This payer does not currently support Transaction or Destination Currency.', // Intermex creation error
            'An Unexpected error has occurred, please try again and if the error persists Report the Error', // Intermex error
            'ACCOUNT HOLDER DOES NOT MATCH WITH BENEFICIARY NAME',
            'User exists, but is not approved', // Rain card creation error due to KYC failure
            'Invalid account info while syncing with Plaid.', // Spendr Plaid sync error
        ];
        foreach ($mutes as $mute) {
            if (str_contains($message, $mute)) {
                $alert = false;
            }
        }

        if (Util::hasPrefix($request->getPathInfo(), [
            '/dev/',
            '/t/',
        ])) {
            $alert = false;
        }

        $previous = $ex->getPrevious();
        if ($persist) {
            $exception = $ex->getTraceAsString();
            if ($previous) {
                $exception .= " \n\n(Previous exception: " . $previous . ')';
            }
            $extraLog = null;
            try {
                if ($alert && Util::isLive()) {
                    $user = Util::user();
                    SlackService::$webhookUrl = SlackService::WEBHOOK_DEVS;
                    SlackService::pure('Exception: ' . $this->simplifyErrorMessage($message), [
                        'url' => $request->getUri(),
                        'type' => get_class($ex),
                        'summary' => Util::exceptionBrief($ex),
                        'exception' => mb_substr($exception, 0, 512),
                        'user' => $user?->getId(),
                    ]);
                }
            } catch (\Throwable $ne) {
                $extraLog = $ne->getMessage();
            }
            Email::debug( 'Uncaught Exception: ' . $message, [
                'type' => get_class($ex),
                'exception' => $exception,
                'new_exception' => $extraLog,
            ]);
        } else {
            if ($previous) {
                $message .= ' (Previous exception: ' . $previous->getMessage() . ')';
            }
            Log::warn('Known exception ' . get_class($ex) . ' : ' . $message);
        }

        if (str_starts_with($message, 'You are already switched to')) {
            $event->setResponse(new ErrorResponse($message));
            $event->allowCustomResponseCode();
        } else if (str_starts_with($message, 'Switch User failed: ')) {
            $this->logout($event, $ex);
        }
    }

    protected function simplifyErrorMessage(string $msg)
    {
        return Util::removePrefix($msg, 'An exception occurred while executing a query: ');
    }

    protected function logout(ExceptionEvent $event, \Throwable $ex)
    {
        $msg = '<meta http-equiv="refresh" content="1;URL=\'/logout\'"/>' . $ex->getMessage();
        $event->setResponse(new Response($msg, 403));
        $event->allowCustomResponseCode();
    }
}
