<?php

namespace CoreBundle\Controller\Webhook;

use Core<PERSON>undle\Controller\BaseController;
use CoreBundle\Exception\FailedException;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Service;
use SalexUserBundle\Entity\User;
use UsUnlockedBundle\Services\SlackService;


class SumsubController extends BaseController
{
    /**
     * @Route("/webhook/sumsub")
     * @param Request $request
     *
     * @return Response
     */
    public function sumsubWebhook(Request $request)
    {
        Log::debug('Got Sumsub webhook', Util::jsonRequest($request));

        $body = Util::s2j($request->getContent());
        $externalUserId = $body['externalUserId'] ?? null;
        if (!$externalUserId) {
            return $this->alertAndReturn('No external User Id from Sumsub webhook', $body);
        }
        $user = User::find($externalUserId);
        if (!$user) {
            return $this->alertAndReturn("Can't find a user with the external User Id from Sumsub webhook", $body);
        }

        $type = $body['type'] ?? null;
        if (in_array($type, [
            'applicantReviewed', 'applicantReset', 'applicantDeleted',
            'applicantDeactivated', 'applicantActivated',
        ])) {
            Service::sendAsync('/t/cron/usu/sumsub/update-member/' . $externalUserId);
        }

        return new SuccessResponse([], 'Success');
    }

    protected function validateWebhookAndUpdate(Request $request, User $user)
    {
        try {
            $ua = $request->headers->get('User-Agent');
            if ($ua !== 'PMI Service') {
                throw new FailedException('invalid user agent: ' . $ua);
            }
            $name = $request->request->get('webhook-name');
            if ($name !== 'Tern+live') {
                throw new FailedException('invalid webhook name: ' . $name);
            }
            $digest = $request->headers->get('x-payload-digest');
            if (!$digest) {
                throw new FailedException('no payload digest');
            }
            $req = $request->request->all() ?? [];
            $clientId = $req['clientId'] ?? '';
            if ($clientId !== 'tern_108259') {
                throw new FailedException('invalid client id: ' . $clientId);
            }
        } catch (\Throwable $t) {
            Log::warn('Failed to validate Sumsub webhook: ' . Util::exceptionBrief($t));
        }
    }

    protected function alertAndReturn(string $error, array $context)
    {
        SlackService::warning($error, $context, SlackService::GROUP_DEV);
        return new Response();
    }
}
