<?php

namespace CoreBundle\Controller\Webhook;

use Core<PERSON>undle\Controller\BaseController;
use Core<PERSON>undle\Entity\Config;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Entity\PayPalSubscription;
use UsUnlockedBundle\Services\Mautic\MauticEventService;
use UsUnlockedBundle\Services\SlackService;
use UsUnlockedBundle\Services\UserService;

class PaypalController extends BaseController
{
    /**
     * @Route("/webhook/paypal")
     * @param Request $request
     *
     * @return Response
     */
    public function paypalWebhook(Request $request)
    {

        $body = Util::s2j($request->getContent());
        Log::debug('Received Paypal webhook', [
            'input' => $body,
        ]);

        if (!isset($body['resource_type'])) {
            throw new FailedException('This is forbidden webhook event!');
        }
        $eventType = $body['event_type'];
        if (!in_array($eventType, [
            'BILLING.SUBSCRIPTION.CREATED',
            'BILLING.SUBSCRIPTION.CANCELLED',
            'BILLING.SUBSCRIPTION.ACTIVATED',
            'BILLING.SUBSCRIPTION.SUSPENDED',
            'BILLING.SUBSCRIPTION.PAYMENT.FAILED'
        ])) {
            Log::debug('This is forbidden Paypal webhook event, since the event type is not allowed!');
            throw new FailedException('This is forbidden webhook event, since the event type is not allowed!');
        }
        $resource = $body['resource'];
        $planId = $resource['plan_id'];
        $planList = [
            Config::get('usu_monthly_paypal_plan'),
            Config::get('usu_annually_paypal_plan'),
            Config::get('usu_upgrade_annually_paypal_plan'),
            'P-5FG845064D657635TM6S6XEY',
            'P-9NN28240DU182515BNCGDJCY'
        ];
        if (!in_array($planId, $planList)) {
            Log::debug('This is forbidden Paypal webhook event, since the plan is not allowed!');
            throw new FailedException('This is forbidden webhook event, since the plan is not allowed!');
        }
        $planType = in_array($planId, ['P-9NN28240DU182515BNCGDJCY', Config::get('usu_upgrade_annually_paypal_plan'), Config::get('usu_annually_paypal_plan')]) ? 'Annually' : 'Monthly';
        if ($eventType === 'BILLING.SUBSCRIPTION.CREATED') {
            Log::info(
                'There is one user created a paypal subscription and is waiting to make the payment!',
                [
                    'Subscribe Id' => $resource['id']
                ]
            );
        } else if ($eventType === 'BILLING.SUBSCRIPTION.ACTIVATED') {
            $paypalSubscript = PayPalSubscription::findBySubscriptionId($resource['id']);
            if ($paypalSubscript) {
                SlackService::check(
                    'The member successfully subscribed to paypal!',
                    [
                        'User' => $paypalSubscript->getUser()->getSignature(),
                        'Plan Type' => $planType,
                        'Subscribe Id' => $resource['id']
                    ]
                );
                SlackService::sendUsuEvents($paypalSubscript->getUser(), 'Subscribed Paypal subscription');
            }

        } else if ($eventType === 'BILLING.SUBSCRIPTION.CANCELLED') {
            $paypalSubscript = PayPalSubscription::findBySubscriptionId($resource['id']);
            if ($paypalSubscript) {
                UserService::updatePayPalSubscriptInfo($paypalSubscript->getUser(), $paypalSubscript->getSubscriptionId());
                SlackService::warning(
                    'This member cancelled the paypal subscription！',
                    [
                        'User' => $paypalSubscript->getUser()->getSignature(),
                        'Plan Type' => $planType,
                        'Subscribe Id' => $resource['id']
                    ]
                );
                SlackService::sendUsuEvents($paypalSubscript->getUser(), 'Cancelled Paypal subscription');
            }
        }  else if ($eventType === 'BILLING.SUBSCRIPTION.SUSPENDED') {
            $paypalSubscript = PayPalSubscription::findBySubscriptionId($resource['id']);
            if ($paypalSubscript) {
                UserService::updatePayPalSubscriptInfo($paypalSubscript->getUser(), $paypalSubscript->getSubscriptionId());
                SlackService::warning(
                    'This member suspended the paypal subscription！',
                    [
                        'User' => $paypalSubscript->getUser()->getSignature(),
                        'Plan Type' => $planType,
                        'Subscribe Id' => $resource['id']
                    ]
                );
                SlackService::sendUsuEvents($paypalSubscript->getUser(), 'Suspended Paypal subscription');
            }
        } else if ($eventType === 'BILLING.SUBSCRIPTION.PAYMENT.FAILED') {
            $paypalSubscript = PayPalSubscription::findBySubscriptionId($resource['id']);
            if ($paypalSubscript) {
                UserService::updatePayPalSubscriptInfo($paypalSubscript->getUser(), $paypalSubscript->getSubscriptionId());
                SlackService::warning(
                    "The member's Paypal subscription payment failed",
                    [
                        'User' => $paypalSubscript->getUser()->getSignature(),
                        'Plan Type' => $planType,
                        'Subscribe Id' => $resource['id']
                    ]
                );
                SlackService::sendUsuEvents($paypalSubscript->getUser(), 'Paypal subscription payment failed');
            }
        }
        return new SuccessResponse([], 'Success');
    }
}
