<?php

namespace CoreBundle\Controller\Webhook;

use Core<PERSON><PERSON>le\Controller\BaseController;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use UsUnlockedBundle\Services\Coinflow\CoinflowQueue;
use UsUnlockedBundle\Services\Coinflow\CoinflowService;
use UsUnlockedBundle\Services\SlackService;

class CoinflowController extends BaseController
{
    #[Route("/webhook/coinflow", name: "webhook_coinflow")]
    public function coinflowAction(Request $request)
    {
        Log::debug('Received Coinflow webhook', Util::jsonRequest($request));

        $auth = $request->headers->get('Authorization');
        if (!$auth || $auth !== Util::getKmsParameter('coinflow_webhook_key')) {
            return new Response('', 401);
        }

        $all = $request->request->all();
        $category = $all['category'] ?? null;
        $eventType = $all['eventType'] ?? null;
        $created = $all['created'] ?? null;
        $data = $all['data'] ?? [];
        $context = compact('category', 'eventType', 'created');
        $context['rawCustomerId'] = $data['rawCustomerId'] ?? $data['customerId'] ?? null;

        if ($category === 'Purchase') {
            $resp = $this->processPurchaseCategory($eventType, $data, $context);
        } else {
            $resp = $this->alertAndReturn(
                'Unknown Coinflow webhook category: ' . $category,
                $context
            );
        }

        return $resp ?? new Response();
    }

    protected function processPurchaseCategory(string $eventType, array $data, array $context): ?Response
    {
        $webhookInfo = Util::s2je($data['webhookInfo']['example'] ?? '{}');
        $context['loadId'] = $webhookInfo['ucl'] ?? null;
        $ucl = UserCardLoad::find($context['loadId']);

        if (in_array($eventType, [
            'Settled',
            'Card Payment Declined',
            'Card Payment Authorized',
            'Payment Expiration',
        ])) {
            $context['paymentId'] = $data['id'] ?? null;
            if (!$context['loadId'] || empty($context['paymentId'])) {
                return $this->alertAndReturn('No load/payment ID from Coinflow webhook', $context);
            }

            if (!$ucl) {
                return $this->alertAndReturn('Unknown load from Coinflow webhook', $context);
            }

            if ($eventType === 'Card Payment Declined') {
                Util::updateMeta($ucl, [
                    'coinflowDeclineCode' => $data['declineCode'] ?? null,
                    'coinflowDeclineDescription' => $data['declineDescription'] ?? null,
                ]);
            }

            CoinflowService::assignPaymentIdWithCheck($ucl, $context['paymentId']);
            CoinflowQueue::updatePayment($ucl);
            return null;
        }

        if (in_array($eventType, [
            'Card Payment Chargeback Opened',
            'Card Payment Chargeback Lost',
            'Card Payment Chargeback Won',
        ])) {
            if (!$context['rawCustomerId'] && $ucl) {
                $context['rawCustomerId'] = $ucl->getUserCard()->getUser()->getId();
            }
            $context['amount'] = Money::format(
                $data['total']['cents'] ?? 0,
                    $data['total']['currency'] ?? 'USD'
            );
            $context = array_merge($context, Util::arrayColumns($data, [
                'chargebackId', 'reasonCode', 'reasonDescription',
            ]));

            if (!empty($context['rawCustomerId'])) {
                $user = User::find($context['rawCustomerId']);
                $user->setStatus(User::STATUS_CLOSED)
                    ->addClosureReason('Chargeback')
                    ->persist();
                $user->addNote('Account closed from Coinflow webhook: ' . $eventType);
                $context['userStatus'] = $user->getStatus();
            }

            SlackService::alert(
                'Coinflow Chargeback Event: ' . $eventType,
                $context,
                SlackService::GROUP_DEV
            );

            if ($ucl) {
                CoinflowService::ensureChargebackUnload($ucl, $data);
            }

            return null;
        }

        if ($eventType === 'Payment Pending Review') {
            SlackService::alert(
                'Coinflow `' . $eventType . '` Event',
                $context,
                SlackService::GROUP_COINFLOW_REVIEW,
            );
            return null;
        }

        if (in_array($eventType, [
            '',
            'PIX Expiration',
        ])) {
            return null;
        }

        return $this->alertAndReturn('Unknown Coinflow purchase webhook event type', $context);
    }

    protected function alertAndReturn(string $error, array $context)
    {
        SlackService::warning($error, $context, SlackService::GROUP_DEV);
        return new Response();
    }
}
