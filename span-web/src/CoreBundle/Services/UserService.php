<?php
/**
 * Created by PhpStorm.
 * User: hans
 * Date: 07/02/2018
 * Time: 21:42
 */

namespace CoreBundle\Services;


use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\Config;
use CoreBundle\Entity\Email;
use CoreBundle\Entity\IpUsage;
use CoreBundle\Entity\LoginAttempt;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserCardLoad;
use CoreBundle\Utils\Bundle;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Util;
use PortalBundle\Exception\PortalException;
use SalexUserBundle\Entity\User;
use SpendrBundle\SpendrBundle;
use TransferMexBundle\Services\MemberService;

class UserService
{
    public static function findUserTryingToLogin($username)
    {
        $user = null;
        $platform = Util::platform();
        if ($platform) {
            if ($platform->isSpendr()) {
                $host = strtolower(Util::request()->getSchemeAndHttpHost());
                if (SpendrBundle::getAppDomain() !== $host) {
                    $user = User::findPlatformUserByEmail($username, array_merge(
                        [
                            Role::ROLE_SPENDR_BANK
                        ],
                        SpendrBundle::getSpendrAdminRoles(),
                        SpendrBundle::getSpendrGroupAdminRoles(),
                        SpendrBundle::getMerchantAdminRoles()
                    ), null, User::STATUS_ACTIVE);
                } else {
                    $user = User::findPlatformUserByEmail($username, SpendrBundle::getMobileRoles(), null, User::STATUS_ACTIVE);
                }
            } else {
                $user = User::findPlatformUserByEmail($username, Bundle::getAdminRoles());
            }
            if (!$user) {
                $user = User::findPlatformUserByEmail($username, [
                    Role::ROLE_API_INVOKER,
                ]);
            }
            if (!$user) {
                $user = User::findPlatformUserByEmail($username, Bundle::getMemberRoles());
            }
            if (!$user) {
                $user = User::findPlatformUserByEmail($username, [
                    Role::ROLE_CONSUMER,
                    Role::ROLE_CONSUMER_SERVICE_AGENT,
                    Role::ROLE_ADMIN,
                    Role::ROLE_AFFILIATE,
                    Role::ROLE_PLATFORM_OWNER,
                    Role::ROLE_PROGRAM_OWNER,
                    Role::ROLE_CORPORATE_USER,
                ]);
            }
            if (!$user) {
                $user = User::findByEmail($username);
                if ($user && !$user->isMasterAdmin()) {
                    $user = null;
                }
            }
        } else {
            $user = User::findByEmail($username);
        }
        return [$user, $username, $platform];
    }

    public static function getActivities(User $user, $limit = 5)
    {
        $all = [];
        $dateFormat = Util::DATE_FORMAT_PORTAL;

        $oneCard = $user->getOneCardInPlatform();
        if ($oneCard) {
            $cp = $oneCard->getCardProgram();
            $all[] = [
                'timestamp' => $user->getCreatedAt()->getTimestamp(),
                'date' => Util::formatDateTime($user->getCreatedAt(), $dateFormat),
                'activity' => 'Welcome to "' . $cp->getName() . '"',
            ];

            $issuedAt = $oneCard->getIssuedAt();
            if ($issuedAt) {
                $initialized = $oneCard->getLoads(UserCardLoad::TYPE_LOAD_CARD)->filter(function (UserCardLoad $load) {
                    return $load->getInitializedAt();
                });
                if ($initialized->isEmpty()) {
                    $all[] = [
                        'timestamp' => $issuedAt->getTimestamp(),
                        'date' => Util::formatDateTime($issuedAt, $dateFormat),
                        'activity' => 'Issued card. <a href="/card-management/' . $oneCard->getHash() . '/load">Load now!</a>',
                    ];
                } else {
                    $balance = $oneCard->getBalance();
                    if ($balance <= 0) {
                        $all[] = [
                            'timestamp' => $issuedAt->getTimestamp(),
                            'date' => Util::formatDateTime($issuedAt, $dateFormat),
                            'activity' => 'Zero balance. <a href="/card-management/' . $oneCard->getHash() . '/load">Load now!</a>',
                        ];
                    } else if ($balance < $oneCard->getWarningValue()) {
                        $all[] = [
                            'timestamp' => $issuedAt->getTimestamp(),
                            'date' => Util::formatDateTime($issuedAt, $dateFormat),
                            'activity' => 'Balance is below threshold. <a href="/card-management/' . $oneCard->getHash() . '/load">Load now!</a>',
                        ];
                    }
                }
            }
        }

        $em = Util::em();
        $rs = $em->getRepository(\CoreBundle\Entity\IpUsage::class)
            ->createQueryBuilder('iu')
            ->where('iu.type = :type')
            ->andWhere('iu.users = :user')
            ->setParameter('type', 'login')
            ->setParameter('user', $user)
            ->orderBy('iu.login_time', 'desc')
            ->setMaxResults(2)
            ->getQuery()
            ->getResult();
        if (count($rs) > 1) {
            /** @var IpUsage $iu */
            $iu = $rs[1];
            $time = $iu->getLoginTime();
            $all[] = [
                'timestamp' => $time->getTimestamp(),
                'date' => Util::formatDateTime($time, $dateFormat),
                'activity' => 'Your last login at ' . Util::formatDateTime($time, Util::TIME_FORMAT),
            ];
        }

        $rs = $em->getRepository(\CoreBundle\Entity\UserCardLoad::class)
            ->createQueryBuilder('ucl')
            ->join('ucl.userCard', 'uc')
            ->where('uc.user = :user')
            ->andWhere('ucl.loadStatus = :status')
            ->setParameter('user', $user)
            ->setParameter('status', UserCardLoad::LOAD_STATUS_LOADED)
            ->orderBy('ucl.loadAt', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getResult();
        if ($rs) {
            /** @var UserCardLoad $load */
            $load = $rs[0];
            $all[] = [
                'timestamp' => $load->getLoadAt()->getTimestamp(),
                'date' => Util::formatDateTime($load->getLoadAt(), $dateFormat),
                'activity' => 'Loaded ' . $load->getLoadAmountText() . ' to your card',
            ];
        }

        $welcome = Config::array(Config::CONFIG_WELCOME_MESSAGE);
        if ($welcome && !empty($welcome['text'])) {
            $timestamp = empty($welcome['date']) ? time() : Util::localDateToTimestamp($welcome['date']);
            $all[] = [
                'timestamp' => $timestamp,
                'date' => Util::formatDateTime(Carbon::createFromTimestamp($timestamp, Util::timezone()), $dateFormat),
                'activity' => empty($welcome['link'])
                    ? $welcome['text']
                    : ('<a href="' . $welcome['link'] . '">' . $welcome['text'] . '</a>'),
            ];
        }

        Util::usort($all, [
            'timestamp' => 'desc',
        ]);
        return array_slice($all, 0, $limit);
    }

    /**
     * @param User $user
     * @return bool
     * 
     */
    public static function deleteTestUser(User $user)
    {
        if (!$user || !$user->isTestData()) {
            return false;
        }
        $id = $user->getId();
        $email = $user->getEmail();

        $conn = Util::em()->getConnection();
        $conn->executeStatement('SET FOREIGN_KEY_CHECKS = 0;');

        $conn->executeStatement("delete from attachment where id in (
            select attachment_id from user_id_verify_attachment uiva
            inner join user_id_verify uiv on uiv.id = uiva.user_id_verify_id
            where uiv.user_id = '$id')");
        $conn->executeStatement("delete from user_id_verify_attachment where user_id_verify_id in (
            select id from user_id_verify uiv where uiv.user_id = '$id')");
        $conn->executeStatement("delete from user_id_verify where user_id = '$id'");
        $conn->executeStatement("delete from email_recipient where address = '$email'");
        $conn->executeStatement("delete from email where recipients like '%\"$email\"%'");
        $conn->executeStatement("delete from error where user_id = '$id'");
        $conn->executeStatement("delete from external_invoke where create_by = '$id'");
        $conn->executeStatement("delete from ip_usage where users_id = '$id'");
        $conn->executeStatement("delete from login_attempt where user = '$email'");
        $conn->executeStatement("delete from notes where toname = '$id'");
        $conn->executeStatement("delete from reshipper_address where user_id = '$id'");
        $conn->executeStatement("delete from transaction where user_id = '$id'");
        $conn->executeStatement("delete from user_affiliate where user_id = '$id'");
        $conn->executeStatement("delete from user_card_load where user_card_id in (
            select id from user_card where user_id = '$id')");
        $conn->executeStatement("delete from user_card_balance where user_card_id in (
            select id from user_card where user_id = '$id')");
        $conn->executeStatement("delete from user_card_decline where user_card_id in (
            select id from user_card where user_id = '$id')");
        $conn->executeStatement("delete from user_card_local_balance_settlement where user_card_id in (
            select id from user_card where user_id = '$id')");
        $conn->executeStatement("delete from user_card_snap where user_card_id in (
            select id from user_card where user_id = '$id')");
        $conn->executeStatement("delete from user_card_transaction where user_card_id in (
            select id from user_card where user_id = '$id')");
        $conn->executeStatement("delete from user_card where user_id = '$id'");
        $conn->executeStatement("delete from user_card_program where user_id = '$id'");
        $conn->executeStatement("delete from user_discount where user_id = '$id'");
        $conn->executeStatement("delete from user_pin where user_id = '$id'");
        $conn->executeStatement("delete from user_pin_log where user_id = '$id'");
        $conn->executeStatement("delete from user_role where user_id = '$id'");
        $conn->executeStatement("delete from user_shipper_address where user_id = '$id'");
        $conn->executeStatement("delete from user_token where user_id = '$id'");
        $conn->executeStatement("delete from user_velocity where user_id = '$id'");
        $conn->executeStatement("delete from users where id = '$id'");

        $conn->executeStatement('SET FOREIGN_KEY_CHECKS = 1;');

        return true;
    }

    public static function getInvalidPasswordMessages()
    {
        return [
            'Minimum 8 characters in length',
            'Contain at least three of the following four character types: uppercase letter, lowercase letter, number and special character',
        ];
    }

    public static function checkNewPasswordSecurity($password, User $user = null, $nullable = false)
    {
        if ($nullable && !$password) {
            return true;
        }

        // lets set min length to 8 characters.
        // Contain at least three of the following four character types: uppercase letter, lowercase letter, number and special character (e.g., -, !, ., *, ;, $, #, @)
        $matches = [[], [], [], []];
        preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{7,}$/', $password, $matches[0]);
        preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@#$%^&*()\-_=+\[{\]}\\|;:\'",<.>\/?])[\s\S]{7,}$/', $password, $matches[1]);
        preg_match('/^(?=.*[a-z])(?=.*\d)(?=.*[`~!@#$%^&*()\-_=+\[{\]}\\|;:\'",<.>\/?])[\s\S]{7,}$/', $password, $matches[2]);
        preg_match('/^(?=.*[A-Z])(?=.*\d)(?=.*[`~!@#$%^&*()\-_=+\[{\]}\\|;:\'",<.>\/?])[\s\S]{7,}$/', $password, $matches[3]);
        if (!$matches[0] && !$matches[1] && !$matches[2] && !$matches[3]) {
            $msg = static::getInvalidPasswordMessages();
            return implode(', and ', $msg);
        }

        if ($user) {
            // Non-repeatable after 10 generations
            $history = Util::json($user, 'meta', 'passwordHistory');
            if ($history) {
                if (Util::checkAnyHashPasswords($history, $password)) {
                    return 'You cannot pick the same password that was in the last 10 changes.';
                }
            }
        }

        return true;
    }

    public static function postActionsAfterChangingPassword(User $user, $password)
    {
        if (!$password) {
            return;
        }

        LoginAttempt::create($user, true, 'change_password');

        // Expires every 90 days (default configuration). Need to save password update time.
        Util::updateJson($user, 'meta', [
            'resetPassword' => true,
            'passwordUpdatedAt' => time(),
        ]);

        // Non-repeatable after 10 generations
        $history = Util::json($user, 'meta', 'passwordHistory');
        if (!$history) {
            $history = [];
        }
        if (!Util::checkAnyHashPasswords($history, $password)) {
            $history[] = Util::encodePassword($user, $password);
            $history = array_unique($history);
            $count = count($history);
            if ($count > 10) {
                $history = array_slice($history, $count - 10);
            }
            Util::updateJson($user, 'meta', [
                'passwordHistory' => $history,
            ]);
        }
    }

    public static function sendResetPasswordEmail(User $entity, CardProgram $cp = null)
    {
        $entity->setConfirmationToken(Util::guid())
            ->setPasswordRequestedAt(Carbon::now()->addDay())
            ->persist();
        Email::sendWithTemplateToUser($entity, Email::TEMPLATE_WELCOME, [
            'action_url' => '/resetting/password/' . $entity->getConfirmationToken(),
        ], $cp);
    }

    public static function sendVerificationCodeEmail(User $entity)
    {
        $key = 'user_common_verify_code_' . $entity->getId();
        if (Data::get($key)) {
            throw PortalException::temp('Please wait at least 2 minutes to send a new code!');
        }

        $verifyCode = Util::randNumber(6);
        Data::set($key, $verifyCode, false, 120);
        Email::sendWithTemplateToUser($entity, Email::VERIFY_CODE, [
            'verifyCode' => $verifyCode,
        ]);
    }

    public static function getVerificationCode(User $entity)
    {
        $key = 'user_common_verify_code_' . $entity->getId();
        return Data::get($key);
    }

    public static function deactivateUser(User $user, string $reason)
    {
        $user->addNote($reason, from: Util::user()?->getId() ?? Util::ADMIN_ID);

        if (Bundle::isTransferMex()) {
            MemberService::deactivateAccount($user, $reason);
        } else {
            $user->setStatus(User::STATUS_INACTIVE, $reason)
                ->persist();
        }
    }
}
