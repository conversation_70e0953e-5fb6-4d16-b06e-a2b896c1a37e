<?php

namespace SkuxBundle;

use CoreBundle\Entity\Module;
use CoreBundle\Entity\Role;
use CoreBundle\Utils\Util;
use Symfony\Component\HttpKernel\Bundle\Bundle;

class SkuxBundle extends Bundle
{
    public static function getApiPrefix()
    {
        return '/api/skux/';
    }

    public static function getUserDashboardType()
    {
        return 'skux';
    }

    public static function apiAdminUserProfile($data)
    {
        $data['cpKey']       = 'cp_skux';
        $data['adminLayout'] = 'h';

        return $data;
    }

    public static function getMemberRoles()
    {
        return [
            Role::ROLE_SKUX_CONSUMER,
            Role::ROLE_SKUX_APP_USER,
        ];
    }

    public static function getMenusForCurrentUser()
    {
        $platform = Util::platform();
        $user     = Util::user();
        $sa       = $user->isMasterAdmin();
        $api      = $user->isApiInvoker();
        $menu     = [];

        if ($api || $sa) {
            $developers =  [
                [
                    'id' => Module::ID_DEV_DOCUMENTATION,
                    'name' => 'Documentation',
                    'route' => '/developer',
                    'mdRoute' => ''
                ],
                [
                    'id' => Module::ID_DEVELOPER_RESOURCES,
                    'name' => 'API Config',
                    'route' => '/admin/developer',
                    'mdRoute' => ''
                ],
            ];
            $menu[] = [
                'id' => Module::ID_DEVELOPER,
                'name' => 'Developer Tools',
                'route' => '',
                'icon' => 'fa fa-fw fa-code',
                'mdIcon' => 'mdi-code-tags',
                'svgIcon' => false,
                'children' => $developers,
            ];
        }

        return $menu;
    }

    public static function getApiPermissionTree($default)
    {
        return [
            'User' => [
                'Register' => 'api_skux_user_register',
                'Update' => 'api_skux_user_update',
                'Close' => 'api_skux_user_close',
            ],
            'Cards' => [
                'Assign Card' => 'api_skux_card_assign',
                'List Cards' => 'api_skux_card_list',
                'Load Value' => 'api_skux_card_load',
                'Get Card Balance' => 'api_skux_card_balance',
                'Get Purse List' => 'api_skux_card_purses',
                'Get Purse Details' => 'api_skux_card_purse',
                'Add Comment' => 'api_skux_card_comment',
                'Change Status' => 'api_skux_card_change_status',
                'Account to account transfer' => 'api_skux_card_transfer',
                'Get Transactions' => 'api_skux_card_transactions',
                'Get Card Info by User' => 'api_skux_get_person_and_card',
            ],
        ];
    }
}
