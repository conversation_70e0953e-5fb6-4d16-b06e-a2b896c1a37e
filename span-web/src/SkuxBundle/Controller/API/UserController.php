<?php


namespace SkuxBundle\Controller\API;

use Carbon\Carbon;
use CoreBundle\Entity\Role;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use SalexUserBundle\Entity\User;
use OpenApi\Annotations as SWG;
use Symfony\Component\Routing\Annotation\Route;

class UserController extends BaseController
{
    /**
     * @Route("/api/skux/user/register", methods={"POST"}, name="api_skux_user_register")
     * @SWG\Post(
     *   tags={"User"},
     *   summary="Register",
     *   description="Register a user",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"firstName", "lastName", "phone", "address", "city", "country"}, properties={
     *     @SWG\Property(property="firstName", description="First name", type="string"),
     *     @SWG\Property(property="lastName", description="Last name", type="string"),
     *     @SWG\Property(property="birthday", description="Date of birth. Format: YYYY-MM-DD", type="string", format="date"),
     *     @SWG\Property(property="emailAddress", description="Email address. Unique.", type="string", format="email"),
     *     @SWG\Property(property="gender", description="Gender, Male or Female", type="string", enum={"Male", "Female"}),
     *     @SWG\Property(property="phone", description="Mobile phone", type="string"),
     *     @SWG\Property(property="address", description="First address line", type="string"),
     *     @SWG\Property(property="addressline", description="Second address line", type="string"),
     *     @SWG\Property(property="city", description="City name", type="string"),
     *     @SWG\Property(property="country", description="ISO country code, 3 letters", type="string"),
     *     @SWG\Property(property="zip", description="Zip/Postal code", type="string"),
     *     @SWG\Property(property="ssn", description="SSN", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxUserResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function register()
    {
        $request = $this->validateParameters(__METHOD__);
        [, $country] = $this->validateCountryCode('country');

        $user = new User();
        $user->setEnabled(true)
            ->setPlainPassword(Util::generatePassword())
            ->setStatus(User::STATUS_ACTIVE);

        $phone = $request->get('phone');
        $phone = Util::formatFullPhone($phone, $country);
        $email = $request->get('emailAddress');
        if (!$email) {
            $email = 'hans+skf_' . Util::intPhone($phone, true) . '_' . Util::randString(4)
                     . '@ternitup.com';
        }

        $birthday = $request->get('birthday')
            ? Carbon::createFromFormat('Y-m-d', $request->get('birthday'))
            : null;

        $user->setFirstName($request->get('firstName'))
            ->setLastName($request->get('lastName'))
            ->setBirthday($birthday)
            ->setGender($request->get('gender'))
            ->setAddress($request->get('address'))
            ->setAddressline($request->get('addressline'))
            ->setCountry($country)
            ->setCity($request->get('city'))
            ->setZip($request->get('zip'))
            ->setMobilephone($phone)
            ->setEmail($email)
            ->setUsername($email)
            ->setSource('skux_user_api')
            ->ensureRole(Role::ROLE_SKUX_CONSUMER)
            ->setRegistrationIp($user->getRegistrationIp() ?: Security::getClientIp())
            ->persist();

        Util::updateMeta($user, [
            'ssn' => SSLEncryptionService::encrypt($request->get('ssn')),
        ]);

        return new SuccessResponse($user->toApiArrayForSkux());
    }

    /**
     * @Route("/api/skux/user/update", methods={"POST"}, name="api_skux_user_update")
     * @SWG\Post(
     *   tags={"User"},
     *   summary="Update user",
     *   description="Update user profile",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userId"}, properties={
     *     @SWG\Property(property="userId", description="The user ID returned from the user register API", type="integer"),
     *     @SWG\Property(property="firstName", description="First name", type="string"),
     *     @SWG\Property(property="lastName", description="Last name", type="string"),
     *     @SWG\Property(property="birthday", description="Date of birth. Format: YYYY-MM-DD", type="string", format="date"),
     *     @SWG\Property(property="emailAddress", description="Email address. Unique.", type="string", format="email"),
     *     @SWG\Property(property="gender", description="Gender, Male or Female", type="string", enum={"Male", "Female"}),
     *     @SWG\Property(property="phone", description="Mobile phone", type="string"),
     *     @SWG\Property(property="address", description="First address line", type="string"),
     *     @SWG\Property(property="addressline", description="Second address line", type="string"),
     *     @SWG\Property(property="city", description="City name", type="string"),
     *     @SWG\Property(property="country", description="ISO country code, 3 letters", type="string"),
     *     @SWG\Property(property="zip", description="Zip/Postal code", type="string"),
     *     @SWG\Property(property="ssn", description="SSN", type="string"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxUserResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function update()
    {
        $request = $this->validateParameters(__METHOD__);
        $user = $this->validateUser($request->get('userId'), Role::ROLE_SKUX_CONSUMER, false);

        $country = $user->getCountry();
        if ($request->get('country')) {
            [, $country] = $this->validateCountryCode('country');
            $user->setCountry($country);
        }

        $phone = $request->get('phone');
        if ($phone) {
            $phone = Util::formatFullPhone($phone, $country);
            $user->setMobilephone($phone);
        }

        $birthday = $request->get('birthday')
            ? Carbon::createFromFormat('Y-m-d', $request->get('birthday'))
            : $user->getBirthday();

        $user->setFirstName($request->get('firstName') ?? $user->getFirstName())
            ->setLastName($request->get('lastName') ?? $user->getLastName())
            ->setBirthday($birthday)
            ->setGender($request->get('gender') ?? $user->getGender())
            ->setAddress($request->get('address') ?? $user->getAddress())
            ->setAddressline($request->get('addressline') ?? $user->getAddressline())
            ->setCity($request->get('city') ?? $user->getCity())
            ->setZip($request->get('zip') ?? $user->getZip())
            ->setEmail($request->get('emailAddress') ?? $user->getEmail())
            ->ensureRole(Role::ROLE_SKUX_CONSUMER)
            ->persist();

        if ($request->get('ssn')) {
            Util::updateMeta($user, [
                'ssn' => SSLEncryptionService::encrypt($request->get('ssn')),
            ]);
        }

        return new SuccessResponse($user->toApiArrayForSkux());
    }

    /**
     * @Route("/api/skux/user/close", methods={"POST"}, name="api_skux_user_close")
     * @SWG\Post(
     *   tags={"User"},
     *   summary="Close user",
     *   description="Change the user status to `closed`",
     *   security={{"x-signature":{}, "x-salt":{}, "x-timestamp":{}, "x-access-key":{}}},
     *   @SWG\RequestBody(required=true, @SWG\MediaType(mediaType="application/x-www-form-urlencoded", schema=@SWG\Schema(required={"userId"}, properties={
     *     @SWG\Property(property="userId", description="The user ID returned from the user register API", type="integer"),
     *   }))),
     *   @SWG\Response(response=200, ref="#/components/schemas/SkuxUserResponse"),
     *   @SWG\Response(response=400, ref="#/components/responses/ErrorParamResponse"),
     *   @SWG\Response(response=401, ref="#/components/responses/RequireAuthResponse"),
     *   @SWG\Response(response=403, ref="#/components/responses/AuthFailedResponse"),
     * )
     */
    public function close()
    {
        $request = $this->validateParameters(__METHOD__);
        $user = $this->validateUser($request->get('userId'), Role::ROLE_SKUX_CONSUMER, false);

        $user->setStatus(User::STATUS_CLOSED)
            ->persist();

        return new SuccessResponse($user->toApiArrayForSkux());
    }
}
