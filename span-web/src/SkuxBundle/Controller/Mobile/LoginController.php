<?php


namespace SkuxBundle\Controller\Mobile;


use Carbon\Carbon;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\Role;
use CoreBundle\Entity\UserToken;
use CoreBundle\Exception\FailedException;
use CoreBundle\Response\FailedResponse;
use CoreBundle\Response\SuccessResponse;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Security;
use CoreBundle\Utils\Util;
use MobileBundle\MobileBundle;
use SalexUserBundle\Entity\User;
use SkuxBundle\Services\UserService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoginController extends BaseController
{
    public $protected = false;

    protected function validatePhoneNumber()
    {
        $phone = trim($this->request->get('phone'));
        if (!$phone) {
            throw new FailedException('Empty phone number!');
        }
        return '+' . Util::intPhone($phone, true);
    }

    /**
     * @Route("/skux/m/register", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function registerAction(Request $request)
    {
        $phone = $this->validatePhoneNumber();
        $code = $this->validateRequired('code');
        $device = MobileBundle::getClientDeviceId();
        $data = $this->skux()->login($phone, $code, $device);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/skux/m/login/sms", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function smsAction(Request $request)
    {
        $phone = $this->validatePhoneNumber();
        $data = $this->skux()->sendVerificationCode($phone);
        $typeName = $data['__typename'] ?? '';
        $msg = '';
        if ($typeName === 'PhoneValidationInProcess') {
            $msg = 'Phone validation is in progress and taking a longer time. The SMS will be sent to you once it is verified. Please try again later if the SMS does not arrive for a long time.';
        }
        return new SuccessResponse($data, $msg);
    }

    /**
     * @Route("/skux/m/login/accept-privacy-policy", methods={"POST"})
     * @param Request $request
     *
     * @return SuccessResponse
     */
    public function acceptPrivacyPolicy(Request $request)
    {
        $phone = $this->validatePhoneNumber();
        $data = $this->skux()->privacyPolicyAccepted($phone);
        return new SuccessResponse($data);
    }

    /**
     * @Route("/skux/m/login/verify", methods={"POST"})
     * @param Request $request
     *
     * @return FailedResponse|SuccessResponse
     */
    public function verifyAction(Request $request)
    {
        $phone = $this->validatePhoneNumber();

        $context = Util::jsonSimpleRequest($request);
        $context['phone'] = $phone;
        Log::debug('Log SKUx login verification details', $context);

        $code = $this->validateRequired('code');
        $device = MobileBundle::getClientDeviceId();

        $skux = $this->skux();
        $skux->login($phone, $code, $device);
        $detail = $skux->currentUser();
        if (empty($detail['uuid'])) {
            return new FailedResponse('Empty user id!');
        }

        $phone = Util::formatFullPhone($phone);
        $email = 'hans+skux_' . $detail['uuid'] . '@ternitup.com';
        $user = User::findPlatformUserByEmail($email);
        if (!$user) {
            $profile = $detail['profile'] ?? [];
            $firstName = $profile['firstName'] ?? '';
            $lastName = $profile['lastName'] ?? '';

            if (Util::isUUID($firstName)) {
                $firstName = '';
            }
            if (Util::isUUID($lastName)) {
                $lastName = '';
            }

            $user = new User();
            $user->setEnabled(true)
                ->setPlainPassword(Util::generatePassword())
                ->setStatus(User::STATUS_ACTIVE)
                ->setFirstName($firstName ?: 'SKUx')
                ->setLastName($lastName ?: 'User')
                ->setMobilephone($phone)
                ->setCountry(Util::parseCountryFromPhoneNumber($phone) ?? Country::usa())
                ->setExternalId($detail['uuid'])
                ->setSource('skux_login_verify')
                ->changeEmail($email, Role::ROLE_SKUX_APP_USER)
            ;
        }

        $user->ensureRole(Role::ROLE_SKUX_APP_USER)
            ->setRegistrationOS($user->getRegistrationOS() ?: MobileBundle::getClientOs())
            ->setRegistrationIp($user->getRegistrationIp() ?: Security::getClientIp())
            ->persist();
        Util::$user = $user;

        $skux->updateToUser($user);
        $skux->updateUserProfile($user, $detail);

        $data = $user->toSkuxApiArray();
        $data['lastOfferViewed'] = Util::meta($user, 'skuxLastOfferViewed');
        if (empty($data['lastOfferViewed'])) {
            try {
                $data['lastOfferViewed'] = $skux->lastOfferViewed();
                if (!empty($data['lastOfferViewed'])) {
                    Util::updateMeta($user, [
                        'skuxLastOfferViewed' => $data['lastOfferViewed'],
                    ]);
                }
            } catch (\Exception $ex) {
                $data['lastOfferViewed'] = null;
                Log::warn('Failed to query the last offer viewed: ' . $ex->getMessage(), [
                    'user' => $user->getId(),
                ]);
            }
        }

        $data['everClaimedOffer'] = $user->everClaimedOffer();

        $ut = UserToken::create($user);
        $data['token'] = $ut->getToken();

        return new SuccessResponse($data);
    }
}
