<?php

namespace SkuxBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Utils\Data;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Util;
use Exception;
use GraphQL\Client;
use GraphQL\Exception\QueryError;
use GraphQL\Mutation;
use GraphQL\Query;
use GraphQL\QueryBuilder\QueryBuilder;
use GraphQL\RawObject;
use GraphQL\SchemaObject\DeepLinkInputObject;
use GraphQL\SchemaObject\LoginInputObject;
use GraphQL\SchemaObject\OfferListOptionsInputObject;
use GraphQL\SchemaObject\OfferQueryObject;
use GraphQL\SchemaObject\RetailerListOptionsInputObject;
use GraphQL\SchemaObject\RetailerQueryObject;
use GraphQL\SchemaObject\RootGetClaimOfferTaskArgumentsObject;
use GraphQL\SchemaObject\RootLastOfferViewedArgumentsObject;
use GraphQL\SchemaObject\RootNeedsTermsAndConditionsArgumentsObject;
use GraphQL\SchemaObject\RootOfferArgumentsObject;
use GraphQL\SchemaObject\RootOffersArgumentsObject;
use GraphQL\SchemaObject\RootQueryObject;
use GraphQL\SchemaObject\RootRetailerArgumentsObject;
use GraphQL\SchemaObject\RootRetailersArgumentsObject;
use GraphQL\SchemaObject\UserCardQueryObject;
use GraphQL\SchemaObject\UserPurseQueryObject;
use GraphQL\Variable;
use PortalBundle\Exception\PortalException;
use Psr\Http\Message\ResponseInterface;
use SalexUserBundle\Entity\User;

final class SkuxService
{
    public $live;
    public $refreshToken;
    public $accessToken;
    public $tokenExpiration;

    /** @var User */
    public $user;

    public function __construct($accessToken = null, $refreshToken = null, $live = null)
    {
        if ($live === null) {
            $live = Util::isLive();
        }
        $this->live = $live;
        $this->accessToken = $accessToken;
        $this->refreshToken = $refreshToken;
    }

    public static function createForUser(User $user)
    {
        $accessToken = Util::meta($user, 'skuxAccessToken');
        $refreshToken = Util::meta($user, 'skuxRefreshToken');
        $tokenExpiration = Util::meta($user, 'skuxTokenExpiration');

        $s = new static($accessToken, $refreshToken, null);
        $s->user = $user;
        $s->tokenExpiration = $tokenExpiration;
        return $s;
    }

    public function getRealtimeTokens()
    {
        $when = null;
        if ($this->user) {
            $uid = $this->user->getId();
            $this->accessToken = Data::get('skux_access_token_' . $uid) ?: $this->accessToken;
            $this->refreshToken = Data::get('skux_refresh_token_' . $uid) ?: $this->refreshToken;
            $this->tokenExpiration = Data::get('skux_expire_token_' . $uid) ?: $this->tokenExpiration;
            $when = Data::get('skux_when_token_' . $uid) ?? microtime();
        }
        return [
            $this->accessToken,
            $this->refreshToken,
            $this->tokenExpiration,
            $when,
        ];
    }

    public function updateRealtimeTokens($user = null)
    {
        $user = $user ?? $this->user;
        if ($user) {
            $uid = $user->getId();
            Data::set('skux_access_token_' . $uid, $this->accessToken);
            Data::set('skux_refresh_token_' . $uid, $this->refreshToken);
            Data::set('skux_expire_token_' . $uid, $this->tokenExpiration);
            Data::set('skux_when_token_' . $uid, microtime());
        }
    }

    public function updateToUser(User $user = null)
    {
        $user = $user ?? $this->user;
        if (!$user) {
            if (!Util::isLive()) {
                Log::debug('SKUX log: unknown user...');
            }
            return;
        }

        $this->updateRealtimeTokens($user);

        $meta = Util::meta($user);
        $oldAt = $meta['skuxAccessToken'] ?? null;
        $oldRt = $meta['skuxRefreshToken'] ?? null;

        [$at, $rt, $expiry] = $this->getRealtimeTokens();
        $meta['skuxAccessToken'] = $at;
        $meta['skuxRefreshToken'] = $rt;
        $meta['skuxTokenExpiration'] = $expiry;

        if ($oldAt !== $at) {
            $meta['skuxAccessTokenAt'] = Util::formatApiDateTime(Carbon::now());
        }
        if ($oldRt !== $rt) {
            $meta['skuxRefreshTokenAt'] = Util::formatApiDateTime(Carbon::now());
        }

        if (!Util::isLive()) {
            Log::debug('SKUX log: saving user tokens', [
                'user' => $user->getId(),
                'oldAccessToken' => $oldAt,
                'oldRefreshToken' => $oldRt,
                'newMeta' => $meta,
            ]);
        }

        Util::updateMeta($user, $meta);
        $this->user = $user;
    }

    protected function request($query, $typeName = '', $variables = [], $handleError = true, $retry = 0)
    {
        $saveEi = Util::isDev() || !Util::isLive();
        if ($query instanceof Mutation && $typeName !== 'refreshToken') {
            $saveEi = true;
        }

        $live = Util::isLive();
        $qs = (string)$query;
        if ($live) {
            $fieldsToClean = [
                'refreshToken',
                'verificationCode',
            ];
            foreach ($fieldsToClean as $field) {
                $matches = [];
                preg_match('|' . $field . ': \"(.+)\"|', $qs, $matches);
                if (!empty($matches[1])) {
                    $qs = str_replace($matches[1], '******', $qs);
                }
            }
        }

        $domain = $this->live ? 'https://app.skux.io' : 'https://stageapp.skux.io';

        [$at, , $expiry, $when] = $tokens = $this->getRealtimeTokens();
        if ($retry < 5
            && $expiry
            && $this->refreshToken
            && $typeName !== 'refreshToken')
        {
            // Refresh the token directly if it's expired or will be expired in 30 seconds
            $expiry = (int)$expiry;
            if ($expiry - time() < 30) {
                try {
                    $this->refreshToken();
                } catch (\Exception $ex) {
                    Log::debug('Failed to refresh token when checking token expiration: '
                               . $ex->getMessage());
                }
            }
        }

        $client = new Client(
            $domain . '/v1/graphql',
            [
                'Authorization' => 'Bearer ' . $at,
            ],
        );

        $context = [
            'query' => $qs,
            'variables' => $variables,
            'user' => $this->user ? $this->user->getId() : null,
        ];

        if (!$live) {
            $context['tokens'] = $tokens;
        }

        $ei = ExternalInvoke::create('skux_' . $typeName, $context, null, $saveEi);

        try {
            $results = $client->runQuery($query, true, $variables);
        } catch (Exception $exception) {
            if ($exception instanceof QueryError) {
                $ei->setResponse($exception->getErrorDetails());
            }
            $msg = $exception->getMessage();
            if (Util::hasPrefix($msg, [
                'Access denied! JWT is expired.',
            ])) {
                if (
                    $retry < 5
                    && $this->refreshToken
                    && $typeName !== 'refreshToken'
                ) {
                    [, , , $newWhen] = $this->getRealtimeTokens();
                    if ($when === $newWhen) { // Tokens were not updated yet
                        try {
                            $this->refreshToken();
                        } catch (\Exception $ex) {
                            Log::debug('Failed to refresh token: ' . $ex->getMessage());
                        }
                    }
                    return $this->request($query, $typeName, $variables, $handleError, ++$retry);
                }
                $msg .= ' Please login again.';
            }

            $msg = 'Got query error with SKUx ' . $typeName . ': ' . $msg;
            $ei->fail(null, $msg)
                ->persist();
            throw PortalException::temp($msg);
        }

        $resp = $results->getResponseObject();
        if ($resp instanceof ResponseInterface) {
            $updated = false;
            $refreshToken = $resp->getHeaderLine('x-skux-refresh-token');
            if ($refreshToken && $this->refreshToken !== $refreshToken) {
                $this->refreshToken = $refreshToken;
                $updated = true;
            }
            $accessToken = $resp->getHeaderLine('x-skux-access-token');
            if ($accessToken && $this->accessToken !== $accessToken) {
                $this->accessToken = $accessToken;
                $expiration = $resp->getHeaderLine('x-skux-token-expiration');
                if ($expiration) {
                    $this->tokenExpiration = $expiration;
                }
                $updated = true;
            }
            if ($updated) {
                $this->updateToUser();
            }

            if (!$live && $saveEi) {
                Util::updateMeta($ei, [
                    'responseHeaders' => $resp->getHeaders(),
                ]);
            }
        }

        $data = $results->getData();

        $ei->setResponse($results->getResponseBody());
        if ($saveEi) {
            $ei->persist();
        }

        if (empty($data)) {
            $msg = 'Empty result from the SKUx API ' . $typeName;
            $ei->fail(null, $msg)
                ->persist();
            throw PortalException::temp($msg);
        }
        if (!$typeName) {
            return $data;
        }
        if (empty($data[$typeName])) {
            $msg = 'Empty result from the SKUx API within type ' . $typeName;
            $ei->fail(null, $msg)
                ->persist();
            throw PortalException::temp($msg);
        }
        $result = $data[$typeName];
        if ($handleError && !empty($result['errorCode'])) {
            $msg = $result['message'] ?? $result['errorCode'];
            $ei->fail(null, $msg)
                ->persist();
            Log::warn('(SKUx) Failed to call ' . $typeName . ': ' . $msg);
            throw PortalException::temp($msg);
        }
        return $result;
    }

    protected function mergeErrorResultSelection(array $set = [])
    {
        return array_merge([
            '... on ErrorResult {
                    errorCode
                    message
                }',
            '__typename',
        ], $set);
    }

    public function sendVerificationCode($phoneNumber)
    {
        $mutation = (new Mutation(__FUNCTION__))
            ->setArguments(['phoneNumber' => $phoneNumber])
            ->setSelectionSet($this->mergeErrorResultSelection([
                '... on PhoneValidationInProcess{
                    success
                    status
                }',
                '... on PhoneValidationSuccess{
                    success
                }',
                '... on PrivacyPolicyRequired{
                    success
                    status
                    privacyPolicyLink
                }',
            ]));
        $data = $this->request($mutation, __FUNCTION__);
        $typeName = $data['__typename'] ?? '';
        if ($typeName === 'PhoneValidationLimitReached') {
            throw PortalException::temp('Phone validation limit reached!');
        }
        return $data;
    }

    public function privacyPolicyAccepted($phoneNumber)
    {
        $mutation = (new Mutation(__FUNCTION__))
            ->setArguments(['phoneNumber' => $phoneNumber])
            ->setSelectionSet($this->mergeErrorResultSelection([
                '... on PrivacyPolicyAcceptedSuccess{
                    success
                    status
                }',
            ]));
        return $this->request($mutation, __FUNCTION__);
    }

    public function register($phoneNumber, $verificationCode, $deviceId)
    {
        $mutation = (new Mutation(__FUNCTION__))
            ->setArguments([
                'input' => '$input',
            ])
            ->setVariables([
                new Variable('input', 'RegisterInput', true),
            ])
            ->setSelectionSet($this->mergeErrorResultSelection([
                '... on User {
                        phoneNumber
                    }',
            ]));
        return $this->request($mutation, __FUNCTION__, [
            'input' => [
                'phoneNumber' => $phoneNumber,
                'verificationCode' => $verificationCode,
                'deviceId' => $deviceId,
            ],
        ]);
    }

    public function login($phoneNumber, $verificationCode, $deviceId)
    {
        $input = new LoginInputObject();
        $input->setPhoneNumber($phoneNumber)
            ->setVerificationCode($verificationCode)
            ->setDeviceId($deviceId);

        $mutation = (new Mutation(__FUNCTION__))
            ->setArguments([
                'input' => $input,
            ])
            ->setSelectionSet($this->mergeErrorResultSelection([
                '... on User {
                        phoneNumber
                    }',
            ]));
        return $this->request($mutation, __FUNCTION__);
    }

    public function refreshToken()
    {
        [, $rt] = $this->getRealtimeTokens();
        $mutation = (new Mutation(__FUNCTION__))
            ->setArguments([
                'refreshToken' => $rt,
            ])
            ->setSelectionSet(['__typename']);
        return $this->request($mutation, __FUNCTION__);
    }

    protected function auth()
    {
        if (!$this->accessToken) {
            throw PortalException::temp('(SKUx) Not logged in yet!');
        }
    }

    public function currentUser()
    {
        $this->auth();

        $rootObject = new RootQueryObject();
        $rootObject->selectCurrentUser()
            ->selectUuid()
            ->selectPhoneNumber()
            ->selectProfile()
                ->selectEmailAddress()
                ->selectEmailAddressVerified()
                ->selectFirstName()
                ->selectLastName()
                ->selectHasIdentityProvider()
        ;

        return $this->request($rootObject->getQuery(), __FUNCTION__);
    }

    public function getLoginEndpoint()
    {
        $this->auth();

        $rootObject = new RootQueryObject();
        $rootObject->selectGetLoginEndpoint()
            ->onLoginEndpointResponse()
                ->selectURL();

        return $this->request($rootObject->getQuery(), __FUNCTION__);
    }

    public function updateUserProfile(User $user = null, $detail = null)
    {
        $user = $user ?? $this->user;
        $detail = $detail ?? $this->currentUser();
        $profile = $detail['profile'] ?? [];
        Util::updateMeta($user, [
            'skuxProfile' => $profile,
        ]);
    }

    protected function selectRetailer(RetailerQueryObject $retailer)
    {
        $retailer->selectUuid()
            ->selectName()
            ->selectLogoUrl()
            ->selectTotalOffers();
    }

    protected function selectOffer(OfferQueryObject $offer, $withCard = false)
    {
        $o = $offer->selectUuid()
            ->selectType()
            ->selectDescription()
            ->selectBannerUrl()
            ->selectContentUrl()
            ->selectAmountInCents()
            ->selectStatus()
            ->selectAssignedOn()
            ->selectExpiresAt();
        $this->selectRetailer($o->selectRetailers());

        if ($withCard) {
            $this->selectUserCard($o->selectUserCard()->onUserCard());
        }

        return $o;
    }

    protected function selectUserCard(UserCardQueryObject $uc, $withPurses = false)
    {
        $uc->selectUuid()
            ->selectAccountNumber()
            ->selectCardName()
            ->selectMaskedCardNumber()
            ->selectCardImage()
            ->selectTermsAndConditionsUrl()
            ->selectStatus()
        ;

        if ($withPurses) {
            $purses = $uc->selectUserPurses()
                ->selectTotalItems()
                ->selectItems();
            $this->selectPurse($purses, true);
        }
    }

    protected function selectPurse(UserPurseQueryObject $purse, $withOffer = false)
    {
        $purse->selectUuid()
            ->selectStatus()
            ->selectOfferUUID()
            ->selectInitialBalanceInCents()
            ->selectCurrentBalanceInCents()
            ->selectOfferCodeUUID();
        if ($withOffer) {
            $this->selectOffer($purse->selectOffer());
        }
    }

    protected function selectError($error)
    {
        $error->selectErrorCode()
            ->selectMessage();
    }

    public function lastOfferViewed($offerId = null)
    {
        $this->auth();

        $arg = new RootLastOfferViewedArgumentsObject();
        $arg->setFirstTime(true);
        if ($offerId) {
            $link = new DeepLinkInputObject();
            $link->setOfferUUID($offerId);
            $arg->setDeepLink($link);
        }

        $rootObject = new RootQueryObject();
        $lov = $rootObject->selectLastOfferViewed($arg);
        $this->selectOffer($lov->onOffer(), true);
        $this->selectError($lov->onOfferNotFoundError());
        $this->selectError($lov->onOfferNotExistsError());

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        return $this->fixOfferDetails($data);
    }

    public function offers(string $options = null)
    {
        $this->auth();

        $builder = new Query('offers');
        $builder->setSelectionSet([
            'totalItems',
            (new Query('items'))->setSelectionSet([
                'uuid',
                'type',
                'description',
                'bannerUrl',
                'contentUrl',
                'amountInCents',
                'status',
                'assignedOn',
                'expiresAt',
                (new Query('retailers'))->setSelectionSet([
                    'uuid',
                    'name',
                    'logoUrl',
                    'totalOffers',
                ]),
                'userCard {
                    ... on UserCard {
                        uuid
                        accountNumber
                        maskedCardNumber
                        cardName
                        cardImage
                        status
                        termsAndConditionsUrl
                    }
                }',
            ]),
        ]);

        if ($options) {
            $builder->setArguments([
                'options' => new RawObject($options),
            ]);
        }

        $data = $this->request($builder, __FUNCTION__);
        $items = $data['items'] ?? [];
        foreach ($items as $i => $item) {
            $items[$i] = $this->fixOfferDetails($item);
        }
        $data['items'] = $items;
        return $data;
    }

    public function offer($uuid)
    {
        $this->auth();

        $options = new RootOfferArgumentsObject();
        $options->setUuid($uuid);

        $rootObject = new RootQueryObject();
        $item = $rootObject->selectOffer($options);
        $item->onOfferNotExistsError()
            ->selectErrorCode()
            ->selectMessage();
        $this->selectOffer($item->onOffer(), true);

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        return $this->fixOfferDetails($data);
    }

    public function needsTermsAndConditions($offerUuid)
    {
        $this->auth();

        $options = new RootNeedsTermsAndConditionsArgumentsObject();
        $options->setOfferUUID($offerUuid);

        $rootObject = new RootQueryObject();
        $union = $rootObject->selectNeedsTermsAndConditions($options);
        $union->onNeedsTermsAndConditions()
                ->selectNeedsTermsAndConditions()
                ->selectTermsAndConditionsUrl();

        $this->selectError($union->onOfferNotExistsError());
        $this->selectError($union->onOfferNotFoundError());

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        $data['termsAndConditionsURL'] = $data['termsAndConditionsUrl'] ??
                                         $data['termsAndConditionsURL'] ??
                                         null;
        return $data;
    }

    public function claimOffer($offerUuid, $deviceId)
    {
        $this->auth();

        $mutation = (new Mutation(__FUNCTION__))
            ->setArguments([
                'offerUUID' => $offerUuid,
                'deviceId' => $deviceId,
            ])
            ->setSelectionSet($this->mergeErrorResultSelection([
                '... on ExternalError {
                        errorCode
                        message
                    }',
                '... on OfferNotExistsError {
                        errorCode
                        message
                    }',
                '... on ClaimPurseTask {
                        uuid
                        status
                    }',
                '... on UserCard {
                        uuid
                        accountNumber
                        maskedCardNumber
                        cardName
                        cardImage
                        termsAndConditionsUrl
                        cardStatus: status
                    }',
            ]));

        $data = $this->request($mutation, __FUNCTION__);
        if (!empty($data['maskedCardNumber'])) {
            $data = $this->fixCardDetails($data);
        }
        return $data;
    }

    public function getClaimPurseTask($taskUuid)
    {
        $this->auth();

        $arg = new RootGetClaimOfferTaskArgumentsObject();
        $arg->setUuid($taskUuid);

        $rootObject = new RootQueryObject();
        $task = $rootObject->selectGetClaimOfferTask($arg);
        $this->selectError($task->onExternalError());

        $ucQuery = $task->onClaimPurseTask()
            ->selectUuid()
            ->selectStatus()
            ->selectUserCard();
        $this->selectUserCard($ucQuery);

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        if (!empty($data['userCard'])) {
            $data['userCard'] = $this->fixCardDetails($data['userCard']);
        }
        return $data;
    }

    public function getUserCards()
    {
        $rootObject = new RootQueryObject();
        $cardQuery = $rootObject->selectGetUserCards()
            ->selectTotalItems()
            ->selectItems();
        $this->selectUserCard($cardQuery, true);

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        $items = $data['items'] ?? [];
        foreach ($items as $i => $uc) {
            $purses = $uc['userPurses'] ?? [];
            $purseItems = $purses['items'] ?? [];
            foreach ($purseItems as $j => $pi) {
                $offer = $pi['offer'] ?? [];
                $purseItems[$j]['offer'] = $this->fixOfferDetails($offer);
            }
            $purses['items'] = $purseItems;
            $items[$i]['userPurses'] = $purses;
            $items[$i] = $this->fixCardDetails($items[$i]);
        }
        $data['items'] = $items;
        return $data;
    }

    public function retailers()
    {
        $this->auth();

        $options = new RetailerListOptionsInputObject();

        $arg = new RootRetailersArgumentsObject();
        $arg->setOptions($options);

        $rootObject = new RootQueryObject();
        $items = $rootObject->selectRetailers($arg)
            ->selectTotalItems()
            ->selectItems();
        $this->selectRetailer($items);

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        $items = $data['items'] ?? [];
        foreach ($items as $i => $item) {
            $items[$i] = $this->fixRetailerDetails($item);
        }
        $data['items'] = $items;
        return $data;
    }

    public function retailer($id)
    {
        $this->auth();

        $options = new RootRetailerArgumentsObject();
        $options->setId($id);

        $rootObject = new RootQueryObject();
        $item = $rootObject->selectRetailer($options);
        $item->onRetailerNotExistsError()
            ->selectErrorCode()
            ->selectMessage();
        $this->selectRetailer($item->onRetailer());

        $data = $this->request($rootObject->getQuery(), __FUNCTION__);
        return $this->fixRetailerDetails($data);
    }

    public function fixOfferDetails($offer)
    {
        $url = $offer['contentUrl'] ?? '';
        [$at] = $this->getRealtimeTokens();
        if (strpos($url, '{app-token}') !== false) {
            $url = str_replace('{app-token}', $at, $url);
        } else {
            $url .= '?access_token=' . $at;
        }
        $offer['contentUrl'] = $url;

        $url = $offer['bannerUrl'] ?? '';
        if ( ! Util::isUrlExists($url)) {
            $offer['bannerUrl'] = 'https://skux-test.virtualcards.us/static/skux/offer.png';
        }

        $retailers = $offer['retailers'] ?? [];
        foreach ($retailers as $i => $retailer) {
            $retailers[$i] = $this->fixRetailerDetails($retailer);
        }
        $offer['retailers'] = $retailers;

        if (!empty($offer['userCard'])) {
            $offer['userCard'] = $this->fixCardDetails($offer['userCard']);
        } else if (array_key_exists('userCard', $offer)) {
            $offer['userCard'] = null;
        }

        return $offer;
    }

    public function fixRetailerDetails($retailer)
    {
        if (empty($retailer['logoUrl'])) {
            return $retailer;
        }

        $url = $retailer['logoUrl'];
        if (Util::isUrlExists($url)) {
            return $retailer;
        }

        $retailer['logoUrl'] = 'https://skux-test.virtualcards.us/static/skux/retailer.png';
        return $retailer;
    }

    public function fixCardDetails($card)
    {
        if (!empty($card['cardImage'])) {
            $card['image'] = $card['cardImage'];
        }

        if (empty($card['image'])) {
            $card['image'] = 'https://skux-test.virtualcards.us/static/skux/card_clean.png';
        }

        if (!empty($card['status'])) {
            $card['cardStatus'] = $card['status'];
        }

        if (Util::isStaging()) {
            $pan = $card['maskedCardNumber'] ?? '';
            $an = $card['accountNumber'] ?? '';
            if (!$an) {
                $an = $card['uuid'] ?? '1111';
            }
            if (!$an) {
                $an = '1111';
            }
            if (!empty($pan)) {
                $pan = str_replace('*', '', $pan);
                if (strlen($pan) <= 2) {
                    $card['maskedCardNumber'] = Util::maskPan(substr($an, -4, 2) . $pan);
                }
            }

            // https://app.asana.com/0/****************/****************
            $id = $card['uuid'] ?? '';
            $cacheKey = 'skux_overwrite_card_status_' . $id;
            if (Data::has($cacheKey)) {
                $card['status'] = $card['cardStatus'] = Data::get($cacheKey);
            }
        }

        return $card;
    }

    public function everClaimedOffer()
    {
        $claimed = false;
        $offers = $this->offers();
        foreach ($offers['items'] as $offer) {
            if (!empty($offer['userCard'])) {
                $claimed = true;
                break;
            }
        }
        return $claimed;
    }
}
