<?php

namespace SkuxBundle\Services;

use Carbon\Carbon;
use CoreBundle\Entity\CardProgram;
use CoreBundle\Entity\CardProgramCardType;
use CoreBundle\Entity\Country;
use CoreBundle\Entity\ExternalInvoke;
use CoreBundle\Entity\UserCard;
use CoreBundle\Entity\UserCardPurse;
use CoreBundle\Exception\FailedException;
use CoreBundle\Services\Common\CardService;
use CoreBundle\Services\SSLEncryptionService;
use CoreBundle\Utils\Log;
use CoreBundle\Utils\Money;
use CoreBundle\Utils\Util;
use FisBundle\Services\Reward\PrepaidAPI;
use SalexUserBundle\Entity\User;

class FisPurseService
{
    public const FIS_PURSE_TO_SKUX_STATUSES = [
//        2 - Active	->	ACTIVATE = 'ACTIVATE', // 1000
//        3 - Closed	->	CLOSE = 'CLOSE', // RequestCode - 1200 - CLOSE
//        6 - Suspended	->	SUSPEND = 'SUSPEND', // 1100
//        7 - Expired	->	EXPIRE = 'EXPIRE', // 1009 - EXPIRE
        'ACTIVE' => 'ACTIVATE', // 2 - Active
        'CLOSED' => 'CLOSE', // 3 - Closed
        'SUSPENDED' => 'SUSPEND', // 6 - Suspended
        'EXPIRED' => 'EXPIRE', // 7 - Expired
    ];

    public const FIS_CARD_TO_SKUX_STATUSES = [
//        DORMANT, 0	->	SUSPEND = 'SUSPEND', // 1100
//        READY, 1	->	ACTIVATE = 'ACTIVATE', // 1000
//        ACTIVE, 2	->	ACTIVATE = 'ACTIVATE', // 1000
//        CLOSED, 3	->	CLOSE = 'CLOSE', // RequestCode - 1200 - CLOSE
//        LOST, 4	->	LOST = 'LOST', // RequestCode - 2111 - LOST
//        REPLACED, 5	->	REPLACE = 'REPLACE', // RequestCode - 1510 - REPLACE
//        SUSPENDED, 6	->	SUSPEND = 'SUSPEND', // 1100
//        FRAUD, 14	->	MARK_FRAUD = 'MARK FRAUD', // 1005 - MARK FRAUD
//        PFRAUD, 15	->	MARK_PFRAUD = 'MARK PFRAUD', // 1006 - MARK PFRAUD
//        VOID, 21	->	EXPIRE = 'EXPIRE', // 1009 - EXPIRE
//        DESTROYED, 23	->	CLOSE = 'CLOSE', // RequestCode - 1200 - CLOSE
        'DORMANT' => 'SUSPEND',
        'READY' => 'ACTIVATE',
        'ACTIVE' => 'ACTIVATE',
        'CLOSED' => 'CLOSE',
        'LOST' => 'LOST',
        'REPLACED' => 'REPLACE',
        'SUSPENDED' => 'SUSPEND',
        'FRAUD' => 'MARK_FRAUD',
        'PFRAUD' => 'MARK_PFRAUD',
        'VOID' => 'EXPIRE',
        'DESTROYED' => 'CLOSE',
    ];

    public const SKUX_TO_FIS_PURSE_STATUSES = [
//        ACTIVATE = 'ACTIVATE', // 1000	->	2 - Active
//        SUSPEND = 'SUSPEND', // 1100	->	6 - Suspended
//        UNSUSPEND = 'UNSUSPEND', // 1110	->	2 - Active
//        NEWACCOUNT = 'NEWACCOUNT', // 1530	->	2 - Active
//        REVALUE = 'REVALUE', // ??? 1710	->	2 - Active
//        PURCHASE = 'PURCHASE', // ??? 94200	->	2 - Active
//        LOSTSTOLEN = 'LOSTSTOLEN', // 1300 - LOSTSTOLEN	->	3 - Closed
//        MARK_FRAUD = 'MARK FRAUD', // 1005 - MARK FRAUD	->	6 - Suspended
//        MARK_PFRAUD = 'MARK PFRAUD', // 1006 - MARK PFRAUD	->	6 - Suspended
//        UNMARK_PFRAUD = 'UNMARK PFRAUD', // 1007 - UNMARK PFRAUD	->	2 - Active
//        EXPIRE = 'EXPIRE', // 1009 - EXPIRE	->	7 - Expired
//        DEACTIVATE = 'DEACTIVATE', // RequestCode - 1010 - DEACTIVATE	->	6 - Suspended
//        CLOSE = 'CLOSE', // RequestCode - 1200 - CLOSE	->	3 - Closed
//        REPLACE = 'REPLACE', // RequestCode - 1510 - REPLACE	->	3 - Closed
//        LOST = 'LOST', // RequestCode - 2111 - LOST	->	3 - Closed
        'ACTIVATE' => '2',
        'SUSPEND' => '6',
        'UNSUSPEND' => '2',
        'NEWACCOUNT' => '2',
        'REVALUE' => '2',
        'PURCHASE' => '2',
        'LOSTSTOLEN' => '3',
        'MARK_FRAUD' => '6',
        'MARK_PFRAUD' => '6',
        'UNMARK_PFRAUD' => '2',
        'EXPIRE' => '7',
        'DEACTIVATE' => '6',
        'CLOSE' => '3',
        'REPLACE' => '3',
        'LOST' => '3',
    ];

    public static function getAPI()
    {
        return new PrepaidAPI(null, 'skux');
    }

    /**
     * @param User $user
     * @param null $packageId
     *
     * @return UserCard
     * @throws FailedException
     */
    public static function assignCard(User $user, $packageId = null)
    {
        $api = self::getAPI();
        $personId = Util::meta($user, 'fisPersonId');
        if ($personId) {
            $params = [
                'Comment' => 'Create extra card for ' . $user->getId(),
            ];

            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->assignCardToExistingPerson($personId, $params, $packageId);
            if ($ei && $ei->isFailed()) {
                throw FailedException::fromEi($ei);
            }
            if (empty($data[4])) {
                throw new FailedException('Invalid proxy number from the API response.');
            }
            [$cardNum, $personId, $cvx2, $expDate, $proxyKey] = $data;
        } else {
            $country = $user->getCountry() ?? Country::usa();
            $ssn = Util::meta($user, 'ssn');
            if ($ssn) {
                $ssn = SSLEncryptionService::tryToDecrypt($ssn);
            }

            $params = [
                'First' => $user->getFirstName(),
                'Last' => $user->getLastName(),
                'Addr1' => $user->getAddress(),
                'Addr2' => $user->getAddressline(),
                'City' => $user->getCity(),
                'Email' => $user->getEmail(),
                'Country' => $country->getNumericCode(),
                'ZipCode' => $user->getZip(),
                'Phone' => null,
                'SSN' => $ssn ?: $user->getId(),
                'ClientUniqueID' => $user->getId(),
                'Comment' => 'Create initial card for ' . $user->getId(),
            ];

            if ($user->getBirthday()) {
                $params['DOB'] = $user->getBirthday()->format('m/d/Y');
            }

            if ($user->getGender()) {
                $params['Gender'] = $user->getGenderAbbr();
            }

            /** @var ExternalInvoke $ei */
            [$ei, $data] = $api->assignCard($params, $packageId);
            if ($ei && $ei->isFailed()) {
                throw FailedException::fromEi($ei);
            }
            if (empty($data[7])) {
                throw new FailedException('Invalid proxy number from the API response.');
            }
            [$cardNum, $personId, $cvx2, $expDate, , , , $proxyKey] = $data;

            Util::updateMeta($user, [
                'fisPersonId' => $personId,
            ]);
        }

        $uc = CardService::create($user, CardProgramCardType::getForCardProgram(CardProgram::skux()));
        $uc->setAccountNumber($proxyKey)
            ->setProxyKey($proxyKey)
            ->setExpireAt(Carbon::parse($expDate))
            ->setStatus(UserCard::STATUS_ACTIVE)
            ->setIssued(true)
            ->setIssuedAt(Carbon::now())
            ->setAccountId($personId)
            ->setBalance(0)
            ->persist();

        $uc->saveMaskedCardNumber($cardNum);
        $uc->setUpdatedPIN($cvx2);
        return $uc;
    }

    public static function aggregatedCard(User $user)
    {
        $uc = new UserCard();
        $uc->setAccountNumber($user->getId())
            ->setPan($user->getId())
            ->setStatus(UserCard::STATUS_ACTIVE);

        $balance = Util::em()->getRepository(UserCard::class)
            ->createQueryBuilder('uc')
            ->where('uc.user = :user')
            ->andWhere('uc.card = :card')
            ->setParameter('user', $user)
            ->setParameter('card', CardProgramCardType::getForCardProgram(CardProgram::skux()))
            ->select('sum(uc.balance)')
            ->getQuery()
            ->getSingleScalarResult();
        $uc->setBalanceOnly($balance ?: 0);

        return $uc;
    }

    public static function syncBalanceStatusSilently(UserCard $uc, UserCardPurse &$purse = null)
    {
        try {
            self::syncBalanceStatus($uc, $purse);
        } catch (\Exception $e) {
            Log::warn('Failed to sync FIS: purse status for ' . $uc->getId()
                      . ': ' . $e->getMessage());
        }
    }

    /**
     * @param UserCard           $uc
     * @param UserCardPurse|NULL $purse
     *
     * @return void
     * @throws FailedException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public static function syncBalanceStatus(UserCard $uc, UserCardPurse &$purse = null)
    {
        $api = self::getAPI();

        // Fetch card balance first
        $data = ExternalInvoke::host($api->openToBuy($uc->getAccountNumber()));
        $balance = Money::normalizeAmount($data[0] ?? 0, 'USD');
        $uc->setBalance($balance)
            ->persist();

        // Update purses
        $result = ExternalInvoke::host(
            $api->getPurseBalance(
                $uc->getAccountNumber(),
                $purse ? $purse->getNumber() : null
            )
        );

        $first = null;
        if ($purse) {
            foreach ($result as $item) {
                $it = $item['PurseNumber'] ?? '';
                if ((string)$it === $purse->getNumber()) {
                    $first = $item;
                    break;
                }
            }
        }
        if (!$first) {
            $first = $result[0] ?? [];
        }

        // Update cards first
        if (empty($uc->getDbaNo())) {
            $uc->setDbaNo($first['PurseNumber']);
        }
        if (in_array($first['CardStatus'], ['READY', 'ACTIVE'])) {
            $status = UserCard::STATUS_ACTIVE;
        } else {
            $status = strtolower($first['CardStatus']);
        }
        $uc->setStatus($status, false, false)
            ->setNativeStatus($first['CardStatus'])
            ->persist();

        // Create or update purses
        $em = Util::em();
        foreach ($result as $it) {
            if (empty($it['PurseNumber'])) {
                continue;
            }
            $pu = $uc->findPurseByNumber($it['PurseNumber']);
            if (!$pu) {
                $pu = new UserCardPurse();
                $pu->setUserCard($uc)
                    ->setNumber($it['PurseNumber']);
            }
            $pu->setCardStatus($it['CardStatus'])
                ->setStatus($it['PurseStatus'])
                ->setType($it['PurseType'])
                ->setEffectiveDate(Carbon::parse($it['PurseEffectiveDate']))
                ->setExpirationDate(Carbon::parse($it['PurseExpirationDate']))
                ->setBalance(Money::normalizeAmount($it['PurseBalance'], 'USD'));

            Util::updateMeta($pu, [
                'fisDetail' => $it,
            ], false);

            $em->persist($pu);

            if ($purse && (string)$purse->getNumber() === (string)$pu->getNumber()) {
                $purse = $pu;
            }
        }
        $em->flush();
    }
}
